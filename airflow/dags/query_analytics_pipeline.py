"""
查詢分析數據管道
處理查詢記錄的收集、清理、統計計算和關鍵詞分析
"""

import json
import pendulum
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from airflow import DAG
from airflow.decorators import dag, task
from airflow.operators.python import PythonOperator
from airflow.providers.postgres.hooks.postgres import PostgresHook
from airflow.providers.redis.hooks.redis_hook import RedisHook
from airflow.providers.elasticsearch.hooks.elasticsearch import ElasticsearchHook
from airflow.models import Variable
from airflow.utils.task_group import TaskGroup

import pandas as pd
import numpy as np
from sklearn.cluster import KMeans
from sklearn.feature_extraction.text import TfidfVectorizer
import structlog

# 配置
logger = structlog.get_logger()

default_args = {
    'owner': 'ai-seo-team',
    'depends_on_past': False,
    'start_date': pendulum.datetime(2024, 1, 1, tz="UTC"),
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
    'catchup': False
}

@dag(
    dag_id='query_analytics_pipeline',
    default_args=default_args,
    description='查詢分析數據管道',
    schedule_interval='@hourly',
    tags=['analytics', 'queries', 'keywords'],
    max_active_runs=1,
    max_active_tasks=10
)
def query_analytics_pipeline():
    """查詢分析數據管道主DAG"""
    
    @task(task_id='extract_query_data')
    def extract_query_data(**context) -> Dict[str, Any]:
        """從Elasticsearch提取查詢數據"""
        logger.info("開始提取查詢數據")
        
        # 獲取時間範圍（過去1小時）
        end_time = context['data_interval_end']
        start_time = end_time - timedelta(hours=1)
        
        # 連接Elasticsearch
        es_hook = ElasticsearchHook(elasticsearch_conn_id='elasticsearch_default')
        es_client = es_hook.get_conn()
        
        # 構建查詢
        query_body = {
            "query": {
                "range": {
                    "timestamp": {
                        "gte": start_time.isoformat(),
                        "lt": end_time.isoformat()
                    }
                }
            },
            "size": 10000,
            "_source": [
                "id", "query", "normalized_query", "source", "intent", 
                "user_id", "session_id", "keywords", "timestamp", 
                "response_time", "results_count", "device_type", "location"
            ]
        }
        
        # 執行查詢
        response = es_client.search(
            index="query_records",
            body=query_body
        )
        
        # 處理結果
        queries = []
        for hit in response['hits']['hits']:
            queries.append(hit['_source'])
        
        logger.info(f"提取了 {len(queries)} 條查詢記錄")
        
        return {
            'queries': queries,
            'total_count': len(queries),
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat()
        }
    
    @task(task_id='clean_and_process_queries')
    def clean_and_process_queries(query_data: Dict[str, Any]) -> Dict[str, Any]:
        """清理和處理查詢數據"""
        logger.info("開始清理和處理查詢數據")
        
        queries = query_data['queries']
        if not queries:
            return {'processed_queries': [], 'stats': {}}
        
        # 轉換為DataFrame
        df = pd.DataFrame(queries)
        
        # 數據清理
        df = df.dropna(subset=['query'])  # 移除空查詢
        df['query'] = df['query'].str.strip()  # 去除空格
        df = df[df['query'].str.len() > 0]  # 移除空字符串
        
        # 標準化處理
        df['normalized_query'] = df['query'].str.lower()
        df['query_length'] = df['query'].str.len()
        df['word_count'] = df['query'].str.split().str.len()
        
        # 設備類型標準化
        df['device_type'] = df['device_type'].fillna('unknown')
        
        # 響應時間處理
        df['response_time'] = pd.to_numeric(df['response_time'], errors='coerce')
        df['response_time'] = df['response_time'].fillna(df['response_time'].median())
        
        # 計算統計信息
        stats = {
            'total_processed': len(df),
            'unique_queries': df['normalized_query'].nunique(),
            'unique_users': df['user_id'].nunique() if 'user_id' in df.columns else 0,
            'avg_query_length': df['query_length'].mean(),
            'avg_word_count': df['word_count'].mean(),
            'avg_response_time': df['response_time'].mean(),
            'source_distribution': df['source'].value_counts().to_dict(),
            'device_distribution': df['device_type'].value_counts().to_dict(),
            'intent_distribution': df['intent'].value_counts().to_dict() if 'intent' in df.columns else {}
        }
        
        logger.info(f"處理完成，共 {len(df)} 條記錄")
        
        return {
            'processed_queries': df.to_dict('records'),
            'stats': stats
        }
    
    @task(task_id='extract_keywords')
    def extract_keywords(processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取和分析關鍵詞"""
        logger.info("開始關鍵詞提取和分析")
        
        queries = processed_data['processed_queries']
        if not queries:
            return {'keywords': [], 'keyword_stats': {}}
        
        df = pd.DataFrame(queries)
        
        # 提取所有關鍵詞
        all_keywords = []
        for _, row in df.iterrows():
            if row.get('keywords'):
                all_keywords.extend(row['keywords'])
        
        # 如果沒有預處理的關鍵詞，從查詢中提取
        if not all_keywords:
            import re
            for query in df['query']:
                words = re.findall(r'\b\w+\b', query.lower())
                all_keywords.extend([w for w in words if len(w) > 2])
        
        # 統計關鍵詞頻率
        keyword_counts = pd.Series(all_keywords).value_counts()
        
        # 關鍵詞統計
        keyword_stats = []
        for keyword, count in keyword_counts.head(100).items():
            # 計算該關鍵詞相關的查詢統計
            related_queries = df[df['query'].str.contains(keyword, case=False, na=False)]
            
            stats = {
                'keyword': keyword,
                'frequency': int(count),
                'unique_sessions': related_queries['session_id'].nunique() if 'session_id' in related_queries.columns else 0,
                'avg_response_time': float(related_queries['response_time'].mean()) if len(related_queries) > 0 else 0,
                'source_distribution': related_queries['source'].value_counts().to_dict(),
                'intent_distribution': related_queries['intent'].value_counts().to_dict() if 'intent' in related_queries.columns else {}
            }
            keyword_stats.append(stats)
        
        logger.info(f"提取了 {len(keyword_stats)} 個關鍵詞")
        
        return {
            'keywords': keyword_stats,
            'keyword_stats': {
                'total_keywords': len(keyword_counts),
                'unique_keywords': len(keyword_counts),
                'top_10': keyword_counts.head(10).to_dict()
            }
        }
    
    @task(task_id='perform_keyword_clustering')
    def perform_keyword_clustering(keyword_data: Dict[str, Any]) -> Dict[str, Any]:
        """執行關鍵詞聚類分析"""
        logger.info("開始關鍵詞聚類分析")
        
        keywords = keyword_data['keywords']
        if len(keywords) < 10:
            return {'clusters': [], 'cluster_stats': {}}
        
        # 準備數據
        keyword_texts = [kw['keyword'] for kw in keywords]
        
        try:
            # TF-IDF向量化
            vectorizer = TfidfVectorizer(
                max_features=1000,
                stop_words='english',
                ngram_range=(1, 2)
            )
            
            # 為每個關鍵詞創建簡單的文檔（這裡可以改進）
            documents = keyword_texts
            tfidf_matrix = vectorizer.fit_transform(documents)
            
            # K-means聚類
            n_clusters = min(10, len(keywords) // 3)  # 動態確定聚類數
            if n_clusters < 2:
                n_clusters = 2
            
            kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
            cluster_labels = kmeans.fit_predict(tfidf_matrix)
            
            # 組織聚類結果
            clusters = {}
            for i, (keyword_data, label) in enumerate(zip(keywords, cluster_labels)):
                cluster_id = f"cluster_{label}"
                if cluster_id not in clusters:
                    clusters[cluster_id] = {
                        'cluster_id': cluster_id,
                        'keywords': [],
                        'total_frequency': 0,
                        'avg_response_time': 0
                    }
                
                clusters[cluster_id]['keywords'].append(keyword_data)
                clusters[cluster_id]['total_frequency'] += keyword_data['frequency']
            
            # 計算聚類統計
            cluster_list = list(clusters.values())
            for cluster in cluster_list:
                cluster['keyword_count'] = len(cluster['keywords'])
                cluster['avg_response_time'] = np.mean([kw['avg_response_time'] for kw in cluster['keywords']])
                cluster['top_keyword'] = max(cluster['keywords'], key=lambda x: x['frequency'])['keyword']
            
            # 按總頻率排序
            cluster_list.sort(key=lambda x: x['total_frequency'], reverse=True)
            
            logger.info(f"完成聚類分析，共 {len(cluster_list)} 個聚類")
            
            return {
                'clusters': cluster_list,
                'cluster_stats': {
                    'total_clusters': len(cluster_list),
                    'avg_keywords_per_cluster': np.mean([c['keyword_count'] for c in cluster_list]),
                    'largest_cluster_size': max([c['keyword_count'] for c in cluster_list]) if cluster_list else 0
                }
            }
            
        except Exception as e:
            logger.error(f"聚類分析失敗: {str(e)}")
            return {'clusters': [], 'cluster_stats': {}}
    
    @task(task_id='calculate_hourly_stats')
    def calculate_hourly_stats(processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """計算小時統計"""
        logger.info("開始計算小時統計")
        
        queries = processed_data['processed_queries']
        if not queries:
            return {'hourly_stats': {}}
        
        df = pd.DataFrame(queries)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df['hour'] = df['timestamp'].dt.floor('H')
        
        # 按小時分組統計
        hourly_stats = []
        for hour, group in df.groupby('hour'):
            stats = {
                'hour': hour.isoformat(),
                'total_queries': len(group),
                'unique_queries': group['normalized_query'].nunique(),
                'unique_users': group['user_id'].nunique() if 'user_id' in group.columns else 0,
                'avg_response_time': float(group['response_time'].mean()),
                'success_rate': float((group['results_count'] > 0).mean()) if 'results_count' in group.columns else 1.0,
                'top_queries': group['normalized_query'].value_counts().head(10).to_dict(),
                'source_distribution': group['source'].value_counts().to_dict(),
                'device_distribution': group['device_type'].value_counts().to_dict(),
                'intent_distribution': group['intent'].value_counts().to_dict() if 'intent' in group.columns else {}
            }
            hourly_stats.append(stats)
        
        logger.info(f"計算了 {len(hourly_stats)} 個小時的統計")
        
        return {'hourly_stats': hourly_stats}
    
    @task(task_id='store_stats_to_redis')
    def store_stats_to_redis(
        processed_data: Dict[str, Any],
        keyword_data: Dict[str, Any],
        cluster_data: Dict[str, Any],
        hourly_data: Dict[str, Any]
    ) -> Dict[str, str]:
        """將統計結果存儲到Redis"""
        logger.info("開始存儲統計結果到Redis")
        
        redis_hook = RedisHook(redis_conn_id='redis_default')
        redis_client = redis_hook.get_conn()
        
        current_hour = datetime.utcnow().strftime('%Y-%m-%d-%H')
        
        try:
            # 存儲基本統計
            basic_stats = processed_data['stats']
            redis_client.hset(
                f"analytics:hourly:{current_hour}",
                mapping={k: json.dumps(v) if isinstance(v, (dict, list)) else str(v) 
                        for k, v in basic_stats.items()}
            )
            redis_client.expire(f"analytics:hourly:{current_hour}", 86400 * 7)  # 7天過期
            
            # 存儲關鍵詞統計
            if keyword_data['keywords']:
                redis_client.set(
                    f"analytics:keywords:{current_hour}",
                    json.dumps(keyword_data['keywords'][:50]),  # 存儲前50個關鍵詞
                    ex=86400 * 7
                )
            
            # 存儲聚類結果
            if cluster_data['clusters']:
                redis_client.set(
                    f"analytics:clusters:{current_hour}",
                    json.dumps(cluster_data['clusters']),
                    ex=86400 * 7
                )
            
            # 更新實時趨勢數據
            for keyword in keyword_data['keywords'][:20]:  # 前20個關鍵詞
                redis_client.zadd('trending_keywords', {keyword['keyword']: keyword['frequency']})
            redis_client.expire('trending_keywords', 3600)  # 1小時過期
            
            logger.info("統計結果已存儲到Redis")
            
            return {"status": "success", "message": "統計結果已存儲到Redis"}
            
        except Exception as e:
            logger.error(f"存儲到Redis失敗: {str(e)}")
            raise
    
    @task(task_id='store_stats_to_postgres')
    def store_stats_to_postgres(
        processed_data: Dict[str, Any],
        keyword_data: Dict[str, Any],
        hourly_data: Dict[str, Any]
    ) -> Dict[str, str]:
        """將統計結果存儲到PostgreSQL"""
        logger.info("開始存儲統計結果到PostgreSQL")
        
        postgres_hook = PostgresHook(postgres_conn_id='postgres_default')
        
        try:
            # 存儲小時統計
            for hour_stat in hourly_data['hourly_stats']:
                insert_sql = """
                INSERT INTO query_hourly_stats (
                    hour, total_queries, unique_queries, unique_users,
                    avg_response_time, success_rate, top_queries,
                    source_distribution, device_distribution, intent_distribution,
                    created_at
                ) VALUES (
                    %(hour)s, %(total_queries)s, %(unique_queries)s, %(unique_users)s,
                    %(avg_response_time)s, %(success_rate)s, %(top_queries)s,
                    %(source_distribution)s, %(device_distribution)s, %(intent_distribution)s,
                    NOW()
                ) ON CONFLICT (hour) DO UPDATE SET
                    total_queries = EXCLUDED.total_queries,
                    unique_queries = EXCLUDED.unique_queries,
                    unique_users = EXCLUDED.unique_users,
                    avg_response_time = EXCLUDED.avg_response_time,
                    success_rate = EXCLUDED.success_rate,
                    top_queries = EXCLUDED.top_queries,
                    source_distribution = EXCLUDED.source_distribution,
                    device_distribution = EXCLUDED.device_distribution,
                    intent_distribution = EXCLUDED.intent_distribution,
                    updated_at = NOW()
                """
                
                postgres_hook.run(insert_sql, parameters={
                    'hour': hour_stat['hour'],
                    'total_queries': hour_stat['total_queries'],
                    'unique_queries': hour_stat['unique_queries'],
                    'unique_users': hour_stat['unique_users'],
                    'avg_response_time': hour_stat['avg_response_time'],
                    'success_rate': hour_stat['success_rate'],
                    'top_queries': json.dumps(hour_stat['top_queries']),
                    'source_distribution': json.dumps(hour_stat['source_distribution']),
                    'device_distribution': json.dumps(hour_stat['device_distribution']),
                    'intent_distribution': json.dumps(hour_stat['intent_distribution'])
                })
            
            # 存儲關鍵詞統計
            current_hour = datetime.utcnow().replace(minute=0, second=0, microsecond=0)
            for keyword in keyword_data['keywords'][:100]:  # 前100個關鍵詞
                insert_sql = """
                INSERT INTO keyword_hourly_stats (
                    hour, keyword, frequency, unique_sessions, avg_response_time,
                    source_distribution, intent_distribution, created_at
                ) VALUES (
                    %(hour)s, %(keyword)s, %(frequency)s, %(unique_sessions)s,
                    %(avg_response_time)s, %(source_distribution)s, %(intent_distribution)s, NOW()
                ) ON CONFLICT (hour, keyword) DO UPDATE SET
                    frequency = EXCLUDED.frequency,
                    unique_sessions = EXCLUDED.unique_sessions,
                    avg_response_time = EXCLUDED.avg_response_time,
                    source_distribution = EXCLUDED.source_distribution,
                    intent_distribution = EXCLUDED.intent_distribution,
                    updated_at = NOW()
                """
                
                postgres_hook.run(insert_sql, parameters={
                    'hour': current_hour,
                    'keyword': keyword['keyword'],
                    'frequency': keyword['frequency'],
                    'unique_sessions': keyword['unique_sessions'],
                    'avg_response_time': keyword['avg_response_time'],
                    'source_distribution': json.dumps(keyword['source_distribution']),
                    'intent_distribution': json.dumps(keyword['intent_distribution'])
                })
            
            logger.info("統計結果已存儲到PostgreSQL")
            
            return {"status": "success", "message": "統計結果已存儲到PostgreSQL"}
            
        except Exception as e:
            logger.error(f"存儲到PostgreSQL失敗: {str(e)}")
            raise
    
    @task(task_id='cleanup_old_stats')
    def cleanup_old_stats(**context) -> Dict[str, str]:
        """清理舊統計數據"""
        logger.info("開始清理舊統計數據")
        
        # 清理Redis舊數據
        redis_hook = RedisHook(redis_conn_id='redis_default')
        redis_client = redis_hook.get_conn()
        
        # 清理7天前的小時統計
        cutoff_time = datetime.utcnow() - timedelta(days=7)
        cutoff_hour = cutoff_time.strftime('%Y-%m-%d-%H')
        
        # 獲取所有analytics鍵並清理舊的
        pattern = "analytics:hourly:*"
        keys = redis_client.keys(pattern)
        deleted_count = 0
        
        for key in keys:
            key_str = key.decode('utf-8') if isinstance(key, bytes) else key
            if key_str < f"analytics:hourly:{cutoff_hour}":
                redis_client.delete(key)
                deleted_count += 1
        
        # 清理PostgreSQL舊數據（保留90天）
        postgres_hook = PostgresHook(postgres_conn_id='postgres_default')
        
        cleanup_queries = [
            "DELETE FROM query_hourly_stats WHERE hour < NOW() - INTERVAL '90 days'",
            "DELETE FROM keyword_hourly_stats WHERE hour < NOW() - INTERVAL '90 days'"
        ]
        
        for query in cleanup_queries:
            postgres_hook.run(query)
        
        logger.info(f"清理完成，刪除了 {deleted_count} 個Redis鍵")
        
        return {"status": "success", "message": f"清理完成，刪除了 {deleted_count} 個舊記錄"}
    
    # 定義任務依賴關係
    query_data = extract_query_data()
    processed_data = clean_and_process_queries(query_data)
    
    # 並行處理不同的分析任務
    with TaskGroup("analysis_tasks") as analysis_group:
        keyword_data = extract_keywords(processed_data)
        cluster_data = perform_keyword_clustering(keyword_data)
        hourly_data = calculate_hourly_stats(processed_data)
    
    # 存儲結果
    with TaskGroup("storage_tasks") as storage_group:
        store_redis = store_stats_to_redis(processed_data, keyword_data, cluster_data, hourly_data)
        store_postgres = store_stats_to_postgres(processed_data, keyword_data, hourly_data)
    
    # 清理任務
    cleanup = cleanup_old_stats()
    
    # 設置依賴關係
    processed_data >> analysis_group >> storage_group >> cleanup

# 實例化DAG
query_analytics_dag = query_analytics_pipeline() 