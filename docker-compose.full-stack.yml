version: '3.8'

services:
  # ========== 前端服務 ==========
  nextjs:
    build:
      context: .
      dockerfile: Dockerfile
      target: runner
    container_name: aiseo-frontend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_FASTAPI_URL=http://fastapi:8000
      - NEXT_PUBLIC_EXPRESS_URL=http://express:3001
      - NEXT_PUBLIC_WEBSOCKET_URL=ws://localhost:8002
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
    depends_on:
      - fastapi
      - express
      - redis
      - websocket-server
    networks:
      - aiseo-network
    restart: unless-stopped

  # ========== 後端API服務 ==========
  fastapi:
    build:
      context: ./backend-fastapi
      dockerfile: Dockerfile
    container_name: aiseo-backend-fastapi
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=${DATABASE_URL:-******************************************************/ai_seo_king}
      - REDIS_URL=redis://redis:6379/0
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - KAFKA_BROKERS=kafka1:29092,kafka2:29093,kafka3:29094
      - SPARK_MASTER_URL=spark://spark-master:7077
      - MLFLOW_TRACKING_URI=http://mlflow:5000
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - postgres
      - redis
      - elasticsearch
      - kafka1
      - kafka2
      - kafka3
      - spark-master
      - mlflow
    networks:
      - aiseo-network
    volumes:
      - fastapi-logs:/app/logs
      - shared-data:/app/shared
    restart: unless-stopped

  express:
    build:
      context: ./backend-express
      dockerfile: Dockerfile
    container_name: aiseo-backend-express
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL:-******************************************************/ai_seo_king}
      - REDIS_URL=redis://redis:6379/1
      - KAFKA_BROKERS=kafka1:29092,kafka2:29093,kafka3:29094
    depends_on:
      - postgres
      - redis
      - kafka1
    networks:
      - aiseo-network
    volumes:
      - express-uploads:/app/uploads
    restart: unless-stopped

  # WebSocket 實時更新服務
  websocket-server:
    build:
      context: ./websocket-server
      dockerfile: Dockerfile
    container_name: aiseo-websocket
    ports:
      - "8002:8002"
    environment:
      - REDIS_URL=redis://redis:6379/2
      - KAFKA_BROKERS=kafka1:29092,kafka2:29093,kafka3:29094
    depends_on:
      - redis
      - kafka1
    networks:
      - aiseo-network
    restart: unless-stopped

  # ========== 資料庫服務 ==========
  postgres:
    image: postgres:15-alpine
    container_name: aiseo-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=ai_seo_user
      - POSTGRES_PASSWORD=ai_seo_password
      - POSTGRES_DB=ai_seo_king
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - aiseo-network
    restart: unless-stopped

  # ========== 快取服務 ==========
  redis:
    image: redis:7-alpine
    container_name: aiseo-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru
    volumes:
      - redis-data:/data
    networks:
      - aiseo-network
    restart: unless-stopped

  # ========== 搜索服務 ==========
  elasticsearch:
    image: elasticsearch:8.11.0
    container_name: aiseo-elasticsearch
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - cluster.name=ai-seo-cluster
      - node.name=aiseo-es-node
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms2g -Xmx2g"
      - xpack.security.enabled=false
      - xpack.ml.enabled=true
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    networks:
      - aiseo-network
    restart: unless-stopped

  kibana:
    image: kibana:8.11.0
    container_name: aiseo-kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - aiseo-network
    restart: unless-stopped

  # ========== 訊息佇列服務 (Kafka) ==========
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: aiseo-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper-data:/var/lib/zookeeper/data
    networks:
      - aiseo-network
    restart: unless-stopped

  kafka1:
    image: confluentinc/cp-kafka:7.4.0
    container_name: aiseo-kafka1
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka1:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 2
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_NUM_PARTITIONS: 3
      KAFKA_DEFAULT_REPLICATION_FACTOR: 2
    volumes:
      - kafka1-data:/var/lib/kafka/data
    networks:
      - aiseo-network
    restart: unless-stopped

  kafka2:
    image: confluentinc/cp-kafka:7.4.0
    container_name: aiseo-kafka2
    depends_on:
      - zookeeper
    ports:
      - "9093:9093"
    environment:
      KAFKA_BROKER_ID: 2
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka2:29093,PLAINTEXT_HOST://localhost:9093
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 2
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_NUM_PARTITIONS: 3
      KAFKA_DEFAULT_REPLICATION_FACTOR: 2
    volumes:
      - kafka2-data:/var/lib/kafka/data
    networks:
      - aiseo-network
    restart: unless-stopped

  kafka3:
    image: confluentinc/cp-kafka:7.4.0
    container_name: aiseo-kafka3
    depends_on:
      - zookeeper
    ports:
      - "9094:9094"
    environment:
      KAFKA_BROKER_ID: 3
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka3:29094,PLAINTEXT_HOST://localhost:9094
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 2
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_NUM_PARTITIONS: 3
      KAFKA_DEFAULT_REPLICATION_FACTOR: 2
    volumes:
      - kafka3-data:/var/lib/kafka/data
    networks:
      - aiseo-network
    restart: unless-stopped

  # Kafka管理UI
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: aiseo-kafka-ui
    ports:
      - "8080:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: aiseo-kafka-cluster
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka1:29092,kafka2:29093,kafka3:29094
    depends_on:
      - kafka1
      - kafka2
      - kafka3
    networks:
      - aiseo-network
    restart: unless-stopped

  # ========== 資料收集服務 (Flume) ==========
  flume:
    build:
      context: ./flume
      dockerfile: Dockerfile
    container_name: aiseo-flume
    environment:
      - KAFKA_BROKERS=kafka1:29092,kafka2:29093,kafka3:29094
      - ELASTICSEARCH_URL=http://elasticsearch:9200
    depends_on:
      - kafka1
      - elasticsearch
    volumes:
      - flume-logs:/opt/flume/logs
      - shared-data:/opt/flume/data
    networks:
      - aiseo-network
    restart: unless-stopped

  # ========== 大數據處理框架 ==========
  # Spark Master
  spark-master:
    image: bitnami/spark:3.5
    container_name: aiseo-spark-master
    ports:
      - "8081:8080"
      - "7077:7077"
    environment:
      - SPARK_MODE=master
      - SPARK_RPC_AUTHENTICATION_ENABLED=no
      - SPARK_RPC_ENCRYPTION_ENABLED=no
      - SPARK_LOCAL_STORAGE_ENCRYPTION_ENABLED=no
      - SPARK_SSL_ENABLED=no
    volumes:
      - spark-data:/opt/bitnami/spark/data
      - shared-data:/opt/bitnami/spark/shared
    networks:
      - aiseo-network
    restart: unless-stopped

  # Spark Worker 1
  spark-worker-1:
    image: bitnami/spark:3.5
    container_name: aiseo-spark-worker-1
    environment:
      - SPARK_MODE=worker
      - SPARK_MASTER_URL=spark://spark-master:7077
      - SPARK_WORKER_MEMORY=2g
      - SPARK_WORKER_CORES=2
    depends_on:
      - spark-master
    volumes:
      - shared-data:/opt/bitnami/spark/shared
    networks:
      - aiseo-network
    restart: unless-stopped

  # Spark Worker 2
  spark-worker-2:
    image: bitnami/spark:3.5
    container_name: aiseo-spark-worker-2
    environment:
      - SPARK_MODE=worker
      - SPARK_MASTER_URL=spark://spark-master:7077
      - SPARK_WORKER_MEMORY=2g
      - SPARK_WORKER_CORES=2
    depends_on:
      - spark-master
    volumes:
      - shared-data:/opt/bitnami/spark/shared
    networks:
      - aiseo-network
    restart: unless-stopped

  # ========== 流處理 (Flink) ==========
  flink-jobmanager:
    image: flink:1.18
    container_name: aiseo-flink-jobmanager
    ports:
      - "8082:8081"
    command: jobmanager
    environment:
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: flink-jobmanager
        taskmanager.numberOfTaskSlots: 2
        parallelism.default: 1
    volumes:
      - flink-data:/opt/flink/data
      - shared-data:/opt/flink/shared
    networks:
      - aiseo-network
    restart: unless-stopped

  flink-taskmanager:
    image: flink:1.18
    depends_on:
      - flink-jobmanager
    command: taskmanager
    deploy:
      replicas: 2
    environment:
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: flink-jobmanager
        taskmanager.numberOfTaskSlots: 2
        parallelism.default: 1
    volumes:
      - shared-data:/opt/flink/shared
    networks:
      - aiseo-network
    restart: unless-stopped

  # ========== 機器學習平台 ==========
  # MLflow
  mlflow:
    image: python:3.11-slim
    container_name: aiseo-mlflow
    ports:
      - "5000:5000"
    command: >
      bash -c "pip install mlflow==2.15.1 psycopg2-binary && 
      mlflow server 
      --backend-store-uri ******************************************************/ai_seo_king
      --default-artifact-root /mlflow/artifacts
      --host 0.0.0.0
      --port 5000"
    environment:
      - MLFLOW_S3_ENDPOINT_URL=http://minio:9000
      - AWS_ACCESS_KEY_ID=minio
      - AWS_SECRET_ACCESS_KEY=minio123
    depends_on:
      - postgres
      - minio
    volumes:
      - mlflow-data:/mlflow
    networks:
      - aiseo-network
    restart: unless-stopped

  # TensorFlow Serving
  tensorflow-serving:
    image: tensorflow/serving:2.14.0
    container_name: aiseo-tensorflow-serving
    ports:
      - "8501:8501"
      - "8500:8500"
    environment:
      - MODEL_NAME=seo_model
    volumes:
      - ./models:/models
    command: >
      --model_config_file=/models/models.config
      --model_config_file_poll_wait_seconds=60
      --allow_version_labels_for_unavailable_models
    networks:
      - aiseo-network
    restart: unless-stopped

  # PyTorch Serve
  pytorch-serve:
    build:
      context: ./pytorch-serve
      dockerfile: Dockerfile
    container_name: aiseo-pytorch-serve
    ports:
      - "8083:8080"
      - "8084:8081"
    volumes:
      - ./models:/home/<USER>/model-store
    networks:
      - aiseo-network
    restart: unless-stopped

  # ========== 物件儲存 (MinIO) ==========
  minio:
    image: minio/minio:latest
    container_name: aiseo-minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minio
      - MINIO_ROOT_PASSWORD=minio123
    command: server /data --console-address ":9001"
    volumes:
      - minio-data:/data
    networks:
      - aiseo-network
    restart: unless-stopped

  # ========== 監控服務 ==========
  prometheus:
    image: prom/prometheus:latest
    container_name: aiseo-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    networks:
      - aiseo-network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: aiseo-grafana
    ports:
      - "3030:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - aiseo-network
    restart: unless-stopped

  # ========== 商業智能 ==========
  superset:
    image: apache/superset:latest
    container_name: aiseo-superset
    ports:
      - "8088:8088"
    environment:
      - SUPERSET_CONFIG_PATH=/app/superset_config.py
      - DATABASE_URL=******************************************************/ai_seo_king
    volumes:
      - ./superset/superset_config.py:/app/superset_config.py
      - superset-data:/app/superset_home
    depends_on:
      - postgres
    networks:
      - aiseo-network
    restart: unless-stopped

volumes:
  postgres-data:
  redis-data:
  elasticsearch-data:
  kafka1-data:
  kafka2-data:
  kafka3-data:
  zookeeper-data:
  spark-data:
  flink-data:
  mlflow-data:
  minio-data:
  prometheus-data:
  grafana-data:
  superset-data:
  fastapi-logs:
  express-uploads:
  flume-logs:
  shared-data:

networks:
  aiseo-network:
    driver: bridge
    name: aiseo-full-stack-network 