# AI SEO 優化王 - Kibana 配置文件

# 服務器配置
server.name: "ai-seo-kibana"
server.host: "0.0.0.0"
server.port: 5601

# Elasticsearch 連接配置
elasticsearch.hosts: ["http://elasticsearch:9200"]
elasticsearch.username: ""
elasticsearch.password: ""

# 請求超時設置
elasticsearch.requestTimeout: 30000
elasticsearch.shardTimeout: 30000

# 日誌配置
logging.appenders:
  file:
    type: file
    fileName: /usr/share/kibana/logs/kibana.log
    layout:
      type: json
logging.root:
  appenders:
    - default
    - file
  level: info

# 監控配置
monitoring.enabled: false
monitoring.kibana.collection.enabled: false

# 遙測配置（禁用）
telemetry.enabled: false
telemetry.optIn: false

# 安全配置
xpack.security.enabled: false
xpack.encryptedSavedObjects.encryptionKey: "ai-seo-king-encryption-key-32-chars"

# 國際化配置
i18n.locale: "zh-CN"

# 數據視圖配置
data.search.aggs.shardDelay.enabled: true

# 新功能配置
newsfeed.enabled: false

# 地圖配置
map.includeElasticMapsService: false

# 高級設置
advanced_settings.enabled: true

# 索引模式配置
kibana.index: ".kibana"
kibana.defaultAppId: "dashboard"

# CSV 導出配置
xpack.reporting.csv.maxSizeBytes: 10485760

# 保存對象配置
savedObjects.maxImportPayloadBytes: 26214400

# UI 配置
uiSettings.overrides:
  "theme:darkMode": false
  "dateFormat": "YYYY-MM-DD HH:mm:ss"
  "dateFormat:tz": "Asia/Taipei"
  "defaultIndex": "seo_*"
  "discover:sampleSize": 500
  "histogram:barTarget": 50
  "visualization:tileMap:maxPrecision": 7
  "csv:separator": ","
  "search:timeout": 600000
