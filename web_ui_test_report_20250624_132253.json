{"timestamp": "2025-06-24T13:22:53.641949", "tests": {"basic_connectivity": {"status": "success", "elasticsearch": true, "web_ui": true}, "elasticsearch_connection": {"status": "success", "cluster_health": "green", "healthy": true}, "web_ui_service": {"status": "success", "main_page": true, "auth_page": true, "config_file": true}, "authentication": {"status": "success", "auth_page_exists": true, "has_login_form": true, "has_username_field": true, "has_password_field": true}, "index_management": {"status": "success", "indices_status": {"seo_content": true, "seo_analysis": true, "seo_keywords": true, "seo_competitors": true}, "total_indices": 5}, "search_functionality": {"status": "failed", "search_results": {"seo_content": {"success": false, "error": "BadRequestError(400, 'parsing_exception', 'unknown query [query]')"}, "seo_keywords": {"success": false, "error": "BadRequestError(400, 'parsing_exception', 'unknown query [query]')"}}, "successful_searches": 0}, "security_features": {"status": "warning", "cors_enabled": false, "config_accessible": true}}, "overall_status": "needs_attention"}