# WebSocket 設置指南

## 🔧 問題解決

如果您看到 WebSocket 連接錯誤，請按照以下步驟解決：

### 錯誤信息
```
WebSocket connection to 'ws://localhost:8003/ws' failed
WebSocket error: Event {isTrusted: true, type: 'error'...}
WebSocket disconnected: 1006
Attempting to reconnect (1/5)...
```

## 🚀 解決方案

### 方案 1: 啟動 WebSocket 服務器（推薦）

1. **打開新的終端窗口**
2. **進入 WebSocket 服務器目錄**
   ```bash
   cd /Users/<USER>/src/ai-seo-king/websocket-server
   ```

3. **啟動 WebSocket 服務器**
   ```bash
   node simple-ws.js
   ```

4. **確認服務器啟動成功**
   您應該看到：
   ```
   WebSocket server started on port 8003
   Server ready at http://localhost:8003
   ```

5. **測試連接**
   ```bash
   curl http://localhost:8003/health
   ```
   應該返回：
   ```json
   {"status":"ok","timestamp":"2024-06-29T...","connections":0}
   ```

### 方案 2: 禁用 WebSocket 功能（臨時解決）

如果您暫時不需要實時功能，可以禁用 WebSocket：

1. **編輯配置文件**
   ```bash
   nano src/config/websocket.ts
   ```

2. **修改配置**
   ```typescript
   export const WEBSOCKET_CONFIG = {
     // 設置為 false 禁用 WebSocket
     enabled: false,
     // ... 其他配置
   };
   ```

3. **重新啟動應用**
   ```bash
   npm run dev
   ```

### 方案 3: 使用 WebSocket 控制組件

我已經創建了一個 WebSocket 控制組件，您可以在應用中使用：

```typescript
import WebSocketControl from '@/components/WebSocketControl';

// 在您的組件中使用
<WebSocketControl showDetails={true} />
```

## 📋 完整啟動流程

### 步驟 1: 啟動 WebSocket 服務器
```bash
# 終端 1
cd /Users/<USER>/src/ai-seo-king/websocket-server
node simple-ws.js
```

### 步驟 2: 啟動主應用
```bash
# 終端 2
cd /Users/<USER>/src/ai-seo-king
npm run dev
```

### 步驟 3: 驗證連接
1. 訪問 http://localhost:3001
2. 檢查瀏覽器控制台，應該看到 "WebSocket connected"
3. 不應該再有連接錯誤

## 🔍 故障排除

### 問題 1: 端口被佔用
```bash
# 檢查端口 8003 是否被佔用
lsof -i :8003

# 如果被佔用，終止進程
kill -9 <PID>
```

### 問題 2: 服務器啟動失敗
```bash
# 檢查 Node.js 版本
node --version

# 重新安裝依賴
cd websocket-server
npm install
```

### 問題 3: 權限問題
```bash
# 確保有執行權限
chmod +x websocket-server/simple-ws.js
```

## 🎯 WebSocket 功能說明

WebSocket 主要用於以下功能：
- **實時通知**: 分析完成、錯誤提醒等
- **進度更新**: 長時間運行任務的進度顯示
- **品牌監控**: 實時品牌數據更新
- **系統狀態**: 服務器狀態變化通知

## 📊 當前狀態

- ✅ **WebSocket 服務器**: 已配置在 `websocket-server/simple-ws.js`
- ✅ **WebSocket Hook**: 已優化錯誤處理
- ✅ **配置系統**: 可以輕鬆啟用/禁用功能
- ✅ **控制組件**: 提供用戶界面控制
- ✅ **錯誤處理**: 優雅處理連接失敗

## 🔧 配置選項

在 `src/config/websocket.ts` 中可以配置：

```typescript
export const WEBSOCKET_CONFIG = {
  enabled: false,                    // 功能開關
  url: 'ws://localhost:8003/ws',     // 服務器地址
  options: {
    autoConnect: true,               // 自動連接
    autoReconnect: true,             // 自動重連
    reconnectInterval: 3000,         // 重連間隔
    maxReconnectAttempts: 5,         // 最大重試次數
    showToastNotifications: false,   // 顯示通知
  },
  debug: true,                       // 調試模式
};
```

## 🎉 總結

現在您有多種方式來處理 WebSocket 連接問題：

1. **啟動服務器**: 獲得完整的實時功能
2. **禁用功能**: 避免錯誤，專注核心功能
3. **動態控制**: 在應用中隨時啟用/禁用

**推薦**: 先禁用 WebSocket 功能測試核心功能，然後根據需要啟動 WebSocket 服務器。

Google AI 摘要監測器的核心功能不依賴 WebSocket，可以正常使用！
