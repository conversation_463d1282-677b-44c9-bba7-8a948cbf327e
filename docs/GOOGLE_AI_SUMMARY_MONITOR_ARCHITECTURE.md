# 台灣 Google AI 摘要監測器 - 系統架構設計

## 🎯 系統概覽

**功能名稱**: 台灣 Google AI 摘要監測器  
**目標**: 監測 Google.com.tw 搜尋結果中 AI 摘要（AI Overview/SGE）出現情況  
**技術棧**: Next.js 15 + TypeScript + PostgreSQL + Redis + Google Custom Search API  

## 🏗️ 系統架構

### 整體架構圖
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   API 服務層    │    │   外部服務      │
│                 │    │                 │    │                 │
│ • 監測儀表板    │◄──►│ • 監測 API      │◄──►│ • Google CSE    │
│ • 配置面板      │    │ • 分析 API      │    │ • OpenAI API    │
│ • 報告視圖      │    │ • 排程服務      │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   數據存儲      │
                       │                 │
                       │ • PostgreSQL    │
                       │ • Redis Cache   │
                       └─────────────────┘
```

### 核心組件

#### 1. 前端組件架構
```
src/app/admin/google-ai-monitor/
├── page.tsx                    # 主頁面
├── components/
│   ├── MonitorDashboard.tsx    # 監測儀表板
│   ├── KeywordConfig.tsx       # 關鍵字配置
│   ├── SummaryAnalytics.tsx    # AI 摘要分析
│   ├── CompetitorComparison.tsx # 競爭對手比較
│   └── HistoryTrends.tsx       # 歷史趨勢
└── types/
    └── monitor.ts              # 類型定義
```

#### 2. API 路由架構
```
src/app/api/google-ai-monitor/
├── keywords/
│   ├── route.ts               # 關鍵字 CRUD
│   └── [id]/route.ts          # 單一關鍵字操作
├── monitoring/
│   ├── route.ts               # 監測任務管理
│   ├── start/route.ts         # 開始監測
│   └── stop/route.ts          # 停止監測
├── analytics/
│   ├── summary/route.ts       # AI 摘要統計
│   ├── trends/route.ts        # 趨勢分析
│   └── competitors/route.ts   # 競爭對手分析
└── search/
    ├── route.ts               # Google 搜尋
    └── parse/route.ts         # 結果解析
```

#### 3. 服務層架構
```
src/services/google-ai-monitor/
├── GoogleSearchService.ts      # Google 搜尋服務
├── AISummaryParser.ts         # AI 摘要解析
├── MonitoringScheduler.ts     # 監測排程
├── AnalyticsService.ts        # 數據分析
└── ComplianceService.ts       # 合規性檢查
```

## 🗄️ 資料庫設計

### 核心資料表

#### 1. 監測關鍵字表 (monitoring_keywords)
```sql
CREATE TABLE monitoring_keywords (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    keyword VARCHAR(255) NOT NULL,
    category VARCHAR(100),
    target_domain VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    monitoring_frequency VARCHAR(20) DEFAULT 'daily', -- daily, weekly, monthly
    last_monitored_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 2. 搜尋結果表 (search_results)
```sql
CREATE TABLE search_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    keyword_id UUID REFERENCES monitoring_keywords(id),
    search_query VARCHAR(500) NOT NULL,
    search_date TIMESTAMP NOT NULL,
    total_results BIGINT,
    has_ai_summary BOOLEAN DEFAULT false,
    ai_summary_position INTEGER,
    search_metadata JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### 3. AI 摘要記錄表 (ai_summaries)
```sql
CREATE TABLE ai_summaries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    search_result_id UUID REFERENCES search_results(id),
    summary_content TEXT,
    summary_sources JSONB, -- 引用來源列表
    user_domain_mentioned BOOLEAN DEFAULT false,
    user_content_quoted TEXT,
    summary_type VARCHAR(50), -- overview, featured_snippet, etc.
    confidence_score DECIMAL(3,2),
    parsed_at TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### 4. 競爭對手表 (competitors)
```sql
CREATE TABLE competitors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    domain VARCHAR(255) NOT NULL,
    company_name VARCHAR(255),
    industry VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### 5. 監測任務表 (monitoring_tasks)
```sql
CREATE TABLE monitoring_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    keyword_id UUID REFERENCES monitoring_keywords(id),
    task_type VARCHAR(50) NOT NULL, -- search, analysis, report
    status VARCHAR(20) DEFAULT 'pending', -- pending, running, completed, failed
    scheduled_at TIMESTAMP NOT NULL,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    error_message TEXT,
    result_data JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### 索引設計
```sql
-- 性能優化索引
CREATE INDEX idx_monitoring_keywords_user_active ON monitoring_keywords(user_id, is_active);
CREATE INDEX idx_search_results_keyword_date ON search_results(keyword_id, search_date DESC);
CREATE INDEX idx_ai_summaries_search_result ON ai_summaries(search_result_id);
CREATE INDEX idx_monitoring_tasks_status_scheduled ON monitoring_tasks(status, scheduled_at);
```

## 🔧 API 設計

### 1. 關鍵字管理 API

#### 創建監測關鍵字
```typescript
POST /api/google-ai-monitor/keywords
{
  "keyword": "AI工具",
  "category": "科技",
  "targetDomain": "example.com",
  "monitoringFrequency": "daily"
}
```

#### 獲取關鍵字列表
```typescript
GET /api/google-ai-monitor/keywords?page=1&limit=20&category=科技
```

### 2. 監測管理 API

#### 開始監測
```typescript
POST /api/google-ai-monitor/monitoring/start
{
  "keywordIds": ["uuid1", "uuid2"],
  "immediate": true
}
```

#### 獲取監測狀態
```typescript
GET /api/google-ai-monitor/monitoring/status
```

### 3. 分析報告 API

#### AI 摘要統計
```typescript
GET /api/google-ai-monitor/analytics/summary?period=30d&keyword=AI工具
```

#### 趨勢分析
```typescript
GET /api/google-ai-monitor/analytics/trends?period=90d&granularity=daily
```

## 🤖 AI 摘要解析算法

### 解析策略
```typescript
interface AISummaryDetection {
  // 檢測策略
  selectors: string[];           // CSS 選擇器
  keywords: string[];            // 關鍵字標識
  patterns: RegExp[];            // 正則表達式
  confidence: number;            // 信心度
}

const detectionStrategies: AISummaryDetection[] = [
  {
    selectors: [
      '[data-attrid="SGE"]',
      '.AI-overview',
      '[aria-label*="AI"]'
    ],
    keywords: ['AI 摘要', 'AI Overview', '生成式 AI'],
    patterns: [/AI\s*摘要/i, /AI\s*Overview/i],
    confidence: 0.9
  }
];
```

### 內容提取流程
1. **HTML 結構分析**: 識別 AI 摘要容器
2. **內容提取**: 提取摘要文字和引用來源
3. **來源分析**: 識別引用的網站和內容
4. **用戶內容匹配**: 檢查是否引用用戶網站內容
5. **信心度評估**: 計算檢測準確性

## 📊 數據分析功能

### 1. AI 摘要出現率統計
```typescript
interface SummaryStats {
  keyword: string;
  totalSearches: number;
  summaryAppearances: number;
  appearanceRate: number;
  averagePosition: number;
  trendDirection: 'up' | 'down' | 'stable';
}
```

### 2. 競爭對手分析
```typescript
interface CompetitorAnalysis {
  competitor: string;
  mentionCount: number;
  shareOfVoice: number;
  averagePosition: number;
  topKeywords: string[];
}
```

### 3. 歷史趨勢追蹤
```typescript
interface TrendData {
  date: string;
  summaryCount: number;
  mentionCount: number;
  averagePosition: number;
  topSources: string[];
}
```

## 🔒 安全性和合規性

### 1. Google API 合規
- **請求頻率限制**: 每秒最多 1 次請求
- **配額管理**: 監控每日 API 使用量
- **錯誤處理**: 實現指數退避重試
- **服務條款遵守**: 不存儲敏感搜尋數據

### 2. 數據安全
```typescript
// API 金鑰加密存儲
const encryptApiKey = (key: string): string => {
  return encrypt(key, process.env.ENCRYPTION_KEY);
};

// 請求頻率限制
const rateLimiter = new RateLimiter({
  windowMs: 1000,  // 1 秒
  max: 1,          // 最多 1 次請求
  skipSuccessfulRequests: false
});
```

### 3. 用戶數據隔離
- **多租戶架構**: 基於 user_id 的數據隔離
- **權限控制**: 基於角色的訪問控制
- **數據加密**: 敏感數據加密存儲

## 🚀 部署和監控

### 1. 環境配置
```bash
# Google Custom Search API
GOOGLE_CSE_API_KEY=your_api_key
GOOGLE_CSE_ENGINE_ID=your_engine_id

# 監測配置
MONITORING_ENABLED=true
DEFAULT_MONITORING_FREQUENCY=daily
MAX_CONCURRENT_SEARCHES=3

# 合規配置
RATE_LIMIT_REQUESTS_PER_SECOND=1
API_QUOTA_DAILY_LIMIT=100
```

### 2. 監控指標
- **API 使用量**: 追蹤每日 API 調用次數
- **成功率**: 監測搜尋和解析成功率
- **響應時間**: 監控 API 響應時間
- **錯誤率**: 追蹤錯誤發生頻率

### 3. 告警機制
- **配額警告**: API 使用量達到 80% 時告警
- **失敗率告警**: 連續失敗率超過 10% 時告警
- **服務異常**: 服務不可用時立即告警

---

## 📋 開發里程碑

### Phase 1: 基礎架構 (Week 1)
- [x] 系統架構設計
- [ ] 資料庫 Schema 建立
- [ ] 基礎 API 路由
- [ ] Google API 整合

### Phase 2: 核心功能 (Week 2)
- [ ] AI 摘要解析算法
- [ ] 監測任務排程
- [ ] 基礎前端界面

### Phase 3: 分析功能 (Week 3)
- [ ] 數據分析服務
- [ ] 報告生成
- [ ] 競爭對手分析

### Phase 4: 優化和部署 (Week 4)
- [ ] 性能優化
- [ ] 安全性加固
- [ ] 文檔完善
- [ ] 生產部署

這個架構設計為台灣 Google AI 摘要監測器提供了完整的技術基礎，確保功能的可擴展性、安全性和合規性。
