# AI SEO 優化王 - `seo_content` 索引快速參考

## 🚀 快速開始

**Web UI 訪問**: http://localhost:8080  
**登入憑證**: admin / aiseo2024  
**索引名稱**: `seo_content`

## 📋 常用查詢範本

### 基礎查詢

#### 查看所有內容（按分數排序）
```json
{
  "query": {"match_all": {}},
  "sort": [{"seo_score": {"order": "desc"}}],
  "size": 20
}
```

#### 查找高分內容（≥80分）
```json
{
  "query": {
    "range": {"seo_score": {"gte": 80}}
  },
  "sort": [{"seo_score": {"order": "desc"}}]
}
```

#### 查找低分內容（<60分）
```json
{
  "query": {
    "range": {"seo_score": {"lt": 60}}
  },
  "sort": [{"seo_score": {"order": "asc"}}]
}
```

### 內容分析查詢

#### 按字數範圍查找
```json
{
  "query": {
    "range": {"word_count": {"gte": 1000, "lte": 2000}}
  }
}
```

#### 查找缺少Meta描述的頁面
```json
{
  "query": {
    "bool": {
      "must_not": {
        "exists": {"field": "meta_tags.description"}
      }
    }
  }
}
```

#### 最近更新的內容
```json
{
  "query": {
    "range": {"updated_at": {"gte": "now-7d"}}
  },
  "sort": [{"updated_at": {"order": "desc"}}]
}
```

### 關鍵詞搜索

#### 標題和內容搜索
```json
{
  "query": {
    "multi_match": {
      "query": "AI SEO",
      "fields": ["title^2", "content", "keywords"]
    }
  },
  "highlight": {
    "fields": {"title": {}, "content": {}}
  }
}
```

#### 精確關鍵詞匹配
```json
{
  "query": {
    "term": {"keywords": "搜索引擎優化"}
  }
}
```

### 統計分析查詢

#### SEO分數分佈統計
```json
{
  "size": 0,
  "aggs": {
    "score_ranges": {
      "range": {
        "field": "seo_score",
        "ranges": [
          {"to": 60, "key": "需改進"},
          {"from": 60, "to": 80, "key": "中等"},
          {"from": 80, "key": "優秀"}
        ]
      }
    }
  }
}
```

#### 網域內容統計
```json
{
  "size": 0,
  "aggs": {
    "domains": {
      "terms": {"field": "domain", "size": 10},
      "aggs": {
        "avg_score": {"avg": {"field": "seo_score"}}
      }
    }
  }
}
```

## 🔧 常用操作命令

### 數據管理

#### 新增單個文檔
```bash
curl -X POST "localhost:9200/seo_content/_doc/new_id" \
-H 'Content-Type: application/json' \
-d '{
  "url": "https://example.com/new-page",
  "title": "新頁面標題",
  "seo_score": 75.0
}'
```

#### 更新文檔
```bash
curl -X POST "localhost:9200/seo_content/_update/doc_id" \
-H 'Content-Type: application/json' \
-d '{
  "doc": {"seo_score": 85.0}
}'
```

#### 刪除文檔
```bash
curl -X DELETE "localhost:9200/seo_content/_doc/doc_id"
```

### 索引維護

#### 刷新索引
```bash
curl -X POST "localhost:9200/seo_content/_refresh"
```

#### 查看索引統計
```bash
curl -X GET "localhost:9200/seo_content/_stats?pretty"
```

## 📊 數據結構速查

### 核心欄位
- `url` (keyword): 頁面URL
- `title` (text): 頁面標題
- `content` (text): 頁面內容
- `seo_score` (float): SEO分數 (0-100)
- `word_count` (long): 字數統計
- `domain` (keyword): 網域名稱
- `status` (keyword): 頁面狀態

### Meta標籤結構
```json
{
  "meta_tags": {
    "title": "Meta標題",
    "description": "Meta描述",
    "keywords": ["關鍵詞陣列"]
  }
}
```

## ⚡ 性能優化提示

1. **使用過濾器**: 精確匹配用 `filter` 而非 `must`
2. **限制返回欄位**: 使用 `_source` 指定需要的欄位
3. **合理設置size**: 瀏覽用10-20，分析用100-500
4. **使用聚合**: 統計分析用 `aggs` 而非大量文檔

## 🚨 常見錯誤

### 查詢語法錯誤
❌ 錯誤：`{"query": {"match": {"seo_score": 80}}}`  
✅ 正確：`{"query": {"term": {"seo_score": 80}}}`

### 欄位類型錯誤
❌ 錯誤：`{"query": {"range": {"title": {"gte": "A"}}}}`  
✅ 正確：`{"query": {"range": {"seo_score": {"gte": 80}}}}`

## 📞 快速支援

- **完整指南**: `docs/SEO_CONTENT_INDEX_GUIDE.md`
- **Web UI**: http://localhost:8080
- **測試工具**: `python3 scripts/test_web_ui.py`
- **診斷工具**: `python3 scripts/elasticsearch_diagnostic.py`

---

**💡 提示**: 將此頁面加入書籤，隨時查閱常用查詢範本！
