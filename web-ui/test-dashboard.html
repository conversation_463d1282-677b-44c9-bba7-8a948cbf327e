<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard 測試 - AI SEO 優化王</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }
        .status-green { background-color: #10b981; }
        .status-yellow { background-color: #f59e0b; }
        .status-red { background-color: #ef4444; }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto p-6">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold text-gray-900 mb-6">Dashboard 功能測試</h1>
            
            <!-- 連接狀態 -->
            <div class="mb-6 p-4 bg-blue-50 rounded-lg">
                <h3 class="text-lg font-semibold mb-2">連接狀態</h3>
                <div id="connection-status" class="flex items-center">
                    <span class="status-indicator status-yellow mr-2"></span>
                    <span>檢查中...</span>
                </div>
            </div>
            
            <!-- 集群信息 -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-4">集群信息</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="bg-gray-50 p-3 rounded">
                        <div class="text-sm text-gray-600">集群名稱</div>
                        <div id="cluster-name" class="font-bold">-</div>
                    </div>
                    <div class="bg-gray-50 p-3 rounded">
                        <div class="text-sm text-gray-600">健康狀態</div>
                        <div id="cluster-status" class="font-bold">-</div>
                    </div>
                    <div class="bg-gray-50 p-3 rounded">
                        <div class="text-sm text-gray-600">節點數</div>
                        <div id="nodes-count" class="font-bold">-</div>
                    </div>
                    <div class="bg-gray-50 p-3 rounded">
                        <div class="text-sm text-gray-600">索引數</div>
                        <div id="indices-count" class="font-bold">-</div>
                    </div>
                </div>
            </div>
            
            <!-- 索引列表 -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-4">索引列表</h3>
                <div id="indices-list" class="bg-gray-50 p-4 rounded">
                    載入中...
                </div>
            </div>
            
            <!-- 操作按鈕 -->
            <div class="flex space-x-4">
                <button onclick="testConnection()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
                    測試連接
                </button>
                <button onclick="loadDashboard()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded">
                    載入儀表板
                </button>
                <a href="elasticsearch-admin.html#dashboard" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded inline-block text-center">
                    打開完整管理界面
                </a>
            </div>
        </div>
    </div>

    <script>
        async function testConnection() {
            const statusEl = document.getElementById('connection-status');
            const statusIndicator = statusEl.querySelector('.status-indicator');
            
            try {
                statusEl.innerHTML = '<span class="status-indicator status-yellow mr-2"></span>連接中...';
                
                const response = await fetch('http://localhost:9200/_cluster/health');
                const health = await response.json();
                
                // 更新連接狀態
                statusEl.innerHTML = `<span class="status-indicator status-green mr-2"></span>已連接`;
                
                // 更新集群信息
                document.getElementById('cluster-name').textContent = health.cluster_name;
                document.getElementById('cluster-status').textContent = health.status.toUpperCase();
                document.getElementById('nodes-count').textContent = health.number_of_nodes;
                
                // 載入索引
                loadIndices();
                
            } catch (error) {
                console.error('連接失敗:', error);
                statusEl.innerHTML = '<span class="status-indicator status-red mr-2"></span>連接失敗';
            }
        }
        
        async function loadIndices() {
            try {
                const response = await fetch('http://localhost:9200/_cat/indices?format=json');
                const indices = await response.json();
                
                document.getElementById('indices-count').textContent = indices.length;
                
                const indicesList = document.getElementById('indices-list');
                indicesList.innerHTML = indices.map(index => `
                    <div class="flex justify-between items-center py-2 border-b">
                        <span class="font-medium">${index.index}</span>
                        <div class="text-sm text-gray-600">
                            <span class="mr-4">${index['docs.count'] || 0} 文檔</span>
                            <span class="px-2 py-1 rounded text-xs ${getStatusClass(index.health)}">${(index.health || 'unknown').toUpperCase()}</span>
                        </div>
                    </div>
                `).join('');
                
            } catch (error) {
                console.error('載入索引失敗:', error);
                document.getElementById('indices-list').innerHTML = '載入失敗';
            }
        }
        
        function getStatusClass(health) {
            switch(health) {
                case 'green': return 'bg-green-100 text-green-800';
                case 'yellow': return 'bg-yellow-100 text-yellow-800';
                case 'red': return 'bg-red-100 text-red-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }
        
        function loadDashboard() {
            window.location.href = 'elasticsearch-admin.html#dashboard';
        }
        
        // 自動測試連接
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(testConnection, 500);
        });
    </script>
</body>
</html> 