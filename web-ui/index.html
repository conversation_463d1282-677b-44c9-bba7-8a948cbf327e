<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI SEO 優化王 - Elasticsearch 管理界面</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="config.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .status-green { color: #10b981; }
        .status-yellow { color: #f59e0b; }
        .status-red { color: #ef4444; }
        .json-viewer {
            background: #1f2937;
            color: #e5e7eb;
            font-family: 'Courier New', monospace;
            border-radius: 8px;
            padding: 16px;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 導航欄 -->
    <nav class="gradient-bg text-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <i class="fas fa-search text-2xl mr-3"></i>
                    <h1 class="text-xl font-bold">AI SEO 優化王 - Elasticsearch 管理</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span id="cluster-status" class="px-3 py-1 rounded-full text-sm bg-white bg-opacity-20">
                        <i class="fas fa-circle text-green-400 mr-1"></i>
                        連接中...
                    </span>
                    <button onclick="refreshAll()" class="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sync-alt mr-2"></i>刷新
                    </button>
                    <button onclick="logout()" class="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt mr-2"></i>登出
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要內容 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 狀態卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6 card-hover transition-all duration-300">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-server text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">集群狀態</p>
                        <p id="cluster-health" class="text-2xl font-semibold text-gray-900">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6 card-hover transition-all duration-300">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-database text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">索引數量</p>
                        <p id="indices-count" class="text-2xl font-semibold text-gray-900">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6 card-hover transition-all duration-300">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                        <i class="fas fa-file-alt text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">文檔總數</p>
                        <p id="docs-count" class="text-2xl font-semibold text-gray-900">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6 card-hover transition-all duration-300">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                        <i class="fas fa-hdd text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">存儲大小</p>
                        <p id="store-size" class="text-2xl font-semibold text-gray-900">-</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要功能區 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- SEO 索引管理 -->
            <div class="bg-white rounded-lg shadow-md">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-list-ul mr-2 text-blue-600"></i>
                        SEO 索引管理
                    </h2>
                </div>
                <div class="p-6">
                    <div id="seo-indices" class="space-y-4">
                        <!-- 動態載入 SEO 索引 -->
                    </div>
                </div>
            </div>

            <!-- 快速搜索 -->
            <div class="bg-white rounded-lg shadow-md">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-search mr-2 text-green-600"></i>
                        快速搜索
                    </h2>
                </div>
                <div class="p-6">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">選擇索引</label>
                        <select id="search-index" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">選擇索引...</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">搜索查詢</label>
                        <textarea id="search-query" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder='{"query": {"match_all": {}}}'></textarea>
                    </div>
                    <button onclick="executeSearch()" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
                        <i class="fas fa-search mr-2"></i>執行搜索
                    </button>
                </div>
            </div>
        </div>

        <!-- 搜索結果 -->
        <div id="search-results" class="mt-8 bg-white rounded-lg shadow-md hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-list mr-2 text-purple-600"></i>
                    搜索結果
                </h2>
            </div>
            <div class="p-6">
                <div id="search-output" class="json-viewer"></div>
            </div>
        </div>

        <!-- 集群信息 -->
        <div class="mt-8 bg-white rounded-lg shadow-md">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-info-circle mr-2 text-indigo-600"></i>
                    集群信息
                </h2>
            </div>
            <div class="p-6">
                <div id="cluster-info" class="json-viewer"></div>
            </div>
        </div>
    </div>

    <!-- 載入遮罩 -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 items-center justify-center hidden z-50">
        <div class="bg-white rounded-lg p-6 flex items-center">
            <i class="fas fa-spinner fa-spin text-blue-600 text-2xl mr-4"></i>
            <span class="text-lg font-medium">載入中...</span>
        </div>
    </div>

    <script>
        // 使用配置文件
        const CONFIG = window.AISEO_CONFIG;
        const ES_URL = CONFIG.elasticsearch.url;
        const SEO_INDICES = CONFIG.seoIndices.map(index => index.name);

        // 工具函數
        function showLoading() {
            document.getElementById('loading-overlay').classList.remove('hidden');
            document.getElementById('loading-overlay').classList.add('flex');
        }

        function hideLoading() {
            document.getElementById('loading-overlay').classList.add('hidden');
            document.getElementById('loading-overlay').classList.remove('flex');
        }

        function formatJSON(obj) {
            return JSON.stringify(obj, null, 2);
        }

        function formatBytes(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // API 調用函數
        async function callElasticsearch(endpoint, method = 'GET', body = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };

                if (body) {
                    options.body = JSON.stringify(body);
                }

                const response = await fetch(`${ES_URL}${endpoint}`, options);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                return await response.json();
            } catch (error) {
                console.error('Elasticsearch API 調用失敗:', error);
                throw error;
            }
        }

        // 載入集群狀態
        async function loadClusterStatus() {
            try {
                const health = await callElasticsearch('/_cluster/health');
                const stats = await callElasticsearch('/_cluster/stats');

                // 更新狀態顯示
                document.getElementById('cluster-health').textContent = health.status;
                document.getElementById('cluster-health').className = `text-2xl font-semibold status-${health.status === 'green' ? 'green' : health.status === 'yellow' ? 'yellow' : 'red'}`;

                document.getElementById('indices-count').textContent = health.number_of_data_nodes;
                document.getElementById('docs-count').textContent = stats.indices.count.toLocaleString();
                document.getElementById('store-size').textContent = formatBytes(stats.indices.store.size_in_bytes);

                // 更新導航欄狀態
                const statusElement = document.getElementById('cluster-status');
                statusElement.innerHTML = `<i class="fas fa-circle text-${health.status === 'green' ? 'green' : health.status === 'yellow' ? 'yellow' : 'red'}-400 mr-1"></i>${health.cluster_name}`;

                // 顯示集群信息
                document.getElementById('cluster-info').textContent = formatJSON({
                    cluster_name: health.cluster_name,
                    status: health.status,
                    number_of_nodes: health.number_of_nodes,
                    number_of_data_nodes: health.number_of_data_nodes,
                    active_primary_shards: health.active_primary_shards,
                    active_shards: health.active_shards,
                    version: stats.nodes.versions[0]
                });

            } catch (error) {
                console.error('載入集群狀態失敗:', error);
                document.getElementById('cluster-status').innerHTML = '<i class="fas fa-circle text-red-400 mr-1"></i>連接失敗';
            }
        }

        // 載入 SEO 索引
        async function loadSEOIndices() {
            try {
                const indices = await callElasticsearch('/_cat/indices?format=json');
                const seoIndicesContainer = document.getElementById('seo-indices');
                const searchIndexSelect = document.getElementById('search-index');

                seoIndicesContainer.innerHTML = '';
                searchIndexSelect.innerHTML = '<option value="">選擇索引...</option>';

                // 過濾 SEO 相關索引
                const seoIndices = indices.filter(index =>
                    SEO_INDICES.some(seoIndex => index.index.includes(seoIndex))
                );

                seoIndices.forEach(index => {
                    // 創建索引卡片
                    const indexCard = document.createElement('div');
                    indexCard.className = 'border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors';
                    indexCard.innerHTML = `
                        <div class="flex justify-between items-center">
                            <div>
                                <h3 class="font-medium text-gray-900">${index.index}</h3>
                                <p class="text-sm text-gray-600">${index['docs.count']} 文檔 • ${index['store.size']}</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="px-2 py-1 text-xs rounded-full ${index.health === 'green' ? 'bg-green-100 text-green-800' : index.health === 'yellow' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}">
                                    ${index.health}
                                </span>
                                <button onclick="viewIndex('${index.index}')" class="text-blue-600 hover:text-blue-800 text-sm">
                                    <i class="fas fa-eye mr-1"></i>查看
                                </button>
                            </div>
                        </div>
                    `;
                    seoIndicesContainer.appendChild(indexCard);

                    // 添加到搜索下拉選單
                    const option = document.createElement('option');
                    option.value = index.index;
                    option.textContent = index.index;
                    searchIndexSelect.appendChild(option);
                });

                if (seoIndices.length === 0) {
                    seoIndicesContainer.innerHTML = '<p class="text-gray-500 text-center py-4">未找到 SEO 相關索引</p>';
                }

            } catch (error) {
                console.error('載入 SEO 索引失敗:', error);
                document.getElementById('seo-indices').innerHTML = '<p class="text-red-500 text-center py-4">載入索引失敗</p>';
            }
        }

        // 查看索引詳情
        async function viewIndex(indexName) {
            try {
                showLoading();
                const mapping = await callElasticsearch(`/${indexName}/_mapping`);
                const settings = await callElasticsearch(`/${indexName}/_settings`);

                const modalContent = `
                    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" onclick="closeModal()">
                        <div class="bg-white rounded-lg max-w-4xl max-h-screen overflow-y-auto m-4" onclick="event.stopPropagation()">
                            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                                <h2 class="text-xl font-semibold">索引詳情: ${indexName}</h2>
                                <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-times text-xl"></i>
                                </button>
                            </div>
                            <div class="p-6">
                                <div class="mb-6">
                                    <h3 class="text-lg font-medium mb-3">映射 (Mapping)</h3>
                                    <div class="json-viewer">${formatJSON(mapping)}</div>
                                </div>
                                <div>
                                    <h3 class="text-lg font-medium mb-3">設置 (Settings)</h3>
                                    <div class="json-viewer">${formatJSON(settings)}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                document.body.insertAdjacentHTML('beforeend', modalContent);
                hideLoading();

            } catch (error) {
                hideLoading();
                alert('載入索引詳情失敗: ' + error.message);
            }
        }

        // 關閉模態框
        function closeModal() {
            const modals = document.querySelectorAll('.fixed.inset-0');
            modals.forEach(modal => modal.remove());
        }

        // 執行搜索
        async function executeSearch() {
            const indexName = document.getElementById('search-index').value;
            const queryText = document.getElementById('search-query').value;

            if (!indexName) {
                alert('請選擇一個索引');
                return;
            }

            if (!queryText.trim()) {
                alert('請輸入搜索查詢');
                return;
            }

            try {
                showLoading();

                let query;
                try {
                    query = JSON.parse(queryText);
                } catch (e) {
                    // 如果不是 JSON，創建簡單的 match 查詢
                    query = {
                        "query": {
                            "multi_match": {
                                "query": queryText,
                                "fields": ["*"]
                            }
                        }
                    };
                }

                const results = await callElasticsearch(`/${indexName}/_search`, 'POST', query);

                // 顯示搜索結果
                document.getElementById('search-results').classList.remove('hidden');
                document.getElementById('search-output').textContent = formatJSON(results);

                // 滾動到結果區域
                document.getElementById('search-results').scrollIntoView({ behavior: 'smooth' });

                hideLoading();

            } catch (error) {
                hideLoading();
                alert('搜索失敗: ' + error.message);
            }
        }

        // 刷新所有數據
        async function refreshAll() {
            showLoading();
            try {
                await Promise.all([
                    loadClusterStatus(),
                    loadSEOIndices()
                ]);
            } catch (error) {
                console.error('刷新數據失敗:', error);
            } finally {
                hideLoading();
            }
        }

        // 認證檢查
        function checkAuth() {
            const token = localStorage.getItem('aiseo_auth_token') || sessionStorage.getItem('aiseo_auth_token');
            if (!token) {
                window.location.href = 'auth.html';
                return false;
            }

            try {
                const auth = JSON.parse(atob(token));
                // 檢查 token 是否過期（24小時）
                if (Date.now() - auth.timestamp > 24 * 60 * 60 * 1000) {
                    logout();
                    return false;
                }
                return true;
            } catch (e) {
                logout();
                return false;
            }
        }

        // 登出功能
        function logout() {
            localStorage.removeItem('aiseo_auth_token');
            sessionStorage.removeItem('aiseo_auth_token');
            window.location.href = 'auth.html';
        }

        // 頁面載入完成後初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 檢查認證狀態
            if (!checkAuth()) {
                return;
            }

            // 設置默認搜索查詢
            document.getElementById('search-query').value = formatJSON({
                "query": {
                    "match_all": {}
                },
                "size": 10
            });

            // 載入初始數據
            refreshAll();

            // 設置定期刷新（每30秒）
            setInterval(loadClusterStatus, 30000);
        });

        // 鍵盤快捷鍵
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + Enter 執行搜索
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                executeSearch();
            }

            // ESC 關閉模態框
            if (e.key === 'Escape') {
                closeModal();
            }
        });
    </script>
</body>
</html>