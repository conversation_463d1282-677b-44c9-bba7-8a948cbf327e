<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索功能測試</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">
            <i class="fas fa-search text-blue-600 mr-3"></i>
            搜索功能測試
        </h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 測試按鈕 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">快速測試</h2>
                <div class="space-y-3">
                    <button onclick="testSearch('match_all')" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                        <i class="fas fa-list mr-2"></i>全部匹配測試
                    </button>
                    <button onclick="testSearch('match')" class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">
                        <i class="fas fa-search mr-2"></i>文本匹配測試
                    </button>
                    <button onclick="testSearch('term')" class="w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg">
                        <i class="fas fa-equals mr-2"></i>精確匹配測試
                    </button>
                    <button onclick="testSearch('range')" class="w-full bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg">
                        <i class="fas fa-sort-numeric-up mr-2"></i>範圍查詢測試
                    </button>
                    <button onclick="testSearch('wildcard')" class="w-full bg-pink-600 hover:bg-pink-700 text-white px-4 py-2 rounded-lg">
                        <i class="fas fa-asterisk mr-2"></i>通配符測試
                    </button>
                    <button onclick="testSearch('fuzzy')" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">
                        <i class="fas fa-magic mr-2"></i>模糊搜索測試
                    </button>
                </div>
            </div>
            
            <!-- 結果顯示 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">測試結果</h2>
                <div id="test-results" class="space-y-3">
                    <p class="text-gray-500">點擊左側按鈕開始測試</p>
                </div>
            </div>
        </div>
        
        <!-- 詳細結果 -->
        <div class="mt-8 bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">詳細結果</h2>
            <div id="detailed-results" class="bg-gray-50 rounded-lg p-4 font-mono text-sm">
                等待測試結果...
            </div>
        </div>
    </div>

    <script>
        const ES_URL = 'http://localhost:9200';
        
        async function testSearch(type) {
            const resultsDiv = document.getElementById('test-results');
            const detailedDiv = document.getElementById('detailed-results');
            
            resultsDiv.innerHTML = `<p class="text-blue-600"><i class="fas fa-spinner fa-spin mr-2"></i>正在測試 ${type}...</p>`;
            
            try {
                let query;
                let description;
                
                switch(type) {
                    case 'match_all':
                        query = { query: { match_all: {} }, size: 3 };
                        description = '獲取所有文檔（限制3條）';
                        break;
                    case 'match':
                        query = { query: { match: { title: 'SEO' } }, size: 3 };
                        description = '搜索標題包含 "SEO" 的文檔';
                        break;
                    case 'term':
                        query = { query: { term: { 'status.keyword': 'published' } }, size: 3 };
                        description = '精確匹配狀態為 "published" 的文檔';
                        break;
                    case 'range':
                        query = { query: { range: { score: { gte: 90 } } }, size: 3 };
                        description = '搜索分數大於等於90的文檔';
                        break;
                    case 'wildcard':
                        query = { query: { wildcard: { title: '*SEO*' } }, size: 3 };
                        description = '通配符搜索標題包含 "SEO" 的文檔';
                        break;
                    case 'fuzzy':
                        query = { query: { fuzzy: { title: { value: 'SEO', fuzziness: 1 } } }, size: 3 };
                        description = '模糊搜索標題類似 "SEO" 的文檔';
                        break;
                }
                
                const response = await fetch(`${ES_URL}/seo_content/_search`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(query)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    const hitCount = result.hits.total?.value || result.hits.total || 0;
                    const took = result.took || 0;
                    
                    resultsDiv.innerHTML = `
                        <div class="p-3 bg-green-50 border border-green-200 rounded-lg">
                            <p class="text-green-800 font-medium">✅ ${type} 測試成功</p>
                            <p class="text-green-600 text-sm">${description}</p>
                            <p class="text-green-600 text-sm">找到 ${hitCount} 條結果，耗時 ${took}ms</p>
                        </div>
                    `;
                    
                    detailedDiv.innerHTML = `
                        <div class="mb-4">
                            <h3 class="font-bold text-lg mb-2">查詢: ${type}</h3>
                            <p class="text-gray-600 mb-2">${description}</p>
                        </div>
                        <div class="mb-4">
                            <h4 class="font-semibold mb-1">查詢 JSON:</h4>
                            <pre class="bg-gray-800 text-green-400 p-3 rounded text-xs overflow-x-auto">${JSON.stringify(query, null, 2)}</pre>
                        </div>
                        <div>
                            <h4 class="font-semibold mb-1">結果:</h4>
                            <pre class="bg-gray-800 text-blue-400 p-3 rounded text-xs overflow-x-auto">${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${result.error?.reason || response.statusText}`);
                }
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="p-3 bg-red-50 border border-red-200 rounded-lg">
                        <p class="text-red-800 font-medium">❌ ${type} 測試失敗</p>
                        <p class="text-red-600 text-sm">${error.message}</p>
                    </div>
                `;
                
                detailedDiv.innerHTML = `
                    <div class="text-red-600">
                        <h3 class="font-bold text-lg mb-2">錯誤: ${type}</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        // 頁面載入後自動測試連接
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                const response = await fetch(`${ES_URL}/_cluster/health`);
                const health = await response.json();
                
                document.getElementById('test-results').innerHTML = `
                    <div class="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <p class="text-blue-800 font-medium">🔗 Elasticsearch 連接正常</p>
                        <p class="text-blue-600 text-sm">集群狀態: ${health.status}</p>
                    </div>
                `;
            } catch (error) {
                document.getElementById('test-results').innerHTML = `
                    <div class="p-3 bg-red-50 border border-red-200 rounded-lg">
                        <p class="text-red-800 font-medium">❌ Elasticsearch 連接失敗</p>
                        <p class="text-red-600 text-sm">${error.message}</p>
                    </div>
                `;
            }
        });
    </script>
</body>
</html> 