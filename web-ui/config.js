// AI SEO 優化王 - Web UI 配置文件

const CONFIG = {
    // Elasticsearch 連接配置
    elasticsearch: {
        url: 'http://localhost:9200',
        timeout: 30000,
        maxRetries: 3
    },
    
    // 安全配置
    security: {
        enabled: true,
        sessionTimeout: 24 * 60 * 60 * 1000, // 24小時
        maxLoginAttempts: 5,
        lockoutDuration: 15 * 60 * 1000 // 15分鐘
    },
    
    // UI 配置
    ui: {
        refreshInterval: 30000, // 30秒
        defaultPageSize: 10,
        maxSearchResults: 100,
        theme: 'light'
    },
    
    // SEO 索引配置
    seoIndices: [
        {
            name: 'seo_content',
            displayName: 'SEO 內容',
            description: '網頁內容和 SEO 數據',
            icon: 'fas fa-file-alt',
            color: 'blue'
        },
        {
            name: 'seo_analysis',
            displayName: 'SEO 分析',
            description: 'SEO 分析結果和建議',
            icon: 'fas fa-chart-line',
            color: 'green'
        },
        {
            name: 'seo_keywords',
            displayName: 'SEO 關鍵詞',
            description: '關鍵詞研究數據',
            icon: 'fas fa-key',
            color: 'purple'
        },
        {
            name: 'seo_competitors',
            displayName: 'SEO 競爭對手',
            description: '競爭對手分析數據',
            icon: 'fas fa-users',
            color: 'red'
        }
    ],
    
    // 預設查詢模板
    queryTemplates: {
        matchAll: {
            name: '查詢所有文檔',
            query: {
                "query": {
                    "match_all": {}
                },
                "size": 10
            }
        },
        recentDocuments: {
            name: '最近文檔',
            query: {
                "query": {
                    "range": {
                        "created_at": {
                            "gte": "now-7d"
                        }
                    }
                },
                "sort": [
                    {
                        "created_at": {
                            "order": "desc"
                        }
                    }
                ],
                "size": 20
            }
        },
        highScoreContent: {
            name: '高分 SEO 內容',
            query: {
                "query": {
                    "range": {
                        "seo_score": {
                            "gte": 80
                        }
                    }
                },
                "sort": [
                    {
                        "seo_score": {
                            "order": "desc"
                        }
                    }
                ],
                "size": 10
            }
        },
        popularKeywords: {
            name: '熱門關鍵詞',
            query: {
                "query": {
                    "range": {
                        "search_volume": {
                            "gte": 1000
                        }
                    }
                },
                "sort": [
                    {
                        "search_volume": {
                            "order": "desc"
                        }
                    }
                ],
                "size": 15
            }
        }
    },
    
    // 監控配置
    monitoring: {
        healthCheckInterval: 60000, // 1分鐘
        alertThresholds: {
            clusterStatus: {
                red: 'error',
                yellow: 'warning',
                green: 'success'
            },
            responseTime: {
                slow: 1000, // 1秒
                verySlow: 5000 // 5秒
            },
            diskUsage: {
                warning: 80, // 80%
                critical: 90 // 90%
            }
        }
    },
    
    // 功能開關
    features: {
        enableAuth: true,
        enableMonitoring: true,
        enableExport: true,
        enableAdvancedSearch: true,
        enableBulkOperations: false, // 危險操作，默認關閉
        enableIndexManagement: true
    },
    
    // 本地化配置
    locale: {
        language: 'zh-TW',
        dateFormat: 'YYYY-MM-DD HH:mm:ss',
        timezone: 'Asia/Taipei',
        currency: 'TWD'
    },
    
    // API 配置
    api: {
        retryAttempts: 3,
        retryDelay: 1000,
        requestTimeout: 30000
    }
};

// 導出配置（如果在 Node.js 環境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}

// 全局配置訪問器
window.AISEO_CONFIG = CONFIG;
