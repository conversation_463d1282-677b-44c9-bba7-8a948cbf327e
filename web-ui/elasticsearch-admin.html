<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI SEO 優化王 - Elasticsearch 管理中心</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .sidebar {
            transition: transform 0.3s ease-in-out;
        }
        .sidebar.hidden {
            transform: translateX(-100%);
        }
        .json-viewer {
            background: #1f2937;
            color: #e5e7eb;
            font-family: 'Courier New', monospace;
            border-radius: 8px;
            padding: 16px;
            max-height: 400px;
            overflow-y: auto;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }
        .status-green { background-color: #10b981; }
        .status-yellow { background-color: #f59e0b; }
        .status-red { background-color: #ef4444; }
        
        /* 警告和通知樣式 */
        .alert {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            max-width: 400px;
            animation: slideIn 0.3s ease-out;
        }
        .alert-success {
            background-color: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        .alert-warning {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            color: #92400e;
        }
        .alert-error {
            background-color: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
        
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        /* 搜索結果樣式增強 */
        .search-result-item {
            transition: all 0.2s ease;
        }
        .search-result-item:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 側邊欄 -->
    <div id="sidebar" class="sidebar fixed left-0 top-0 h-full w-64 bg-white shadow-lg z-40">
        <div class="p-4 border-b">
            <h2 class="text-lg font-bold text-gray-800">
                <i class="fas fa-search text-blue-600 mr-2"></i>
                ES 管理中心
            </h2>
        </div>
        <nav class="mt-4">
            <a href="#dashboard" onclick="showSection('dashboard')" class="nav-item flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                <i class="fas fa-tachometer-alt mr-3"></i>
                儀表板
            </a>
            <a href="#indices" onclick="showSection('indices')" class="nav-item flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                <i class="fas fa-database mr-3"></i>
                索引管理
            </a>
            <a href="#search" onclick="showSection('search')" class="nav-item flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                <i class="fas fa-search mr-3"></i>
                數據搜索
            </a>
            <a href="#analytics" onclick="showSection('analytics')" class="nav-item flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                <i class="fas fa-chart-bar mr-3"></i>
                分析統計
            </a>
            <a href="#settings" onclick="showSection('settings')" class="nav-item flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                <i class="fas fa-cog mr-3"></i>
                系統設置
            </a>
        </nav>
    </div>

    <!-- 主要內容區 -->
    <div class="ml-64">
        <!-- 頂部導航欄 -->
        <header class="gradient-bg text-white shadow-lg">
            <div class="px-6 py-4 flex justify-between items-center">
                <div class="flex items-center">
                    <button onclick="toggleSidebar()" class="mr-4 p-2 rounded hover:bg-white hover:bg-opacity-20">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="text-xl font-bold">AI SEO 優化王 - Elasticsearch 管理中心</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <div id="cluster-status" class="flex items-center px-3 py-1 rounded-full bg-white bg-opacity-20">
                        <span class="status-indicator status-yellow mr-2"></span>
                        <span id="status-text">連接中...</span>
                    </div>
                    <button onclick="refreshAll()" class="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sync-alt mr-2"></i>刷新
                    </button>
                </div>
            </div>
        </header>

        <!-- 儀表板區域 -->
        <section id="dashboard-section" class="section-content p-6 hidden">
            <div class="mb-6">
                <h2 class="text-2xl font-bold text-gray-900 mb-2">集群概覽</h2>
                <p class="text-gray-600">實時監控 Elasticsearch 集群狀態和性能指標</p>
            </div>

            <!-- 狀態卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow-md p-6 card-hover transition-all duration-300">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 text-green-600">
                            <i class="fas fa-server text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">集群狀態</p>
                            <p id="cluster-health" class="text-2xl font-semibold text-gray-900">-</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6 card-hover transition-all duration-300">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                            <i class="fas fa-database text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">索引數量</p>
                            <p id="indices-count" class="text-2xl font-semibold text-gray-900">-</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6 card-hover transition-all duration-300">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                            <i class="fas fa-file-alt text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">文檔總數</p>
                            <p id="docs-count" class="text-2xl font-semibold text-gray-900">-</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6 card-hover transition-all duration-300">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                            <i class="fas fa-hdd text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">存儲大小</p>
                            <p id="store-size" class="text-2xl font-semibold text-gray-900">-</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 圖表區域 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <div class="bg-white rounded-lg shadow-md">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">索引文檔分布</h3>
                    </div>
                    <div class="p-6">
                        <canvas id="docsChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">存儲空間使用</h3>
                    </div>
                    <div class="p-6">
                        <canvas id="storageChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- SEO 索引概覽 -->
            <div class="bg-white rounded-lg shadow-md">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-list-ul mr-2 text-blue-600"></i>
                        SEO 索引概覽
                    </h3>
                </div>
                <div class="p-6">
                    <div id="seo-indices-overview" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <!-- 動態載入 SEO 索引 -->
                    </div>
                </div>
            </div>
        </section>

        <!-- 索引管理區域 -->
        <section id="indices-section" class="section-content p-6 hidden">
            <div class="mb-6">
                <h2 class="text-2xl font-bold text-gray-900 mb-2">索引管理</h2>
                <p class="text-gray-600">管理和監控所有 Elasticsearch 索引</p>
            </div>

            <div class="bg-white rounded-lg shadow-md">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">索引列表</h3>
                    <button onclick="refreshIndices()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                        <i class="fas fa-sync-alt mr-2"></i>刷新
                    </button>
                </div>
                <div class="p-6">
                    <div id="indices-table" class="overflow-x-auto">
                        <!-- 動態載入索引表格 -->
                    </div>
                </div>
            </div>
        </section>

        <!-- 數據搜索區域 -->
        <section id="search-section" class="section-content p-6 hidden">
            <div class="mb-6">
                <h2 class="text-2xl font-bold text-gray-900 mb-2">數據搜索</h2>
                <p class="text-gray-600">搜索和查看索引中的數據</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- 搜索表單 -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-lg shadow-md">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900">搜索配置</h3>
                        </div>
                        <div class="p-6 space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">選擇索引</label>
                                <select id="search-index" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">選擇索引...</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">搜索類型</label>
                                <select id="search-type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="match_all">全部匹配</option>
                                    <option value="match">文本匹配</option>
                                    <option value="term">精確匹配</option>
                                    <option value="range">範圍查詢</option>
                                    <option value="wildcard">通配符搜索</option>
                                    <option value="fuzzy">模糊搜索</option>
                                    <option value="custom">自定義查詢</option>
                                </select>
                            </div>
                            <div id="search-params">
                                <!-- 動態載入搜索參數 -->
                            </div>
                            <button onclick="executeSearch()" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
                                <i class="fas fa-search mr-2"></i>執行搜索
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 搜索結果 -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow-md">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900">搜索結果</h3>
                        </div>
                        <div class="p-6">
                            <div id="search-results" class="min-h-64">
                                <div class="flex items-center justify-center h-64 text-gray-500">
                                    <div class="text-center">
                                        <i class="fas fa-search text-4xl mb-4"></i>
                                        <p>請選擇索引並執行搜索</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 分析統計區域 -->
        <section id="analytics-section" class="section-content p-6 hidden">
            <div class="mb-6">
                <h2 class="text-2xl font-bold text-gray-900 mb-2">分析統計</h2>
                <p class="text-gray-600">深入分析索引數據和使用情況</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="bg-white rounded-lg shadow-md">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">SEO 內容分析</h3>
                    </div>
                    <div class="p-6">
                        <canvas id="seoContentChart" width="400" height="300"></canvas>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">關鍵詞分布</h3>
                    </div>
                    <div class="p-6">
                        <canvas id="keywordsChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
        </section>

        <!-- 系統設置區域 -->
        <section id="settings-section" class="section-content p-6 hidden">
            <div class="mb-6">
                <h2 class="text-2xl font-bold text-gray-900 mb-2">系統設置</h2>
                <p class="text-gray-600">配置和管理系統設置</p>
            </div>

            <!-- 系統信息區域 -->
            <div class="mb-8">
                <div class="bg-white rounded-lg shadow-md">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">
                            <i class="fas fa-info-circle mr-2 text-blue-600"></i>
                            系統信息
                        </h3>
                    </div>
                    <div class="p-6">
                        <div id="system-info-container">
                            <!-- 動態載入系統信息 -->
                            <div class="flex items-center justify-center h-32 text-gray-500">
                                <div class="text-center">
                                    <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                                    <p>載入系統信息中...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 連接設置區域 -->
            <div class="bg-white rounded-lg shadow-md">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-cog mr-2 text-green-600"></i>
                        連接設置
                    </h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-server mr-1"></i>
                                Elasticsearch URL
                            </label>
                            <input type="text" id="es-url" value="http://localhost:9200" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="請輸入 Elasticsearch 服務器地址">
                            <p class="mt-1 text-sm text-gray-500">例如: http://localhost:9200</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-clock mr-1"></i>
                                刷新間隔 (秒)
                            </label>
                            <input type="number" id="refresh-interval" value="30" min="5" max="300"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="請輸入刷新間隔">
                            <p class="mt-1 text-sm text-gray-500">最小值: 5秒，最大值: 300秒</p>
                        </div>
                    </div>
                    
                    <div class="mt-6 flex flex-wrap gap-3">
                        <button onclick="saveSettings()" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors shadow-sm">
                            <i class="fas fa-save mr-2"></i>保存設置
                        </button>
                        <button onclick="testConnection()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors shadow-sm">
                            <i class="fas fa-plug mr-2"></i>測試連接
                        </button>
                        <button onclick="admin.loadSystemInfo()" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg transition-colors shadow-sm">
                            <i class="fas fa-sync-alt mr-2"></i>刷新信息
                        </button>
                    </div>
                    
                    <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div class="flex items-start">
                            <i class="fas fa-exclamation-triangle text-yellow-600 text-lg mr-3 mt-0.5"></i>
                            <div>
                                <h4 class="text-yellow-800 font-semibold mb-1">注意事項</h4>
                                <ul class="text-yellow-700 text-sm space-y-1">
                                    <li>• 修改 URL 後請先測試連接確保可以正常訪問</li>
                                    <li>• 設置會自動保存到瀏覽器本地存儲</li>
                                    <li>• 刷新間隔過短可能會影響性能</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- 載入遮罩 -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 items-center justify-center hidden z-50">
        <div class="bg-white rounded-lg p-6 flex items-center">
            <i class="fas fa-spinner fa-spin text-blue-600 text-2xl mr-4"></i>
            <span class="text-lg font-medium">載入中...</span>
        </div>
    </div>

    <!-- 載入指示器 -->
    <div id="loading-overlay" class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p class="text-gray-700">載入中...</p>
        </div>
    </div>

    <script src="elasticsearch-admin.js"></script>
</body>
</html> 