<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI SEO 優化王 - 登入驗證</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center">
    <div class="bg-white rounded-lg shadow-xl p-8 w-full max-w-md">
        <div class="text-center mb-8">
            <i class="fas fa-search text-4xl text-blue-600 mb-4"></i>
            <h1 class="text-2xl font-bold text-gray-900">AI SEO 優化王</h1>
            <p class="text-gray-600 mt-2">Elasticsearch 管理界面</p>
        </div>

        <form id="login-form" class="space-y-6">
            <div>
                <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-user mr-2"></i>用戶名
                </label>
                <input type="text" id="username" name="username" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="請輸入用戶名">
            </div>

            <div>
                <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-lock mr-2"></i>密碼
                </label>
                <input type="password" id="password" name="password" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="請輸入密碼">
            </div>

            <div class="flex items-center justify-between">
                <label class="flex items-center">
                    <input type="checkbox" id="remember" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                    <span class="ml-2 text-sm text-gray-600">記住我</span>
                </label>
                <a href="#" class="text-sm text-blue-600 hover:text-blue-800">忘記密碼？</a>
            </div>

            <button type="submit" 
                    class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
                <i class="fas fa-sign-in-alt mr-2"></i>登入
            </button>
        </form>

        <div class="mt-6 text-center">
            <p class="text-xs text-gray-500">
                © 2024 AI SEO 優化王. 保留所有權利.
            </p>
        </div>
    </div>

    <script>
        // 簡單的認證邏輯
        const DEFAULT_CREDENTIALS = {
            username: 'admin',
            password: 'aiseo2024'
        };

        document.getElementById('login-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const remember = document.getElementById('remember').checked;
            
            // 驗證憑證
            if (username === DEFAULT_CREDENTIALS.username && password === DEFAULT_CREDENTIALS.password) {
                // 設置認證 token
                const token = btoa(JSON.stringify({
                    username: username,
                    timestamp: Date.now(),
                    remember: remember
                }));
                
                if (remember) {
                    localStorage.setItem('aiseo_auth_token', token);
                } else {
                    sessionStorage.setItem('aiseo_auth_token', token);
                }
                
                // 重定向到主界面
                window.location.href = 'index.html';
            } else {
                alert('用戶名或密碼錯誤！\n\n默認憑證：\n用戶名: admin\n密碼: aiseo2024');
            }
        });

        // 檢查是否已經登入
        function checkAuth() {
            const token = localStorage.getItem('aiseo_auth_token') || sessionStorage.getItem('aiseo_auth_token');
            if (token) {
                try {
                    const auth = JSON.parse(atob(token));
                    // 檢查 token 是否過期（24小時）
                    if (Date.now() - auth.timestamp < 24 * 60 * 60 * 1000) {
                        window.location.href = 'index.html';
                        return;
                    }
                } catch (e) {
                    // Token 無效，清除
                    localStorage.removeItem('aiseo_auth_token');
                    sessionStorage.removeItem('aiseo_auth_token');
                }
            }
        }

        // 頁面載入時檢查認證狀態
        document.addEventListener('DOMContentLoaded', checkAuth);
    </script>
</body>
</html>
