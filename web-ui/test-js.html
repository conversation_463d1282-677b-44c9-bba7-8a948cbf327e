<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript 測試頁面</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <h1>JavaScript 測試頁面</h1>
    
    <div id="test-results">
        <p>正在測試 JavaScript 載入...</p>
    </div>
    
    <button onclick="testConnection()">測試 Elasticsearch 連接</button>
    <button onclick="showSection('dashboard')">測試切換區域</button>
    
    <script src="elasticsearch-admin.js"></script>
    <script>
        // 測試腳本
        document.addEventListener('DOMContentLoaded', function() {
            const resultsDiv = document.getElementById('test-results');
            let results = [];
            
            // 測試 1: 檢查 ElasticsearchAdmin 類是否載入
            try {
                if (typeof window.ElasticsearchAdmin === 'function') {
                    results.push('<div class="status success">✓ ElasticsearchAdmin 類載入成功</div>');
                } else {
                    results.push('<div class="status error">✗ ElasticsearchAdmin 類載入失敗</div>');
                }
            } catch (e) {
                results.push('<div class="status error">✗ ElasticsearchAdmin 類載入錯誤: ' + e.message + '</div>');
            }
            
            // 測試 2: 檢查全局函數是否可用
            const globalFunctions = ['toggleSidebar', 'showSection', 'refreshAll', 'executeSearch', 'saveSettings', 'testConnection'];
            globalFunctions.forEach(func => {
                try {
                    if (typeof window[func] === 'function') {
                        results.push('<div class="status success">✓ 全局函數 ' + func + ' 載入成功</div>');
                    } else {
                        results.push('<div class="status error">✗ 全局函數 ' + func + ' 載入失敗</div>');
                    }
                } catch (e) {
                    results.push('<div class="status error">✗ 全局函數 ' + func + ' 載入錯誤: ' + e.message + '</div>');
                }
            });
            
            // 測試 3: 檢查 admin 實例是否創建
            setTimeout(() => {
                try {
                    if (window.admin && typeof window.admin === 'object') {
                        results.push('<div class="status success">✓ admin 實例創建成功</div>');
                    } else {
                        results.push('<div class="status error">✗ admin 實例創建失敗</div>');
                    }
                } catch (e) {
                    results.push('<div class="status error">✗ admin 實例創建錯誤: ' + e.message + '</div>');
                }
                
                // 更新結果
                resultsDiv.innerHTML = results.join('');
            }, 1000);
        });
    </script>
</body>
</html> 