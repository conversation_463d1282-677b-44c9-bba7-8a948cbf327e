<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>索引管理測試 - AI SEO 優化王</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-green { background-color: #10b981; }
        .status-yellow { background-color: #f59e0b; }
        .status-red { background-color: #ef4444; }
        .status-gray { background-color: #6b7280; }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto p-6">
        <div class="bg-white rounded-lg shadow-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h1 class="text-2xl font-bold text-gray-900">索引管理功能測試</h1>
                <p class="text-gray-600 mt-2">測試 http://localhost:8080/#indices 的各項功能</p>
            </div>
            
            <div class="p-6">
                <!-- 測試狀態 -->
                <div class="mb-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">系統狀態檢查</h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="border rounded-lg p-4">
                            <div class="flex items-center">
                                <span class="status-indicator status-gray" id="es-status"></span>
                                <span class="font-medium">Elasticsearch</span>
                            </div>
                            <div class="text-sm text-gray-600 mt-2" id="es-status-text">檢查中...</div>
                        </div>
                        <div class="border rounded-lg p-4">
                            <div class="flex items-center">
                                <span class="status-indicator status-gray" id="api-status"></span>
                                <span class="font-medium">API 連接</span>
                            </div>
                            <div class="text-sm text-gray-600 mt-2" id="api-status-text">檢查中...</div>
                        </div>
                        <div class="border rounded-lg p-4">
                            <div class="flex items-center">
                                <span class="status-indicator status-gray" id="indices-status"></span>
                                <span class="font-medium">索引數據</span>
                            </div>
                            <div class="text-sm text-gray-600 mt-2" id="indices-status-text">檢查中...</div>
                        </div>
                    </div>
                </div>

                <!-- 功能測試按鈕 -->
                <div class="mb-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">功能測試</h2>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <button onclick="testLoadIndices()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm">
                            <i class="fas fa-list mr-2"></i>載入索引列表
                        </button>
                        <button onclick="testViewDetails()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm">
                            <i class="fas fa-eye mr-2"></i>查看詳情
                        </button>
                        <button onclick="testSearch()" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm">
                            <i class="fas fa-search mr-2"></i>搜索功能
                        </button>
                        <button onclick="openMainInterface()" class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg text-sm">
                            <i class="fas fa-external-link-alt mr-2"></i>打開主界面
                        </button>
                    </div>
                </div>

                <!-- 測試結果 -->
                <div class="mb-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">測試結果</h2>
                    <div id="test-results" class="bg-gray-50 rounded-lg p-4 min-h-32">
                        <p class="text-gray-500 text-center">點擊上方按鈕開始測試...</p>
                    </div>
                </div>

                <!-- 索引預覽 -->
                <div>
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">索引預覽</h2>
                    <div id="indices-preview" class="bg-white border rounded-lg overflow-hidden">
                        <div class="p-4 text-center text-gray-500">
                            載入中...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const esUrl = 'http://localhost:9200';
        let testResults = [];

        // 初始化檢查
        document.addEventListener('DOMContentLoaded', function() {
            checkSystemStatus();
            loadIndicesPreview();
        });

        async function checkSystemStatus() {
            // 檢查 Elasticsearch
            try {
                const healthResponse = await fetch(`${esUrl}/_cluster/health`);
                const health = await healthResponse.json();
                
                updateStatus('es-status', 'green', `${health.cluster_name} - ${health.status.toUpperCase()}`);
                
                // 檢查 API 連接
                const indicesResponse = await fetch(`${esUrl}/_cat/indices?format=json`);
                const indices = await indicesResponse.json();
                
                updateStatus('api-status', 'green', `成功獲取 ${indices.length} 個索引`);
                updateStatus('indices-status', 'green', `發現 ${indices.filter(i => i.index.startsWith('seo_')).length} 個 SEO 索引`);
                
            } catch (error) {
                console.error('系統檢查失敗:', error);
                updateStatus('es-status', 'red', '連接失敗');
                updateStatus('api-status', 'red', 'API 不可用');
                updateStatus('indices-status', 'red', '無法載入');
            }
        }

        function updateStatus(elementId, status, text) {
            const indicator = document.getElementById(elementId);
            const textElement = document.getElementById(elementId + '-text');
            
            if (indicator) {
                indicator.className = `status-indicator status-${status}`;
            }
            if (textElement) {
                textElement.textContent = text;
            }
        }

        async function testLoadIndices() {
            addTestResult('開始測試載入索引列表...');
            
            try {
                const response = await fetch(`${esUrl}/_cat/indices?format=json&bytes=b&s=index`);
                const indices = await response.json();
                
                addTestResult(`✅ 成功載入 ${indices.length} 個索引`, 'success');
                addTestResult(`SEO 索引: ${indices.filter(i => i.index.startsWith('seo_')).map(i => i.index).join(', ')}`, 'info');
                
            } catch (error) {
                addTestResult(`❌ 載入失敗: ${error.message}`, 'error');
            }
        }

        async function testViewDetails() {
            addTestResult('開始測試查看索引詳情...');
            
            try {
                const indexName = 'seo_content';
                const mappingResponse = await fetch(`${esUrl}/${indexName}/_mapping`);
                const mapping = await mappingResponse.json();
                
                addTestResult(`✅ 成功獲取 ${indexName} 映射信息`, 'success');
                addTestResult(`映射字段數: ${Object.keys(mapping[indexName]?.mappings?.properties || {}).length}`, 'info');
                
            } catch (error) {
                addTestResult(`❌ 詳情獲取失敗: ${error.message}`, 'error');
            }
        }

        async function testSearch() {
            addTestResult('開始測試搜索功能...');
            
            try {
                const query = {
                    query: { match_all: {} },
                    size: 5
                };
                
                const response = await fetch(`${esUrl}/seo_content/_search`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(query)
                });
                
                const result = await response.json();
                
                addTestResult(`✅ 搜索完成，找到 ${result.hits.total.value} 個文檔`, 'success');
                addTestResult(`返回 ${result.hits.hits.length} 個結果`, 'info');
                
            } catch (error) {
                addTestResult(`❌ 搜索失敗: ${error.message}`, 'error');
            }
        }

        function openMainInterface() {
            addTestResult('正在打開主界面 #indices 頁面...', 'info');
            window.open('http://localhost:8080/elasticsearch-admin.html#indices', '_blank');
        }

        async function loadIndicesPreview() {
            try {
                const response = await fetch(`${esUrl}/_cat/indices?format=json&bytes=b&s=index`);
                const indices = await response.json();
                
                const preview = document.getElementById('indices-preview');
                preview.innerHTML = `
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">索引名稱</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">狀態</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">文檔數</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">大小</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                ${indices.slice(0, 10).map(index => `
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${index.index}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="status-indicator status-${index.health}"></span>
                                            ${(index.health || 'unknown').toUpperCase()}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formatNumber(index['docs.count'] || 0)}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formatBytes(parseBytes(index['store.size']) || 0)}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                `;
                
            } catch (error) {
                document.getElementById('indices-preview').innerHTML = `
                    <div class="p-4 text-center text-red-600">
                        載入失敗: ${error.message}
                    </div>
                `;
            }
        }

        function addTestResult(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const result = { message, type, timestamp };
            testResults.push(result);
            
            const container = document.getElementById('test-results');
            const resultElement = document.createElement('div');
            resultElement.className = `mb-2 p-2 rounded text-sm ${getResultClass(type)}`;
            resultElement.innerHTML = `
                <span class="text-gray-500">[${timestamp}]</span>
                <span class="ml-2">${message}</span>
            `;
            
            container.appendChild(resultElement);
            container.scrollTop = container.scrollHeight;
        }

        function getResultClass(type) {
            switch(type) {
                case 'success': return 'bg-green-100 text-green-800 border border-green-200';
                case 'error': return 'bg-red-100 text-red-800 border border-red-200';
                case 'warning': return 'bg-yellow-100 text-yellow-800 border border-yellow-200';
                default: return 'bg-blue-100 text-blue-800 border border-blue-200';
            }
        }

        function formatNumber(num) {
            return new Intl.NumberFormat('zh-TW').format(num);
        }

        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function parseBytes(sizeStr) {
            if (!sizeStr) return 0;
            const units = { 'b': 1, 'kb': 1024, 'mb': 1024 * 1024, 'gb': 1024 * 1024 * 1024 };
            const match = sizeStr.toLowerCase().match(/^(\d+(?:\.\d+)?)\s*([a-z]+)$/);
            if (match) {
                const value = parseFloat(match[1]);
                const unit = match[2];
                return value * (units[unit] || 1);
            }
            return parseInt(sizeStr) || 0;
        }
    </script>
</body>
</html> 