<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings 功能測試</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">
            <i class="fas fa-cog text-blue-600 mr-3"></i>
            Settings 功能測試
        </h1>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">功能測試</h3>
                <div class="space-y-3">
                    <button onclick="testConnection()" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded">
                        測試 Elasticsearch 連接
                    </button>
                    <button onclick="testSettings()" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded">
                        測試設置功能
                    </button>
                    <button onclick="openSettings()" class="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded">
                        打開設置頁面
                    </button>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">測試結果</h3>
                <div id="results" class="space-y-2">
                    <p class="text-gray-500">點擊測試按鈕查看結果</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        async function testConnection() {
            const results = document.getElementById('results');
            try {
                const response = await fetch('http://localhost:9200/');
                if (response.ok) {
                    const info = await response.json();
                    results.innerHTML = `<p class="text-green-600">✅ 連接成功: ${info.cluster_name}</p>`;
                } else {
                    results.innerHTML = `<p class="text-red-600">❌ 連接失敗: ${response.status}</p>`;
                }
            } catch (error) {
                results.innerHTML = `<p class="text-red-600">❌ 錯誤: ${error.message}</p>`;
            }
        }

        function testSettings() {
            const results = document.getElementById('results');
            // 測試本地存儲
            try {
                localStorage.setItem('test-key', 'test-value');
                const value = localStorage.getItem('test-key');
                localStorage.removeItem('test-key');
                
                if (value === 'test-value') {
                    results.innerHTML = `<p class="text-green-600">✅ 本地存儲功能正常</p>`;
                } else {
                    results.innerHTML = `<p class="text-red-600">❌ 本地存儲異常</p>`;
                }
            } catch (error) {
                results.innerHTML = `<p class="text-red-600">❌ 本地存儲錯誤: ${error.message}</p>`;
            }
        }

        function openSettings() {
            window.open('elasticsearch-admin.html#settings', '_blank');
        }
    </script>
</body>
</html> 