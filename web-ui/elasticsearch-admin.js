// Elasticsearch 管理界面 JavaScript
(function() {
    'use strict';
    
    // 避免重複初始化
    if (window.ElasticsearchAdmin) {
        return;
    }

    class ElasticsearchAdmin {
        constructor() {
            this.esUrl = 'http://localhost:9200';
            this.refreshInterval = 30000;
            this.charts = {};
            this.refreshTimer = null;
            this.indices = [];
            
            // 從本地存儲載入設置
            this.loadSettingsFromStorage();
            
            this.init();
        }

        init() {
            this.setupEventListeners();
            this.setupRouting();
            this.loadClusterInfo();
            this.loadIndices();
            
            // 檢查 URL hash 或默認顯示 dashboard
            const hash = window.location.hash.substring(1) || 'dashboard';
            this.showSection(hash);
        }

        setupRouting() {
            // 監聽 hash 變化
            window.addEventListener('hashchange', () => {
                const hash = window.location.hash.substring(1) || 'dashboard';
                this.showSection(hash);
            });
        }

        setupEventListeners() {
            const searchType = document.getElementById('search-type');
            if (searchType) {
                searchType.addEventListener('change', () => {
                    this.updateSearchParams();
                });
            }
            
            // 添加回車鍵搜索支持
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && (e.target.id === 'search-field' || e.target.id === 'search-value')) {
                    this.executeSearch();
                }
            });
        }

        showSection(sectionName) {
            // 更新 URL hash
            if (window.location.hash !== `#${sectionName}`) {
                window.location.hash = sectionName;
            }
            
            // 隱藏所有區域
            document.querySelectorAll('.section-content').forEach(section => {
                section.classList.add('hidden');
            });

            // 移除導航活動狀態
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('bg-blue-50', 'text-blue-600');
            });

            // 顯示選中區域
            const targetSection = document.getElementById(`${sectionName}-section`);
            if (targetSection) {
                targetSection.classList.remove('hidden');
            }

            // 激活導航項
            const navItem = document.querySelector(`a[href="#${sectionName}"]`);
            if (navItem) {
                navItem.classList.add('bg-blue-50', 'text-blue-600');
            }

            // 載入對應數據
            switch(sectionName) {
                case 'dashboard':
                    this.loadDashboardData();
                    break;
                case 'indices':
                    this.loadIndicesTable();
                    break;
                case 'search':
                    this.loadSearchIndices();
                    break;
                case 'analytics':
                    this.loadAnalytics();
                    break;
                case 'settings':
                    this.loadSettings();
                    break;
            }
        }

        async loadClusterInfo() {
            try {
                this.showLoading();
                
                const healthResponse = await fetch(`${this.esUrl}/_cluster/health`);
                const health = await healthResponse.json();
                
                const statsResponse = await fetch(`${this.esUrl}/_cluster/stats`);
                const stats = await statsResponse.json();

                this.updateClusterStatus(health);
                this.updateClusterStats(stats);

            } catch (error) {
                console.error('載入集群信息失敗:', error);
                this.updateConnectionStatus(false);
            } finally {
                this.hideLoading();
            }
        }

        updateClusterStatus(health) {
            const statusText = document.getElementById('status-text');
            const healthElement = document.getElementById('cluster-health');
            const statusIndicator = document.querySelector('#cluster-status .status-indicator');
            
            if (statusText) statusText.textContent = `集群: ${health.cluster_name}`;
            if (healthElement) healthElement.textContent = health.status.toUpperCase();

            if (statusIndicator) {
                statusIndicator.className = 'status-indicator';
                switch(health.status) {
                    case 'green':
                        statusIndicator.classList.add('status-green');
                        break;
                    case 'yellow':
                        statusIndicator.classList.add('status-yellow');
                        break;
                    case 'red':
                        statusIndicator.classList.add('status-red');
                        break;
                }
            }

            this.updateConnectionStatus(true);
        }

        updateClusterStats(stats) {
            const elements = {
                'indices-count': stats.indices.count || 0,
                'docs-count': this.formatNumber(stats.indices.docs.count || 0),
                'store-size': this.formatBytes(stats.indices.store.size_in_bytes || 0)
            };

            Object.entries(elements).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) element.textContent = value;
            });
        }

        async loadIndices() {
            try {
                const response = await fetch(`${this.esUrl}/_cat/indices?format=json&bytes=b`);
                const indices = await response.json();
                
                this.indices = indices;
                this.updateSEOIndicesOverview(indices);
                
                return indices;
            } catch (error) {
                console.error('載入索引失敗:', error);
                return [];
            }
        }

        updateSEOIndicesOverview(indices) {
            const seoIndices = ['seo_content', 'seo_analysis', 'seo_keywords', 'seo_competitors'];
            const container = document.getElementById('seo-indices-overview');
            
            if (!container) return;

            const descriptions = {
                'seo_content': { icon: 'fas fa-file-alt', name: 'SEO 內容', desc: '網頁內容和 SEO 數據' },
                'seo_analysis': { icon: 'fas fa-chart-line', name: 'SEO 分析', desc: 'SEO 分析結果和建議' },
                'seo_keywords': { icon: 'fas fa-key', name: 'SEO 關鍵詞', desc: '關鍵詞研究數據' },
                'seo_competitors': { icon: 'fas fa-users', name: 'SEO 競爭對手', desc: '競爭對手分析數據' }
            };

            container.innerHTML = seoIndices.map(indexName => {
                const index = indices.find(i => i.index === indexName);
                const desc = descriptions[indexName];
                
                if (!index) {
                    return `
                        <div class="bg-gray-100 rounded-lg p-4 text-center">
                            <i class="${desc.icon} text-3xl text-gray-400 mb-2"></i>
                            <h4 class="font-semibold text-gray-600">${desc.name}</h4>
                            <p class="text-sm text-gray-500 mb-2">${desc.desc}</p>
                            <span class="text-sm text-red-600">未創建</span>
                        </div>
                    `;
                }

                return `
                    <div class="bg-white border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="admin.viewIndexDetails('${indexName}')">
                        <div class="flex items-center justify-between mb-2">
                            <i class="${desc.icon} text-2xl text-blue-600"></i>
                            <span class="status-indicator status-${index.health || 'green'}"></span>
                        </div>
                        <h4 class="font-semibold text-gray-900">${desc.name}</h4>
                        <p class="text-sm text-gray-600 mb-2">${desc.desc}</p>
                        <div class="text-sm space-y-1">
                            <div class="flex justify-between">
                                <span class="text-gray-500">文檔:</span>
                                <span class="font-medium">${this.formatNumber(index['docs.count'] || 0)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-500">大小:</span>
                                <span class="font-medium">${this.formatBytes(this.parseBytes(index['store.size']) || 0)}</span>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        loadDashboardData() {
            // 延遲一下確保 DOM 元素已經顯示
            setTimeout(() => {
                if (this.indices && this.indices.length > 0) {
                    this.updateDocsChart();
                    this.updateStorageChart();
                } else {
                    // 如果索引數據還沒載入，重新載入
                    this.loadIndices().then(() => {
                        this.updateDocsChart();
                        this.updateStorageChart();
                    });
                }
            }, 100);
        }

        updateDocsChart() {
            const canvas = document.getElementById('docsChart');
            if (!canvas) {
                console.warn('Canvas docsChart not found');
                return;
            }
            
            // 確保畫布元素可見
            if (canvas.offsetParent === null) {
                console.warn('Canvas docsChart is not visible');
                return;
            }
            
            const ctx = canvas.getContext('2d');
            const seoIndices = this.indices.filter(i => i.index.startsWith('seo_'));
            
            if (seoIndices.length === 0) {
                // 如果沒有 SEO 索引，顯示示例數據
                const labels = ['CONTENT', 'ANALYSIS', 'KEYWORDS', 'COMPETITORS'];
                const data = [0, 0, 0, 0];
                
                if (this.charts.docs) {
                    this.charts.docs.destroy();
                }

                this.charts.docs = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: labels,
                        datasets: [{
                            data: data,
                            backgroundColor: ['#3B82F6', '#8B5CF6', '#10B981', '#F59E0B']
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: true,
                        plugins: {
                            legend: { position: 'bottom' }
                        }
                    }
                });
                return;
            }

            const labels = seoIndices.map(i => i.index.replace('seo_', '').toUpperCase());
            const data = seoIndices.map(i => parseInt(i['docs.count']) || 0);

            if (this.charts.docs) {
                this.charts.docs.destroy();
            }

            this.charts.docs = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: ['#3B82F6', '#8B5CF6', '#10B981', '#F59E0B']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    plugins: {
                        legend: { position: 'bottom' }
                    }
                }
            });
        }

        updateStorageChart() {
            const canvas = document.getElementById('storageChart');
            if (!canvas) {
                console.warn('Canvas storageChart not found');
                return;
            }
            
            // 確保畫布元素可見
            if (canvas.offsetParent === null) {
                console.warn('Canvas storageChart is not visible');
                return;
            }
            
            const ctx = canvas.getContext('2d');
            const seoIndices = this.indices.filter(i => i.index.startsWith('seo_'));
            
            if (seoIndices.length === 0) {
                // 如果沒有 SEO 索引，顯示示例數據
                const labels = ['CONTENT', 'ANALYSIS', 'KEYWORDS', 'COMPETITORS'];
                const data = [0, 0, 0, 0];
                
                if (this.charts.storage) {
                    this.charts.storage.destroy();
                }

                this.charts.storage = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: '存儲大小 (MB)',
                            data: data,
                            backgroundColor: '#3B82F6'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: true,
                        scales: { y: { beginAtZero: true } }
                    }
                });
                return;
            }

            const labels = seoIndices.map(i => i.index.replace('seo_', '').toUpperCase());
            const data = seoIndices.map(i => {
                const bytes = this.parseBytes(i['store.size']);
                return bytes / 1024 / 1024; // 轉換為MB
            });

            if (this.charts.storage) {
                this.charts.storage.destroy();
            }

            this.charts.storage = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '存儲大小 (MB)',
                        data: data,
                        backgroundColor: '#3B82F6'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    scales: { y: { beginAtZero: true } }
                }
            });
        }

        async loadIndicesTable() {
            try {
                const response = await fetch(`${this.esUrl}/_cat/indices?format=json&bytes=b&s=index`);
                const indices = await response.json();
                
                // 更新索引統計
                this.updateIndicesStats(indices);
                
                const tableContainer = document.getElementById('indices-table');
                if (!tableContainer) return;
                
                const table = `
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">索引名稱</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">狀態</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">文檔數</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">大小</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            ${indices.map(index => `
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${index.index}</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-medium rounded-full ${this.getStatusClass(index.health)}">
                                            ${(index.health || 'unknown').toUpperCase()}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${this.formatNumber(index['docs.count'] || 0)}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${this.formatBytes(this.parseBytes(index['store.size']) || 0)}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="admin.viewIndexDetails('${index.index}')" class="text-blue-600 hover:text-blue-900 mr-2">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                        <button onclick="admin.searchInIndex('${index.index}')" class="text-green-600 hover:text-green-900 mr-2">
                                            <i class="fas fa-search"></i> 搜索
                                        </button>
                                        ${!index.index.startsWith('.') ? `
                                        <button onclick="admin.deleteIndex('${index.index}')" class="text-red-600 hover:text-red-900">
                                            <i class="fas fa-trash"></i> 刪除
                                        </button>
                                        ` : ''}
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;
                
                tableContainer.innerHTML = table;
                
            } catch (error) {
                console.error('載入索引表格失敗:', error);
            }
        }

        updateIndicesStats(indices) {
            // 計算統計信息
            const stats = {
                total: indices.length,
                green: indices.filter(i => i.health === 'green').length,
                yellow: indices.filter(i => i.health === 'yellow').length,
                red: indices.filter(i => i.health === 'red').length,
                totalDocs: indices.reduce((sum, i) => sum + parseInt(i['docs.count'] || 0), 0),
                totalSize: indices.reduce((sum, i) => sum + this.parseBytes(i['store.size'] || '0'), 0)
            };

            // 檢查是否存在統計容器，如果不存在則創建
            let statsContainer = document.getElementById('indices-stats');
            if (!statsContainer) {
                // 在索引表格前插入統計區域
                const tableContainer = document.getElementById('indices-table');
                if (tableContainer && tableContainer.parentNode) {
                    statsContainer = document.createElement('div');
                    statsContainer.id = 'indices-stats';
                    statsContainer.className = 'mb-6';
                    tableContainer.parentNode.insertBefore(statsContainer, tableContainer.parentNode.firstChild.nextSibling.nextSibling);
                }
            }

            if (statsContainer) {
                statsContainer.innerHTML = `
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                        <div class="bg-white border rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-blue-600">${stats.total}</div>
                            <div class="text-sm text-gray-600">總索引數</div>
                        </div>
                        <div class="bg-white border rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-green-600">${this.formatNumber(stats.totalDocs)}</div>
                            <div class="text-sm text-gray-600">總文檔數</div>
                        </div>
                        <div class="bg-white border rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-purple-600">${this.formatBytes(stats.totalSize)}</div>
                            <div class="text-sm text-gray-600">總存儲大小</div>
                        </div>
                        <div class="bg-white border rounded-lg p-4 text-center">
                            <div class="flex justify-center space-x-2 mb-2">
                                <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">${stats.green} 綠</span>
                                <span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">${stats.yellow} 黃</span>
                                <span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">${stats.red} 紅</span>
                            </div>
                            <div class="text-sm text-gray-600">健康狀態</div>
                        </div>
                    </div>
                `;
            }
        }

        loadSearchIndices() {
            const select = document.getElementById('search-index');
            if (!select) return;
            
            select.innerHTML = '<option value="">選擇索引...</option>' + 
                this.indices.map(index => 
                    `<option value="${index.index}">${index.index}</option>`
                ).join('');
            
            this.updateSearchParams();
        }

        updateSearchParams() {
            const searchType = document.getElementById('search-type')?.value || 'match_all';
            const paramsContainer = document.getElementById('search-params');
            if (!paramsContainer) return;
            
            let paramsHTML = '';
            
            switch(searchType) {
                case 'match':
                    paramsHTML = `
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">搜索字段</label>
                            <input type="text" id="search-field" placeholder="title, content, description" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            <p class="text-xs text-gray-500 mt-1">支援全文搜索的字段</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">搜索值</label>
                            <input type="text" id="search-value" placeholder="搜索內容" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            <p class="text-xs text-gray-500 mt-1">按 Enter 鍵快速搜索</p>
                        </div>
                    `;
                    break;
                case 'term':
                    paramsHTML = `
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">精確字段</label>
                            <input type="text" id="search-field" placeholder="status, category, type" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            <p class="text-xs text-gray-500 mt-1">用於精確匹配的字段</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">精確值</label>
                            <input type="text" id="search-value" placeholder="精確值" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            <p class="text-xs text-gray-500 mt-1">完全匹配的值</p>
                        </div>
                    `;
                    break;
                case 'range':
                    paramsHTML = `
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">範圍字段</label>
                            <input type="text" id="search-field" placeholder="date, score, price" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            <p class="text-xs text-gray-500 mt-1">數值或日期字段</p>
                        </div>
                        <div class="grid grid-cols-2 gap-2">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">最小值</label>
                                <input type="text" id="range-from" placeholder="0" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">最大值</label>
                                <input type="text" id="range-to" placeholder="100" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            </div>
                        </div>
                    `;
                    break;
                case 'wildcard':
                    paramsHTML = `
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">通配符字段</label>
                            <input type="text" id="search-field" placeholder="title, filename" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">通配符模式</label>
                            <input type="text" id="search-value" placeholder="*SEO* 或 test?" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            <p class="text-xs text-gray-500 mt-1">* 表示多個字符，? 表示單個字符</p>
                        </div>
                    `;
                    break;
                case 'fuzzy':
                    paramsHTML = `
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">模糊搜索字段</label>
                            <input type="text" id="search-field" placeholder="title, name" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">搜索值</label>
                            <input type="text" id="search-value" placeholder="搜索內容" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">模糊度 (0-2)</label>
                            <input type="number" id="fuzziness" min="0" max="2" value="1" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            <p class="text-xs text-gray-500 mt-1">允許的字符差異數量</p>
                        </div>
                    `;
                    break;
                case 'custom':
                    paramsHTML = `
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">自定義查詢 JSON</label>
                            <textarea id="custom-query" rows="8" class="w-full px-3 py-2 border border-gray-300 rounded-md font-mono text-sm" placeholder='{
  "query": {
    "bool": {
      "must": [
        {"match": {"title": "SEO"}}
      ]
    }
  },
  "size": 10
}'></textarea>
                            <p class="text-xs text-gray-500 mt-1">完整的 Elasticsearch 查詢 JSON</p>
                        </div>
                    `;
                    break;
                default:
                    paramsHTML = `
                        <div class="text-center py-4">
                            <p class="text-gray-500">全部匹配查詢，無需額外參數</p>
                        </div>
                    `;
            }
            
            paramsContainer.innerHTML = paramsHTML;
        }

        async executeSearch() {
            const indexName = document.getElementById('search-index')?.value;
            const searchType = document.getElementById('search-type')?.value || 'match_all';
            
            if (!indexName) {
                this.showAlert('請選擇索引', 'warning');
                return;
            }
            
            let query = { query: { match_all: {} }, size: 10 };
            
            try {
                // 根據搜索類型構建查詢
                switch(searchType) {
                    case 'match':
                        const matchField = document.getElementById('search-field')?.value;
                        const matchValue = document.getElementById('search-value')?.value;
                        if (matchField && matchValue) {
                            query.query = { match: { [matchField]: matchValue } };
                        } else {
                            this.showAlert('請填寫搜索字段和值', 'warning');
                            return;
                        }
                        break;
                        
                    case 'term':
                        const termField = document.getElementById('search-field')?.value;
                        const termValue = document.getElementById('search-value')?.value;
                        if (termField && termValue) {
                            query.query = { term: { [termField + '.keyword']: termValue } };
                        } else {
                            this.showAlert('請填寫精確字段和值', 'warning');
                            return;
                        }
                        break;
                        
                    case 'range':
                        const rangeField = document.getElementById('search-field')?.value;
                        const rangeFrom = document.getElementById('range-from')?.value;
                        const rangeTo = document.getElementById('range-to')?.value;
                        if (rangeField && (rangeFrom || rangeTo)) {
                            const rangeQuery = {};
                            if (rangeFrom) rangeQuery.gte = rangeFrom;
                            if (rangeTo) rangeQuery.lte = rangeTo;
                            query.query = { range: { [rangeField]: rangeQuery } };
                        } else {
                            this.showAlert('請填寫範圍字段和至少一個範圍值', 'warning');
                            return;
                        }
                        break;
                        
                    case 'wildcard':
                        const wildcardField = document.getElementById('search-field')?.value;
                        const wildcardValue = document.getElementById('search-value')?.value;
                        if (wildcardField && wildcardValue) {
                            query.query = { wildcard: { [wildcardField]: wildcardValue } };
                        } else {
                            this.showAlert('請填寫通配符字段和模式', 'warning');
                            return;
                        }
                        break;
                        
                    case 'fuzzy':
                        const fuzzyField = document.getElementById('search-field')?.value;
                        const fuzzyValue = document.getElementById('search-value')?.value;
                        const fuzziness = document.getElementById('fuzziness')?.value || '1';
                        if (fuzzyField && fuzzyValue) {
                            query.query = { 
                                fuzzy: { 
                                    [fuzzyField]: {
                                        value: fuzzyValue,
                                        fuzziness: fuzziness
                                    }
                                } 
                            };
                        } else {
                            this.showAlert('請填寫模糊搜索字段和值', 'warning');
                            return;
                        }
                        break;
                        
                    case 'custom':
                        const customQuery = document.getElementById('custom-query')?.value;
                        if (customQuery) {
                            try {
                                query = JSON.parse(customQuery);
                            } catch (parseError) {
                                this.showAlert('自定義查詢 JSON 格式錯誤: ' + parseError.message, 'error');
                                return;
                            }
                        } else {
                            this.showAlert('請輸入自定義查詢 JSON', 'warning');
                            return;
                        }
                        break;
                }
                
                this.showLoading();
                
                const response = await fetch(`${this.esUrl}/${indexName}/_search`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(query)
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(`HTTP ${response.status}: ${errorData.error?.reason || response.statusText}`);
                }
                
                const results = await response.json();
                this.displaySearchResults(results);
                this.showAlert(`搜索完成！找到 ${results.hits.total?.value || results.hits.total || 0} 條結果`, 'success');
                
            } catch (error) {
                console.error('搜索失敗:', error);
                this.showAlert('搜索失敗: ' + error.message, 'error');
                this.displaySearchResults({ hits: { hits: [], total: 0 } });
            } finally {
                this.hideLoading();
            }
        }

        displaySearchResults(results) {
            const container = document.getElementById('search-results');
            if (!container) return;
            
            if (results.hits && results.hits.hits.length > 0) {
                const hits = results.hits.hits;
                const total = results.hits.total?.value || results.hits.total || 0;
                const took = results.took || 0;
                
                container.innerHTML = `
                    <div class="mb-4 p-3 bg-blue-50 rounded-lg">
                        <div class="flex justify-between items-center">
                            <p class="text-sm text-blue-700">
                                <i class="fas fa-info-circle mr-1"></i>
                                找到 <strong>${total}</strong> 條結果，顯示前 <strong>${hits.length}</strong> 條
                            </p>
                            <p class="text-xs text-blue-600">耗時: ${took}ms</p>
                        </div>
                    </div>
                    <div class="space-y-4">
                        ${hits.map((hit, index) => `
                            <div class="search-result-item border rounded-lg p-4 bg-white hover:shadow-md transition-all duration-200">
                                <div class="flex justify-between items-start mb-3">
                                    <div class="flex items-center">
                                        <span class="inline-flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-800 text-xs font-medium rounded-full mr-3">
                                            ${index + 1}
                                        </span>
                                        <h4 class="font-medium text-gray-900">文檔 ID: ${hit._id}</h4>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-star mr-1"></i>
                                            ${hit._score?.toFixed(3) || 'N/A'}
                                        </span>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            ${hit._index}
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                        <div>
                                            <span class="font-medium text-gray-600">索引:</span>
                                            <span class="text-gray-900">${hit._index}</span>
                                        </div>
                                        <div>
                                            <span class="font-medium text-gray-600">類型:</span>
                                            <span class="text-gray-900">${hit._type || '_doc'}</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="border-t pt-3">
                                    <div class="flex justify-between items-center mb-2">
                                        <h5 class="font-medium text-gray-700">
                                            <i class="fas fa-file-code mr-1"></i>
                                            文檔內容
                                        </h5>
                                        <button onclick="this.parentElement.nextElementSibling.classList.toggle('hidden')" 
                                                class="text-blue-600 hover:text-blue-800 text-sm">
                                            <i class="fas fa-eye mr-1"></i>
                                            切換顯示
                                        </button>
                                    </div>
                                    <div class="json-viewer text-xs">
                                        ${this.formatJsonForDisplay(hit._source)}
                                    </div>
                                </div>
                                
                                ${hit.highlight ? `
                                    <div class="mt-3 border-t pt-3">
                                        <h5 class="font-medium text-gray-700 mb-2">
                                            <i class="fas fa-highlighter mr-1"></i>
                                            高亮匹配
                                        </h5>
                                        <div class="text-sm">
                                            ${Object.entries(hit.highlight).map(([field, highlights]) => `
                                                <div class="mb-1">
                                                    <span class="font-medium text-gray-600">${field}:</span>
                                                    <span class="text-gray-900">${highlights.join(' ... ')}</span>
                                                </div>
                                            `).join('')}
                                        </div>
                                    </div>
                                ` : ''}
                            </div>
                        `).join('')}
                    </div>
                    
                    ${total > hits.length ? `
                        <div class="mt-6 text-center">
                            <div class="inline-flex items-center px-4 py-2 bg-blue-50 text-blue-700 rounded-lg">
                                <i class="fas fa-info-circle mr-2"></i>
                                還有 ${total - hits.length} 條結果未顯示，請調整查詢條件或增加 size 參數
                            </div>
                        </div>
                    ` : ''}
                `;
            } else {
                container.innerHTML = `
                    <div class="flex items-center justify-center h-64 text-gray-500">
                        <div class="text-center">
                            <i class="fas fa-search text-4xl mb-4 text-gray-300"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">沒有找到相關結果</h3>
                            <p class="text-gray-600 mb-4">請嘗試調整搜索條件或選擇其他索引</p>
                            <div class="space-y-2 text-sm text-gray-500">
                                <p>• 檢查搜索字段是否存在於選定的索引中</p>
                                <p>• 嘗試使用不同的搜索類型</p>
                                <p>• 確認搜索值的格式是否正確</p>
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        formatJsonForDisplay(obj) {
            if (!obj) return '';
            
            const jsonStr = JSON.stringify(obj, null, 2);
            
            // 簡單的語法高亮
            return jsonStr
                .replace(/(".*?")\s*:/g, '<span style="color: #d73a49;">$1</span>:')
                .replace(/:\s*(".*?")/g, ': <span style="color: #032f62;">$1</span>')
                .replace(/:\s*(\d+)/g, ': <span style="color: #005cc5;">$1</span>')
                .replace(/:\s*(true|false|null)/g, ': <span style="color: #d73a49;">$1</span>');
        }

        async viewIndexDetails(indexName) {
            try {
                // 獲取索引詳細信息
                const [mappingResponse, settingsResponse] = await Promise.all([
                    fetch(`${this.esUrl}/${indexName}/_mapping`),
                    fetch(`${this.esUrl}/${indexName}/_settings`)
                ]);
                
                const mapping = await mappingResponse.json();
                const settings = await settingsResponse.json();
                
                // 創建模態框顯示詳細信息
                this.showIndexDetailsModal(indexName, mapping, settings);
                
            } catch (error) {
                console.error('載入索引詳情失敗:', error);
                this.showAlert('載入索引詳情失敗', 'error');
            }
        }

        showIndexDetailsModal(indexName, mapping, settings) {
            // 創建模態框
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-lg max-w-4xl max-h-screen overflow-y-auto m-4">
                    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900">索引詳情: ${indexName}</h3>
                        <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="text-md font-semibold text-gray-900 mb-3">映射 (Mapping)</h4>
                                <div class="bg-gray-50 rounded-md p-4 max-h-96 overflow-y-auto">
                                    <pre class="text-sm text-gray-700"><code>${JSON.stringify(mapping, null, 2)}</code></pre>
                                </div>
                            </div>
                            <div>
                                <h4 class="text-md font-semibold text-gray-900 mb-3">設置 (Settings)</h4>
                                <div class="bg-gray-50 rounded-md p-4 max-h-96 overflow-y-auto">
                                    <pre class="text-sm text-gray-700"><code>${JSON.stringify(settings, null, 2)}</code></pre>
                                </div>
                            </div>
                        </div>
                        <div class="mt-6 flex justify-end space-x-3">
                            <button onclick="admin.searchInIndex('${indexName}'); this.closest('.fixed').remove()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                                <i class="fas fa-search mr-2"></i>搜索此索引
                            </button>
                            <button onclick="this.closest('.fixed').remove()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                                關閉
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
        }

        searchInIndex(indexName) {
            this.showSection('search');
            const searchIndex = document.getElementById('search-index');
            if (searchIndex) {
                searchIndex.value = indexName;
            }
        }

        async deleteIndex(indexName) {
            if (!confirm(`確定要刪除索引 "${indexName}" 嗎？\n\n此操作不可撤銷，所有數據將永久丟失！`)) {
                return;
            }
            
            try {
                this.showLoading();
                const response = await fetch(`${this.esUrl}/${indexName}`, {
                    method: 'DELETE'
                });
                
                if (response.ok) {
                    this.showAlert(`索引 "${indexName}" 已成功刪除`, 'success');
                    this.loadIndicesTable(); // 重新載入索引表格
                    this.loadIndices(); // 重新載入索引數據
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
            } catch (error) {
                console.error('刪除索引失敗:', error);
                this.showAlert(`刪除索引失敗: ${error.message}`, 'error');
            } finally {
                this.hideLoading();
            }
        }

        loadAnalytics() {
            // 模擬分析數據
            this.setupSEOContentChart();
            this.setupKeywordsChart();
        }

        setupSEOContentChart() {
            const canvas = document.getElementById('seoContentChart');
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            
            if (this.charts.seoContent) {
                this.charts.seoContent.destroy();
            }

            this.charts.seoContent = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: 'SEO 內容增長',
                        data: [120, 190, 300, 500, 720, 890],
                        borderColor: '#3B82F6',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: { y: { beginAtZero: true } }
                }
            });
        }

        setupKeywordsChart() {
            const canvas = document.getElementById('keywordsChart');
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            
            if (this.charts.keywords) {
                this.charts.keywords.destroy();
            }

            this.charts.keywords = new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: ['高流量', '中流量', '低流量', '長尾詞', '品牌詞'],
                    datasets: [{
                        label: '關鍵詞分布',
                        data: [80, 60, 40, 90, 70],
                        borderColor: '#8B5CF6',
                        backgroundColor: 'rgba(139, 92, 246, 0.2)'
                    }]
                },
                options: { responsive: true }
            });
        }

        refreshAll() {
            this.loadClusterInfo();
            this.loadIndices();
            
            const activeSection = document.querySelector('.section-content:not(.hidden)');
            if (activeSection) {
                const sectionId = activeSection.id.replace('-section', '');
                this.showSection(sectionId);
            }
        }

        updateConnectionStatus(connected) {
            const statusIndicator = document.querySelector('#cluster-status .status-indicator');
            const statusText = document.getElementById('status-text');
            const healthElement = document.getElementById('cluster-health');
            
            if (statusIndicator) {
                statusIndicator.className = 'status-indicator';
                statusIndicator.classList.add(connected ? 'status-green' : 'status-red');
            }
            
            if (!connected) {
                if (statusText) statusText.textContent = '連接失敗';
                if (healthElement) healthElement.textContent = 'ERROR';
            }
        }

        formatNumber(num) {
            return new Intl.NumberFormat('zh-TW').format(num);
        }

        formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        parseBytes(sizeStr) {
            if (!sizeStr) return 0;
            
            const units = { 'b': 1, 'kb': 1024, 'mb': 1024 * 1024, 'gb': 1024 * 1024 * 1024 };
            const match = sizeStr.toLowerCase().match(/^(\d+(?:\.\d+)?)\s*([a-z]+)$/);
            
            if (match) {
                const value = parseFloat(match[1]);
                const unit = match[2];
                return value * (units[unit] || 1);
            }
            
            return parseInt(sizeStr) || 0;
        }

        getStatusClass(health) {
            switch(health) {
                case 'green': return 'bg-green-100 text-green-800';
                case 'yellow': return 'bg-yellow-100 text-yellow-800';
                case 'red': return 'bg-red-100 text-red-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        showLoading() {
            const overlay = document.getElementById('loading-overlay');
            if (overlay) {
                overlay.classList.remove('hidden');
                overlay.classList.add('flex');
            }
        }

        hideLoading() {
            const overlay = document.getElementById('loading-overlay');
            if (overlay) {
                overlay.classList.add('hidden');
                overlay.classList.remove('flex');
            }
        }

        loadSettings() {
            // 載入當前設置到表單
            const esUrlInput = document.getElementById('es-url');
            const refreshIntervalInput = document.getElementById('refresh-interval');
            
            if (esUrlInput) {
                esUrlInput.value = this.esUrl;
            }
            
            if (refreshIntervalInput) {
                refreshIntervalInput.value = this.refreshInterval / 1000;
            }
            
            // 載入系統信息
            this.loadSystemInfo();
        }

        async loadSystemInfo() {
            try {
                // 獲取 Elasticsearch 版本信息
                const response = await fetch(`${this.esUrl}/`);
                const info = await response.json();
                
                this.displaySystemInfo(info);
            } catch (error) {
                console.error('載入系統信息失敗:', error);
                this.displaySystemInfo(null);
            }
        }

        displaySystemInfo(info) {
            const container = document.getElementById('system-info-container');
            if (!container) return;

            if (!info) {
                container.innerHTML = `
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle text-red-500 text-xl mr-3"></i>
                            <div>
                                <h4 class="text-red-800 font-semibold">無法連接到 Elasticsearch</h4>
                                <p class="text-red-600 text-sm">請檢查連接設置和服務狀態</p>
                            </div>
                        </div>
                    </div>
                `;
                return;
            }

            container.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 class="text-blue-800 font-semibold mb-3">
                            <i class="fas fa-server mr-2"></i>Elasticsearch 信息
                        </h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">版本:</span>
                                <span class="font-medium text-gray-900">${info.version.number}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">集群名稱:</span>
                                <span class="font-medium text-gray-900">${info.cluster_name}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">節點名稱:</span>
                                <span class="font-medium text-gray-900">${info.name}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Lucene 版本:</span>
                                <span class="font-medium text-gray-900">${info.version.lucene_version}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <h4 class="text-green-800 font-semibold mb-3">
                            <i class="fas fa-check-circle mr-2"></i>連接狀態
                        </h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">狀態:</span>
                                <span class="font-medium text-green-600">
                                    <i class="fas fa-circle text-green-500 mr-1"></i>正常連接
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">URL:</span>
                                <span class="font-medium text-gray-900">${this.esUrl}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">刷新間隔:</span>
                                <span class="font-medium text-gray-900">${this.refreshInterval / 1000}秒</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        async saveSettings() {
            try {
                const esUrlInput = document.getElementById('es-url');
                const refreshIntervalInput = document.getElementById('refresh-interval');
                
                const newEsUrl = esUrlInput?.value?.trim();
                const newRefreshInterval = parseInt(refreshIntervalInput?.value) * 1000;
                
                if (!newEsUrl) {
                    this.showAlert('請輸入有效的 Elasticsearch URL', 'error');
                    return;
                }
                
                if (!newRefreshInterval || newRefreshInterval < 5000) {
                    this.showAlert('刷新間隔不能少於 5 秒', 'error');
                    return;
                }
                
                // 測試新的連接
                const testResponse = await fetch(`${newEsUrl}/`);
                if (!testResponse.ok) {
                    throw new Error('連接測試失敗');
                }
                
                // 保存設置
                this.esUrl = newEsUrl;
                this.refreshInterval = newRefreshInterval;
                
                // 保存到本地存儲
                localStorage.setItem('es-admin-url', this.esUrl);
                localStorage.setItem('es-admin-refresh-interval', this.refreshInterval.toString());
                
                this.showAlert('設置已成功保存', 'success');
                
                // 重新載入系統信息
                this.loadSystemInfo();
                
                // 重新載入集群信息
                this.loadClusterInfo();
                
            } catch (error) {
                console.error('保存設置失敗:', error);
                this.showAlert('保存設置失敗: ' + error.message, 'error');
            }
        }

        async testConnection() {
            try {
                const esUrlInput = document.getElementById('es-url');
                const testUrl = esUrlInput?.value?.trim() || this.esUrl;
                
                this.showAlert('正在測試連接...', 'info');
                
                const response = await fetch(`${testUrl}/`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const info = await response.json();
                
                // 測試集群健康狀況
                const healthResponse = await fetch(`${testUrl}/_cluster/health`);
                const health = await healthResponse.json();
                
                this.showAlert(`連接測試成功！集群狀態: ${health.status.toUpperCase()}`, 'success');
                
            } catch (error) {
                console.error('連接測試失敗:', error);
                this.showAlert('連接測試失敗: ' + error.message, 'error');
            }
        }

        loadSettingsFromStorage() {
            // 從本地存儲載入設置
            const savedUrl = localStorage.getItem('es-admin-url');
            const savedInterval = localStorage.getItem('es-admin-refresh-interval');
            
            if (savedUrl) {
                this.esUrl = savedUrl;
            }
            
            if (savedInterval) {
                this.refreshInterval = parseInt(savedInterval);
            }
        }

        showAlert(message, type = 'info') {
            const alertContainer = document.createElement('div');
            alertContainer.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transform transition-all duration-300 ease-in-out`;
            
            const styles = {
                success: 'bg-green-100 border border-green-400 text-green-700',
                error: 'bg-red-100 border border-red-400 text-red-700',
                warning: 'bg-yellow-100 border border-yellow-400 text-yellow-700',
                info: 'bg-blue-100 border border-blue-400 text-blue-700'
            };
            
            const icons = {
                success: 'fas fa-check-circle',
                error: 'fas fa-exclamation-circle',
                warning: 'fas fa-exclamation-triangle',
                info: 'fas fa-info-circle'
            };
            
            alertContainer.className += ` ${styles[type] || styles.info}`;
            
            alertContainer.innerHTML = `
                <div class="flex items-center">
                    <i class="${icons[type] || icons.info} text-lg mr-3"></i>
                    <span class="font-medium">${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            // 初始動畫
            alertContainer.style.transform = 'translateX(100%)';
            document.body.appendChild(alertContainer);
            
            // 顯示動畫
            setTimeout(() => {
                alertContainer.style.transform = 'translateX(0)';
            }, 10);
            
            // 自動隱藏
            setTimeout(() => {
                alertContainer.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (alertContainer.parentNode) {
                        alertContainer.remove();
                    }
                }, 300);
            }, 4000);
        }
    }

    // 全局函數
    window.toggleSidebar = function() {
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            sidebar.classList.toggle('hidden');
        }
    };

    window.showSection = function(sectionName) {
        if (window.admin) {
            window.admin.showSection(sectionName);
        }
    };

    window.refreshAll = function() {
        if (window.admin) {
            window.admin.refreshAll();
        }
    };

    window.executeSearch = function() {
        if (window.admin) {
            window.admin.executeSearch();
        }
    };

    window.saveSettings = function() {
        if (window.admin) {
            window.admin.saveSettings();
        }
    };

    window.testConnection = function() {
        if (window.admin) {
            window.admin.testConnection();
        }
    };

    window.refreshIndices = function() {
        if (window.admin) {
            window.admin.loadIndicesTable();
        }
    };

    window.refreshAll = function() {
        if (window.admin) {
            window.admin.refreshAll();
        }
    };

    // 將 ElasticsearchAdmin 類暴露到全局
    window.ElasticsearchAdmin = ElasticsearchAdmin;

    // 初始化
    document.addEventListener('DOMContentLoaded', () => {
        window.admin = new ElasticsearchAdmin();
    });
})(); 