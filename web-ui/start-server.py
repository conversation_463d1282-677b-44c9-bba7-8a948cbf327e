#!/usr/bin/env python3
"""
AI SEO 優化王 - Elasticsearch 管理界面服務器
運行在 http://localhost:8080/elasticsearch-admin.html
"""

import http.server
import socketserver
import os
import webbrowser
from urllib.parse import urlparse

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.path.dirname(os.path.realpath(__file__)), **kwargs)
    
    def end_headers(self):
        # 添加 CORS 標頭
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        super().end_headers()
    
    def do_GET(self):
        if self.path == '/':
            self.path = '/elasticsearch-admin.html'
        elif self.path == '/index.html':
            self.path = '/elasticsearch-admin.html'
        return super().do_GET()
    
    def log_message(self, format, *args):
        print(f"[{self.date_time_string()}] {format % args}")

def start_server():
    PORT = 8080
    HOST = 'localhost'
    
    try:
        with socketserver.TCPServer((HOST, PORT), CustomHTTPRequestHandler) as httpd:
            print("=" * 60)
            print("🚀 AI SEO 優化王 - Elasticsearch 管理界面")
            print("=" * 60)
            print(f"🌐 服務器啟動成功！")
            print(f"📍 地址: http://{HOST}:{PORT}")
            print(f"🔗 管理界面: http://{HOST}:{PORT}/elasticsearch-admin.html")
            print("=" * 60)
            print("📋 主要功能:")
            print("   ✅ 儀表板概覽 - 集群狀態監控")
            print("   ✅ 索引管理 - SEO 索引管理")
            print("   ✅ 數據搜索 - 高級搜索功能")
            print("   ✅ 分析統計 - 可視化圖表")
            print("   ✅ 系統設置 - 連接配置")
            print("=" * 60)
            print("📊 SEO 專用索引:")
            print("   📄 seo_content - SEO 內容數據")
            print("   📈 seo_analysis - SEO 分析結果")
            print("   🔑 seo_keywords - 關鍵詞數據")
            print("   👥 seo_competitors - 競爭對手數據")
            print("=" * 60)
            print("💡 使用提示:")
            print("   • 確保 Elasticsearch 運行在 localhost:9200")
            print("   • 使用 Ctrl+C 停止服務器")
            print("   • 支持實時數據刷新和搜索")
            print("=" * 60)
            
            # 自動打開瀏覽器
            try:
                webbrowser.open(f'http://{HOST}:{PORT}/elasticsearch-admin.html')
                print("🔗 瀏覽器已自動打開管理界面")
            except:
                print("⚠️ 無法自動打開瀏覽器，請手動訪問上述地址")
            
            print("\n⏳ 服務器運行中...")
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 端口 {PORT} 已被占用")
            print("💡 請檢查是否已有其他服務運行在此端口")
            print("💡 或者修改 PORT 變量使用其他端口")
        else:
            print(f"❌ 服務器啟動失敗: {e}")
    except KeyboardInterrupt:
        print("\n👋 服務器已停止")

if __name__ == "__main__":
    start_server() 