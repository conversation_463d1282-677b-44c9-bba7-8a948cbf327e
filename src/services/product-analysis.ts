/**
 * 產品分析服務
 * 處理產品分析相關的 API 調用和數據管理
 */

import { toast } from 'sonner';

// 產品分析相關類型定義
export interface ProductAnalysisRequest {
  brandId?: string;
  brandName?: string;
  analysisType: 'full' | 'responses' | 'citations' | 'topics' | 'content';
  timeRange?: '7d' | '30d' | '90d' | '1y';
  aiEngines?: string[];
  competitors?: string[];
  queries?: string[];
}

export interface ProductAnalysisResponse {
  success: boolean;
  data?: {
    analysisId: string;
    status: 'queued' | 'processing' | 'completed' | 'failed';
    estimatedTime: string;
    message: string;
    progress?: number;
  };
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

export interface AnalysisStatus {
  analysisId: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  progress: number;
  message: string;
  startedAt: string;
  estimatedCompletion?: string;
  results?: any;
}

class ProductAnalysisService {
  private baseUrl = '/api/product-analysis';

  /**
   * 觸發新的產品分析
   */
  async startAnalysis(request: ProductAnalysisRequest): Promise<ProductAnalysisResponse> {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error?.message || '啟動分析失敗');
      }

      return data;
    } catch (error) {
      console.error('產品分析服務錯誤:', error);
      throw error;
    }
  }

  /**
   * 獲取分析狀態
   */
  async getAnalysisStatus(analysisId: string): Promise<AnalysisStatus> {
    try {
      const response = await fetch(`${this.baseUrl}/status?analysisId=${analysisId}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error?.message || '獲取分析狀態失敗');
      }

      return data.data;
    } catch (error) {
      console.error('獲取分析狀態錯誤:', error);
      throw error;
    }
  }

  /**
   * 獲取儀表板數據
   */
  async getDashboardData(section?: string) {
    try {
      const url = section ? `${this.baseUrl}?section=${section}` : this.baseUrl;
      const response = await fetch(url);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error?.message || '獲取儀表板數據失敗');
      }

      return data.data;
    } catch (error) {
      console.error('獲取儀表板數據錯誤:', error);
      throw error;
    }
  }

  /**
   * 刷新數據
   */
  async refreshData(): Promise<any> {
    try {
      // 模擬刷新延遲
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 獲取最新的儀表板數據
      const data = await this.getDashboardData();

      // 確保返回的數據結構完整
      return {
        overview: data.overview || {
          totalBrands: 12,
          totalAnalyses: 248,
          averageVisibility: 78.5,
          activeReports: 6,
          lastUpdated: new Date().toISOString(),
        },
        visibilityTrends: data.visibilityTrends || [],
        aiEngineDistribution: data.aiEngineDistribution || [],
        topBrands: data.topBrands || [],
        recentAnalyses: data.recentAnalyses || [],
      };
    } catch (error) {
      console.error('刷新數據錯誤:', error);
      throw error;
    }
  }

  /**
   * 導出報告
   */
  async exportReport(format: 'pdf' | 'excel' | 'csv' = 'pdf', filters?: any): Promise<Blob> {
    try {
      const response = await fetch(`${this.baseUrl}/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          format,
          filters,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || '導出報告失敗');
      }

      return await response.blob();
    } catch (error) {
      console.error('導出報告錯誤:', error);
      throw error;
    }
  }

  /**
   * 快速分析 - 用於「開始分析」按鈕
   */
  async quickAnalysis(options?: {
    brandName?: string;
    analysisType?: 'full' | 'quick';
    includeCompetitors?: boolean;
  }): Promise<ProductAnalysisResponse> {
    const defaultRequest: ProductAnalysisRequest = {
      brandName: options?.brandName || '預設品牌',
      analysisType: options?.analysisType === 'quick' ? 'responses' : 'full',
      timeRange: '30d',
      aiEngines: ['ChatGPT', 'Gemini', 'Perplexity', 'Claude'],
      competitors: options?.includeCompetitors ? ['競爭對手A', '競爭對手B'] : [],
    };

    return await this.startAnalysis(defaultRequest);
  }

  /**
   * 輪詢分析狀態 - 改善後的版本
   */
  async pollAnalysisStatus(
    analysisId: string, 
    onProgress?: (status: AnalysisStatus) => void,
    options?: {
      maxAttempts?: number;
      interval?: number;
      maxDuration?: number;
    }
  ): Promise<AnalysisStatus> {
    const maxAttempts = options?.maxAttempts || 150; // 增加到150次
    const interval = options?.interval || 2000; // 2秒間隔
    const maxDuration = options?.maxDuration || 10 * 60 * 1000; // 10分鐘最大時長
    
    let attempts = 0;
    const startTime = Date.now();
    
    while (attempts < maxAttempts) {
      try {
        // 檢查是否超過最大時長
        if (Date.now() - startTime > maxDuration) {
          throw new Error(`分析時間超過 ${maxDuration / 60000} 分鐘限制`);
        }

        const status = await this.getAnalysisStatus(analysisId);
        
        if (onProgress) {
          onProgress(status);
        }

        // 如果完成或失敗，返回結果
        if (status.status === 'completed' || status.status === 'failed') {
          return status;
        }

        // 等待指定間隔後再次檢查
        await new Promise(resolve => setTimeout(resolve, interval));
        attempts++;
      } catch (error) {
        console.error(`輪詢分析狀態錯誤 (第 ${attempts + 1} 次):`, error);
        attempts++;
        
        // 如果達到最大嘗試次數，拋出錯誤
        if (attempts >= maxAttempts) {
          throw new Error(`分析狀態輪詢失敗：已嘗試 ${maxAttempts} 次，最後錯誤：${error instanceof Error ? error.message : '未知錯誤'}`);
        }
        
        // 錯誤時等待更長時間
        await new Promise(resolve => setTimeout(resolve, Math.min(5000, interval * 2)));
      }
    }

    throw new Error(`分析狀態輪詢超時：已嘗試 ${maxAttempts} 次，耗時 ${(Date.now() - startTime) / 1000} 秒`);
  }
}

// 創建服務實例
export const productAnalysisService = new ProductAnalysisService();

// 便捷的 Hook 函數
export function useProductAnalysis() {
  const startQuickAnalysis = async (options?: {
    brandName?: string;
    analysisType?: 'full' | 'quick';
    includeCompetitors?: boolean;
    onProgress?: (status: AnalysisStatus) => void;
  }) => {
    try {
      const response = await productAnalysisService.quickAnalysis({
        brandName: options?.brandName,
        analysisType: options?.analysisType,
        includeCompetitors: options?.includeCompetitors,
      });

      if (response.success && response.data) {
        // 如果有進度回調，開始輪詢狀態
        if (options?.onProgress) {
          // 使用更合理的輪詢參數
          productAnalysisService.pollAnalysisStatus(
            response.data.analysisId,
            options.onProgress,
            {
              maxAttempts: 150, // 5分鐘 (150 * 2秒)
              interval: 2000,   // 2秒間隔
              maxDuration: 10 * 60 * 1000 // 10分鐘最大時長
            }
          ).catch(error => {
            console.error('輪詢分析狀態失敗:', error);
            // 不要拋出錯誤，而是通過 onProgress 通知
            if (options.onProgress) {
              options.onProgress({
                analysisId: response.data.analysisId,
                status: 'failed',
                progress: 0,
                message: `輪詢超時：${error.message}`,
                startedAt: new Date().toISOString(),
              });
            }
          });
        }

        return response.data;
      } else {
        throw new Error(response.error?.message || '啟動分析失敗');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '啟動分析時發生未知錯誤';
      throw error;
    }
  };

  const refreshDashboard = async () => {
    try {
      const data = await productAnalysisService.refreshData();
      return data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '刷新數據失敗';
      throw error;
    }
  };

  const exportDashboardReport = async (format: 'pdf' | 'excel' | 'csv' = 'pdf') => {
    try {
      const blob = await productAnalysisService.exportReport(format);
      
      // 創建下載連結
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `product-analysis-report.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '導出報告失敗';
      throw error;
    }
  };

  return {
    startQuickAnalysis,
    refreshDashboard,
    exportDashboardReport,
    productAnalysisService,
  };
}
