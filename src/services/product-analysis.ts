/**
 * 產品分析服務
 * 處理產品分析相關的 API 調用和數據管理
 */

import { toast } from 'sonner';

// 產品分析相關類型定義
export interface ProductAnalysisRequest {
  brandId?: string;
  brandName?: string;
  analysisType: 'full' | 'responses' | 'citations' | 'topics' | 'content';
  timeRange?: '7d' | '30d' | '90d' | '1y';
  aiEngines?: string[];
  competitors?: string[];
  queries?: string[];
}

export interface ProductAnalysisResponse {
  success: boolean;
  data?: {
    analysisId: string;
    status: 'queued' | 'processing' | 'completed' | 'failed';
    estimatedTime: string;
    message: string;
    progress?: number;
  };
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

export interface AnalysisStatus {
  analysisId: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  progress: number;
  message: string;
  startedAt: string;
  estimatedCompletion?: string;
  results?: any;
}

class ProductAnalysisService {
  private baseUrl = '/api/product-analysis';

  /**
   * 觸發新的產品分析
   */
  async startAnalysis(request: ProductAnalysisRequest): Promise<ProductAnalysisResponse> {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error?.message || '啟動分析失敗');
      }

      return data;
    } catch (error) {
      console.error('產品分析服務錯誤:', error);
      throw error;
    }
  }

  /**
   * 獲取分析狀態
   */
  async getAnalysisStatus(analysisId: string): Promise<AnalysisStatus> {
    try {
      const response = await fetch(`${this.baseUrl}/status?analysisId=${analysisId}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error?.message || '獲取分析狀態失敗');
      }

      return data.data;
    } catch (error) {
      console.error('獲取分析狀態錯誤:', error);
      throw error;
    }
  }

  /**
   * 獲取儀表板數據
   */
  async getDashboardData(section?: string) {
    try {
      const url = section ? `${this.baseUrl}?section=${section}` : this.baseUrl;
      const response = await fetch(url);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error?.message || '獲取儀表板數據失敗');
      }

      return data.data;
    } catch (error) {
      console.error('獲取儀表板數據錯誤:', error);
      throw error;
    }
  }

  /**
   * 刷新數據
   */
  async refreshData(): Promise<any> {
    try {
      // 模擬刷新延遲
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 獲取最新的儀表板數據
      return await this.getDashboardData();
    } catch (error) {
      console.error('刷新數據錯誤:', error);
      throw error;
    }
  }

  /**
   * 導出報告
   */
  async exportReport(format: 'pdf' | 'excel' | 'csv' = 'pdf', filters?: any): Promise<Blob> {
    try {
      const response = await fetch(`${this.baseUrl}/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          format,
          filters,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || '導出報告失敗');
      }

      return await response.blob();
    } catch (error) {
      console.error('導出報告錯誤:', error);
      throw error;
    }
  }

  /**
   * 快速分析 - 用於「開始分析」按鈕
   */
  async quickAnalysis(options?: {
    brandName?: string;
    analysisType?: 'full' | 'quick';
    includeCompetitors?: boolean;
  }): Promise<ProductAnalysisResponse> {
    const defaultRequest: ProductAnalysisRequest = {
      brandName: options?.brandName || '預設品牌',
      analysisType: options?.analysisType === 'quick' ? 'responses' : 'full',
      timeRange: '30d',
      aiEngines: ['ChatGPT', 'Gemini', 'Perplexity', 'Claude'],
      competitors: options?.includeCompetitors ? ['競爭對手A', '競爭對手B'] : [],
    };

    return await this.startAnalysis(defaultRequest);
  }

  /**
   * 輪詢分析狀態
   */
  async pollAnalysisStatus(
    analysisId: string, 
    onProgress?: (status: AnalysisStatus) => void,
    maxAttempts: number = 30
  ): Promise<AnalysisStatus> {
    let attempts = 0;
    
    while (attempts < maxAttempts) {
      try {
        const status = await this.getAnalysisStatus(analysisId);
        
        if (onProgress) {
          onProgress(status);
        }

        if (status.status === 'completed' || status.status === 'failed') {
          return status;
        }

        // 等待 2 秒後再次檢查
        await new Promise(resolve => setTimeout(resolve, 2000));
        attempts++;
      } catch (error) {
        console.error('輪詢分析狀態錯誤:', error);
        attempts++;
        
        if (attempts >= maxAttempts) {
          throw error;
        }
        
        // 錯誤時等待更長時間
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }

    throw new Error('分析狀態輪詢超時');
  }
}

// 創建服務實例
export const productAnalysisService = new ProductAnalysisService();

// 便捷的 Hook 函數
export function useProductAnalysis() {
  const startQuickAnalysis = async (options?: {
    brandName?: string;
    analysisType?: 'full' | 'quick';
    includeCompetitors?: boolean;
    onProgress?: (status: AnalysisStatus) => void;
  }) => {
    try {
      toast.loading('正在啟動分析...', { id: 'analysis-start' });
      
      const response = await productAnalysisService.quickAnalysis({
        brandName: options?.brandName,
        analysisType: options?.analysisType,
        includeCompetitors: options?.includeCompetitors,
      });

      if (response.success && response.data) {
        toast.success('分析已開始！', { id: 'analysis-start' });
        
        // 如果有進度回調，開始輪詢狀態
        if (options?.onProgress) {
          productAnalysisService.pollAnalysisStatus(
            response.data.analysisId,
            options.onProgress
          ).catch(error => {
            console.error('輪詢分析狀態失敗:', error);
            toast.error('無法獲取分析進度');
          });
        }

        return response.data;
      } else {
        throw new Error(response.error?.message || '啟動分析失敗');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '啟動分析時發生未知錯誤';
      toast.error(errorMessage, { id: 'analysis-start' });
      throw error;
    }
  };

  const refreshDashboard = async () => {
    try {
      toast.loading('正在刷新數據...', { id: 'refresh-data' });
      
      const data = await productAnalysisService.refreshData();
      
      toast.success('數據已刷新！', { id: 'refresh-data' });
      return data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '刷新數據失敗';
      toast.error(errorMessage, { id: 'refresh-data' });
      throw error;
    }
  };

  const exportDashboardReport = async (format: 'pdf' | 'excel' | 'csv' = 'pdf') => {
    try {
      toast.loading('正在生成報告...', { id: 'export-report' });
      
      const blob = await productAnalysisService.exportReport(format);
      
      // 創建下載連結
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `product-analysis-report.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success('報告已下載！', { id: 'export-report' });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '導出報告失敗';
      toast.error(errorMessage, { id: 'export-report' });
      throw error;
    }
  };

  return {
    startQuickAnalysis,
    refreshDashboard,
    exportDashboardReport,
    productAnalysisService,
  };
}
