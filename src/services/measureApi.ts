/**
 * 測量功能 API 服務
 */

import { fastApiClient, ApiResponse } from '@/lib/api/client';
import { measureCache, CACHE_KEYS } from '@/lib/cache/measureCache';
import axios from 'axios';

// 測量功能數據類型
export interface MeasureStats {
  total_brands: number;
  active_campaigns: number;
  total_queries: number;
  avg_visibility: number;
  competitor_tracked: number;
  sentiment_score: number;
  recent_analysis: Array<{
    id: number;
    brand: string;
    type: string;
    status: string;
    score: number;
    timestamp: string;
    change: string;
  }>;
}

export interface VisibilityData {
  overall: {
    score: number;
    change: string;
    trend: string;
    queries: number;
    mentions: number;
  };
  by_engine: Array<{
    engine: string;
    score: number;
    queries: number;
    mentions: number;
  }>;
  top_queries: Array<{
    query: string;
    mentions: number;
    visibility: number;
  }>;
}

export interface CompetitiveData {
  competitors: Array<{
    id: number;
    name: string;
    visibility: number;
    change: string;
    rank: number;
    queries?: number;
    mentions?: number;
    shareOfVoice?: number;
  }>;
  opportunities: Array<{
    title: string;
    description: string;
    impact: string;
    effort: string;
  }>;
}

export interface JourneyData {
  stages: Array<{
    id: string;
    name: string;
    queries: number;
  }>;
  personas: Array<{
    id: number;
    name: string;
    size: string;
    visibility: number;
    conversion_rate: number;
  }>;
  funnel: Array<{
    stage: string;
    visitors: number;
    rate: number;
  }>;
}

export interface SentimentData {
  overall: {
    score: number;
    trend: string;
    change: string;
    positive: number;
    neutral: number;
    negative: number;
  };
  by_engine: Array<{
    engine: string;
    score: number;
    positive: number;
    negative: number;
  }>;
  hallucinations: Array<{
    id: number;
    content: string;
    severity: string;
    engine: string;
    frequency: number;
  }>;
  safety_issues: Array<{
    id: number;
    type: string;
    title: string;
    impact: string;
    occurrences: number;
  }>;
}

export interface MeasureSettings {
  analysis_frequency: string;
  auto_analysis: boolean;
  data_retention: number;
  email_notifications: boolean;
  slack_notifications: boolean;
  webhook_url: string;
  alert_threshold: number;
  enabled_engines: string[];
  query_limit: number;
  concurrent_requests: number;
  hallucination_detection: boolean;
  sentiment_monitoring: boolean;
  brand_safety_alerts: boolean;
  negative_threshold: number;
  rate_limit_per_hour: number;
  timeout_seconds: number;
}

/**
 * 測量功能 API 服務類
 */
export class MeasureApiService {
  private static readonly BASE_PATH = '/api/admin/measure';

  /**
   * 獲取測量功能統計
   */
  static async getStats(options: { forceRefresh?: boolean } = {}): Promise<ApiResponse<MeasureStats>> {
    return measureCache.getWithCache(
      CACHE_KEYS.STATS,
      () => fastApiClient.get<MeasureStats>(`${this.BASE_PATH}/stats`),
      undefined,
      {
        ttl: 2 * 60 * 1000, // 2 分鐘快取
        forceRefresh: options.forceRefresh,
        throttleInterval: 5000, // 5 秒節流
      }
    );
  }

  /**
   * 獲取品牌可見度數據
   */
  static async getVisibilityData(params?: {
    brand_id?: string;
    time_range?: string;
    engines?: string;
  }, options: { forceRefresh?: boolean } = {}): Promise<ApiResponse<VisibilityData>> {
    try {
      const queryParams = new URLSearchParams();
      if (params?.brand_id) queryParams.append('brand_id', params.brand_id);
      if (params?.time_range) queryParams.append('time_range', params.time_range);
      if (params?.engines) queryParams.append('engines', params.engines);

      const url = `${this.BASE_PATH}/visibility${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

      // 使用 axios 直接調用 NextJS API 路由
      const response = await axios.get(url);
      
      return {
        success: response.data.success,
        message: response.data.message,
        data: response.data.data
      };
    } catch (error: any) {
      console.error('獲取品牌可見度數據錯誤:', error);
      return {
        success: false,
        message: error.response?.data?.message || '載入數據失敗',
        error: {
          message: error.message,
          status: error.response?.status,
          code: error.code
        }
      };
    }
  }

  /**
   * 獲取競爭分析數據
   */
  static async getCompetitiveData(params?: {
    brand_id?: string;
    competitor_id?: string;
    metric?: string;
  }, options: { forceRefresh?: boolean } = {}): Promise<ApiResponse<CompetitiveData>> {
    const queryParams = new URLSearchParams();
    if (params?.brand_id) queryParams.append('brand_id', params.brand_id);
    if (params?.competitor_id) queryParams.append('competitor_id', params.competitor_id);
    if (params?.metric) queryParams.append('metric', params.metric);

    const url = `${this.BASE_PATH}/competitive${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

    return measureCache.getWithCache(
      CACHE_KEYS.COMPETITIVE,
      () => fastApiClient.get<CompetitiveData>(url),
      params,
      {
        ttl: 5 * 60 * 1000, // 5 分鐘快取
        forceRefresh: options.forceRefresh,
        throttleInterval: 5000, // 5 秒節流
      }
    );
  }

  /**
   * 獲取購買旅程數據
   */
  static async getJourneyData(params?: {
    persona?: string;
    stage?: string;
  }): Promise<ApiResponse<JourneyData>> {
    const queryParams = new URLSearchParams();
    if (params?.persona) queryParams.append('persona', params.persona);
    if (params?.stage) queryParams.append('stage', params.stage);

    const url = `${this.BASE_PATH}/journey${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return fastApiClient.get<JourneyData>(url);
  }

  /**
   * 獲取情感分析數據
   */
  static async getSentimentData(params?: {
    engine?: string;
    time_range?: string;
  }): Promise<ApiResponse<SentimentData>> {
    const queryParams = new URLSearchParams();
    if (params?.engine) queryParams.append('engine', params.engine);
    if (params?.time_range) queryParams.append('time_range', params.time_range);

    const url = `${this.BASE_PATH}/sentiment${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return fastApiClient.get<SentimentData>(url);
  }

  /**
   * 獲取測量功能設定
   */
  static async getSettings(): Promise<ApiResponse<MeasureSettings>> {
    return fastApiClient.get<MeasureSettings>(`${this.BASE_PATH}/settings`);
  }

  /**
   * 更新測量功能設定
   */
  static async updateSettings(settings: Partial<MeasureSettings>): Promise<ApiResponse<void>> {
    return fastApiClient.put<void>(`${this.BASE_PATH}/settings`, settings);
  }

  /**
   * 啟動測量分析
   */
  static async startAnalysis(params: {
    brand_id: string;
    analysis_type: string;
    engines?: string[];
    time_range?: string;
    settings?: any;
  }): Promise<ApiResponse<{ analysis_id: string }>> {
    return fastApiClient.post<{ analysis_id: string }>(`${this.BASE_PATH}/analyze`, params);
  }

  /**
   * 獲取分析結果
   */
  static async getAnalysisResult(analysisId: string): Promise<ApiResponse<any>> {
    return fastApiClient.get<any>(`${this.BASE_PATH}/analysis/${analysisId}`);
  }
}

export default MeasureApiService;
