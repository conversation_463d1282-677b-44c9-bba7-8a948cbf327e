/**
 * AI 摘要內容解析算法
 * 負責識別、解析和分析 Google AI 摘要內容
 */

import * as cheerio from 'cheerio';
import OpenAI from 'openai';
import type { 
  AISummary, 
  AISummarySource, 
  SummaryType,
  AISummaryDetectionConfig 
} from '@/types/google-ai-monitor';

// 檢測策略
interface DetectionStrategy {
  name: string;
  selectors: string[];
  keywords: string[];
  patterns: RegExp[];
  confidence: number;
  priority: number;
}

// 解析結果
interface ParseResult {
  isAISummary: boolean;
  confidence: number;
  summaryType: SummaryType;
  content: string;
  htmlContent: string;
  sources: ExtractedSource[];
  metadata: {
    wordCount: number;
    characterCount: number;
    language: string;
    detectionMethod: string;
  };
}

// 提取的來源
interface ExtractedSource {
  url: string;
  domain: string;
  title?: string;
  description?: string;
  citationText?: string;
  position: number;
  relevanceScore: number;
}

// 內容相似度結果
interface SimilarityResult {
  similarity: number;
  matchedSegments: string[];
  confidence: number;
}

export class AISummaryParser {
  private openai: OpenAI;
  private detectionConfig: AISummaryDetectionConfig;
  private detectionStrategies: DetectionStrategy[];

  constructor() {
    // 初始化 OpenAI 客戶端
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    // 載入檢測配置
    this.detectionConfig = this.loadDetectionConfig();
    
    // 初始化檢測策略
    this.detectionStrategies = this.initializeDetectionStrategies();
  }

  /**
   * 載入檢測配置
   */
  private loadDetectionConfig(): AISummaryDetectionConfig {
    return {
      selectors: [
        '[data-attrid="SGE"]',
        '.AI-overview',
        '.ai-overview',
        '[aria-label*="AI"]',
        '[data-testid*="ai"]',
        '.generative-ai',
        '.ai-generated',
        '.search-generative-experience',
      ],
      keywords: [
        'AI Overview',
        'AI 摘要',
        'AI 概述',
        'Generative AI',
        '生成式 AI',
        'AI-generated',
        '人工智慧摘要',
        'AI 搜尋',
        'Search Generative Experience',
        'SGE',
      ],
      patterns: [
        /AI\s*(Overview|摘要|概述)/i,
        /Generative\s*AI/i,
        /生成式\s*AI/i,
        /AI[-\s]*generated/i,
        /人工智慧\s*摘要/i,
        /Search\s*Generative\s*Experience/i,
        /SGE/i,
      ],
      confidenceThreshold: 0.8,
      minContentLength: 50,
    };
  }

  /**
   * 初始化檢測策略
   */
  private initializeDetectionStrategies(): DetectionStrategy[] {
    return [
      {
        name: 'Google AI Overview',
        selectors: [
          '[data-attrid="SGE"]',
          '.AI-overview',
          '.ai-overview',
        ],
        keywords: ['AI Overview', 'AI 摘要'],
        patterns: [/AI\s*Overview/i, /AI\s*摘要/i],
        confidence: 0.95,
        priority: 1,
      },
      {
        name: 'Generative AI Content',
        selectors: [
          '.generative-ai',
          '.ai-generated',
          '[data-testid*="generative"]',
        ],
        keywords: ['Generative AI', '生成式 AI'],
        patterns: [/Generative\s*AI/i, /生成式\s*AI/i],
        confidence: 0.9,
        priority: 2,
      },
      {
        name: 'Search Generative Experience',
        selectors: [
          '.search-generative-experience',
          '[data-sge]',
        ],
        keywords: ['Search Generative Experience', 'SGE'],
        patterns: [/Search\s*Generative\s*Experience/i, /SGE/i],
        confidence: 0.85,
        priority: 3,
      },
      {
        name: 'AI Powered Results',
        selectors: [
          '[aria-label*="AI"]',
          '[data-testid*="ai"]',
          '.ai-powered',
        ],
        keywords: ['AI-powered', 'AI 驅動', '人工智慧'],
        patterns: [/AI[-\s]*powered/i, /AI\s*驅動/i, /人工智慧/i],
        confidence: 0.75,
        priority: 4,
      },
    ];
  }

  /**
   * 解析 HTML 內容以檢測 AI 摘要
   */
  async parseHTML(html: string, targetDomain?: string): Promise<ParseResult | null> {
    const $ = cheerio.load(html);
    
    // 嘗試各種檢測策略
    for (const strategy of this.detectionStrategies) {
      const result = await this.applyDetectionStrategy($, strategy, targetDomain);
      
      if (result && result.confidence >= this.detectionConfig.confidenceThreshold) {
        return result;
      }
    }

    // 如果沒有找到明確的 AI 摘要，嘗試使用 AI 分析
    return this.analyzeWithAI(html, targetDomain);
  }

  /**
   * 應用檢測策略
   */
  private async applyDetectionStrategy(
    $: cheerio.CheerioAPI,
    strategy: DetectionStrategy,
    targetDomain?: string
  ): Promise<ParseResult | null> {
    let bestMatch: cheerio.Cheerio<cheerio.Element> | null = null;
    let bestConfidence = 0;

    // 嘗試 CSS 選擇器
    for (const selector of strategy.selectors) {
      const elements = $(selector);
      
      if (elements.length > 0) {
        const confidence = this.calculateSelectorConfidence(elements, strategy);
        
        if (confidence > bestConfidence) {
          bestMatch = elements.first();
          bestConfidence = confidence;
        }
      }
    }

    // 如果沒有找到選擇器匹配，嘗試關鍵字和模式匹配
    if (!bestMatch) {
      const textMatch = this.findTextMatches($, strategy);
      if (textMatch) {
        bestMatch = textMatch.element;
        bestConfidence = textMatch.confidence;
      }
    }

    if (bestMatch && bestConfidence >= strategy.confidence) {
      return this.extractContentFromElement($, bestMatch, strategy, targetDomain);
    }

    return null;
  }

  /**
   * 計算選擇器信心度
   */
  private calculateSelectorConfidence(
    elements: cheerio.Cheerio<cheerio.Element>,
    strategy: DetectionStrategy
  ): number {
    let confidence = strategy.confidence;
    
    // 根據元素數量調整信心度
    if (elements.length === 1) {
      confidence += 0.05; // 單一匹配更可靠
    } else if (elements.length > 3) {
      confidence -= 0.1; // 太多匹配可能不準確
    }

    return Math.min(confidence, 1.0);
  }

  /**
   * 查找文字匹配
   */
  private findTextMatches(
    $: cheerio.CheerioAPI,
    strategy: DetectionStrategy
  ): { element: cheerio.Cheerio<cheerio.Element>; confidence: number } | null {
    let bestMatch: cheerio.Cheerio<cheerio.Element> | null = null;
    let bestConfidence = 0;

    // 搜尋包含關鍵字的元素
    $('*').each((_, element) => {
      const $element = $(element);
      const text = $element.text().toLowerCase();
      
      let matchCount = 0;
      let totalKeywords = strategy.keywords.length + strategy.patterns.length;

      // 檢查關鍵字
      for (const keyword of strategy.keywords) {
        if (text.includes(keyword.toLowerCase())) {
          matchCount++;
        }
      }

      // 檢查模式
      for (const pattern of strategy.patterns) {
        if (pattern.test(text)) {
          matchCount++;
        }
      }

      const confidence = (matchCount / totalKeywords) * strategy.confidence;
      
      if (confidence > bestConfidence && confidence >= 0.5) {
        bestMatch = $element;
        bestConfidence = confidence;
      }
    });

    return bestMatch ? { element: bestMatch, confidence: bestConfidence } : null;
  }

  /**
   * 從元素提取內容
   */
  private async extractContentFromElement(
    $: cheerio.CheerioAPI,
    element: cheerio.Cheerio<cheerio.Element>,
    strategy: DetectionStrategy,
    targetDomain?: string
  ): Promise<ParseResult> {
    const content = element.text().trim();
    const htmlContent = element.html() || '';
    
    // 提取來源連結
    const sources = this.extractSources($, element);
    
    // 分析內容語言
    const language = this.detectLanguage(content);
    
    // 檢查用戶網域提及
    let userContentSimilarity = 0;
    if (targetDomain) {
      userContentSimilarity = await this.calculateUserContentSimilarity(
        content, 
        targetDomain
      );
    }

    return {
      isAISummary: true,
      confidence: strategy.confidence,
      summaryType: this.determineSummaryType(strategy.name),
      content,
      htmlContent,
      sources,
      metadata: {
        wordCount: content.split(/\s+/).length,
        characterCount: content.length,
        language,
        detectionMethod: strategy.name,
      },
    };
  }

  /**
   * 提取來源連結
   */
  private extractSources(
    $: cheerio.CheerioAPI,
    container: cheerio.Cheerio<cheerio.Element>
  ): ExtractedSource[] {
    const sources: ExtractedSource[] = [];
    
    // 查找容器內的連結
    container.find('a[href]').each((index, link) => {
      const $link = $(link);
      const url = $link.attr('href');
      
      if (url && this.isValidUrl(url)) {
        const domain = this.extractDomain(url);
        const title = $link.text().trim() || $link.attr('title');
        const description = $link.attr('aria-label') || $link.attr('data-description');
        
        sources.push({
          url,
          domain,
          title,
          description,
          citationText: $link.text().trim(),
          position: index + 1,
          relevanceScore: this.calculateRelevanceScore($link),
        });
      }
    });

    return sources;
  }

  /**
   * 使用 AI 分析內容
   */
  private async analyzeWithAI(html: string, targetDomain?: string): Promise<ParseResult | null> {
    try {
      const prompt = `
請分析以下 HTML 內容，判斷是否包含 Google AI 摘要（AI Overview）或其他 AI 生成的搜尋結果摘要。

HTML 內容:
${html.substring(0, 5000)} // 限制長度避免超過 token 限制

請回答以下問題：
1. 是否包含 AI 摘要？
2. 如果是，摘要的內容是什麼？
3. 信心度（0-1）？
4. 摘要類型（ai_overview, featured_snippet, knowledge_panel, other）？

請以 JSON 格式回應：
{
  "isAISummary": boolean,
  "confidence": number,
  "summaryType": string,
  "content": string,
  "reasoning": string
}
`;

      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o-mini',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.1,
        max_tokens: 1000,
      });

      const result = JSON.parse(response.choices[0]?.message?.content || '{}');
      
      if (result.isAISummary && result.confidence >= this.detectionConfig.confidenceThreshold) {
        return {
          isAISummary: true,
          confidence: result.confidence,
          summaryType: result.summaryType || 'other',
          content: result.content || '',
          htmlContent: '',
          sources: [],
          metadata: {
            wordCount: (result.content || '').split(/\s+/).length,
            characterCount: (result.content || '').length,
            language: this.detectLanguage(result.content || ''),
            detectionMethod: 'AI Analysis',
          },
        };
      }
    } catch (error) {
      console.error('AI 分析失敗:', error);
    }

    return null;
  }

  /**
   * 計算用戶內容相似度
   */
  private async calculateUserContentSimilarity(
    summaryContent: string,
    targetDomain: string
  ): Promise<number> {
    // 這裡可以實現更複雜的相似度計算
    // 例如使用 TF-IDF、餘弦相似度或 AI 模型
    
    // 簡單的關鍵字匹配實現
    const domainKeywords = targetDomain.split('.')[0].toLowerCase();
    const contentLower = summaryContent.toLowerCase();
    
    if (contentLower.includes(domainKeywords)) {
      return 0.8;
    }
    
    return 0.0;
  }

  /**
   * 檢測內容語言
   */
  private detectLanguage(content: string): string {
    // 簡單的語言檢測
    const chineseChars = content.match(/[\u4e00-\u9fff]/g);
    const totalChars = content.length;
    
    if (chineseChars && chineseChars.length / totalChars > 0.3) {
      return 'zh-TW';
    }
    
    return 'en';
  }

  /**
   * 確定摘要類型
   */
  private determineSummaryType(strategyName: string): SummaryType {
    if (strategyName.includes('AI Overview')) {
      return 'ai_overview';
    } else if (strategyName.includes('Featured')) {
      return 'featured_snippet';
    } else if (strategyName.includes('Knowledge')) {
      return 'knowledge_panel';
    }
    
    return 'other';
  }

  /**
   * 驗證 URL
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 提取網域
   */
  private extractDomain(url: string): string {
    try {
      return new URL(url).hostname;
    } catch {
      return '';
    }
  }

  /**
   * 計算相關性評分
   */
  private calculateRelevanceScore(link: cheerio.Cheerio<cheerio.Element>): number {
    // 基於連結位置、文字長度等因素計算相關性
    const text = link.text().trim();
    const hasTitle = !!link.attr('title');
    const hasDescription = !!link.attr('aria-label');
    
    let score = 0.5; // 基礎分數
    
    if (text.length > 10) score += 0.2;
    if (hasTitle) score += 0.15;
    if (hasDescription) score += 0.15;
    
    return Math.min(score, 1.0);
  }

  /**
   * 更新檢測配置
   */
  updateConfig(newConfig: Partial<AISummaryDetectionConfig>): void {
    this.detectionConfig = { ...this.detectionConfig, ...newConfig };
  }

  /**
   * 獲取檢測統計
   */
  getDetectionStats(): {
    strategiesCount: number;
    selectorsCount: number;
    keywordsCount: number;
    patternsCount: number;
  } {
    const totalSelectors = this.detectionStrategies.reduce(
      (sum, strategy) => sum + strategy.selectors.length, 0
    );
    const totalKeywords = this.detectionStrategies.reduce(
      (sum, strategy) => sum + strategy.keywords.length, 0
    );
    const totalPatterns = this.detectionStrategies.reduce(
      (sum, strategy) => sum + strategy.patterns.length, 0
    );

    return {
      strategiesCount: this.detectionStrategies.length,
      selectorsCount: totalSelectors,
      keywordsCount: totalKeywords,
      patternsCount: totalPatterns,
    };
  }
}

// 導出單例實例
export const aiSummaryParser = new AISummaryParser();
