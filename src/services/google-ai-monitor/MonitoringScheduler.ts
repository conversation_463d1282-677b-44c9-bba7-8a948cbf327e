/**
 * 監測任務排程系統
 * 負責管理和執行定期監測任務
 */

import cron from 'node-cron';
import { EventEmitter } from 'events';
import type { 
  MonitoringTask, 
  MonitoringKeyword, 
  TaskType, 
  TaskStatus, 
  Priority,
  MonitoringFrequency,
  CreateTaskRequest,
  MonitoringEvent
} from '@/types/google-ai-monitor';
import { googleSearchService } from './GoogleSearchService';
// import { aiSummaryParser } from './AISummaryParser'; // 暫時禁用以避免編譯錯誤

// 任務執行結果
interface TaskExecutionResult {
  success: boolean;
  data?: any;
  error?: string;
  executionTime: number;
}

// 排程配置
interface ScheduleConfig {
  enabled: boolean;
  maxConcurrentTasks: number;
  retryDelay: number; // 重試延遲（毫秒）
  taskTimeout: number; // 任務超時（毫秒）
}

// 任務隊列項目
interface QueuedTask {
  task: MonitoringTask;
  keyword?: MonitoringKeyword;
  priority: Priority;
  scheduledAt: Date;
  attempts: number;
}

export class MonitoringScheduler extends EventEmitter {
  private tasks: Map<string, MonitoringTask> = new Map();
  private taskQueue: QueuedTask[] = [];
  private runningTasks: Set<string> = new Set();
  private cronJobs: Map<string, cron.ScheduledTask> = new Map();
  private config: ScheduleConfig;
  private isRunning: boolean = false;

  constructor(config: Partial<ScheduleConfig> = {}) {
    super();
    
    this.config = {
      enabled: true,
      maxConcurrentTasks: 3,
      retryDelay: 5000, // 5 秒
      taskTimeout: 60000, // 60 秒
      ...config,
    };

    // 啟動任務處理器
    this.startTaskProcessor();
    
    // 設置定期清理
    this.setupPeriodicCleanup();
  }

  /**
   * 啟動排程器
   */
  start(): void {
    if (this.isRunning) {
      console.log('監測排程器已在運行中');
      return;
    }

    this.isRunning = true;
    console.log('監測排程器已啟動');
    
    // 載入現有的監測關鍵字並設置排程
    this.loadAndScheduleKeywords();
    
    this.emit('scheduler_started');
  }

  /**
   * 停止排程器
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    
    // 停止所有 cron 任務
    this.cronJobs.forEach((job) => {
      job.stop();
    });
    this.cronJobs.clear();
    
    // 清空任務隊列
    this.taskQueue = [];
    
    console.log('監測排程器已停止');
    this.emit('scheduler_stopped');
  }

  /**
   * 添加監測關鍵字排程
   */
  scheduleKeyword(keyword: MonitoringKeyword): void {
    const cronExpression = this.getCronExpression(keyword.monitoringFrequency);
    const jobId = `keyword_${keyword.id}`;

    // 如果已存在，先停止舊的任務
    if (this.cronJobs.has(jobId)) {
      this.cronJobs.get(jobId)?.stop();
    }

    // 創建新的 cron 任務
    const job = cron.schedule(cronExpression, () => {
      this.scheduleSearchTask(keyword);
    }, {
      scheduled: false,
      timezone: 'Asia/Taipei',
    });

    this.cronJobs.set(jobId, job);
    
    if (this.isRunning && keyword.isActive) {
      job.start();
      console.log(`已為關鍵字 "${keyword.keyword}" 設置 ${keyword.monitoringFrequency} 排程`);
    }
  }

  /**
   * 移除關鍵字排程
   */
  unscheduleKeyword(keywordId: string): void {
    const jobId = `keyword_${keywordId}`;
    const job = this.cronJobs.get(jobId);
    
    if (job) {
      job.stop();
      this.cronJobs.delete(jobId);
      console.log(`已移除關鍵字 ${keywordId} 的排程`);
    }
  }

  /**
   * 立即執行關鍵字監測
   */
  async executeKeywordMonitoring(keyword: MonitoringKeyword): Promise<TaskExecutionResult> {
    const task = this.createSearchTask(keyword, 1); // 高優先級
    return this.executeTask(task, keyword);
  }

  /**
   * 批量執行監測
   */
  async executeBatchMonitoring(keywords: MonitoringKeyword[]): Promise<TaskExecutionResult[]> {
    const results: TaskExecutionResult[] = [];
    
    for (const keyword of keywords) {
      try {
        const result = await this.executeKeywordMonitoring(keyword);
        results.push(result);
        
        // 在批量執行之間添加延遲
        await this.delay(2000);
      } catch (error) {
        results.push({
          success: false,
          error: error instanceof Error ? error.message : '未知錯誤',
          executionTime: 0,
        });
      }
    }
    
    return results;
  }

  /**
   * 創建自定義任務
   */
  createTask(request: CreateTaskRequest): MonitoringTask {
    const task: MonitoringTask = {
      id: this.generateTaskId(),
      keywordId: request.keywordId,
      taskType: request.taskType,
      status: 'pending',
      priority: request.priority || 2,
      scheduledAt: request.scheduledAt || new Date(),
      retryCount: 0,
      maxRetries: 3,
      resultData: {},
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.tasks.set(task.id, task);
    return task;
  }

  /**
   * 獲取任務狀態
   */
  getTaskStatus(taskId: string): MonitoringTask | undefined {
    return this.tasks.get(taskId);
  }

  /**
   * 獲取排程統計
   */
  getSchedulerStats(): {
    isRunning: boolean;
    activeJobs: number;
    queuedTasks: number;
    runningTasks: number;
    totalTasks: number;
  } {
    return {
      isRunning: this.isRunning,
      activeJobs: this.cronJobs.size,
      queuedTasks: this.taskQueue.length,
      runningTasks: this.runningTasks.size,
      totalTasks: this.tasks.size,
    };
  }

  /**
   * 載入並排程關鍵字
   */
  private async loadAndScheduleKeywords(): Promise<void> {
    try {
      // 這裡應該從資料庫載入活躍的監測關鍵字
      // const keywords = await keywordService.getActiveKeywords();
      // keywords.forEach(keyword => this.scheduleKeyword(keyword));
      
      console.log('已載入並設置關鍵字排程');
    } catch (error) {
      console.error('載入關鍵字排程失敗:', error);
    }
  }

  /**
   * 獲取 cron 表達式
   */
  private getCronExpression(frequency: MonitoringFrequency): string {
    switch (frequency) {
      case 'daily':
        return '0 9 * * *'; // 每天上午 9 點
      case 'weekly':
        return '0 9 * * 1'; // 每週一上午 9 點
      case 'monthly':
        return '0 9 1 * *'; // 每月 1 號上午 9 點
      default:
        return '0 9 * * *'; // 預設每天
    }
  }

  /**
   * 排程搜尋任務
   */
  private scheduleSearchTask(keyword: MonitoringKeyword): void {
    const task = this.createSearchTask(keyword, 2); // 中等優先級
    this.addToQueue(task, keyword);
  }

  /**
   * 創建搜尋任務
   */
  private createSearchTask(keyword: MonitoringKeyword, priority: Priority): MonitoringTask {
    return {
      id: this.generateTaskId(),
      keywordId: keyword.id,
      taskType: 'search',
      status: 'pending',
      priority,
      scheduledAt: new Date(),
      retryCount: 0,
      maxRetries: 3,
      resultData: {},
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  /**
   * 添加任務到隊列
   */
  private addToQueue(task: MonitoringTask, keyword?: MonitoringKeyword): void {
    const queuedTask: QueuedTask = {
      task,
      keyword,
      priority: task.priority,
      scheduledAt: task.scheduledAt,
      attempts: 0,
    };

    // 按優先級插入隊列
    const insertIndex = this.taskQueue.findIndex(
      (item) => item.priority > queuedTask.priority
    );
    
    if (insertIndex === -1) {
      this.taskQueue.push(queuedTask);
    } else {
      this.taskQueue.splice(insertIndex, 0, queuedTask);
    }

    this.emit('task_queued', { taskId: task.id, priority: task.priority });
  }

  /**
   * 啟動任務處理器
   */
  private startTaskProcessor(): void {
    setInterval(() => {
      this.processTaskQueue();
    }, 1000); // 每秒檢查一次
  }

  /**
   * 處理任務隊列
   */
  private async processTaskQueue(): Promise<void> {
    if (!this.isRunning || this.taskQueue.length === 0) {
      return;
    }

    // 檢查是否達到並發限制
    if (this.runningTasks.size >= this.config.maxConcurrentTasks) {
      return;
    }

    // 取出下一個任務
    const queuedTask = this.taskQueue.shift();
    if (!queuedTask) {
      return;
    }

    // 檢查任務是否應該執行
    if (queuedTask.scheduledAt > new Date()) {
      // 重新加入隊列
      this.taskQueue.unshift(queuedTask);
      return;
    }

    // 執行任務
    this.executeQueuedTask(queuedTask);
  }

  /**
   * 執行隊列中的任務
   */
  private async executeQueuedTask(queuedTask: QueuedTask): Promise<void> {
    const { task, keyword } = queuedTask;
    
    this.runningTasks.add(task.id);
    task.status = 'running';
    task.startedAt = new Date();
    
    this.emit('task_started', { taskId: task.id, timestamp: new Date() });

    try {
      const result = await this.executeTask(task, keyword);
      
      if (result.success) {
        task.status = 'completed';
        task.resultData = result.data || {};
      } else {
        throw new Error(result.error || '任務執行失敗');
      }
    } catch (error) {
      await this.handleTaskError(task, error, queuedTask);
    } finally {
      task.completedAt = new Date();
      task.executionTimeMs = task.completedAt.getTime() - (task.startedAt?.getTime() || 0);
      task.updatedAt = new Date();
      
      this.runningTasks.delete(task.id);
      this.tasks.set(task.id, task);
      
      this.emit('task_completed', { 
        taskId: task.id, 
        duration: task.executionTimeMs,
        timestamp: new Date() 
      });
    }
  }

  /**
   * 執行任務
   */
  private async executeTask(
    task: MonitoringTask, 
    keyword?: MonitoringKeyword
  ): Promise<TaskExecutionResult> {
    const startTime = Date.now();

    try {
      switch (task.taskType) {
        case 'search':
          return await this.executeSearchTask(task, keyword);
        case 'analysis':
          return await this.executeAnalysisTask(task);
        case 'report':
          return await this.executeReportTask(task);
        case 'cleanup':
          return await this.executeCleanupTask(task);
        default:
          throw new Error(`不支援的任務類型: ${task.taskType}`);
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知錯誤',
        executionTime: Date.now() - startTime,
      };
    }
  }

  /**
   * 執行搜尋任務
   */
  private async executeSearchTask(
    task: MonitoringTask,
    keyword?: MonitoringKeyword
  ): Promise<TaskExecutionResult> {
    if (!keyword) {
      throw new Error('搜尋任務需要關鍵字資訊');
    }

    const startTime = Date.now();
    
    // 執行 Google 搜尋
    const searchResult = await googleSearchService.search(keyword.keyword);
    
    // 如果有 AI 摘要，進行解析 (暫時禁用以避免編譯錯誤)
    let aiSummaryData = null;
    /*
    if (searchResult.hasAiSummary && searchResult.aiSummaryContent) {
      const parseResult = await aiSummaryParser.parseHTML(
        searchResult.aiSummaryContent,
        keyword.targetDomain
      );
      aiSummaryData = parseResult;
    }
    */

    return {
      success: true,
      data: {
        searchResult,
        aiSummaryData,
        keyword: keyword.keyword,
        timestamp: new Date(),
      },
      executionTime: Date.now() - startTime,
    };
  }

  /**
   * 執行分析任務
   */
  private async executeAnalysisTask(task: MonitoringTask): Promise<TaskExecutionResult> {
    // 實現分析邏輯
    return {
      success: true,
      data: { message: '分析任務完成' },
      executionTime: 1000,
    };
  }

  /**
   * 執行報告任務
   */
  private async executeReportTask(task: MonitoringTask): Promise<TaskExecutionResult> {
    // 實現報告生成邏輯
    return {
      success: true,
      data: { message: '報告任務完成' },
      executionTime: 2000,
    };
  }

  /**
   * 執行清理任務
   */
  private async executeCleanupTask(task: MonitoringTask): Promise<TaskExecutionResult> {
    // 實現數據清理邏輯
    return {
      success: true,
      data: { message: '清理任務完成' },
      executionTime: 500,
    };
  }

  /**
   * 處理任務錯誤
   */
  private async handleTaskError(
    task: MonitoringTask,
    error: any,
    queuedTask: QueuedTask
  ): Promise<void> {
    task.retryCount++;
    task.errorMessage = error instanceof Error ? error.message : '未知錯誤';
    
    this.emit('task_failed', {
      taskId: task.id,
      error: {
        code: 'EXECUTION_ERROR',
        message: task.errorMessage,
        details: error,
        timestamp: new Date(),
        retryable: task.retryCount < task.maxRetries,
      },
      timestamp: new Date(),
    });

    // 如果還有重試次數，重新加入隊列
    if (task.retryCount < task.maxRetries) {
      task.status = 'pending';
      queuedTask.attempts++;
      queuedTask.scheduledAt = new Date(Date.now() + this.config.retryDelay);
      this.addToQueue(task, queuedTask.keyword);
    } else {
      task.status = 'failed';
    }
  }

  /**
   * 設置定期清理
   */
  private setupPeriodicCleanup(): void {
    // 每天凌晨 2 點執行清理
    cron.schedule('0 2 * * *', () => {
      this.cleanupOldTasks();
    }, {
      timezone: 'Asia/Taipei',
    });
  }

  /**
   * 清理舊任務
   */
  private cleanupOldTasks(): void {
    const cutoffDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7 天前
    
    for (const [taskId, task] of this.tasks.entries()) {
      if (task.createdAt < cutoffDate && task.status !== 'running') {
        this.tasks.delete(taskId);
      }
    }
    
    console.log('已清理舊任務記錄');
  }

  /**
   * 生成任務 ID
   */
  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 延遲函數
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ScheduleConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

// 導出單例實例
export const monitoringScheduler = new MonitoringScheduler();
