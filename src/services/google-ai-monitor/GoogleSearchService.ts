/**
 * Google Custom Search API 整合服務
 * 負責執行 Google 搜尋並提取結果數據
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { RateLimiter } from 'limiter';
import type { 
  GoogleSearchConfig, 
  SearchResult, 
  MonitoringError 
} from '@/types/google-ai-monitor';

// Google Custom Search API 響應類型
interface GoogleSearchResponse {
  kind: string;
  url: {
    type: string;
    template: string;
  };
  queries: {
    request: Array<{
      title: string;
      totalResults: string;
      searchTerms: string;
      count: number;
      startIndex: number;
      inputEncoding: string;
      outputEncoding: string;
      safe: string;
      cx: string;
    }>;
    nextPage?: Array<{
      title: string;
      totalResults: string;
      searchTerms: string;
      count: number;
      startIndex: number;
      inputEncoding: string;
      outputEncoding: string;
      safe: string;
      cx: string;
    }>;
  };
  context: {
    title: string;
  };
  searchInformation: {
    searchTime: number;
    formattedSearchTime: string;
    totalResults: string;
    formattedTotalResults: string;
  };
  items?: Array<{
    kind: string;
    title: string;
    htmlTitle: string;
    link: string;
    displayLink: string;
    snippet: string;
    htmlSnippet: string;
    cacheId?: string;
    formattedUrl: string;
    htmlFormattedUrl: string;
    pagemap?: {
      [key: string]: any;
    };
  }>;
}

// 搜尋選項
interface SearchOptions {
  location?: string;
  language?: string;
  dateRestrict?: string; // 'd1' = 過去1天, 'w1' = 過去1週, 'm1' = 過去1月
  safe?: 'active' | 'off';
  num?: number; // 結果數量 (1-10)
  start?: number; // 起始位置
}

// 搜尋結果項目
interface SearchResultItem {
  title: string;
  url: string;
  snippet: string;
  displayUrl: string;
  position: number;
  isAd?: boolean;
}

// 解析後的搜尋結果
interface ParsedSearchResult {
  query: string;
  totalResults: number;
  searchTime: number;
  items: SearchResultItem[];
  hasAiSummary: boolean;
  aiSummaryPosition?: number;
  aiSummaryContent?: string;
  metadata: {
    location: string;
    language: string;
    timestamp: Date;
    apiQuotaUsed: number;
  };
}

export class GoogleSearchService {
  private client: AxiosInstance;
  private rateLimiter: RateLimiter;
  private config: GoogleSearchConfig;
  private quotaUsed: number = 0;
  private lastResetDate: Date = new Date();

  constructor(config: GoogleSearchConfig) {
    this.config = config;
    
    // 初始化 HTTP 客戶端
    this.client = axios.create({
      baseURL: 'https://www.googleapis.com/customsearch/v1',
      timeout: 30000,
      headers: {
        'User-Agent': 'AI-SEO-King-Monitor/1.0',
      },
    });

    // 初始化頻率限制器 (每秒最多 1 次請求)
    this.rateLimiter = new RateLimiter({
      tokensPerInterval: this.config.rateLimit.requestsPerSecond,
      interval: 'second',
    });

    // 設置請求攔截器
    this.setupInterceptors();
  }

  /**
   * 設置 HTTP 攔截器
   */
  private setupInterceptors(): void {
    // 請求攔截器 - 添加 API 金鑰和頻率限制
    this.client.interceptors.request.use(
      async (config) => {
        // 等待頻率限制器
        await this.rateLimiter.removeTokens(1);
        
        // 檢查每日配額
        this.checkDailyQuota();
        
        // 添加 API 金鑰
        config.params = {
          ...config.params,
          key: this.config.apiKey,
          cx: this.config.engineId,
        };

        return config;
      },
      (error) => Promise.reject(error)
    );

    // 響應攔截器 - 處理錯誤和配額追蹤
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        // 更新配額使用量
        this.updateQuotaUsage();
        return response;
      },
      (error) => {
        // 處理 API 錯誤
        return Promise.reject(this.handleApiError(error));
      }
    );
  }

  /**
   * 檢查每日配額
   */
  private checkDailyQuota(): void {
    const today = new Date();
    const isNewDay = today.toDateString() !== this.lastResetDate.toDateString();
    
    if (isNewDay) {
      this.quotaUsed = 0;
      this.lastResetDate = today;
    }

    if (this.quotaUsed >= this.config.rateLimit.dailyQuota) {
      throw new Error(`已達到每日 API 配額限制 (${this.config.rateLimit.dailyQuota})`);
    }
  }

  /**
   * 更新配額使用量
   */
  private updateQuotaUsage(): void {
    this.quotaUsed += 1;
  }

  /**
   * 處理 API 錯誤
   */
  private handleApiError(error: any): MonitoringError {
    if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 400:
          return {
            code: 'INVALID_REQUEST',
            message: '無效的搜尋請求',
            details: data,
            timestamp: new Date(),
            retryable: false,
          };
        case 403:
          return {
            code: 'QUOTA_EXCEEDED',
            message: 'API 配額已用完',
            details: data,
            timestamp: new Date(),
            retryable: false,
          };
        case 429:
          return {
            code: 'RATE_LIMIT_EXCEEDED',
            message: '請求頻率過高',
            details: data,
            timestamp: new Date(),
            retryable: true,
          };
        case 500:
          return {
            code: 'SERVER_ERROR',
            message: 'Google API 服務器錯誤',
            details: data,
            timestamp: new Date(),
            retryable: true,
          };
        default:
          return {
            code: 'UNKNOWN_ERROR',
            message: `未知錯誤 (${status})`,
            details: data,
            timestamp: new Date(),
            retryable: true,
          };
      }
    }

    return {
      code: 'NETWORK_ERROR',
      message: '網路連接錯誤',
      details: error.message,
      timestamp: new Date(),
      retryable: true,
    };
  }

  /**
   * 執行 Google 搜尋
   */
  async search(
    query: string, 
    options: SearchOptions = {}
  ): Promise<ParsedSearchResult> {
    try {
      const searchParams = {
        q: query,
        gl: options.location || this.config.location || 'tw', // 台灣
        hl: options.language || this.config.language || 'zh-TW', // 繁體中文
        safe: options.safe || 'off',
        num: options.num || 10,
        start: options.start || 1,
        ...(options.dateRestrict && { dateRestrict: options.dateRestrict }),
      };

      console.log(`執行 Google 搜尋: "${query}"`);
      
      const response = await this.client.get<GoogleSearchResponse>('', {
        params: searchParams,
      });

      return this.parseSearchResponse(query, response.data, options);
    } catch (error) {
      console.error('Google 搜尋失敗:', error);
      throw error;
    }
  }

  /**
   * 解析 Google 搜尋響應
   */
  private parseSearchResponse(
    query: string,
    response: GoogleSearchResponse,
    options: SearchOptions
  ): ParsedSearchResult {
    const items: SearchResultItem[] = [];
    let hasAiSummary = false;
    let aiSummaryPosition: number | undefined;
    let aiSummaryContent: string | undefined;

    // 解析搜尋結果項目
    if (response.items) {
      response.items.forEach((item, index) => {
        // 檢查是否為 AI 摘要
        const isAiSummary = this.detectAISummary(item);
        
        if (isAiSummary) {
          hasAiSummary = true;
          aiSummaryPosition = index + 1;
          aiSummaryContent = item.snippet;
        }

        items.push({
          title: item.title,
          url: item.link,
          snippet: item.snippet,
          displayUrl: item.displayLink,
          position: index + 1,
          isAd: false, // Google CSE 通常不包含廣告
        });
      });
    }

    return {
      query,
      totalResults: parseInt(response.searchInformation.totalResults) || 0,
      searchTime: response.searchInformation.searchTime,
      items,
      hasAiSummary,
      aiSummaryPosition,
      aiSummaryContent,
      metadata: {
        location: options.location || this.config.location || 'tw',
        language: options.language || this.config.language || 'zh-TW',
        timestamp: new Date(),
        apiQuotaUsed: this.quotaUsed,
      },
    };
  }

  /**
   * 檢測是否為 AI 摘要
   * 注意: Google Custom Search API 可能不會直接返回 AI Overview
   * 這個方法主要用於檢測其他類型的 AI 生成內容
   */
  private detectAISummary(item: any): boolean {
    const aiIndicators = [
      'AI Overview',
      'AI 摘要',
      'AI 概述',
      'Generative AI',
      '生成式 AI',
      'AI-generated',
    ];

    // 檢查標題和摘要中的 AI 指標
    const titleAndSnippet = `${item.title} ${item.snippet}`.toLowerCase();
    
    return aiIndicators.some(indicator => 
      titleAndSnippet.includes(indicator.toLowerCase())
    );
  }

  /**
   * 搜尋特定網域的結果
   */
  async searchSite(
    query: string,
    domain: string,
    options: SearchOptions = {}
  ): Promise<ParsedSearchResult> {
    const siteQuery = `site:${domain} ${query}`;
    return this.search(siteQuery, options);
  }

  /**
   * 批量搜尋多個關鍵字
   */
  async batchSearch(
    queries: string[],
    options: SearchOptions = {}
  ): Promise<ParsedSearchResult[]> {
    const results: ParsedSearchResult[] = [];
    
    for (const query of queries) {
      try {
        const result = await this.search(query, options);
        results.push(result);
        
        // 在批量搜尋之間添加延遲以避免頻率限制
        await this.delay(1000);
      } catch (error) {
        console.error(`搜尋 "${query}" 失敗:`, error);
        // 繼續處理其他查詢
      }
    }
    
    return results;
  }

  /**
   * 獲取配額使用情況
   */
  getQuotaUsage(): { used: number; remaining: number; limit: number } {
    return {
      used: this.quotaUsed,
      remaining: this.config.rateLimit.dailyQuota - this.quotaUsed,
      limit: this.config.rateLimit.dailyQuota,
    };
  }

  /**
   * 測試 API 連接
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.search('test', { num: 1 });
      return true;
    } catch (error) {
      console.error('API 連接測試失敗:', error);
      return false;
    }
  }

  /**
   * 延遲函數
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<GoogleSearchConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // 更新頻率限制器
    if (newConfig.rateLimit) {
      this.rateLimiter = new RateLimiter({
        tokensPerInterval: this.config.rateLimit.requestsPerSecond,
        interval: 'second',
      });
    }
  }

  /**
   * 重置配額計數器
   */
  resetQuota(): void {
    this.quotaUsed = 0;
    this.lastResetDate = new Date();
  }
}

// 創建服務實例的工廠函數
export function createGoogleSearchService(): GoogleSearchService {
  const config: GoogleSearchConfig = {
    apiKey: process.env.GOOGLE_CSE_API_KEY || '',
    engineId: process.env.GOOGLE_CSE_ENGINE_ID || '',
    location: process.env.GOOGLE_SEARCH_LOCATION || 'tw',
    language: process.env.GOOGLE_SEARCH_LANGUAGE || 'zh-TW',
    rateLimit: {
      requestsPerSecond: parseInt(process.env.GOOGLE_SEARCH_RATE_LIMIT || '1'),
      dailyQuota: parseInt(process.env.GOOGLE_SEARCH_DAILY_QUOTA || '100'),
    },
  };

  if (!config.apiKey || !config.engineId) {
    throw new Error('Google Custom Search API 配置不完整');
  }

  return new GoogleSearchService(config);
}

// 導出單例實例
export const googleSearchService = createGoogleSearchService();
