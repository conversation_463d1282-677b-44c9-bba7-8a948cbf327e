/**
 * 台灣 Google AI 摘要監測器 - TypeScript 類型定義
 */

// 基礎類型
export type MonitoringFrequency = 'daily' | 'weekly' | 'monthly';
export type TaskStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
export type TaskType = 'search' | 'analysis' | 'report' | 'cleanup';
export type SummaryType = 'ai_overview' | 'featured_snippet' | 'knowledge_panel' | 'other';
export type PeriodType = 'daily' | 'weekly' | 'monthly';
export type Priority = 1 | 2 | 3; // 1=高, 2=中, 3=低

// 監測關鍵字
export interface MonitoringKeyword {
  id: string;
  userId: string;
  keyword: string;
  category?: string;
  targetDomain?: string;
  isActive: boolean;
  monitoringFrequency: MonitoringFrequency;
  lastMonitoredAt?: Date;
  nextMonitoringAt?: Date;
  monitoringConfig: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

// 創建監測關鍵字請求
export interface CreateKeywordRequest {
  keyword: string;
  category?: string;
  targetDomain?: string;
  monitoringFrequency?: MonitoringFrequency;
  monitoringConfig?: Record<string, any>;
}

// 更新監測關鍵字請求
export interface UpdateKeywordRequest {
  keyword?: string;
  category?: string;
  targetDomain?: string;
  isActive?: boolean;
  monitoringFrequency?: MonitoringFrequency;
  monitoringConfig?: Record<string, any>;
}

// 搜尋結果
export interface SearchResult {
  id: string;
  keywordId: string;
  searchQuery: string;
  searchDate: Date;
  searchLocation: string;
  searchLanguage: string;
  totalResults?: number;
  hasAiSummary: boolean;
  aiSummaryPosition?: number;
  organicResultsCount: number;
  adsCount: number;
  searchMetadata: Record<string, any>;
  rawHtml?: string;
  createdAt: Date;
}

// AI 摘要
export interface AISummary {
  id: string;
  searchResultId: string;
  summaryContent: string;
  summaryHtml?: string;
  summarySources: AISummarySource[];
  userDomainMentioned: boolean;
  userContentQuoted?: string;
  userContentSimilarity?: number;
  summaryType: SummaryType;
  confidenceScore: number;
  language: string;
  wordCount?: number;
  characterCount?: number;
  parsedAt: Date;
  createdAt: Date;
}

// AI 摘要來源
export interface AISummarySource {
  id: string;
  aiSummaryId: string;
  sourceUrl: string;
  sourceDomain?: string;
  sourceTitle?: string;
  sourceDescription?: string;
  citationText?: string;
  positionInSummary?: number;
  isUserDomain: boolean;
  relevanceScore?: number;
  createdAt: Date;
}

// 競爭對手
export interface Competitor {
  id: string;
  userId: string;
  domain: string;
  companyName?: string;
  industry?: string;
  description?: string;
  isActive: boolean;
  monitoringPriority: Priority;
  lastAnalyzedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// 監測任務
export interface MonitoringTask {
  id: string;
  keywordId?: string;
  taskType: TaskType;
  status: TaskStatus;
  priority: Priority;
  scheduledAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  retryCount: number;
  maxRetries: number;
  errorMessage?: string;
  errorCode?: string;
  resultData: Record<string, any>;
  executionTimeMs?: number;
  createdAt: Date;
  updatedAt: Date;
}

// 監測統計
export interface MonitoringStatistics {
  id: string;
  keywordId: string;
  datePeriod: Date;
  periodType: PeriodType;
  totalSearches: number;
  aiSummaryAppearances: number;
  userDomainMentions: number;
  averageAiPosition?: number;
  topCompetitors: string[];
  summaryTopics: string[];
  createdAt: Date;
  updatedAt: Date;
}

// 系統配置
export interface SystemConfig {
  id: string;
  configKey: string;
  configValue: Record<string, any>;
  description?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// API 響應類型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// 分頁查詢參數
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 關鍵字查詢參數
export interface KeywordQueryParams extends PaginationParams {
  category?: string;
  isActive?: boolean;
  search?: string;
}

// 搜尋結果查詢參數
export interface SearchResultQueryParams extends PaginationParams {
  keywordId?: string;
  hasAiSummary?: boolean;
  dateFrom?: string;
  dateTo?: string;
}

// 監測概覽
export interface MonitoringOverview {
  id: string;
  keyword: string;
  category?: string;
  targetDomain?: string;
  monitoringFrequency: MonitoringFrequency;
  lastMonitoredAt?: Date;
  totalSearches: number;
  aiSummaryCount: number;
  userMentions: number;
  aiSummaryRate: number;
}

// 分析報告
export interface AnalyticsReport {
  keyword: string;
  period: {
    start: Date;
    end: Date;
    type: PeriodType;
  };
  summary: {
    totalSearches: number;
    aiSummaryAppearances: number;
    appearanceRate: number;
    userDomainMentions: number;
    mentionRate: number;
    averagePosition?: number;
  };
  trends: TrendData[];
  competitors: CompetitorAnalysis[];
  topSources: SourceAnalysis[];
}

// 趨勢數據
export interface TrendData {
  date: string;
  summaryCount: number;
  mentionCount: number;
  averagePosition?: number;
  topSources: string[];
}

// 競爭對手分析
export interface CompetitorAnalysis {
  domain: string;
  companyName?: string;
  mentionCount: number;
  shareOfVoice: number;
  averagePosition?: number;
  topKeywords: string[];
  trendDirection: 'up' | 'down' | 'stable';
}

// 來源分析
export interface SourceAnalysis {
  domain: string;
  mentionCount: number;
  shareOfVoice: number;
  averageRelevance: number;
  isCompetitor: boolean;
  topCitations: string[];
}

// Google 搜尋配置
export interface GoogleSearchConfig {
  apiKey: string;
  engineId: string;
  location: string;
  language: string;
  rateLimit: {
    requestsPerSecond: number;
    dailyQuota: number;
  };
}

// AI 摘要檢測配置
export interface AISummaryDetectionConfig {
  selectors: string[];
  keywords: string[];
  patterns: string[];
  confidenceThreshold: number;
  minContentLength: number;
}

// 監測任務創建請求
export interface CreateTaskRequest {
  keywordId?: string;
  taskType: TaskType;
  priority?: Priority;
  scheduledAt?: Date;
  config?: Record<string, any>;
}

// 批量監測請求
export interface BatchMonitoringRequest {
  keywordIds: string[];
  immediate?: boolean;
  config?: Record<string, any>;
}

// 監測狀態
export interface MonitoringStatus {
  isRunning: boolean;
  activeTasks: number;
  pendingTasks: number;
  completedToday: number;
  failedToday: number;
  nextScheduledTask?: Date;
  apiQuotaUsed: number;
  apiQuotaRemaining: number;
}

// 儀表板數據
export interface DashboardData {
  overview: {
    totalKeywords: number;
    activeKeywords: number;
    totalSearchesToday: number;
    aiSummaryRate: number;
    userMentionRate: number;
  };
  recentActivity: {
    recentSummaries: AISummary[];
    recentMentions: AISummary[];
    recentTasks: MonitoringTask[];
  };
  trends: {
    summaryTrend: TrendData[];
    mentionTrend: TrendData[];
    topKeywords: string[];
  };
  alerts: {
    type: 'info' | 'warning' | 'error';
    message: string;
    timestamp: Date;
  }[];
}

// 導出報告請求
export interface ExportReportRequest {
  keywordIds?: string[];
  dateFrom: string;
  dateTo: string;
  format: 'pdf' | 'excel' | 'csv';
  includeRawData?: boolean;
  language?: string;
}

// 錯誤類型
export interface MonitoringError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
  retryable: boolean;
}

// 事件類型
export type MonitoringEvent = 
  | { type: 'task_started'; taskId: string; timestamp: Date }
  | { type: 'task_completed'; taskId: string; duration: number; timestamp: Date }
  | { type: 'task_failed'; taskId: string; error: MonitoringError; timestamp: Date }
  | { type: 'ai_summary_detected'; summaryId: string; keyword: string; timestamp: Date }
  | { type: 'user_mention_found'; summaryId: string; keyword: string; timestamp: Date }
  | { type: 'quota_warning'; usage: number; limit: number; timestamp: Date };

// WebSocket 消息類型
export interface WebSocketMessage {
  type: 'monitoring_event' | 'status_update' | 'error';
  data: MonitoringEvent | MonitoringStatus | MonitoringError;
  timestamp: Date;
}
