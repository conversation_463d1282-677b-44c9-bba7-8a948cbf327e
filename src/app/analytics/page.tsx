/**
 * Analytics 頁面
 * 處理各種 analytics 相關請求，防止 404 錯誤
 */

'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

export default function AnalyticsPage() {
  const router = useRouter();
  const [requestType, setRequestType] = useState<'script' | 'page' | 'unknown'>('unknown');

  useEffect(() => {
    // 檢測請求類型
    const detectRequestType = (): void => {
      const userAgent = navigator.userAgent;
      const referrer = document.referrer;
      const acceptHeader = 'text/html'; // 瀏覽器默認

      // 檢查是否是 Socket.IO 探測請求
      if (referrer === '' && userAgent.includes('Mozilla')) {
        console.log('🔍 檢測到可能的 Socket.IO 探測請求');
        setRequestType('script');
        return;
      }

      // 檢查是否是腳本請求
      if (acceptHeader.includes('javascript') || acceptHeader.includes('json')) {
        console.log('📜 檢測到腳本請求');
        setRequestType('script');
        return;
      }

      // 默認為頁面請求
      console.log('📄 檢測到頁面請求');
      setRequestType('page');
    };

    detectRequestType();

    // 如果是頁面請求，延遲重定向
    if (requestType === 'page') {
      const timer = setTimeout(() => {
        router.replace('/admin/analytics');
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [router, requestType]);

  // 如果是腳本請求，返回空內容
  if (requestType === 'script') {
    return (
      <div style={{ display: 'none' }}>
        {/* 空的 analytics 響應 */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // AI SEO 優化王 - Analytics 腳本響應
              console.log('Analytics endpoint accessed:', new Date().toISOString());
              // 空響應，防止 404 錯誤
            `
          }}
        />
      </div>
    );
  }

  // 頁面請求的 UI
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center max-w-md mx-auto p-6">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h1 className="text-xl font-semibold text-gray-900 mb-2">
          正在載入分析頁面
        </h1>
        <p className="text-gray-600 mb-4">
          正在重定向到管理面板的分析頁面...
        </p>
        <div className="text-sm text-gray-500">
          如果沒有自動跳轉，請點擊{' '}
          <button
            onClick={() => router.push('/admin/analytics')}
            className="text-blue-600 hover:text-blue-800 underline"
          >
            這裡
          </button>
        </div>
      </div>
    </div>
  );
}
