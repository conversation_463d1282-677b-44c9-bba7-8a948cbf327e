/**
 * 產品分析狀態 API 路由
 * 提供分析進度查詢和狀態更新功能
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// 分析狀態接口
interface AnalysisStatus {
  analysisId: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  progress: number;
  message: string;
  startedAt: string;
  estimatedCompletion?: string;
  completedAt?: string;
  results?: any;
  error?: string;
}

// 模擬的分析狀態存儲 - 實際應用中會使用數據庫或 Redis
const analysisStatusStore = new Map<string, AnalysisStatus>();

// 模擬分析進度的步驟 - 縮短時間間隔
const analysisSteps = [
  { progress: 10, message: '初始化分析環境' },
  { progress: 25, message: '從 AI 引擎收集數據' },
  { progress: 45, message: '分析品牌可見度' },
  { progress: 65, message: '處理回應內容' },
  { progress: 80, message: '分析引用情況' },
  { progress: 95, message: '生成分析報告' },
  { progress: 100, message: '分析完成' },
];

// GET 處理函數 - 獲取分析狀態
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const analysisId = searchParams.get('analysisId');

    if (!analysisId) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'MISSING_PARAMETER',
          message: '缺少 analysisId 參數'
        }
      }, { status: 400 });
    }

    // 檢查分析狀態
    let status = analysisStatusStore.get(analysisId);

    if (!status) {
      // 如果沒有找到狀態，創建一個新的
      status = {
        analysisId,
        status: 'queued',
        progress: 0,
        message: '分析已加入隊列',
        startedAt: new Date().toISOString(),
        estimatedCompletion: new Date(Date.now() + 3 * 60 * 1000).toISOString(), // 3分鐘後
      };
      analysisStatusStore.set(analysisId, status);
    }

    // 模擬進度更新（優化後的時間安排）
    if (status.status === 'processing' || status.status === 'queued') {
      const currentTime = Date.now();
      const startTime = new Date(status.startedAt).getTime();
      const elapsedSeconds = (currentTime - startTime) / 1000;

      // 根據經過的時間更新進度 - 總共約2-3分鐘完成
      if (elapsedSeconds < 10) {
        // 前10秒：排隊狀態
        status.status = 'queued';
        status.progress = 0;
        status.message = '分析已加入隊列，等待開始';
      } else if (elapsedSeconds < 150) {
        // 10秒到2.5分鐘：處理狀態
        status.status = 'processing';
        const processTime = elapsedSeconds - 10; // 減去排隊時間
        const stepIndex = Math.min(
          Math.floor(processTime / 20), // 每20秒進入下一個步驟
          analysisSteps.length - 2
        );
        const step = analysisSteps[stepIndex];
        status.progress = step.progress;
        status.message = step.message;
        
        // 更新預計完成時間
        const remainingSteps = analysisSteps.length - 1 - stepIndex;
        const estimatedRemainingTime = remainingSteps * 20 * 1000; // 每步20秒
        status.estimatedCompletion = new Date(currentTime + estimatedRemainingTime).toISOString();
      } else {
        // 2.5分鐘後：完成
        status.status = 'completed';
        status.progress = 100;
        status.message = '分析完成';
        status.completedAt = new Date().toISOString();
        status.results = {
          summary: '分析已完成，發現了多個優化機會',
          visibilityScore: 78.5 + Math.random() * 20, // 78.5-98.5 的隨機分數
          improvementAreas: [
            '提升在 ChatGPT 中的可見度',
            '優化品牌引用策略',
            '改善內容相關性',
            '增強競爭優勢定位'
          ],
          recommendations: [
            '增加目標關鍵字密度',
            '建立更多權威性內容',
            '優化品牌提及頻率',
            '建立品牌知識圖譜',
            '提升內容品質與相關性'
          ],
          aiEngineBreakdown: {
            'ChatGPT': { visibility: 85.2, improvement: '+5.3%' },
            'Gemini': { visibility: 79.8, improvement: '+2.1%' },
            'Perplexity': { visibility: 76.4, improvement: '+8.7%' },
            'Claude': { visibility: 68.9, improvement: '+12.4%' }
          },
          nextSteps: [
            '實施內容優化策略',
            '監控品牌提及變化',
            '定期重新分析進度'
          ]
        };
      }

      // 更新存儲
      analysisStatusStore.set(analysisId, status);
    }

    return NextResponse.json({
      success: true,
      data: status
    });

  } catch (error) {
    console.error('獲取分析狀態錯誤:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '獲取分析狀態時發生錯誤'
      }
    }, { status: 500 });
  }
}

// POST 處理函數 - 更新分析狀態
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    
    // 驗證請求數據
    const updateSchema = z.object({
      analysisId: z.string().min(1, '分析 ID 不能為空'),
      status: z.enum(['queued', 'processing', 'completed', 'failed']).optional(),
      progress: z.number().min(0).max(100).optional(),
      message: z.string().optional(),
      error: z.string().optional(),
      results: z.any().optional(),
    });

    const validatedData = updateSchema.parse(body);

    // 獲取現有狀態
    const existingStatus = analysisStatusStore.get(validatedData.analysisId);
    
    if (!existingStatus) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: '找不到指定的分析'
        }
      }, { status: 404 });
    }

    // 更新狀態
    const updatedStatus: AnalysisStatus = {
      ...existingStatus,
      ...validatedData,
      ...(validatedData.status === 'completed' && {
        completedAt: new Date().toISOString()
      }),
    };

    analysisStatusStore.set(validatedData.analysisId, updatedStatus);

    return NextResponse.json({
      success: true,
      data: updatedStatus,
      message: '分析狀態已更新'
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '請求數據格式錯誤',
          details: error.errors
        }
      }, { status: 400 });
    }

    console.error('更新分析狀態錯誤:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '更新分析狀態時發生錯誤'
      }
    }, { status: 500 });
  }
}

// DELETE 處理函數 - 刪除分析狀態
export async function DELETE(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const analysisId = searchParams.get('analysisId');

    if (!analysisId) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'MISSING_PARAMETER',
          message: '缺少 analysisId 參數'
        }
      }, { status: 400 });
    }

    // 檢查分析是否存在
    if (!analysisStatusStore.has(analysisId)) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: '找不到指定的分析'
        }
      }, { status: 404 });
    }

    // 刪除分析狀態
    analysisStatusStore.delete(analysisId);

    return NextResponse.json({
      success: true,
      data: {
        message: `分析 ${analysisId} 的狀態已刪除`
      }
    });

  } catch (error) {
    console.error('刪除分析狀態錯誤:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '刪除分析狀態時發生錯誤'
      }
    }, { status: 500 });
  }
}

// 清理過期的分析狀態（可以通過定時任務調用）
export async function PATCH(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const maxAge = parseInt(searchParams.get('maxAge') || '3600'); // 默認1小時

    const now = Date.now();
    let cleanedCount = 0;

    for (const [analysisId, status] of analysisStatusStore.entries()) {
      const statusAge = now - new Date(status.startedAt).getTime();
      
      // 如果狀態超過最大年齡且已完成或失敗，則刪除
      if (statusAge > maxAge * 1000 && 
          (status.status === 'completed' || status.status === 'failed')) {
        analysisStatusStore.delete(analysisId);
        cleanedCount++;
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        message: `已清理 ${cleanedCount} 個過期的分析狀態`,
        cleanedCount,
        remainingCount: analysisStatusStore.size
      }
    });

  } catch (error) {
    console.error('清理分析狀態錯誤:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '清理分析狀態時發生錯誤'
      }
    }, { status: 500 });
  }
}
