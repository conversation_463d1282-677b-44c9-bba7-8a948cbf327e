/**
 * 產品分析設定 API 路由
 * 提供載入、保存和測試產品分析設定功能
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// 產品分析設定接口
interface ProductAnalysisSettings {
  ai: {
    openaiApiKey: string;
    model: string;
    maxTokens: number;
    temperature: number;
    isConfigured: boolean;
  };
  analysis: {
    autoAnalysisEnabled: boolean;
    analysisInterval: number;
    maxConcurrentAnalyses: number;
    retryAttempts: number;
    timeoutSeconds: number;
    enabledEngines: string[];
    visibilityThreshold: number;
  };
  notifications: {
    emailEnabled: boolean;
    slackEnabled: boolean;
    webhookEnabled: boolean;
    webhookUrl: string;
    notificationTypes: string[];
    alertThresholds: {
      lowVisibility: number;
      negativeResponse: number;
      citationDrop: number;
    };
  };
  content: {
    contentAnalysisEnabled: boolean;
    topicTrackingEnabled: boolean;
    competitorMonitoring: boolean;
    brandSafetyEnabled: boolean;
    hallucinationDetection: boolean;
  };
  reports: {
    autoReportGeneration: boolean;
    reportFrequency: string;
    retentionDays: number;
    exportFormats: string[];
  };
}

// 檢查認證（臨時簡化版本，用於開發測試）
function checkAuth(request: NextRequest): boolean {
  // 在開發環境中暫時跳過認證檢查
  if (process.env.NODE_ENV === 'development') {
    return true;
  }
  
  // 檢查認證 token
  const authHeader = request.headers.get('authorization');
  const token = authHeader?.replace('Bearer ', '') ||
                request.cookies.get('aiseo_auth_token')?.value;
  
  return !!token; // 簡化的檢查，實際應該驗證 token
}

// 模擬設定儲存（在實際應用中應該連接到數據庫）
let currentSettings: ProductAnalysisSettings = {
  ai: {
    openaiApiKey: '',
    model: 'gpt-4o-mini',
    maxTokens: 4000,
    temperature: 0.7,
    isConfigured: false
  },
  analysis: {
    autoAnalysisEnabled: true,
    analysisInterval: 24,
    maxConcurrentAnalyses: 5,
    retryAttempts: 3,
    timeoutSeconds: 300,
    enabledEngines: ['chatgpt', 'gemini', 'perplexity', 'copilot', 'claude'],
    visibilityThreshold: 70
  },
  notifications: {
    emailEnabled: true,
    slackEnabled: false,
    webhookEnabled: false,
    webhookUrl: '',
    notificationTypes: ['visibility_change', 'response_analysis', 'citation_update', 'report_ready'],
    alertThresholds: {
      lowVisibility: 50,
      negativeResponse: 30,
      citationDrop: 20
    }
  },
  content: {
    contentAnalysisEnabled: true,
    topicTrackingEnabled: true,
    competitorMonitoring: true,
    brandSafetyEnabled: true,
    hallucinationDetection: true
  },
  reports: {
    autoReportGeneration: true,
    reportFrequency: 'weekly',
    retentionDays: 90,
    exportFormats: ['pdf', 'csv', 'json']
  }
};

// 設定驗證 schema
const settingsSchema = z.object({
  ai: z.object({
    openaiApiKey: z.string(),
    model: z.enum(['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-3.5-turbo']),
    maxTokens: z.number().min(100).max(8000),
    temperature: z.number().min(0).max(2),
    isConfigured: z.boolean()
  }),
  analysis: z.object({
    autoAnalysisEnabled: z.boolean(),
    analysisInterval: z.number().min(1).max(168),
    maxConcurrentAnalyses: z.number().min(1).max(10),
    retryAttempts: z.number().min(0).max(10),
    timeoutSeconds: z.number().min(30).max(600),
    enabledEngines: z.array(z.string()),
    visibilityThreshold: z.number().min(0).max(100)
  }),
  notifications: z.object({
    emailEnabled: z.boolean(),
    slackEnabled: z.boolean(),
    webhookEnabled: z.boolean(),
    webhookUrl: z.string().url().optional().or(z.literal('')),
    notificationTypes: z.array(z.string()),
    alertThresholds: z.object({
      lowVisibility: z.number().min(0).max(100),
      negativeResponse: z.number().min(0).max(100),
      citationDrop: z.number().min(0).max(100)
    })
  }),
  content: z.object({
    contentAnalysisEnabled: z.boolean(),
    topicTrackingEnabled: z.boolean(),
    competitorMonitoring: z.boolean(),
    brandSafetyEnabled: z.boolean(),
    hallucinationDetection: z.boolean()
  }),
  reports: z.object({
    autoReportGeneration: z.boolean(),
    reportFrequency: z.enum(['daily', 'weekly', 'monthly', 'quarterly']),
    retentionDays: z.number().min(7).max(365),
    exportFormats: z.array(z.string())
  })
});

// GET 處理函數 - 載入設定
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // 檢查認證
    if (!checkAuth(request)) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '未授權訪問'
        }
      }, { status: 401 });
    }

    // 模擬檢查 API 金鑰狀態
    const isConfigured = currentSettings.ai.openaiApiKey.length > 0;
    const maskedSettings = {
      ...currentSettings,
      ai: {
        ...currentSettings.ai,
        openaiApiKey: isConfigured ? 'sk-***************************' : '',
        isConfigured
      }
    };

    return NextResponse.json({
      success: true,
      data: maskedSettings,
      meta: {
        lastUpdated: new Date().toISOString(),
        version: '1.0.0'
      }
    });

  } catch (error) {
    console.error('載入產品分析設定錯誤:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'LOAD_SETTINGS_ERROR',
        message: '載入設定時發生錯誤'
      }
    }, { status: 500 });
  }
}

// POST 處理函數 - 保存設定
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // 檢查認證
    if (!checkAuth(request)) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '未授權訪問'
        }
      }, { status: 401 });
    }

    const body = await request.json();
    
    // 驗證設定數據
    const validatedSettings = settingsSchema.parse(body);

    // 保存設定（在實際應用中會保存到數據庫）
    currentSettings = {
      ...validatedSettings,
      ai: {
        ...validatedSettings.ai,
        isConfigured: validatedSettings.ai.openaiApiKey.length > 0
      }
    };

    // 模擬設定保存延遲
    await new Promise(resolve => setTimeout(resolve, 500));

    return NextResponse.json({
      success: true,
      message: '設定儲存成功',
      data: {
        lastUpdated: new Date().toISOString(),
        settingsCount: Object.keys(validatedSettings).length
      }
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '設定數據格式錯誤',
          details: error.errors
        }
      }, { status: 400 });
    }

    console.error('保存產品分析設定錯誤:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'SAVE_SETTINGS_ERROR',
        message: '保存設定時發生錯誤'
      }
    }, { status: 500 });
  }
}

// PUT 處理函數 - 測試連接
export async function PUT(request: NextRequest): Promise<NextResponse> {
  try {
    // 檢查認證
    if (!checkAuth(request)) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '未授權訪問'
        }
      }, { status: 401 });
    }

    const body = await request.json();
    const { testType = 'all' } = body;

    // 模擬測試各種連接
    const testResults: Record<string, any> = {};

    if (testType === 'all' || testType === 'openai') {
      // 模擬 OpenAI API 測試
      await new Promise(resolve => setTimeout(resolve, 1000));
      testResults.openai = {
        status: currentSettings.ai.openaiApiKey ? 'success' : 'error',
        latency: currentSettings.ai.openaiApiKey ? '245ms' : 'N/A',
        message: currentSettings.ai.openaiApiKey ? 'API 金鑰驗證成功' : 'API 金鑰未設定'
      };
    }

    if (testType === 'all' || testType === 'engines') {
      // 模擬 AI 引擎測試
      await new Promise(resolve => setTimeout(resolve, 1500));
      testResults.engines = {};
      
      for (const engine of currentSettings.analysis.enabledEngines) {
        // 模擬一些引擎可能失敗
        const isSuccess = Math.random() > 0.2; // 80% 成功率
        testResults.engines[engine] = isSuccess ? 'success' : 'error';
      }
    }

    if (testType === 'all' || testType === 'webhook') {
      // 模擬 Webhook 測試
      if (currentSettings.notifications.webhookEnabled && currentSettings.notifications.webhookUrl) {
        await new Promise(resolve => setTimeout(resolve, 800));
        testResults.webhook = {
          status: 'success',
          responseTime: '156ms',
          message: 'Webhook 端點回應正常'
        };
      }
    }

    return NextResponse.json({
      success: true,
      message: '連接測試完成',
      data: testResults
    });

  } catch (error) {
    console.error('測試連接錯誤:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'TEST_CONNECTION_ERROR',
        message: '測試連接時發生錯誤'
      }
    }, { status: 500 });
  }
} 