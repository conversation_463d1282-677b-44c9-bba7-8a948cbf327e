import { NextRequest, NextResponse } from 'next/server';

// 模擬查詢數據
const mockQueries = [
  {
    id: '1',
    query: 'AI SEO 最佳實踐',
    count: 1247,
    growth: '+25%',
    category: 'SEO優化',
    intent: 'information',
    timestamp: new Date().toISOString()
  },
  {
    id: '2', 
    query: '如何優化品牌可見度',
    count: 982,
    growth: '+18%',
    category: '品牌分析',
    intent: 'howto',
    timestamp: new Date().toISOString()
  },
  {
    id: '3',
    query: '競爭對手分析工具',
    count: 756,
    growth: '+12%',
    category: '競爭情報',
    intent: 'tool_search',
    timestamp: new Date().toISOString()
  }
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const timeRange = searchParams.get('timeRange');

    let filteredQueries = mockQueries;

    // 根據類別過濾
    if (category && category !== 'all') {
      filteredQueries = filteredQueries.filter(q => 
        q.category.toLowerCase().includes(category.toLowerCase())
      );
    }

    const response = {
      success: true,
      data: {
        queries: filteredQueries,
        total: filteredQueries.length,
        summary: {
          totalQueries: 12847,
          growthRate: '+15.3%',
          avgResponseTime: '1.2s',
          successRate: '94.7%'
        }
      }
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('查詢數據獲取失敗:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '查詢數據獲取失敗' 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { query, category, intent } = body;

    // 模擬新增查詢記錄
    const newQuery = {
      id: String(mockQueries.length + 1),
      query,
      count: 1,
      growth: 'new',
      category,
      intent,
      timestamp: new Date().toISOString()
    };

    mockQueries.push(newQuery);

    return NextResponse.json({
      success: true,
      data: newQuery
    });
  } catch (error) {
    console.error('查詢創建失敗:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '查詢創建失敗' 
      },
      { status: 500 }
    );
  }
} 