import { NextRequest, NextResponse } from 'next/server';

// 模擬洞察數據
const mockInsights = [
  {
    id: '1',
    title: 'AI SEO 查詢激增',
    impact: 'high',
    category: '趨勢洞察',
    description: 'AI SEO 相關查詢在過去 30 天內增長了 185%',
    actionItems: ['優化 AI SEO 內容', '增加相關文檔', '擴展功能範圍'],
    confidence: 95,
    timestamp: new Date().toISOString(),
    metrics: {
      growth: '+185%',
      volume: 15420,
      timeframe: '30天'
    }
  },
  {
    id: '2',
    title: '企業用戶偏好變化',
    impact: 'medium',
    category: '用戶行為',
    description: '企業用戶更傾向於使用批量分析功能',
    actionItems: ['改進批量處理 UI', '增加企業級功能', '優化性能'],
    confidence: 87,
    timestamp: new Date().toISOString(),
    metrics: {
      adoption: '+45%',
      satisfaction: '4.2/5',
      retention: '89%'
    }
  },
  {
    id: '3',
    title: '移動端使用率上升',
    impact: 'medium',
    category: '設備趨勢',
    description: '移動端用戶使用率較上月增長 45%',
    actionItems: ['優化移動端體驗', '響應式設計改進', '移動端專屬功能'],
    confidence: 92,
    timestamp: new Date().toISOString(),
    metrics: {
      mobileUsers: '+45%',
      sessionTime: '3.2分鐘',
      bounceRate: '-12%'
    }
  }
];

const trendData = [
  {
    id: '1',
    trend: 'AI SEO 工具需求',
    direction: 'up',
    strength: 'strong',
    growth: '+185%',
    confidence: 95,
    description: '用戶對 AI 驅動的 SEO 工具需求急劇增長'
  },
  {
    id: '2',
    trend: '企業級功能偏好',
    direction: 'up',
    strength: 'moderate',
    growth: '+67%',
    confidence: 87,
    description: '企業用戶更偏好批量處理和高級分析功能'
  },
  {
    id: '3',
    trend: '移動優先使用',
    direction: 'up',
    strength: 'moderate',
    growth: '+45%',
    confidence: 92,
    description: '移動端使用者快速增長，需要優化移動體驗'
  }
];

const segmentData = [
  {
    id: '1',
    segment: '企業用戶',
    size: 2847,
    growth: '+23%',
    characteristics: ['高價值功能使用', '長期訂閱', '團隊協作'],
    behavior: {
      avgSessionTime: '12.5分鐘',
      featuresUsed: 8.3,
      satisfactionScore: 4.2
    }
  },
  {
    id: '2',
    segment: '個人專業用戶',
    size: 5621,
    growth: '+18%',
    characteristics: ['頻繁使用', '功能探索', '社群分享'],
    behavior: {
      avgSessionTime: '8.7分鐘',
      featuresUsed: 5.8,
      satisfactionScore: 4.1
    }
  },
  {
    id: '3',
    segment: '新手用戶',
    size: 3245,
    growth: '+34%',
    characteristics: ['學習階段', '基礎功能', '教程依賴'],
    behavior: {
      avgSessionTime: '4.2分鐘',
      featuresUsed: 2.1,
      satisfactionScore: 3.8
    }
  }
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type'); // 'insights', 'trends', 'segments'
    const impact = searchParams.get('impact');
    const category = searchParams.get('category');

    let responseData;

    switch (type) {
      case 'trends':
        responseData = {
          trends: trendData,
          total: trendData.length
        };
        break;
      
      case 'segments':
        responseData = {
          segments: segmentData,
          total: segmentData.length
        };
        break;
      
      default: // insights
        let filteredInsights = mockInsights;
        
        if (impact && impact !== 'all') {
          filteredInsights = filteredInsights.filter(insight => 
            insight.impact === impact
          );
        }
        
        if (category && category !== 'all') {
          filteredInsights = filteredInsights.filter(insight => 
            insight.category.toLowerCase().includes(category.toLowerCase())
          );
        }
        
        responseData = {
          insights: filteredInsights,
          total: filteredInsights.length,
          summary: {
            totalInsights: 247,
            highConfidence: 189,
            actionableItems: 156,
            implemented: 89
          }
        };
    }

    return NextResponse.json({
      success: true,
      data: responseData
    });
  } catch (error) {
    console.error('洞察數據獲取失敗:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '洞察數據獲取失敗' 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { title, category, impact, description, actionItems } = body;

    // 模擬新增洞察
    const newInsight = {
      id: String(mockInsights.length + 1),
      title,
      impact,
      category,
      description,
      actionItems: actionItems || [],
      confidence: Math.floor(Math.random() * 20) + 80, // 80-99 的隨機置信度
      timestamp: new Date().toISOString(),
      metrics: {
        growth: '+0%',
        volume: 0,
        timeframe: '即時'
      }
    };

    mockInsights.push(newInsight);

    return NextResponse.json({
      success: true,
      data: newInsight
    });
  } catch (error) {
    console.error('洞察創建失敗:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '洞察創建失敗' 
      },
      { status: 500 }
    );
  }
} 