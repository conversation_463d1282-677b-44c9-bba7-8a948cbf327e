import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// 查詢智能分析請求模式
const queryAnalysisSchema = z.object({
  query: z.string().min(1, '查詢不能為空').max(500, '查詢過長'),
});

// GET 處理函數 - 獲取歷史查詢分析
export async function GET(
  request: NextRequest,
  { params }: { params: { brandId: string } }
): Promise<NextResponse> {
  try {
    const { brandId } = params;
    const { searchParams } = new URL(request.url);

    // 解析查詢參數
    const intentLevel = searchParams.get('intent_level');
    const competitionLevel = searchParams.get('competition_level');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');

    // 模擬歷史查詢數據
    const mockQueryHistory = [
      {
        id: '1',
        brandId: brandId,
        fullQuery: 'best HR software for small teams',
        queryHash: 'hash_1',
        intentLevel: 'high',
        conversionRate: 68.5,
        searchVolume: 825,
        competitionLevel: 'low',
        clickPotential: 'high',
        dataSources: {
          openai: true,
          reddit: true,
          autocomplete: true,
          social: false
        },
        rationale: '這個查詢顯示了高購買意圖，用戶正在尋找具體的解決方案',
        topSolutions: {
          'BambooHR': { percentage: 22.5, description: 'HR management platform' },
          'Workday': { percentage: 20.8, description: 'Enterprise HR solution' },
          'Gusto': { percentage: 19.7, description: 'Small business HR' }
        },
        relatedKeywords: {
          'HR software for teams': { volume: 1200, growth: '+12%' },
          'Best HR platforms': { volume: 850, growth: '+8%' }
        },
        metrics: {
          searchVolume: 825,
          competition: 'Low',
          clickPotential: 'High',
          conversionRate: '68%'
        },
        analysisStatus: 'completed',
        analyzedAt: '2024-01-15T10:30:00Z',
        createdAt: '2024-01-15T10:25:00Z',
        updatedAt: '2024-01-15T10:30:00Z'
      },
      {
        id: '2',
        brandId: brandId,
        fullQuery: 'project management tools comparison',
        queryHash: 'hash_2',
        intentLevel: 'medium',
        conversionRate: 45.2,
        searchVolume: 1250,
        competitionLevel: 'high',
        clickPotential: 'medium',
        dataSources: {
          openai: true,
          reddit: true,
          autocomplete: true,
          social: true
        },
        rationale: '比較型查詢，用戶處於研究階段',
        topSolutions: {
          'Asana': { percentage: 18.3, description: 'Team collaboration tool' },
          'Trello': { percentage: 16.7, description: 'Visual project management' },
          'Monday.com': { percentage: 15.2, description: 'Work management platform' }
        },
        relatedKeywords: {
          'project management software': { volume: 2100, growth: '+5%' },
          'task management tools': { volume: 1400, growth: '+3%' }
        },
        metrics: {
          searchVolume: 1250,
          competition: 'High',
          clickPotential: 'Medium',
          conversionRate: '45%'
        },
        analysisStatus: 'completed',
        analyzedAt: '2024-01-14T15:45:00Z',
        createdAt: '2024-01-14T15:40:00Z',
        updatedAt: '2024-01-14T15:45:00Z'
      }
    ];

    // 應用篩選
    let filteredData = mockQueryHistory;
    
    if (intentLevel && intentLevel !== 'all') {
      filteredData = filteredData.filter(item => item.intentLevel === intentLevel);
    }
    
    if (competitionLevel && competitionLevel !== 'all') {
      filteredData = filteredData.filter(item => item.competitionLevel === competitionLevel);
    }

    // 應用分頁
    const paginatedData = filteredData.slice(offset, offset + limit);

    return NextResponse.json({
      success: true,
      message: '查詢智能分析結果獲取成功',
      data: paginatedData,
      total: filteredData.length,
      pagination: {
        limit,
        offset,
        hasMore: offset + limit < filteredData.length
      }
    });

  } catch (error) {
    console.error('獲取查詢智能分析失敗:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '獲取查詢智能分析失敗'
      }
    }, { status: 500 });
  }
}

// POST 處理函數 - 執行新的查詢分析
export async function POST(
  request: NextRequest,
  { params }: { params: { brandId: string } }
): Promise<NextResponse> {
  try {
    const { brandId } = params;
    const body = await request.json();
    
    // 驗證請求數據
    const validatedData = queryAnalysisSchema.parse(body);

    // 模擬 AI 分析過程
    const analysisResult = {
      id: `analysis_${Date.now()}`,
      brandId: brandId,
      fullQuery: validatedData.query,
      queryHash: `hash_${Date.now()}`,
      intentLevel: 'high', // AI 分析結果
      conversionRate: 72.3,
      searchVolume: 950,
      competitionLevel: 'medium',
      clickPotential: 'high',
      dataSources: {
        openai: true,
        reddit: true,
        autocomplete: true,
        social: true
      },
      rationale: `經過 AI 分析，「${validatedData.query}」顯示出強烈的購買意圖。用戶正在尋找具體的解決方案，這類查詢通常有較高的轉換率。建議針對此查詢優化相關內容。`,
      topSolutions: {
        '您的產品': { percentage: 15.2, description: '基於品牌匹配度分析' },
        '競爭對手A': { percentage: 18.7, description: '市場領導者' },
        '競爭對手B': { percentage: 16.4, description: '新興解決方案' }
      },
      relatedKeywords: {
        [`${validatedData.query} review`]: { volume: 650, growth: '+10%' },
        [`best ${validatedData.query}`]: { volume: 480, growth: '+15%' },
        [`${validatedData.query} comparison`]: { volume: 320, growth: '+8%' }
      },
      metrics: {
        searchVolume: 950,
        competition: 'Medium',
        clickPotential: 'High',
        conversionRate: '72%'
      },
      analysisStatus: 'completed',
      analyzedAt: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      message: '查詢智能分析完成',
      data: analysisResult
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '請求數據格式錯誤',
          details: error.errors
        }
      }, { status: 400 });
    }

    console.error('查詢智能分析失敗:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '查詢智能分析失敗'
      }
    }, { status: 500 });
  }
} 