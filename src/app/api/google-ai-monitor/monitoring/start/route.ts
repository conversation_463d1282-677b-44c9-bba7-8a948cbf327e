/**
 * Google AI 監測器 - 啟動監測 API
 */

import { NextRequest, NextResponse } from 'next/server';
import type { ApiResponse } from '@/types/google-ai-monitor';
import { monitoringScheduler } from '@/services/google-ai-monitor/MonitoringScheduler';

// POST - 啟動監測系統
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // 檢查監測系統是否已經在運行
    const schedulerStats = monitoringScheduler.getSchedulerStats();
    
    if (schedulerStats.isRunning) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'ALREADY_RUNNING',
          message: '監測系統已在運行中',
        },
      }, { status: 409 });
    }

    // 啟動監測系統
    monitoringScheduler.start();

    const response: ApiResponse<{ 
      message: string; 
      startedAt: Date;
      activeJobs: number;
    }> = {
      success: true,
      data: {
        message: '監測系統已啟動',
        startedAt: new Date(),
        activeJobs: monitoringScheduler.getSchedulerStats().activeJobs,
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('啟動監測系統失敗:', error);
    
    return NextResponse.json({
      success: false,
      error: {
        code: 'START_FAILED',
        message: '啟動監測系統失敗',
        details: error instanceof Error ? error.message : '未知錯誤',
      },
    }, { status: 500 });
  }
}
