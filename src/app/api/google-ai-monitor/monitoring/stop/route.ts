/**
 * Google AI 監測器 - 停止監測 API
 */

import { NextRequest, NextResponse } from 'next/server';
import type { ApiResponse } from '@/types/google-ai-monitor';
import { monitoringScheduler } from '@/services/google-ai-monitor/MonitoringScheduler';

// POST - 停止監測系統
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // 檢查監測系統是否在運行
    const schedulerStats = monitoringScheduler.getSchedulerStats();
    
    if (!schedulerStats.isRunning) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'NOT_RUNNING',
          message: '監測系統未在運行',
        },
      }, { status: 409 });
    }

    // 停止監測系統
    monitoringScheduler.stop();

    const response: ApiResponse<{ 
      message: string; 
      stoppedAt: Date;
      finalStats: {
        totalTasks: number;
        runningTasks: number;
        queuedTasks: number;
      };
    }> = {
      success: true,
      data: {
        message: '監測系統已停止',
        stoppedAt: new Date(),
        finalStats: {
          totalTasks: schedulerStats.totalTasks,
          runningTasks: schedulerStats.runningTasks,
          queuedTasks: schedulerStats.queuedTasks,
        },
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('停止監測系統失敗:', error);
    
    return NextResponse.json({
      success: false,
      error: {
        code: 'STOP_FAILED',
        message: '停止監測系統失敗',
        details: error instanceof Error ? error.message : '未知錯誤',
      },
    }, { status: 500 });
  }
}
