/**
 * Google AI 監測器 - 監測控制 API
 * 處理監測系統的啟動、停止和狀態查詢
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import type { 
  MonitoringStatus, 
  BatchMonitoringRequest,
  ApiResponse 
} from '@/types/google-ai-monitor';
import { monitoringScheduler } from '@/services/google-ai-monitor/MonitoringScheduler';
import { googleSearchService } from '@/services/google-ai-monitor/GoogleSearchService';

// 批量監測請求驗證
const batchMonitoringSchema = z.object({
  keywordIds: z.array(z.string()).min(1, '至少需要一個關鍵字'),
  immediate: z.boolean().default(false),
  config: z.record(z.any()).optional(),
});

// 獲取用戶 ID
function getUserId(request: NextRequest): string {
  return 'user1'; // 模擬用戶 ID
}

// GET - 獲取監測狀態
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const userId = getUserId(request);
    
    // 獲取排程器統計
    const schedulerStats = monitoringScheduler.getSchedulerStats();
    
    // 獲取 API 配額使用情況
    const quotaUsage = googleSearchService.getQuotaUsage();
    
    // 模擬監測狀態數據
    const monitoringStatus: MonitoringStatus = {
      isRunning: schedulerStats.isRunning,
      activeTasks: schedulerStats.runningTasks,
      pendingTasks: schedulerStats.queuedTasks,
      completedToday: 24, // 這應該從數據庫查詢
      failedToday: 1,     // 這應該從數據庫查詢
      nextScheduledTask: new Date(Date.now() + 3600000), // 下一個小時
      apiQuotaUsed: Math.round((quotaUsage.used / quotaUsage.limit) * 100),
      apiQuotaRemaining: quotaUsage.remaining,
    };

    const response: ApiResponse<MonitoringStatus> = {
      success: true,
      data: monitoringStatus,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('獲取監測狀態失敗:', error);
    
    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '獲取監測狀態失敗',
      },
    }, { status: 500 });
  }
}

// POST - 批量執行監測
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const validatedData = batchMonitoringSchema.parse(body);
    const userId = getUserId(request);

    // 檢查 API 配額
    const quotaUsage = googleSearchService.getQuotaUsage();
    if (quotaUsage.remaining < validatedData.keywordIds.length) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'QUOTA_INSUFFICIENT',
          message: `API 配額不足，剩餘 ${quotaUsage.remaining} 次，需要 ${validatedData.keywordIds.length} 次`,
        },
      }, { status: 429 });
    }

    // 檢查並發限制
    const schedulerStats = monitoringScheduler.getSchedulerStats();
    if (schedulerStats.runningTasks >= 5) { // 假設最大並發為 5
      return NextResponse.json({
        success: false,
        error: {
          code: 'CONCURRENT_LIMIT_EXCEEDED',
          message: '當前執行任務過多，請稍後再試',
        },
      }, { status: 429 });
    }

    // 模擬關鍵字數據 (實際應該從數據庫獲取)
    const mockKeywords = [
      {
        id: '1',
        userId: 'user1',
        keyword: 'AI工具',
        category: '科技',
        targetDomain: 'example.com',
        isActive: true,
        monitoringFrequency: 'daily' as const,
        lastMonitoredAt: new Date(Date.now() - 3600000),
        nextMonitoringAt: new Date(Date.now() + 3600000),
        monitoringConfig: {},
        createdAt: new Date(Date.now() - 86400000),
        updatedAt: new Date(Date.now() - 3600000),
      },
      {
        id: '2',
        userId: 'user1',
        keyword: '電動車',
        category: '汽車',
        targetDomain: 'example.com',
        isActive: true,
        monitoringFrequency: 'weekly' as const,
        lastMonitoredAt: new Date(Date.now() - 86400000),
        nextMonitoringAt: new Date(Date.now() + 86400000 * 6),
        monitoringConfig: {},
        createdAt: new Date(Date.now() - 86400000 * 7),
        updatedAt: new Date(Date.now() - 86400000),
      },
    ];

    // 過濾出有效的關鍵字
    const validKeywords = mockKeywords.filter(k => 
      validatedData.keywordIds.includes(k.id) && 
      k.userId === userId && 
      k.isActive
    );

    if (validKeywords.length === 0) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'NO_VALID_KEYWORDS',
          message: '沒有找到有效的關鍵字',
        },
      }, { status: 400 });
    }

    // 執行批量監測
    const results = await monitoringScheduler.executeBatchMonitoring(validKeywords);
    
    // 統計結果
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;

    const response: ApiResponse<{
      totalRequested: number;
      totalExecuted: number;
      successCount: number;
      failureCount: number;
      results: any[];
    }> = {
      success: true,
      data: {
        totalRequested: validatedData.keywordIds.length,
        totalExecuted: validKeywords.length,
        successCount,
        failureCount,
        results: results.map((result, index) => ({
          keywordId: validKeywords[index]?.id,
          keyword: validKeywords[index]?.keyword,
          success: result.success,
          executionTime: result.executionTime,
          error: result.error,
        })),
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('批量執行監測失敗:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '請求數據格式錯誤',
          details: error.errors,
        },
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '批量執行監測失敗',
      },
    }, { status: 500 });
  }
}

// PUT - 更新監測配置
export async function PUT(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const { action, config } = z.object({
      action: z.enum(['update_config', 'reset_quota']),
      config: z.record(z.any()).optional(),
    }).parse(body);

    switch (action) {
      case 'update_config':
        if (config) {
          // 更新監測配置
          monitoringScheduler.updateConfig(config);
        }
        break;
        
      case 'reset_quota':
        // 重置 API 配額計數器
        googleSearchService.resetQuota();
        break;
        
      default:
        return NextResponse.json({
          success: false,
          error: {
            code: 'INVALID_ACTION',
            message: '無效的操作',
          },
        }, { status: 400 });
    }

    const response: ApiResponse<{ message: string }> = {
      success: true,
      data: { message: '配置已更新' },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('更新監測配置失敗:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '請求數據格式錯誤',
          details: error.errors,
        },
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '更新監測配置失敗',
      },
    }, { status: 500 });
  }
}
