/**
 * Google AI 監測器 - 單個關鍵字管理 API
 * 處理特定關鍵字的操作
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import type { 
  MonitoringKeyword, 
  CreateKeywordRequest, 
  ApiResponse 
} from '@/types/google-ai-monitor';
import { monitoringScheduler } from '@/services/google-ai-monitor/MonitoringScheduler';

// 更新關鍵字驗證模式
const updateKeywordSchema = z.object({
  keyword: z.string().min(1, '關鍵字不能為空').max(255, '關鍵字過長').optional(),
  category: z.string().max(100, '分類名稱過長').optional(),
  targetDomain: z.string().max(255, '網域名稱過長').optional(),
  isActive: z.boolean().optional(),
  monitoringFrequency: z.enum(['daily', 'weekly', 'monthly']).optional(),
  monitoringConfig: z.record(z.any()).optional(),
});

// 模擬關鍵字服務 (與主路由共享)
class KeywordService {
  private keywords: MonitoringKeyword[] = [
    {
      id: '1',
      userId: 'user1',
      keyword: 'AI工具',
      category: '科技',
      targetDomain: 'example.com',
      isActive: true,
      monitoringFrequency: 'daily',
      lastMonitoredAt: new Date(Date.now() - 3600000),
      nextMonitoringAt: new Date(Date.now() + 3600000),
      monitoringConfig: {},
      createdAt: new Date(Date.now() - 86400000),
      updatedAt: new Date(Date.now() - 3600000),
    },
    {
      id: '2',
      userId: 'user1',
      keyword: '電動車',
      category: '汽車',
      targetDomain: 'example.com',
      isActive: true,
      monitoringFrequency: 'weekly',
      lastMonitoredAt: new Date(Date.now() - 86400000),
      nextMonitoringAt: new Date(Date.now() + 86400000 * 6),
      monitoringConfig: {},
      createdAt: new Date(Date.now() - 86400000 * 7),
      updatedAt: new Date(Date.now() - 86400000),
    },
  ];

  async getKeywordById(keywordId: string, userId: string): Promise<MonitoringKeyword | null> {
    return this.keywords.find(k => k.id === keywordId && k.userId === userId) || null;
  }

  async updateKeyword(
    keywordId: string, 
    userId: string, 
    data: Partial<CreateKeywordRequest & { isActive: boolean }>
  ): Promise<MonitoringKeyword | null> {
    const index = this.keywords.findIndex(k => k.id === keywordId && k.userId === userId);
    
    if (index === -1) {
      return null;
    }

    const updatedKeyword = {
      ...this.keywords[index],
      ...data,
      updatedAt: new Date(),
    };

    // 如果頻率改變，重新計算下次監測時間
    if (data.monitoringFrequency) {
      updatedKeyword.nextMonitoringAt = this.calculateNextMonitoring(data.monitoringFrequency);
    }

    this.keywords[index] = updatedKeyword;
    return updatedKeyword;
  }

  async deleteKeyword(keywordId: string, userId: string): Promise<boolean> {
    const index = this.keywords.findIndex(k => k.id === keywordId && k.userId === userId);
    
    if (index === -1) {
      return false;
    }

    this.keywords.splice(index, 1);
    return true;
  }

  private calculateNextMonitoring(frequency: 'daily' | 'weekly' | 'monthly'): Date {
    const now = new Date();
    
    switch (frequency) {
      case 'daily':
        return new Date(now.getTime() + 24 * 60 * 60 * 1000);
      case 'weekly':
        return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
      case 'monthly':
        return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
      default:
        return new Date(now.getTime() + 24 * 60 * 60 * 1000);
    }
  }
}

const keywordService = new KeywordService();

// 獲取用戶 ID
function getUserId(request: NextRequest): string {
  return 'user1'; // 模擬用戶 ID
}

// GET - 獲取單個關鍵字詳情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse> {
  try {
    const keywordId = params.id;
    const userId = getUserId(request);

    const keyword = await keywordService.getKeywordById(keywordId, userId);

    if (!keyword) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'KEYWORD_NOT_FOUND',
          message: '關鍵字不存在',
        },
      }, { status: 404 });
    }

    const response: ApiResponse<MonitoringKeyword> = {
      success: true,
      data: keyword,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('獲取關鍵字詳情失敗:', error);
    
    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '獲取關鍵字詳情失敗',
      },
    }, { status: 500 });
  }
}

// PUT - 更新關鍵字
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse> {
  try {
    const keywordId = params.id;
    const userId = getUserId(request);
    const body = await request.json();
    const validatedData = updateKeywordSchema.parse(body);

    // 檢查關鍵字是否存在
    const existingKeyword = await keywordService.getKeywordById(keywordId, userId);
    if (!existingKeyword) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'KEYWORD_NOT_FOUND',
          message: '關鍵字不存在',
        },
      }, { status: 404 });
    }

    // 如果更新關鍵字名稱，檢查是否與其他關鍵字重複
    if (validatedData.keyword && validatedData.keyword !== existingKeyword.keyword) {
      // 這裡應該檢查數據庫中是否有重複的關鍵字
      // 為了簡化，暫時跳過這個檢查
    }

    const updatedKeyword = await keywordService.updateKeyword(keywordId, userId, validatedData);

    if (!updatedKeyword) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UPDATE_FAILED',
          message: '更新關鍵字失敗',
        },
      }, { status: 500 });
    }

    // 如果狀態或頻率改變，更新排程
    if (validatedData.isActive !== undefined || validatedData.monitoringFrequency) {
      try {
        if (updatedKeyword.isActive) {
          monitoringScheduler.scheduleKeyword(updatedKeyword);
        } else {
          monitoringScheduler.unscheduleKeyword(keywordId);
        }
      } catch (schedulerError) {
        console.error('更新排程失敗:', schedulerError);
        // 不影響主要更新操作
      }
    }

    const response: ApiResponse<MonitoringKeyword> = {
      success: true,
      data: updatedKeyword,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('更新關鍵字失敗:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '請求數據格式錯誤',
          details: error.errors,
        },
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '更新關鍵字失敗',
      },
    }, { status: 500 });
  }
}

// DELETE - 刪除關鍵字
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse> {
  try {
    const keywordId = params.id;
    const userId = getUserId(request);

    // 檢查關鍵字是否存在
    const existingKeyword = await keywordService.getKeywordById(keywordId, userId);
    if (!existingKeyword) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'KEYWORD_NOT_FOUND',
          message: '關鍵字不存在',
        },
      }, { status: 404 });
    }

    // 停止排程
    try {
      monitoringScheduler.unscheduleKeyword(keywordId);
    } catch (schedulerError) {
      console.error('停止排程失敗:', schedulerError);
      // 不影響刪除操作
    }

    const deleted = await keywordService.deleteKeyword(keywordId, userId);

    if (!deleted) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'DELETE_FAILED',
          message: '刪除關鍵字失敗',
        },
      }, { status: 500 });
    }

    const response: ApiResponse<{ deleted: boolean }> = {
      success: true,
      data: { deleted: true },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('刪除關鍵字失敗:', error);
    
    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '刪除關鍵字失敗',
      },
    }, { status: 500 });
  }
}

// POST - 立即執行關鍵字監測
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse> {
  try {
    const keywordId = params.id;
    const userId = getUserId(request);
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action !== 'execute') {
      return NextResponse.json({
        success: false,
        error: {
          code: 'INVALID_ACTION',
          message: '無效的操作',
        },
      }, { status: 400 });
    }

    // 檢查關鍵字是否存在且啟用
    const keyword = await keywordService.getKeywordById(keywordId, userId);
    if (!keyword) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'KEYWORD_NOT_FOUND',
          message: '關鍵字不存在',
        },
      }, { status: 404 });
    }

    if (!keyword.isActive) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'KEYWORD_INACTIVE',
          message: '關鍵字未啟用',
        },
      }, { status: 400 });
    }

    // 執行立即監測
    try {
      const result = await monitoringScheduler.executeKeywordMonitoring(keyword);
      
      const response: ApiResponse<{ 
        taskId: string; 
        status: string; 
        executionTime: number;
      }> = {
        success: true,
        data: {
          taskId: `immediate_${Date.now()}`,
          status: result.success ? 'completed' : 'failed',
          executionTime: result.executionTime,
        },
      };

      return NextResponse.json(response);
    } catch (executionError) {
      console.error('執行監測失敗:', executionError);
      
      return NextResponse.json({
        success: false,
        error: {
          code: 'EXECUTION_FAILED',
          message: '執行監測失敗',
          details: executionError instanceof Error ? executionError.message : '未知錯誤',
        },
      }, { status: 500 });
    }
  } catch (error) {
    console.error('立即執行監測失敗:', error);
    
    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '立即執行監測失敗',
      },
    }, { status: 500 });
  }
}
