/**
 * Google AI 監測器 - 關鍵字管理 API
 * 處理關鍵字的 CRUD 操作
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import type { 
  MonitoringKeyword, 
  CreateKeywordRequest, 
  ApiResponse,
  PaginationParams 
} from '@/types/google-ai-monitor';

// 請求驗證模式
const createKeywordSchema = z.object({
  keyword: z.string().min(1, '關鍵字不能為空').max(255, '關鍵字過長'),
  category: z.string().max(100, '分類名稱過長').optional(),
  targetDomain: z.string().max(255, '網域名稱過長').optional(),
  monitoringFrequency: z.enum(['daily', 'weekly', 'monthly']).default('daily'),
  monitoringConfig: z.record(z.any()).default({}),
});

const updateKeywordSchema = createKeywordSchema.partial();

const querySchema = z.object({
  page: z.string().transform(Number).default('1'),
  limit: z.string().transform(Number).default('20'),
  category: z.string().optional(),
  isActive: z.string().transform(val => val === 'true').optional(),
  search: z.string().optional(),
  sortBy: z.string().default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// 模擬數據庫操作
class KeywordService {
  private keywords: MonitoringKeyword[] = [
    {
      id: '1',
      userId: 'user1',
      keyword: 'AI工具',
      category: '科技',
      targetDomain: 'example.com',
      isActive: true,
      monitoringFrequency: 'daily',
      lastMonitoredAt: new Date(Date.now() - 3600000),
      nextMonitoringAt: new Date(Date.now() + 3600000),
      monitoringConfig: {},
      createdAt: new Date(Date.now() - 86400000),
      updatedAt: new Date(Date.now() - 3600000),
    },
    {
      id: '2',
      userId: 'user1',
      keyword: '電動車',
      category: '汽車',
      targetDomain: 'example.com',
      isActive: true,
      monitoringFrequency: 'weekly',
      lastMonitoredAt: new Date(Date.now() - 86400000),
      nextMonitoringAt: new Date(Date.now() + 86400000 * 6),
      monitoringConfig: {},
      createdAt: new Date(Date.now() - 86400000 * 7),
      updatedAt: new Date(Date.now() - 86400000),
    },
  ];

  async getKeywords(
    userId: string, 
    params: PaginationParams & { category?: string; isActive?: boolean; search?: string }
  ): Promise<{ keywords: MonitoringKeyword[]; total: number }> {
    let filtered = this.keywords.filter(k => k.userId === userId);

    // 應用篩選
    if (params.category) {
      filtered = filtered.filter(k => k.category === params.category);
    }
    
    if (params.isActive !== undefined) {
      filtered = filtered.filter(k => k.isActive === params.isActive);
    }
    
    if (params.search) {
      const searchLower = params.search.toLowerCase();
      filtered = filtered.filter(k => 
        k.keyword.toLowerCase().includes(searchLower) ||
        (k.category?.toLowerCase().includes(searchLower) || false)
      );
    }

    // 排序
    filtered.sort((a, b) => {
      const aValue = a[params.sortBy as keyof MonitoringKeyword];
      const bValue = b[params.sortBy as keyof MonitoringKeyword];
      
      if (aValue < bValue) return params.sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return params.sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    // 分頁
    const page = params.page || 1;
    const limit = params.limit || 20;
    const offset = (page - 1) * limit;
    const paginatedKeywords = filtered.slice(offset, offset + limit);

    return {
      keywords: paginatedKeywords,
      total: filtered.length,
    };
  }

  async createKeyword(userId: string, data: CreateKeywordRequest): Promise<MonitoringKeyword> {
    const newKeyword: MonitoringKeyword = {
      id: Date.now().toString(),
      userId,
      ...data,
      isActive: true,
      lastMonitoredAt: undefined,
      nextMonitoringAt: this.calculateNextMonitoring(data.monitoringFrequency || 'daily'),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.keywords.push(newKeyword);
    return newKeyword;
  }

  async updateKeyword(
    keywordId: string, 
    userId: string, 
    data: Partial<CreateKeywordRequest>
  ): Promise<MonitoringKeyword | null> {
    const index = this.keywords.findIndex(k => k.id === keywordId && k.userId === userId);
    
    if (index === -1) {
      return null;
    }

    const updatedKeyword = {
      ...this.keywords[index],
      ...data,
      updatedAt: new Date(),
    };

    // 如果頻率改變，重新計算下次監測時間
    if (data.monitoringFrequency) {
      updatedKeyword.nextMonitoringAt = this.calculateNextMonitoring(data.monitoringFrequency);
    }

    this.keywords[index] = updatedKeyword;
    return updatedKeyword;
  }

  async deleteKeyword(keywordId: string, userId: string): Promise<boolean> {
    const index = this.keywords.findIndex(k => k.id === keywordId && k.userId === userId);
    
    if (index === -1) {
      return false;
    }

    this.keywords.splice(index, 1);
    return true;
  }

  async getKeywordById(keywordId: string, userId: string): Promise<MonitoringKeyword | null> {
    return this.keywords.find(k => k.id === keywordId && k.userId === userId) || null;
  }

  private calculateNextMonitoring(frequency: 'daily' | 'weekly' | 'monthly'): Date {
    const now = new Date();
    
    switch (frequency) {
      case 'daily':
        return new Date(now.getTime() + 24 * 60 * 60 * 1000);
      case 'weekly':
        return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
      case 'monthly':
        return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
      default:
        return new Date(now.getTime() + 24 * 60 * 60 * 1000);
    }
  }
}

const keywordService = new KeywordService();

// 獲取用戶 ID (在實際應用中從 JWT token 或 session 中獲取)
function getUserId(request: NextRequest): string {
  // 模擬用戶 ID
  return 'user1';
}

// GET - 獲取關鍵字列表
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const queryParams = querySchema.parse(Object.fromEntries(searchParams));
    const userId = getUserId(request);

    const { keywords, total } = await keywordService.getKeywords(userId, queryParams);

    const response: ApiResponse<{ keywords: MonitoringKeyword[] }> = {
      success: true,
      data: { keywords },
      pagination: {
        page: queryParams.page,
        limit: queryParams.limit,
        total,
        totalPages: Math.ceil(total / queryParams.limit),
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('獲取關鍵字列表失敗:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '請求參數格式錯誤',
          details: error.errors,
        },
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '獲取關鍵字列表失敗',
      },
    }, { status: 500 });
  }
}

// POST - 創建新關鍵字
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const validatedData = createKeywordSchema.parse(body);
    const userId = getUserId(request);

    // 檢查關鍵字是否已存在
    const { keywords } = await keywordService.getKeywords(userId, { page: 1, limit: 1000 });
    const existingKeyword = keywords.find(k => 
      k.keyword.toLowerCase() === validatedData.keyword.toLowerCase()
    );

    if (existingKeyword) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'KEYWORD_EXISTS',
          message: '該關鍵字已存在',
        },
      }, { status: 409 });
    }

    const newKeyword = await keywordService.createKeyword(userId, validatedData);

    const response: ApiResponse<MonitoringKeyword> = {
      success: true,
      data: newKeyword,
    };

    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    console.error('創建關鍵字失敗:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '請求數據格式錯誤',
          details: error.errors,
        },
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '創建關鍵字失敗',
      },
    }, { status: 500 });
  }
}

// PUT - 批量更新關鍵字狀態
export async function PUT(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const { keywordIds, isActive } = z.object({
      keywordIds: z.array(z.string()),
      isActive: z.boolean(),
    }).parse(body);
    
    const userId = getUserId(request);
    const updatedKeywords: MonitoringKeyword[] = [];

    for (const keywordId of keywordIds) {
      const updated = await keywordService.updateKeyword(keywordId, userId, { isActive });
      if (updated) {
        updatedKeywords.push(updated);
      }
    }

    const response: ApiResponse<{ keywords: MonitoringKeyword[] }> = {
      success: true,
      data: { keywords: updatedKeywords },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('批量更新關鍵字失敗:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '請求數據格式錯誤',
          details: error.errors,
        },
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '批量更新關鍵字失敗',
      },
    }, { status: 500 });
  }
}

// DELETE - 批量刪除關鍵字
export async function DELETE(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const keywordIds = searchParams.get('ids')?.split(',') || [];
    
    if (keywordIds.length === 0) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'MISSING_PARAMETER',
          message: '請提供要刪除的關鍵字 ID',
        },
      }, { status: 400 });
    }

    const userId = getUserId(request);
    let deletedCount = 0;

    for (const keywordId of keywordIds) {
      const deleted = await keywordService.deleteKeyword(keywordId, userId);
      if (deleted) {
        deletedCount++;
      }
    }

    const response: ApiResponse<{ deletedCount: number }> = {
      success: true,
      data: { deletedCount },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('批量刪除關鍵字失敗:', error);
    
    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '批量刪除關鍵字失敗',
      },
    }, { status: 500 });
  }
}
