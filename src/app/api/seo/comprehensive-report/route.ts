import { NextRequest, NextResponse } from 'next/server';
import { SEOReportGenerator, SEOReportData, SEOReportResult } from '@/services/seo-report-generator';
import { logger } from '@/lib/monitoring/logger';

export const runtime = 'nodejs';

interface ComprehensiveReportRequest {
  url: string;
  title?: string;
  content: string;
  targetKeywords: string[];
  competitorUrls?: string[];
  businessType?: 'ecommerce' | 'blog' | 'corporate' | 'local' | 'saas';
  targetMarket?: 'taiwan' | 'hongkong' | 'macau' | 'all';
  includeCompetitorAnalysis?: boolean;
  includeTechnicalAnalysis?: boolean;
  includeContentAnalysis?: boolean;
  format?: 'json' | 'markdown' | 'pdf';
}

/**
 * POST /api/seo/comprehensive-report
 * 生成詳細的 SEO 分析結果和優化建議報告
 */
export async function POST(request: NextRequest) {
  const requestId = `seo_report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  try {
    const body: ComprehensiveReportRequest = await request.json();
    
    // 驗證請求
    if (!body.url || !body.content) {
      return NextResponse.json(
        { 
          error: '必須提供網站 URL 和內容',
          code: 'MISSING_REQUIRED_FIELDS' 
        },
        { status: 400 }
      );
    }

    if (!body.targetKeywords || body.targetKeywords.length === 0) {
      return NextResponse.json(
        { 
          error: '必須提供至少一個目標關鍵字',
          code: 'MISSING_TARGET_KEYWORDS' 
        },
        { status: 400 }
      );
    }

    if (body.content.length < 100) {
      return NextResponse.json(
        { 
          error: '內容長度必須至少100個字元',
          code: 'CONTENT_TOO_SHORT' 
        },
        { status: 400 }
      );
    }

    logger.info('開始生成 SEO 綜合報告', { 
      requestId,
      url: body.url,
      contentLength: body.content.length,
      keywordCount: body.targetKeywords.length,
      businessType: body.businessType,
      targetMarket: body.targetMarket,
      format: body.format || 'json'
    });

    const startTime = Date.now();
    const reportGenerator = new SEOReportGenerator();

    // 準備報告數據
    const reportData: SEOReportData = {
      url: body.url,
      title: body.title,
      content: body.content,
      targetKeywords: body.targetKeywords,
      competitorUrls: body.competitorUrls,
      businessType: body.businessType || 'corporate',
      targetMarket: body.targetMarket || 'all'
    };

    // 生成綜合報告
    const report = await reportGenerator.generateComprehensiveReport(reportData);
    
    const processingTime = Date.now() - startTime;

    // 根據格式返回結果
    switch (body.format) {
      case 'markdown':
        const markdownReport = await generateMarkdownReport(report);
        return NextResponse.json({
          success: true,
          requestId,
          data: {
            format: 'markdown',
            content: markdownReport,
            metadata: report.metadata
          },
          processingTime
        });

      case 'pdf':
        // TODO: 實現 PDF 生成
        return NextResponse.json({
          success: true,
          requestId,
          data: {
            format: 'pdf',
            downloadUrl: `/api/reports/pdf?reportId=${report.metadata.reportId}`,
            metadata: report.metadata
          },
          processingTime
        });

      default: // json
        return NextResponse.json({
          success: true,
          requestId,
          data: {
            format: 'json',
            report,
            metadata: report.metadata
          },
          processingTime
        });
    }

  } catch (error) {
    logger.error('SEO 綜合報告生成失敗', { error, requestId });
    
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : '內部伺服器錯誤',
        code: 'REPORT_GENERATION_FAILED',
        requestId
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/seo/comprehensive-report
 * 獲取報告生成服務狀態
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const reportId = searchParams.get('reportId');

    if (reportId) {
      // 獲取特定報告狀態（實際實現中應該從數據庫查詢）
      return NextResponse.json({
        success: true,
        data: {
          reportId,
          status: 'completed',
          downloadUrl: `/api/reports/pdf?reportId=${reportId}`,
          createdAt: new Date().toISOString()
        }
      });
    }

    // 返回服務狀態
    return NextResponse.json({
      success: true,
      data: {
        serviceStatus: 'operational',
        version: '1.0.0',
        supportedFormats: ['json', 'markdown', 'pdf'],
        supportedBusinessTypes: ['ecommerce', 'blog', 'corporate', 'local', 'saas'],
        supportedMarkets: ['taiwan', 'hongkong', 'macau', 'all'],
        features: {
          technicalAnalysis: true,
          contentOptimization: true,
          competitorAnalysis: true,
          actionPlan: true,
          roiProjection: true,
          traditionalChinese: true
        }
      }
    });

  } catch (error) {
    logger.error('獲取報告服務狀態失敗', { error });
    
    return NextResponse.json(
      { 
        success: false,
        error: '無法獲取服務狀態' 
      },
      { status: 500 }
    );
  }
}

/**
 * 生成 Markdown 格式報告
 */
async function generateMarkdownReport(report: any): Promise<string> {
  const { executiveSummary, technicalAnalysis, contentOptimization, competitorAnalysis, actionPlan, roiProjection, metadata } = report;

  return `# AI SEO 優化王 - 詳細 SEO 分析結果與優化建議報告

## 📊 執行摘要

**分析時間**: ${new Date(metadata.generatedAt).toLocaleString('zh-TW')}  
**報告 ID**: ${metadata.reportId}  
**AI 模型**: ${metadata.aiModel}  
**目標市場**: ${metadata.targetMarket}  

### 🎯 整體 SEO 評分
- **綜合評分**: ${executiveSummary.overallScore}/100 ${getScoreEmoji(executiveSummary.overallScore)}
- **技術 SEO**: ${executiveSummary.technicalScore}/100
- **內容品質**: ${executiveSummary.contentScore}/100
- **用戶體驗**: ${executiveSummary.userExperienceScore}/100
- **競爭力**: ${executiveSummary.competitivenessScore}/100

---

## 🔧 技術分析部分

### 1. Core Web Vitals 指標分析

| 指標 | 目前數值 | Google 標準 | 評級 |
|------|----------|-------------|------|
| **LCP** | ${technicalAnalysis.coreWebVitals.lcp}ms | <2500ms | ${technicalAnalysis.coreWebVitals.lcp > 2500 ? '⚠️ 需要改善' : '✅ 良好'} |
| **FID** | ${technicalAnalysis.coreWebVitals.fid}ms | <100ms | ${technicalAnalysis.coreWebVitals.fid > 100 ? '⚠️ 需要改善' : '✅ 良好'} |
| **CLS** | ${technicalAnalysis.coreWebVitals.cls} | <0.1 | ${technicalAnalysis.coreWebVitals.cls > 0.1 ? '⚠️ 需要改善' : '✅ 良好'} |

#### 🚀 優化建議
${technicalAnalysis.coreWebVitals.recommendations.map((rec: string, index: number) => `${index + 1}. ${rec}`).join('\n')}

### 2. 行動裝置友善性評估

- **行動友善評分**: ${technicalAnalysis.mobileOptimization.score}/100 ${getScoreEmoji(technicalAnalysis.mobileOptimization.score)}

#### 🔧 行動裝置優化建議
${technicalAnalysis.mobileOptimization.recommendations.map((rec: string, index: number) => `${index + 1}. ${rec}`).join('\n')}

### 3. 結構化資料驗證

- **覆蓋率**: ${technicalAnalysis.structuredData.coverage}%
- **已實施的 Schema**: ${technicalAnalysis.structuredData.implementedSchemas.join(', ')}

#### ✅ 結構化資料建議
${technicalAnalysis.structuredData.recommendations.map((rec: string, index: number) => `${index + 1}. ${rec}`).join('\n')}

### 4. 連結健康度檢查

- **內部連結**: ${technicalAnalysis.linkHealth.internalLinks} 個
- **外部連結**: ${technicalAnalysis.linkHealth.externalLinks} 個
- **失效連結**: ${technicalAnalysis.linkHealth.brokenLinks} 個

#### 🔧 連結優化建議
${technicalAnalysis.linkHealth.recommendations.map((rec: string, index: number) => `${index + 1}. ${rec}`).join('\n')}

---

## 📝 內容優化建議

### 1. 關鍵字分析
${contentOptimization.keywordAnalysis.recommendations.map((rec: string, index: number) => `${index + 1}. ${rec}`).join('\n')}

### 2. 標題結構優化
- **H1 標籤**: ${contentOptimization.headingOptimization.h1Count} 個
- **H2 標籤**: ${contentOptimization.headingOptimization.h2Count} 個
- **H3 標籤**: ${contentOptimization.headingOptimization.h3Count} 個

#### 建議
${contentOptimization.headingOptimization.recommendations.map((rec: string, index: number) => `${index + 1}. ${rec}`).join('\n')}

### 3. Meta 標籤優化
- **Title 長度**: ${contentOptimization.metaTagOptimization.titleLength} 字元
- **描述長度**: ${contentOptimization.metaTagOptimization.descriptionLength} 字元
- **重複 Title**: ${contentOptimization.metaTagOptimization.duplicateTitles} 個
- **重複描述**: ${contentOptimization.metaTagOptimization.duplicateDescriptions} 個

#### 建議
${contentOptimization.metaTagOptimization.recommendations.map((rec: string, index: number) => `${index + 1}. ${rec}`).join('\n')}

### 4. 圖片優化
- **總圖片數**: ${contentOptimization.imageOptimization.totalImages} 張
- **有 Alt 標籤**: ${contentOptimization.imageOptimization.imagesWithAlt} 張
- **已優化**: ${contentOptimization.imageOptimization.optimizedImages} 張

#### 建議
${contentOptimization.imageOptimization.recommendations.map((rec: string, index: number) => `${index + 1}. ${rec}`).join('\n')}

---

## 🏆 競爭對手分析

### 競爭對手排名
${competitorAnalysis.competitors.map((comp: any, index: number) => 
  `${index + 1}. **${comp.name}** - 域名權重: ${comp.domainAuthority}, 有機流量: ${comp.organicTraffic.toLocaleString()}/月`
).join('\n')}

### 關鍵字機會
${competitorAnalysis.keywordGaps.map((gap: any, index: number) => 
  `${index + 1}. **${gap.keyword}** - 搜尋量: ${gap.searchVolume.toLocaleString()}/月, 難度: ${gap.difficulty}`
).join('\n')}

### 內容策略建議
${competitorAnalysis.contentStrategy.map((strategy: string, index: number) => `${index + 1}. ${strategy}`).join('\n')}

---

## 🎯 可執行的改進計劃

### 第一階段：${actionPlan.phase1.title} (${actionPlan.phase1.duration})
${actionPlan.phase1.tasks.map((task: any, index: number) => 
  `${index + 1}. **${task.task}** (${task.priority.toUpperCase()})
   - 預估時間: ${task.estimatedTime}
   - 資源需求: ${task.resources}
   - 預期效果: ${task.expectedImpact}`
).join('\n\n')}

### 第二階段：${actionPlan.phase2.title} (${actionPlan.phase2.duration})
${actionPlan.phase2.tasks.map((task: any, index: number) => 
  `${index + 1}. **${task.task}** (${task.priority.toUpperCase()})
   - 預估時間: ${task.estimatedTime}
   - 資源需求: ${task.resources}
   - 預期效果: ${task.expectedImpact}`
).join('\n\n')}

### 第三階段：${actionPlan.phase3.title} (${actionPlan.phase3.duration})
${actionPlan.phase3.tasks.map((task: any, index: number) => 
  `${index + 1}. **${task.task}** (${task.priority.toUpperCase()})
   - 預估時間: ${task.estimatedTime}
   - 資源需求: ${task.resources}
   - 預期效果: ${task.expectedImpact}`
).join('\n\n')}

### 第四階段：${actionPlan.phase4.title} (${actionPlan.phase4.duration})
${actionPlan.phase4.tasks.map((task: any, index: number) => 
  `${index + 1}. **${task.task}** (${task.priority.toUpperCase()})
   - 預估時間: ${task.estimatedTime}
   - 資源需求: ${task.resources}
   - 預期效果: ${task.expectedImpact}`
).join('\n\n')}

---

## 📈 預期 SEO 效果和 ROI 評估

### 🎯 3 個月預期成果
- **整體 SEO 評分**: ${executiveSummary.overallScore} → ${executiveSummary.overallScore + roiProjection.threeMonthProjection.overallScoreImprovement} (+${roiProjection.threeMonthProjection.overallScoreImprovement} 分)
- **有機流量增長**: +${roiProjection.threeMonthProjection.organicTrafficGrowth}%
- **關鍵字排名**: +${roiProjection.threeMonthProjection.keywordRankingIncrease}%
- **轉換率提升**: +${roiProjection.threeMonthProjection.conversionRateImprovement}%

### 💰 ROI 計算
**投資成本**: NT$ ${roiProjection.investment.total.toLocaleString()}
- 技術優化: NT$ ${roiProjection.investment.technicalOptimization.toLocaleString()}
- 內容製作: NT$ ${roiProjection.investment.contentCreation.toLocaleString()}
- 工具和監控: NT$ ${roiProjection.investment.toolsAndMonitoring.toLocaleString()}

**預期收益**: NT$ ${roiProjection.expectedReturns.total.toLocaleString()} (年化)
- 有機流量價值提升: NT$ ${roiProjection.expectedReturns.organicTrafficValue.toLocaleString()}
- 轉換率改善收益: NT$ ${roiProjection.expectedReturns.conversionImprovement.toLocaleString()}

**ROI**: ${roiProjection.roi}% (年化投資報酬率)

---

## 📞 後續支援和監控

### 🔍 監控建議
1. **每週監控** - Core Web Vitals 指標、關鍵字排名變化
2. **每月報告** - SEO 綜合表現報告、競爭對手分析更新

---

*本報告由 AI SEO 優化王系統自動生成，結合 ${metadata.aiModel} AI 分析和專業 SEO 知識，為${metadata.targetMarket}市場量身定制。*

**報告版本**: ${metadata.version}  
**生成時間**: ${new Date(metadata.generatedAt).toLocaleString('zh-TW')}  
**有效期限**: ${new Date(metadata.validUntil).toLocaleDateString('zh-TW')}  
**下次更新**: ${new Date(metadata.nextUpdateDue).toLocaleDateString('zh-TW')}`;
}

/**
 * 根據分數獲取表情符號
 */
function getScoreEmoji(score: number): string {
  if (score >= 90) return '⭐⭐⭐⭐⭐';
  if (score >= 80) return '⭐⭐⭐⭐';
  if (score >= 70) return '⭐⭐⭐';
  if (score >= 60) return '⭐⭐';
  return '⭐';
}
