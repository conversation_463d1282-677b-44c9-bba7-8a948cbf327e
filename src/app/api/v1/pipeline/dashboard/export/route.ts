import { NextRequest, NextResponse } from 'next/server';

const BACKEND_URL = process.env.FASTAPI_BACKEND_URL || 'http://localhost:8000';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format') || 'json';
    const timeRange = searchParams.get('time_range') || '1h';
    
    const response = await fetch(`${BACKEND_URL}/api/v1/pipeline/dashboard/export?format=${format}&time_range=${timeRange}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': request.headers.get('Authorization') || '',
      },
    });

    if (response.ok) {
      const blob = await response.blob();
      const headers = new Headers();
      
      // 設置適當的 Content-Type
      switch (format) {
        case 'csv':
          headers.set('Content-Type', 'text/csv');
          break;
        case 'prometheus':
          headers.set('Content-Type', 'text/plain');
          break;
        default:
          headers.set('Content-Type', 'application/json');
      }
      
      headers.set('Content-Disposition', `attachment; filename="pipeline_metrics.${format}"`);
      
      return new NextResponse(blob, {
        status: 200,
        headers,
      });
    } else {
      const errorData = await response.json();
      return NextResponse.json(errorData, { status: response.status });
    }
  } catch (error) {
    console.error('Export API error:', error);
    return NextResponse.json(
      { success: false, message: '無法導出指標數據' }, 
      { status: 500 }
    );
  }
} 