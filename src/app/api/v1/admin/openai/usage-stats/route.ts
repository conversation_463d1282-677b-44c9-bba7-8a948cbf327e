import { NextRequest, NextResponse } from 'next/server';

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:8000';
const REQUEST_TIMEOUT = 10000; // 10 秒

export async function GET(request: NextRequest) {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), REQUEST_TIMEOUT);

    const response = await fetch(`${BACKEND_URL}/api/v1/admin/openai/usage-stats`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`Backend error: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('OpenAI usage stats GET error:', error);
    
    // 返回默認使用統計而不是錯誤
    return NextResponse.json({
      success: true,
      message: "使用默認統計",
      data: {
        total_requests: 0,
        successful_requests: 0,
        failed_requests: 0,
        total_tokens: 0,
        total_prompt_tokens: 0,
        total_completion_tokens: 0,
        total_estimated_cost: 0,
        average_response_time: 0,
        last_request_at: null,
        today: {
          requests: 0,
          tokens: 0,
          estimated_cost: 0
        },
        this_month: {
          requests: 0,
          tokens: 0,
          estimated_cost: 0
        }
      }
    });
  }
} 