import { NextRequest, NextResponse } from 'next/server';

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:8000';

// 增加請求超時時間
const REQUEST_TIMEOUT = 10000; // 10 秒

export async function GET(request: NextRequest) {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), REQUEST_TIMEOUT);

    const response = await fetch(`${BACKEND_URL}/api/v1/admin/openai/settings`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`Backend error: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('OpenAI settings GET error:', error);
    
    // 返回默認設置而不是錯誤，讓頁面可以正常加載
    return NextResponse.json({
      success: true,
      message: "使用默認設置",
      data: {
        api_key_masked: '',
        model: 'gpt-4o-mini',
        temperature: 0.7,
        max_tokens: 4000,
        timeout: 30000,
        max_retries: 3,
        is_configured: false
      }
    });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    
    const response = await fetch(`${BACKEND_URL}/api/v1/admin/openai/settings`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`Backend error: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('OpenAI settings PUT error:', error);
    return NextResponse.json(
      { error: '更新 OpenAI 設置失敗' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const response = await fetch(`${BACKEND_URL}/api/v1/admin/openai/settings`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Backend error: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('OpenAI settings DELETE error:', error);
    return NextResponse.json(
      { error: '重置 OpenAI 設置失敗' },
      { status: 500 }
    );
  }
} 