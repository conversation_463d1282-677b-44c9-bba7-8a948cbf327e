import { NextRequest, NextResponse } from 'next/server';

// WebSocket 連接管理器
class WebSocketManager {
  private connections: Map<string, any> = new Map();
  private subscriptions: Map<string, Set<string>> = new Map();

  addConnection(connectionId: string, socket: any) {
    this.connections.set(connectionId, socket);
    console.log(`WebSocket 連接已添加: ${connectionId}`);
  }

  removeConnection(connectionId: string) {
    this.connections.delete(connectionId);
    // 清理訂閱
    this.subscriptions.forEach((subscribers, topic) => {
      subscribers.delete(connectionId);
    });
    console.log(`WebSocket 連接已移除: ${connectionId}`);
  }

  subscribe(connectionId: string, topic: string, filters?: any) {
    if (!this.subscriptions.has(topic)) {
      this.subscriptions.set(topic, new Set());
    }
    this.subscriptions.get(topic)!.add(connectionId);
    
    console.log(`連接 ${connectionId} 已訂閱主題: ${topic}`);
    
    // 發送初始數據
    this.sendInitialData(connectionId, topic, filters);
  }

  unsubscribe(connectionId: string, topic: string) {
    const subscribers = this.subscriptions.get(topic);
    if (subscribers) {
      subscribers.delete(connectionId);
    }
    console.log(`連接 ${connectionId} 已取消訂閱主題: ${topic}`);
  }

  broadcast(topic: string, data: any) {
    const subscribers = this.subscriptions.get(topic);
    if (!subscribers) return;

    subscribers.forEach(connectionId => {
      const socket = this.connections.get(connectionId);
      if (socket) {
        try {
          socket.send(JSON.stringify({
            type: 'data',
            topic: topic,
            data: data,
            timestamp: new Date().toISOString()
          }));
        } catch (error) {
          console.error(`廣播失敗 ${connectionId}:`, error);
          this.removeConnection(connectionId);
        }
      }
    });
  }

  private sendInitialData(connectionId: string, topic: string, filters?: any) {
    const socket = this.connections.get(connectionId);
    if (!socket) return;

    let initialData;
    
    switch (topic) {
      case 'queries':
        initialData = this.generateQueryData(filters);
        break;
      case 'trends':
        initialData = this.generateTrendData();
        break;
      case 'analytics':
        initialData = this.generateAnalyticsData();
        break;
      default:
        initialData = { message: `歡迎訂閱 ${topic}` };
    }

    try {
      socket.send(JSON.stringify({
        type: 'initial',
        topic: topic,
        data: initialData,
        timestamp: new Date().toISOString()
      }));
    } catch (error) {
      console.error(`發送初始數據失敗:`, error);
    }
  }

  private generateQueryData(filters?: any) {
    return {
      recent_queries: [
        {
          id: Date.now().toString(),
          query: 'AI SEO 優化技巧',
          source: 'web',
          intent: 'seo_optimization',
          confidence: 0.92,
          timestamp: new Date().toISOString()
        }
      ],
      stats: {
        queries_per_minute: Math.floor(Math.random() * 20) + 5,
        active_users: Math.floor(Math.random() * 100) + 50
      }
    };
  }

  private generateTrendData() {
    return {
      current_trends: [
        { keyword: 'AI SEO', growth: '+185%' },
        { keyword: '語音搜索', growth: '+89%' },
        { keyword: '本地化優化', growth: '+67%' }
      ],
      peak_hours: ['14:00', '15:00', '16:00']
    };
  }

  private generateAnalyticsData() {
    return {
      realtime_metrics: {
        total_queries_today: Math.floor(Math.random() * 1000) + 2000,
        success_rate: (Math.random() * 0.05 + 0.95).toFixed(3),
        avg_response_time: Math.floor(Math.random() * 100) + 50,
        active_sessions: Math.floor(Math.random() * 50) + 100
      }
    };
  }
}

const wsManager = new WebSocketManager();

// 實時數據推送服務
class RealTimeDataService {
  private intervals: Map<string, NodeJS.Timeout> = new Map();

  start() {
    // 每秒推送實時查詢數據
    const queryInterval = setInterval(() => {
      const data = this.generateQueryUpdate();
      wsManager.broadcast('queries', data);
    }, 1000);

    // 每5秒推送趨勢數據
    const trendInterval = setInterval(() => {
      const data = this.generateTrendUpdate();
      wsManager.broadcast('trends', data);
    }, 5000);

    // 每10秒推送分析數據
    const analyticsInterval = setInterval(() => {
      const data = this.generateAnalyticsUpdate();
      wsManager.broadcast('analytics', data);
    }, 10000);

    this.intervals.set('queries', queryInterval);
    this.intervals.set('trends', trendInterval);
    this.intervals.set('analytics', analyticsInterval);

    console.log('實時數據推送服務已啟動');
  }

  stop() {
    this.intervals.forEach((interval, key) => {
      clearInterval(interval);
      console.log(`停止 ${key} 數據推送`);
    });
    this.intervals.clear();
  }

  private generateQueryUpdate() {
    return {
      new_query: {
        id: Date.now().toString(),
        query: this.getRandomQuery(),
        source: Math.random() > 0.5 ? 'web' : 'api',
        intent: this.getRandomIntent(),
        confidence: (Math.random() * 0.3 + 0.7).toFixed(2),
        timestamp: new Date().toISOString()
      },
      stats_update: {
        queries_per_minute: Math.floor(Math.random() * 20) + 5,
        active_users: Math.floor(Math.random() * 100) + 50
      }
    };
  }

  private generateTrendUpdate() {
    return {
      trending_now: [
        { keyword: 'AI 內容生成', mentions: Math.floor(Math.random() * 50) + 20 },
        { keyword: 'SEO 自動化', mentions: Math.floor(Math.random() * 40) + 15 },
        { keyword: '競爭分析', mentions: Math.floor(Math.random() * 30) + 10 }
      ],
      momentum_changes: [
        { topic: 'voice_search', change: '+12%' },
        { topic: 'local_seo', change: '+8%' }
      ]
    };
  }

  private generateAnalyticsUpdate() {
    return {
      metrics: {
        total_queries_today: Math.floor(Math.random() * 1000) + 2000,
        success_rate: (Math.random() * 0.05 + 0.95).toFixed(3),
        avg_response_time: Math.floor(Math.random() * 100) + 50,
        error_count: Math.floor(Math.random() * 5),
        peak_concurrent_users: Math.floor(Math.random() * 200) + 100
      },
      alerts: Math.random() > 0.8 ? [
        {
          type: 'performance',
          message: '響應時間超過閾值',
          severity: 'warning',
          timestamp: new Date().toISOString()
        }
      ] : []
    };
  }

  private getRandomQuery(): string {
    const queries = [
      'AI SEO 最佳實踐',
      '關鍵字排名優化',
      '競爭對手分析',
      '內容策略規劃',
      '網站性能監控',
      '社交媒體 SEO',
      '本地搜索優化',
      '移動端 SEO'
    ];
    return queries[Math.floor(Math.random() * queries.length)];
  }

  private getRandomIntent(): string {
    const intents = [
      'product_research',
      'seo_optimization', 
      'competitor_analysis',
      'pricing_inquiry',
      'technical_support'
    ];
    return intents[Math.floor(Math.random() * intents.length)];
  }
}

const dataService = new RealTimeDataService();

// 檢查是否已啟動服務
let serviceStarted = false;

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    switch (action) {
      case 'status':
        return NextResponse.json({
          success: true,
          status: serviceStarted ? 'running' : 'stopped',
          connections: wsManager['connections'].size,
          subscriptions: Array.from(wsManager['subscriptions'].keys())
        });

      case 'start':
        if (!serviceStarted) {
          dataService.start();
          serviceStarted = true;
        }
        return NextResponse.json({
          success: true,
          message: '實時數據服務已啟動'
        });

      case 'stop':
        if (serviceStarted) {
          dataService.stop();
          serviceStarted = false;
        }
        return NextResponse.json({
          success: true,
          message: '實時數據服務已停止'
        });

      default:
        return NextResponse.json({
          success: true,
          message: 'WebSocket API 服務正常運行',
          endpoints: {
            status: '/api/websocket?action=status',
            start: '/api/websocket?action=start',
            stop: '/api/websocket?action=stop'
          }
        });
    }

  } catch (error) {
    console.error('WebSocket API 錯誤:', error);
    return NextResponse.json(
      { error: '服務器內部錯誤' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, connectionId, topic, filters } = body;

    switch (action) {
      case 'subscribe':
        wsManager.subscribe(connectionId, topic, filters);
        break;

      case 'unsubscribe':
        wsManager.unsubscribe(connectionId, topic);
        break;

      case 'broadcast':
        const { data } = body;
        wsManager.broadcast(topic, data);
        break;

      default:
        return NextResponse.json(
          { error: '不支援的操作' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      message: `操作 ${action} 執行成功`
    });

  } catch (error) {
    console.error('WebSocket 操作錯誤:', error);
    return NextResponse.json(
      { error: '操作執行失敗' },
      { status: 500 }
    );
  }
}

// 導出 WebSocket 管理器供其他模組使用
export { wsManager }; 