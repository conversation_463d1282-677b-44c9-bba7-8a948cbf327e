import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';

// 模擬數據庫操作
interface ProcessedQuery {
  id: string;
  query_text: string;
  normalized_query: string;
  source: string;
  user_id?: string;
  session_id?: string;
  intent_type?: string;
  intent_confidence?: number;
  timestamp: string;
  metadata?: any;
}

// 查詢處理器
class QueryProcessor {
  async process(data: any): Promise<ProcessedQuery> {
    // 正規化查詢文本
    const normalizedQuery = data.text
      .toLowerCase()
      .trim()
      .replace(/[^\w\s]/g, ' ')
      .replace(/\s+/g, ' ');

    // 基礎意圖分類（簡化版）
    const intentClassification = this.classifyIntent(normalizedQuery);

    return {
      id: uuidv4(),
      query_text: data.text,
      normalized_query: normalizedQuery,
      source: data.source,
      user_id: data.userId,
      session_id: data.sessionId || uuidv4(),
      intent_type: intentClassification.intent,
      intent_confidence: intentClassification.confidence,
      timestamp: new Date().toISOString(),
      metadata: data.metadata
    };
  }

  private classifyIntent(query: string): { intent: string; confidence: number } {
    // 簡化的意圖分類邏輯
    const patterns = [
      { intent: 'product_research', keywords: ['產品', '研究', '分析', '市場'], confidence: 0.85 },
      { intent: 'seo_optimization', keywords: ['seo', 'optimization', '優化', '排名'], confidence: 0.90 },
      { intent: 'competitor_analysis', keywords: ['競爭', '對手', '比較', 'competitor'], confidence: 0.88 },
      { intent: 'pricing_inquiry', keywords: ['價格', '費用', '價錢', 'pricing', 'cost'], confidence: 0.82 },
      { intent: 'technical_support', keywords: ['問題', '錯誤', '幫助', 'support', 'error'], confidence: 0.80 },
    ];

    for (const pattern of patterns) {
      if (pattern.keywords.some(keyword => query.includes(keyword))) {
        return { intent: pattern.intent, confidence: pattern.confidence };
      }
    }

    return { intent: 'general_inquiry', confidence: 0.50 };
  }
}

// 分析佇列模擬
class AnalyticsQueue {
  async push(query: ProcessedQuery): Promise<void> {
    // 這裡可以集成真實的佇列系統如 Redis 或 Bull
    console.log('推送到分析佇列:', query.id);
    
    // 模擬異步處理
    setTimeout(() => {
      this.processAnalytics(query);
    }, 1000);
  }

  private async processAnalytics(query: ProcessedQuery): Promise<void> {
    // 實時分析處理
    console.log('處理分析:', query.id);
    
    // 這裡可以觸發各種分析
    // - 更新趨勢統計
    // - 更新用戶行為分析
    // - 觸發 AI 模型預測
    // - 發送實時通知
  }
}

const queryProcessor = new QueryProcessor();
const analyticsQueue = new AnalyticsQueue();

// 模擬數據庫
const mockDatabase = {
  queries: new Map<string, ProcessedQuery>(),
  
  async insert(query: ProcessedQuery): Promise<void> {
    this.queries.set(query.id, query);
    console.log(`查詢已存儲: ${query.id}`);
  },

  async find(filters: any = {}): Promise<ProcessedQuery[]> {
    const queries = Array.from(this.queries.values());
    
    // 簡單過濾邏輯
    if (filters.source) {
      return queries.filter(q => q.source === filters.source);
    }
    if (filters.intent_type) {
      return queries.filter(q => q.intent_type === filters.intent_type);
    }
    
    return queries;
  }
};

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { query, source, userId, metadata } = body;

    // 輸入驗證
    if (!query || typeof query !== 'string') {
      return NextResponse.json(
        { error: '查詢文本不能為空' },
        { status: 400 }
      );
    }

    // 處理查詢
    const processedQuery = await queryProcessor.process({
      text: query,
      source: source || 'web',
      userId: userId,
      sessionId: request.headers.get('x-session-id'),
      metadata: metadata
    });

    // 存儲到數據庫
    await mockDatabase.insert(processedQuery);

    // 觸發實時分析
    await analyticsQueue.push(processedQuery);

    return NextResponse.json(
      { 
        success: true, 
        id: processedQuery.id,
        intent: processedQuery.intent_type,
        confidence: processedQuery.intent_confidence
      },
      { status: 201 }
    );

  } catch (error) {
    console.error('查詢收集錯誤:', error);
    return NextResponse.json(
      { error: '服務器內部錯誤' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const source = searchParams.get('source');
    const intent = searchParams.get('intent');
    const limit = parseInt(searchParams.get('limit') || '50');

    const filters: any = {};
    if (source) filters.source = source;
    if (intent) filters.intent_type = intent;

    const queries = await mockDatabase.find(filters);
    
    // 排序並限制結果
    const sortedQueries = queries
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);

    // 統計信息
    const stats = {
      total: queries.length,
      by_source: this.groupBy(queries, 'source'),
      by_intent: this.groupBy(queries, 'intent_type'),
      recent_count: queries.filter(q => 
        new Date(q.timestamp).getTime() > Date.now() - 24 * 60 * 60 * 1000
      ).length
    };

    return NextResponse.json({
      success: true,
      data: sortedQueries,
      stats: stats,
      pagination: {
        total: queries.length,
        limit: limit,
        returned: sortedQueries.length
      }
    });

  } catch (error) {
    console.error('查詢獲取錯誤:', error);
    return NextResponse.json(
      { error: '服務器內部錯誤' },
      { status: 500 }
    );
  }
}

// 輔助函數
function groupBy(array: any[], key: string): Record<string, number> {
  return array.reduce((result, item) => {
    const group = item[key] || 'unknown';
    result[group] = (result[group] || 0) + 1;
    return result;
  }, {});
} 