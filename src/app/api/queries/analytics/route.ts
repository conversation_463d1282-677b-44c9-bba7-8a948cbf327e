import { NextRequest, NextResponse } from 'next/server';

// 實時分析數據生成器
class RealTimeAnalytics {
  generateQueryTrends(dateRange: string = 'last7days') {
    const now = new Date();
    const days = this.getDaysFromRange(dateRange);
    const trends = [];

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      trends.push({
        date: date.toISOString().split('T')[0],
        queries: Math.floor(Math.random() * 500) + 100,
        unique_users: Math.floor(Math.random() * 200) + 50,
        avg_confidence: (Math.random() * 0.3 + 0.7).toFixed(2)
      });
    }

    return trends;
  }

  generateIntentDistribution() {
    return [
      { intent: 'product_research', count: 1247, percentage: 35.2 },
      { intent: 'seo_optimization', count: 892, percentage: 25.1 },
      { intent: 'competitor_analysis', count: 634, percentage: 17.9 },
      { intent: 'pricing_inquiry', count: 423, percentage: 11.9 },
      { intent: 'technical_support', count: 356, percentage: 10.0 }
    ];
  }

  generateTopQueries() {
    return [
      { query: 'AI SEO 最佳實踐', count: 156, avg_confidence: 0.92 },
      { query: '競爭對手分析工具', count: 134, avg_confidence: 0.88 },
      { query: '關鍵字排名優化', count: 128, avg_confidence: 0.91 },
      { query: '產品市場研究', count: 115, avg_confidence: 0.85 },
      { query: '網站流量分析', count: 98, avg_confidence: 0.87 },
      { query: 'SEO 內容策略', count: 87, avg_confidence: 0.83 },
      { query: '社交媒體監控', count: 76, avg_confidence: 0.86 },
      { query: '品牌提及追蹤', count: 65, avg_confidence: 0.89 }
    ];
  }

  generateGeographicData() {
    return [
      { country: 'Taiwan', queries: 2456, users: 1234 },
      { country: 'China', queries: 1876, users: 987 },
      { country: 'Hong Kong', queries: 987, users: 456 },
      { country: 'Singapore', queries: 654, users: 321 },
      { country: 'Malaysia', queries: 432, users: 234 }
    ];
  }

  generateRealTimeMetrics() {
    return {
      queries_per_minute: Math.floor(Math.random() * 20) + 5,
      active_users: Math.floor(Math.random() * 100) + 50,
      avg_response_time: (Math.random() * 200 + 100).toFixed(0) + 'ms',
      success_rate: (Math.random() * 0.05 + 0.95).toFixed(3),
      top_intent_current: 'product_research',
      alert_count: Math.floor(Math.random() * 3)
    };
  }

  private getDaysFromRange(range: string): number {
    switch (range) {
      case 'last24hours': return 1;
      case 'last7days': return 7;
      case 'last30days': return 30;
      case 'last90days': return 90;
      default: return 7;
    }
  }
}

const analytics = new RealTimeAnalytics();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'overview';
    const dateRange = searchParams.get('dateRange') || 'last7days';

    let data;

    switch (type) {
      case 'trends':
        data = {
          trends: analytics.generateQueryTrends(dateRange),
          summary: {
            total_queries: 12847,
            growth_rate: '+23.5%',
            peak_hour: '14:00-15:00',
            busiest_day: 'Tuesday'
          }
        };
        break;

      case 'intents':
        data = {
          distribution: analytics.generateIntentDistribution(),
          trends: analytics.generateQueryTrends(dateRange).map(day => ({
            ...day,
            top_intent: 'product_research'
          }))
        };
        break;

      case 'queries':
        data = {
          top_queries: analytics.generateTopQueries(),
          emerging_queries: [
            { query: 'AI 驅動的 SEO', growth: '+145%' },
            { query: '語音搜索優化', growth: '+89%' },
            { query: '本地化 SEO 策略', growth: '+67%' }
          ]
        };
        break;

      case 'geographic':
        data = {
          countries: analytics.generateGeographicData(),
          regions: [
            { region: 'Asia Pacific', percentage: 78.2 },
            { region: 'North America', percentage: 15.4 },
            { region: 'Europe', percentage: 6.4 }
          ]
        };
        break;

      case 'realtime':
        data = {
          metrics: analytics.generateRealTimeMetrics(),
          recent_queries: [
            { query: 'SEO 自動化工具', time: '剛剛', intent: 'product_research' },
            { query: '關鍵字密度分析', time: '1分鐘前', intent: 'seo_optimization' },
            { query: '競品定價策略', time: '2分鐘前', intent: 'competitor_analysis' }
          ]
        };
        break;

      default:
        // 綜合概覽
        data = {
          summary: {
            total_queries: 12847,
            unique_users: 4523,
            avg_confidence: 0.87,
            active_sessions: 234
          },
          trends: analytics.generateQueryTrends(dateRange).slice(-7),
          top_intents: analytics.generateIntentDistribution().slice(0, 3),
          recent_activity: analytics.generateRealTimeMetrics()
        };
    }

    return NextResponse.json({
      success: true,
      type: type,
      dateRange: dateRange,
      data: data,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('分析數據獲取錯誤:', error);
    return NextResponse.json(
      { error: '服務器內部錯誤' },
      { status: 500 }
    );
  }
}

// 提供實時分析配置
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { metric_type, filters, alert_thresholds } = body;

    // 保存分析配置
    const config = {
      id: Date.now().toString(),
      metric_type,
      filters,
      alert_thresholds,
      created_at: new Date().toISOString(),
      status: 'active'
    };

    return NextResponse.json({
      success: true,
      message: '分析配置已保存',
      config: config
    });

  } catch (error) {
    console.error('分析配置錯誤:', error);
    return NextResponse.json(
      { error: '配置保存失敗' },
      { status: 500 }
    );
  }
} 