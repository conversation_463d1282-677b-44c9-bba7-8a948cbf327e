import type { Metadata } from "next";
import { Noto_Sans_TC } from "next/font/google";
import { Toaster } from "sonner";
import dynamic from "next/dynamic";
import Script from "next/script";
import "./globals.css";

// Layout Components - 直接導入以避免 hydration 問題
import Navbar from "@/components/layout/Navbar";

import Footer from "@/components/layout/Footer";

// 客戶端專用組件 - 這些組件確實需要客戶端渲染
const EnhancedWebVitalsReporter = dynamic(() => import("@/components/EnhancedWebVitalsReporter"), {
  ssr: false
});

const HydrationDebugger = dynamic(() => import("@/components/HydrationDebugger"), {
  ssr: false
});

const RequestInterceptor = dynamic(() => import("@/components/RequestInterceptor"), {
  ssr: false
});

// Context Providers
import { AuthProvider } from "@/contexts/AuthContext";
import { SEOProvider } from "@/contexts/SEOContext";
import ErrorBoundary from "@/components/ErrorBoundary";

// Metadata
import { defaultMetadata, structuredData, generateJsonLd } from "@/lib/metadata";

// Web Vitals - 已使用 dynamic import

const notoSansTC = Noto_Sans_TC({
  variable: "--font-noto-sans-tc",
  subsets: ["latin"],
  weight: ["100", "300", "400", "500", "700", "900"],
  display: "swap",
});

export const metadata: Metadata = defaultMetadata;

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-TW" className="scroll-smooth">
      <head>
        {/* 預載入關鍵資源 */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        {/* 全局 NaN 攔截器 - 在所有腳本之前執行 */}
        <Script
          id="nan-interceptor"
          strategy="beforeInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              // 🚨 全局 NaN 攔截器 - 防止任何 NaN 值傳遞給 React DOM
              (function() {
                console.log('🔧 Installing global NaN interceptor...');
                
                // 攔截 Object.defineProperty 以防止 NaN 值設定到 DOM 元素
                const originalDefineProperty = Object.defineProperty;
                Object.defineProperty = function(obj, prop, descriptor) {
                  if (descriptor && descriptor.value !== undefined) {
                    if (typeof descriptor.value === 'number' && isNaN(descriptor.value)) {
                      console.warn('🚨 Global NaN Interceptor: Blocked NaN value for property', prop);
                      descriptor.value = '';
                    } else if (String(descriptor.value) === 'NaN') {
                      console.warn('🚨 Global NaN Interceptor: Blocked "NaN" string for property', prop);
                      descriptor.value = '';
                    }
                  }
                  return originalDefineProperty.call(this, obj, prop, descriptor);
                };
                
                // 攔截 setAttribute 防止 NaN 值設定到 HTML 屬性
                const originalSetAttribute = Element.prototype.setAttribute;
                Element.prototype.setAttribute = function(name, value) {
                  if (typeof value === 'number' && isNaN(value)) {
                    console.warn('🚨 Global NaN Interceptor: Blocked NaN in setAttribute for', name);
                    value = '';
                  } else if (String(value) === 'NaN') {
                    console.warn('🚨 Global NaN Interceptor: Blocked "NaN" string in setAttribute for', name);
                    value = '';
                  }
                  return originalSetAttribute.call(this, name, value);
                };
                
                // 攔截 input value 設定
                Object.defineProperty(HTMLInputElement.prototype, 'value', {
                  get: function() {
                    return this.getAttribute('value') || '';
                  },
                  set: function(val) {
                    if (typeof val === 'number' && isNaN(val)) {
                      console.warn('🚨 Global NaN Interceptor: Blocked NaN in input.value');
                      val = '';
                    } else if (String(val) === 'NaN') {
                      console.warn('🚨 Global NaN Interceptor: Blocked "NaN" string in input.value');
                      val = '';
                    }
                    this.setAttribute('value', String(val));
                  }
                });
                
                console.log('✅ Global NaN interceptor installed successfully');
              })();
            `
          }}
        />
      </head>
      <body className={`${notoSansTC.variable} font-sans antialiased zh-text`}>
        <ErrorBoundary showDetails={process.env.NODE_ENV === 'development'}>
          <AuthProvider>
            <SEOProvider>
              <Navbar />
              <main className="min-h-screen">
                {children}
              </main>
              <Footer />

              {/* Toast 通知系統 */}
              <Toaster
                position="top-right"
                toastOptions={{
                  duration: 4000,
                  style: {
                    background: 'hsl(var(--card))',
                    color: 'hsl(var(--card-foreground))',
                    border: '1px solid hsl(var(--border))',
                  },
                }}
                closeButton
                richColors
              />

              {/* Web Vitals 監控 - 增強版 - 暫時禁用以修復 Request body 錯誤 */}
              {/* <EnhancedWebVitalsReporter /> */}

              {/* Hydration 調試工具 (僅開發環境) */}
              {process.env.NODE_ENV === 'development' && <HydrationDebugger />}

              {/* 請求攔截器 (僅開發環境) - 已禁用以修復 CORS 問題 */}
              {/* {process.env.NODE_ENV === 'development' && <RequestInterceptor />} */}
            </SEOProvider>
          </AuthProvider>
        </ErrorBoundary>

        {/* 使用 Next.js Script 組件確保載入順序一致 */}
        <Script
          id="structured-data-organization"
          type="application/ld+json"
          strategy="beforeInteractive"
          dangerouslySetInnerHTML={{
            __html: generateJsonLd(structuredData.organization),
          }}
        />
        <Script
          id="structured-data-website"
          type="application/ld+json"
          strategy="beforeInteractive"
          dangerouslySetInnerHTML={{
            __html: generateJsonLd(structuredData.website),
          }}
        />
        <Script
          id="structured-data-software"
          type="application/ld+json"
          strategy="beforeInteractive"
          dangerouslySetInnerHTML={{
            __html: generateJsonLd(structuredData.softwareApplication),
          }}
        />
      </body>
    </html>
  );
}
