@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* 淺色主題作為預設 - 優雅的白色和藍色調配色方案 */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 0 0% 100%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 220 14.3% 95.9%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;

    /* 圖表和數據視覺化色彩 - 明亮清新的色調 */
    --chart-1: 221 83% 53%;
    --chart-2: 142 71% 45%;
    --chart-3: 38 92% 50%;
    --chart-4: 271 81% 56%;
    --chart-5: 340 82% 52%;

    /* 側邊欄 - 淺色主題 */
    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 222.2 84% 4.9%;
    --sidebar-primary: 221.2 83.2% 53.3%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 220 14.3% 95.9%;
    --sidebar-accent-foreground: 222.2 84% 4.9%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-ring: 221.2 83.2% 53.3%;

    /* 自定義顏色 - 淺色主題 */
    --primary-light: 221 83% 65%;
    --primary-dark: 221 83% 45%;
    --surface-1: 220 14.3% 95.9%;
    --surface-2: 210 40% 94%;
    --surface-3: 210 40% 92%;
    --surface-hover: 210 40% 90%;
    --surface-active: 210 40% 88%;

    /* 擴展的顏色系統 */
    --accent-green: 142 76% 36%;
    --accent-green-light: 142 69% 58%;
    --accent-green-dark: 142 84% 24%;
    --success: 142 76% 36%;
    --warning: 38 92% 50%;
    --error: 0 84.2% 60.2%;
    --info: 221.2 83.2% 53.3%;

    /* 文字顏色層次 */
    --text-primary: 222.2 84% 4.9%;
    --text-secondary: 215.4 16.3% 46.9%;
    --text-tertiary: 215.4 16.3% 65%;
    --text-disabled: 215.4 16.3% 75%;

    /* 邊框層次 */
    --border-light: 214.3 31.8% 95%;
    --border-medium: 214.3 31.8% 91.4%;
    --border-strong: 214.3 31.8% 85%;

    /* 陰影顏色 */
    --shadow-light: 221 39% 11% / 0.05;
    --shadow-medium: 221 39% 11% / 0.1;
    --shadow-strong: 221 39% 11% / 0.15;
  }

  .dark {
    /* 深色主題移到 .dark 類別下，供選擇性使用 */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;

    /* 圖表和數據視覺化色彩 - 深色主題 */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /* 側邊欄 - 深色主題 */
    --sidebar-background: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 217.2 91.2% 59.8%;
    --sidebar-primary-foreground: 222.2 84% 4.9%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 224.3 76.3% 94.1%;

    /* 自定義顏色 - 深色主題 */
    --primary-light: 217 91% 70%;
    --primary-dark: 217 91% 40%;
    --surface-1: 217 33% 20%;
    --surface-2: 217 33% 25%;
    --surface-3: 217 33% 30%;
    --surface-hover: 217 33% 35%;
    --surface-active: 217 33% 40%;

    /* 擴展的顏色系統 - 深色主題 */
    --accent-green: 142 76% 45%;
    --accent-green-light: 142 69% 65%;
    --accent-green-dark: 142 84% 35%;
    --success: 142 76% 45%;
    --warning: 38 92% 60%;
    --error: 0 62.8% 50%;
    --info: 217.2 91.2% 59.8%;

    /* 文字顏色層次 - 深色主題 */
    --text-primary: 210 40% 98%;
    --text-secondary: 215 20.2% 65.1%;
    --text-tertiary: 215 20.2% 50%;
    --text-disabled: 215 20.2% 35%;

    /* 邊框層次 - 深色主題 */
    --border-light: 217.2 32.6% 25%;
    --border-medium: 217.2 32.6% 17.5%;
    --border-strong: 217.2 32.6% 12%;

    /* 陰影顏色 - 深色主題 */
    --shadow-light: 0 0% 0% / 0.1;
    --shadow-medium: 0 0% 0% / 0.2;
    --shadow-strong: 0 0% 0% / 0.3;
  }
}


@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: 'rlig' 1, 'calt' 1;
  }

  html {
    scroll-behavior: smooth;
  }

  /* 改善全局滾動條樣式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-surface-1;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-border-medium rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-border-strong;
  }

  /* 選擇文字的顏色 */
  ::selection {
    @apply bg-primary/20 text-primary;
  }

  /* 焦點樣式 */
  :focus-visible {
    @apply outline-none ring-2 ring-primary ring-offset-2 ring-offset-background;
  }
}

@layer components {
  /* 繁體中文特殊樣式 */
  .zh-text {
    font-feature-settings: "kern" 1;
    text-rendering: optimizeLegibility;
  }

  /* 自定義滾動條 - 淺色主題樣式 */
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-background;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-secondary rounded;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-primary;
  }

  /* 深色主題的滾動條樣式 */
  .dark .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-surface-2;
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-surface-3;
  }

  /* 按鈕基礎樣式 */
  .btn-base {
    @apply inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-primary {
    @apply btn-base bg-primary text-primary-foreground hover:bg-primary/90 shadow-button hover:shadow-button-hover;
  }

  .btn-secondary {
    @apply btn-base bg-secondary text-secondary-foreground hover:bg-secondary/80 border border-border-medium;
  }

  .btn-ghost {
    @apply btn-base hover:bg-surface-hover hover:text-text-primary;
  }

  /* 卡片樣式 */
  .card-base {
    @apply bg-card text-card-foreground rounded-xl border border-border-light shadow-card;
  }

  .card-hover {
    @apply card-base hover:shadow-card-hover transition-shadow duration-200;
  }

  /* 輸入框樣式 */
  .input-base {
    @apply flex h-10 w-full rounded-lg border border-border-medium bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-text-tertiary focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  /* 文字樣式 */
  .text-gradient {
    @apply bg-gradient-to-r from-primary to-accent-green bg-clip-text text-transparent;
  }

  /* 動畫類別 */
  .animate-fade-in-up {
    animation: fade-in-up 0.6s ease-out;
  }

  .animate-fade-in-down {
    animation: fade-in-down 0.6s ease-out;
  }

  .animate-scale-in {
    animation: scale-in 0.3s ease-out;
  }
}

@layer utilities {
  /* 間距實用類別 */
  .space-y-section {
    @apply space-y-16 md:space-y-24;
  }

  .space-y-content {
    @apply space-y-6 md:space-y-8;
  }

  /* 容器實用類別 */
  .container-section {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* 文字實用類別 */
  .text-balance {
    text-wrap: balance;
  }

  /* 漸變背景 */
  .bg-gradient-primary {
    @apply bg-gradient-to-r from-primary to-primary-light;
  }

  .bg-gradient-accent {
    @apply bg-gradient-to-r from-accent-green to-accent-green-light;
  }

  .bg-gradient-hero {
    @apply bg-gradient-to-br from-primary/10 via-accent-green/5 to-primary/5;
  }
}

/* 自定義動畫關鍵幀 */
@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-down {
  0% {
    opacity: 0;
    transform: translateY(-30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
