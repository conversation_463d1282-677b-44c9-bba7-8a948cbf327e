@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* 淺色主題作為預設 - 優雅的白色和藍色調配色方案 */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 0 0% 100%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 220 14.3% 95.9%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;

    /* 圖表和數據視覺化色彩 - 明亮清新的色調 */
    --chart-1: 221 83% 53%;
    --chart-2: 142 71% 45%;
    --chart-3: 38 92% 50%;
    --chart-4: 271 81% 56%;
    --chart-5: 340 82% 52%;

    /* 側邊欄 - 淺色主題 */
    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 222.2 84% 4.9%;
    --sidebar-primary: 221.2 83.2% 53.3%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 220 14.3% 95.9%;
    --sidebar-accent-foreground: 222.2 84% 4.9%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-ring: 221.2 83.2% 53.3%;

    /* 自定義顏色 - 淺色主題 */
    --primary-light: 221 83% 65%;
    --primary-dark: 221 83% 45%;
    --surface-1: 220 14.3% 95.9%;
    --surface-2: 210 40% 94%;
    --surface-3: 210 40% 92%;
  }

  .dark {
    /* 深色主題移到 .dark 類別下，供選擇性使用 */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;

    /* 圖表和數據視覺化色彩 - 深色主題 */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /* 側邊欄 - 深色主題 */
    --sidebar-background: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 217.2 91.2% 59.8%;
    --sidebar-primary-foreground: 222.2 84% 4.9%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 224.3 76.3% 94.1%;

    /* 自定義顏色 - 深色主題 */
    --primary-light: 217 91% 70%;
    --primary-dark: 217 91% 40%;
    --surface-1: 217 33% 20%;
    --surface-2: 217 33% 25%;
    --surface-3: 217 33% 30%;
  }
}


@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* 繁體中文特殊樣式 */
  .zh-text {
    font-feature-settings: "kern" 1;
    text-rendering: optimizeLegibility;
  }

  /* 自定義滾動條 - 淺色主題樣式 */
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-background;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-secondary rounded;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-primary;
  }

  /* 深色主題的滾動條樣式 */
  .dark .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-surface-2;
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-surface-3;
  }
}
