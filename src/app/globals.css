/* 字體導入優化 - 確保最佳的字體載入和渲染 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Noto+Sans+TC:wght@100;200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* 淺色主題作為預設 - 藍色主色，橘色次色 */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 214.3 94% 57%;         /* 藍色作為主要顏色 */
    --primary-foreground: 0 0% 100%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 33 100% 62%;            /* 橘色作為強調色 */
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 214.3 94% 57%;
    --radius: 0.5rem;

    /* 圖表和數據視覺化色彩 - 以藍色為主的配色 */
    --chart-1: 214 94% 57%;           /* 主要藍色 */
    --chart-2: 33 100% 62%;           /* 橘色 */
    --chart-3: 142 71% 45%;           /* 綠色 */
    --chart-4: 271 81% 56%;           /* 紫色 */
    --chart-5: 340 82% 52%;           /* 粉色 */

    /* 側邊欄 - 淺色主題 */
    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 222.2 84% 4.9%;
    --sidebar-primary: 214.3 94% 57%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 220 14.3% 95.9%;
    --sidebar-accent-foreground: 222.2 84% 4.9%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-ring: 214.3 94% 57%;

    /* 自定義顏色 - 藍色主色系統 */
    --primary-light: 214 94% 70%;     /* 淺藍色 */
    --primary-dark: 214 94% 45%;      /* 深藍色 */
    --surface-1: 220 14.3% 95.9%;
    --surface-2: 210 40% 94%;
    --surface-3: 210 40% 92%;
    --surface-hover: 210 40% 90%;
    --surface-active: 210 40% 88%;

    /* 橘色次要顏色系統 */
    --secondary-orange: 33 100% 62%;      /* 鮮明橘色作為次要顏色 */
    --secondary-orange-light: 33 100% 75%; /* 淺橘色 */
    --secondary-orange-dark: 33 100% 45%;  /* 深橘色 */
    --accent-green: 142 76% 36%;
    --accent-green-light: 142 69% 58%;
    --accent-green-dark: 142 84% 24%;
    --success: 142 76% 36%;
    --warning: 33 100% 62%;               /* 使用橘色作為警告色 */
    --error: 0 84.2% 60.2%;
    --info: 214.3 94% 57%;                /* 使用藍色作為信息色 */

    /* 文字顏色層次 - 優化對比度 */
    --text-primary: 222.2 84% 4.9%;
    --text-secondary: 215.4 20% 40%;  /* 增加對比度 */
    --text-tertiary: 215.4 18% 55%;   /* 增加對比度 */
    --text-disabled: 215.4 16.3% 70%; /* 改善可讀性 */
    --text-muted: 215.4 15% 45%;      /* 新增靜音文字色 */

    /* 邊框層次 */
    --border-light: 214.3 31.8% 95%;
    --border-medium: 214.3 31.8% 91.4%;
    --border-strong: 214.3 31.8% 85%;

    /* 陰影顏色 */
    --shadow-light: 214 39% 11% / 0.05;
    --shadow-medium: 214 39% 11% / 0.1;
    --shadow-strong: 214 39% 11% / 0.15;

    /* 字體配置變數 */
    --font-sans: 'Inter', 'Noto Sans TC', 'Source Han Sans TC', 'PingFang TC', 'Microsoft JhengHei', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-mono: 'SF Mono', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', monospace;

    /* 間距系統 */
    --spacing-xs: 0.25rem;   /* 4px */
    --spacing-sm: 0.5rem;    /* 8px */
    --spacing-md: 1rem;      /* 16px */
    --spacing-lg: 1.5rem;    /* 24px */
    --spacing-xl: 2rem;      /* 32px */
    --spacing-2xl: 3rem;     /* 48px */
    --spacing-3xl: 4rem;     /* 64px */

    /* 字體大小系統 - 改善可讀性 */
    --text-xs: 0.75rem;      /* 12px */
    --text-sm: 0.875rem;     /* 14px */
    --text-base: 1rem;       /* 16px */
    --text-lg: 1.125rem;     /* 18px */
    --text-xl: 1.25rem;      /* 20px */
    --text-2xl: 1.5rem;      /* 24px */
    --text-3xl: 1.875rem;    /* 30px */
    --text-4xl: 2.25rem;     /* 36px */
    --text-5xl: 3rem;        /* 48px */
    --text-6xl: 3.75rem;     /* 60px */

    /* 行高系統 - 優化閱讀體驗 */
    --leading-none: 1;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;
  }

  .dark {
    /* 深色主題移到 .dark 類別下，供選擇性使用 */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 214.3 94% 67%;         /* 深色主題藍色 */
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 33 100% 68%;            /* 深色主題橘色 */
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;

    /* 圖表和數據視覺化色彩 - 深色主題 */
    --chart-1: 214 94% 67%;           /* 深色主題藍色 */
    --chart-2: 33 100% 68%;           /* 深色主題橘色 */
    --chart-3: 160 60% 45%;           /* 綠色 */
    --chart-4: 280 65% 60%;           /* 紫色 */
    --chart-5: 340 75% 55%;           /* 粉色 */

    /* 側邊欄 - 深色主題 */
    --sidebar-background: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 214.3 94% 67%;
    --sidebar-primary-foreground: 222.2 84% 4.9%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 224.3 76.3% 94.1%;

    /* 自定義顏色 - 深色主題 */
    --primary-light: 214 94% 75%;     /* 深色主題淺藍色 */
    --primary-dark: 214 94% 55%;      /* 深色主題深藍色 */
    --surface-1: 217 33% 20%;
    --surface-2: 217 33% 25%;
    --surface-3: 217 33% 30%;
    --surface-hover: 217 33% 35%;
    --surface-active: 217 33% 40%;

    /* 擴展的顏色系統 - 深色主題，橘色作為次要顏色 */
    --secondary-orange: 33 100% 68%;      /* 深色主題下的橘色 */
    --secondary-orange-light: 33 100% 80%; /* 深色主題淺橘色 */
    --secondary-orange-dark: 33 100% 55%;  /* 深色主題深橘色 */
    --accent-green: 142 76% 45%;
    --accent-green-light: 142 69% 65%;
    --accent-green-dark: 142 84% 35%;
    --success: 142 76% 45%;
    --warning: 33 100% 68%;               /* 深色主題使用橘色作為警告色 */
    --error: 0 62.8% 50%;
    --info: 214.3 94% 67%;                /* 深色主題使用藍色作為信息色 */

    /* 文字顏色層次 - 深色主題優化 */
    --text-primary: 210 40% 98%;
    --text-secondary: 215 20.2% 75%;
    --text-tertiary: 215 20.2% 60%;
    --text-disabled: 215 20.2% 45%;
    --text-muted: 215 18% 55%;

    /* 邊框層次 - 深色主題 */
    --border-light: 217.2 32.6% 25%;
    --border-medium: 217.2 32.6% 17.5%;
    --border-strong: 217.2 32.6% 12%;

    /* 陰影顏色 - 深色主題 */
    --shadow-light: 0 0% 0% / 0.1;
    --shadow-medium: 0 0% 0% / 0.2;
    --shadow-strong: 0 0% 0% / 0.3;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  /* 根本字體和頁面設定 - 大幅優化 */
  html {
    scroll-behavior: smooth;
    font-feature-settings: 'rlig' 1, 'calt' 1, 'kern' 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    @apply bg-background text-foreground;
    font-family: var(--font-sans);
    line-height: var(--leading-normal);
    font-weight: 400;
    color: hsl(var(--text-primary));
    
    /* 進階字體渲染優化 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    font-feature-settings: 'rlig' 1, 'calt' 1, 'kern' 1, 'ss01' 1;
    font-optical-sizing: auto;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }

  /* 標題層次結構優化 */
  h1, h2, h3, h4, h5, h6 {
    color: hsl(var(--text-primary));
    font-weight: 700;
    line-height: var(--leading-tight);
    letter-spacing: -0.025em;
    font-feature-settings: 'rlig' 1, 'calt' 1, 'kern' 1;
  }

  h1 {
    font-size: var(--text-4xl);
    line-height: var(--leading-tight);
    font-weight: 800;
    letter-spacing: -0.04em;
  }

  h2 {
    font-size: var(--text-3xl);
    line-height: var(--leading-tight);
    font-weight: 700;
    letter-spacing: -0.035em;
  }

  h3 {
    font-size: var(--text-2xl);
    line-height: var(--leading-snug);
    font-weight: 600;
    letter-spacing: -0.025em;
  }

  h4 {
    font-size: var(--text-xl);
    line-height: var(--leading-snug);
    font-weight: 600;
  }

  h5 {
    font-size: var(--text-lg);
    line-height: var(--leading-snug);
    font-weight: 600;
  }

  h6 {
    font-size: var(--text-base);
    line-height: var(--leading-snug);
    font-weight: 600;
  }

  /* 段落和文字優化 */
  p {
    color: hsl(var(--text-primary));
    line-height: var(--leading-relaxed);
    font-size: var(--text-base);
    margin-bottom: var(--spacing-md);
  }

  /* 鏈接樣式優化 */
  a {
    color: hsl(var(--primary));
    text-decoration: none;
    transition: color 0.2s ease;
    font-weight: 500;
  }

  a:hover {
    color: hsl(var(--primary-dark));
    text-decoration: underline;
    text-decoration-thickness: 2px;
    text-underline-offset: 3px;
  }

  /* 列表樣式優化 */
  ul, ol {
    line-height: var(--leading-relaxed);
    color: hsl(var(--text-primary));
  }

  li {
    margin-bottom: var(--spacing-xs);
  }

  /* 代碼樣式優化 */
  code {
    font-family: var(--font-mono);
    font-size: 0.875em;
    background-color: hsl(var(--surface-1));
    padding: 0.2em 0.4em;
    border-radius: 0.25rem;
    color: hsl(var(--text-primary));
    font-weight: 500;
  }

  pre {
    font-family: var(--font-mono);
    background-color: hsl(var(--surface-1));
    padding: var(--spacing-lg);
    border-radius: var(--radius);
    overflow-x: auto;
    line-height: var(--leading-relaxed);
    color: hsl(var(--text-primary));
  }

  /* 改善全局滾動條樣式 */
  ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-surface-1;
    border-radius: 5px;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-border-medium;
    border-radius: 5px;
    border: 2px solid hsl(var(--surface-1));
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-border-strong;
  }

  /* 選擇文字的顏色 */
  ::selection {
    background-color: hsl(var(--primary) / 0.2);
    color: hsl(var(--primary));
  }

  ::-moz-selection {
    background-color: hsl(var(--primary) / 0.2);
    color: hsl(var(--primary));
  }

  /* 焦點樣式增強 */
  :focus-visible {
    @apply outline-none ring-2 ring-primary ring-offset-2 ring-offset-background;
    border-radius: 0.25rem;
  }

  /* 表單元素基礎樣式 */
  input, textarea, select {
    font-family: var(--font-sans);
    line-height: var(--leading-normal);
    color: hsl(var(--text-primary));
  }

  /* 按鈕基礎樣式 */
  button {
    font-family: var(--font-sans);
    font-weight: 500;
    line-height: var(--leading-normal);
  }
}

@layer components {
  /* 繁體中文特殊樣式增強 */
  .zh-text {
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* 自定義滾動條 - 淺色主題樣式 */
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-background;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-secondary rounded;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-primary;
  }

  /* 深色主題的滾動條樣式 */
  .dark .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-surface-2;
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-surface-3;
  }

  /* 按鈕基礎樣式增強 */
  .btn-base {
    @apply inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200;
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2;
    @apply disabled:pointer-events-none disabled:opacity-50;
    font-family: var(--font-sans);
    line-height: var(--leading-normal);
    font-weight: 500;
    letter-spacing: -0.01em;
  }

  .btn-primary {
    @apply btn-base bg-primary text-primary-foreground hover:bg-primary/90;
    @apply shadow-button hover:shadow-button-hover;
    font-weight: 600;
  }

  .btn-secondary {
    @apply btn-base bg-secondary text-secondary-foreground hover:bg-secondary/80;
    @apply border border-border-medium;
    font-weight: 500;
  }

  .btn-ghost {
    @apply btn-base hover:bg-surface-hover;
    color: hsl(var(--text-primary));
    font-weight: 500;
  }

  /* 卡片樣式增強 */
  .card-base {
    @apply bg-card text-card-foreground rounded-xl border border-border-light shadow-card;
    font-family: var(--font-sans);
  }

  .card-hover {
    @apply card-base hover:shadow-card-hover transition-shadow duration-200;
  }

  /* 輸入框樣式增強 */
  .input-base {
    @apply flex h-10 w-full rounded-lg border border-border-medium bg-background px-3 py-2 text-sm;
    @apply ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium;
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2;
    @apply disabled:cursor-not-allowed disabled:opacity-50;
    
    font-family: var(--font-sans);
    line-height: var(--leading-normal);
    color: hsl(var(--text-primary));
    font-weight: 400;
  }

  .input-base::placeholder {
    color: hsl(var(--text-tertiary));
    font-weight: 400;
  }

  /* 文字樣式增強 */
  .text-gradient {
    @apply bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent;
    font-weight: 700;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* 文字大小和層次樣式 */
  .text-display {
    font-size: var(--text-6xl);
    line-height: var(--leading-none);
    font-weight: 900;
    letter-spacing: -0.05em;
  }

  .text-headline {
    font-size: var(--text-4xl);
    line-height: var(--leading-tight);
    font-weight: 800;
    letter-spacing: -0.04em;
  }

  .text-title {
    font-size: var(--text-2xl);
    line-height: var(--leading-snug);
    font-weight: 700;
    letter-spacing: -0.025em;
  }

  .text-body {
    font-size: var(--text-base);
    line-height: var(--leading-relaxed);
    font-weight: 400;
    color: hsl(var(--text-primary));
  }

  .text-caption {
    font-size: var(--text-sm);
    line-height: var(--leading-normal);
    font-weight: 500;
    color: hsl(var(--text-secondary));
  }

  .text-overline {
    font-size: var(--text-xs);
    line-height: var(--leading-normal);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: hsl(var(--text-tertiary));
  }

  /* 動畫類別增強 */
  .animate-fade-in-up {
    animation: fade-in-up 0.6s ease-out;
  }

  .animate-fade-in-down {
    animation: fade-in-down 0.6s ease-out;
  }

  .animate-scale-in {
    animation: scale-in 0.3s ease-out;
  }

  /* 交互狀態樣式 */
  .interactive-hover {
    @apply transition-all duration-200 ease-in-out;
    @apply hover:scale-105 hover:shadow-lg;
  }

  /* 專業表格樣式 */
  .table-enhanced {
    @apply w-full border-collapse;
    font-family: var(--font-sans);
  }

  .table-enhanced th {
    @apply bg-surface-1 text-left px-4 py-3 font-semibold;
    color: hsl(var(--text-primary));
    font-size: var(--text-sm);
    font-weight: 600;
    border-bottom: 1px solid hsl(var(--border-medium));
  }

  .table-enhanced td {
    @apply px-4 py-3 border-b border-border-light;
    color: hsl(var(--text-primary));
    font-size: var(--text-sm);
    line-height: var(--leading-relaxed);
  }
}

@layer utilities {
  /* 間距實用類別增強 */
  .space-y-section {
    @apply space-y-16 md:space-y-24;
  }

  .space-y-content {
    @apply space-y-6 md:space-y-8;
  }

  /* 容器實用類別增強 */
  .container-section {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
    max-width: 1400px;
  }

  /* 文字實用類別增強 */
  .text-balance {
    text-wrap: balance;
  }

  .text-pretty {
    text-wrap: pretty;
  }

  /* 漸變背景增強 */
  .bg-gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary-light)) 100%);
  }

  .bg-gradient-accent {
    background: linear-gradient(135deg, hsl(var(--accent)) 0%, hsl(var(--secondary-orange-light)) 100%);
  }

  .bg-gradient-hero {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 50%, hsl(var(--primary-light)) 100%);
  }

  .bg-gradient-card {
    background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--surface-1)) 100%);
  }

  .bg-gradient-secondary {
    background: linear-gradient(135deg, hsl(var(--secondary-orange)) 0%, hsl(var(--secondary-orange-light)) 100%);
  }

  .bg-gradient-primary-orange {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--secondary-orange)) 100%);
  }

  .bg-gradient-orange-accent {
    background: linear-gradient(135deg, hsl(var(--secondary-orange)) 0%, hsl(var(--accent)) 100%);
  }

  .bg-gradient-blue-primary {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary-light)) 100%);
  }

  .bg-gradient-blue-orange {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--secondary-orange)) 50%, hsl(var(--primary-light)) 100%);
  }

  .text-gradient-orange {
    background: linear-gradient(
      135deg,
      hsl(var(--primary)) 0%,
      hsl(var(--secondary-orange)) 50%,
      hsl(var(--accent)) 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-blue {
    background: linear-gradient(
      135deg,
      hsl(var(--primary)) 0%,
      hsl(var(--primary-light)) 50%,
      hsl(var(--primary-dark)) 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-primary {
    background: linear-gradient(
      135deg,
      hsl(var(--primary)) 0%,
      hsl(var(--accent)) 50%,
      hsl(var(--primary-light)) 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .shadow-glow {
    box-shadow: 0 0 20px hsla(var(--primary), 0.15), 0 8px 32px hsla(var(--primary), 0.1);
  }

  .shadow-subtle {
    box-shadow: 0 1px 3px hsla(var(--shadow-light));
  }

  .shadow-moderate {
    box-shadow: 0 4px 6px hsla(var(--shadow-medium));
  }

  .shadow-strong {
    box-shadow: 0 10px 15px hsla(var(--shadow-strong));
  }

  /* 響應式文字大小 */
  .text-responsive-xs { font-size: clamp(0.7rem, 0.8vw, 0.75rem); }
  .text-responsive-sm { font-size: clamp(0.8rem, 1vw, 0.875rem); }
  .text-responsive-base { font-size: clamp(0.9rem, 1.2vw, 1rem); }
  .text-responsive-lg { font-size: clamp(1rem, 1.4vw, 1.125rem); }
  .text-responsive-xl { font-size: clamp(1.1rem, 1.6vw, 1.25rem); }
  .text-responsive-2xl { font-size: clamp(1.3rem, 2vw, 1.5rem); }
  .text-responsive-3xl { font-size: clamp(1.6rem, 2.5vw, 1.875rem); }
  .text-responsive-4xl { font-size: clamp(2rem, 3vw, 2.25rem); }
  .text-responsive-5xl { font-size: clamp(2.5rem, 4vw, 3rem); }
  .text-responsive-6xl { font-size: clamp(3rem, 5vw, 3.75rem); }
}

/* 自定義動畫關鍵幀增強 */
@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-down {
  0% {
    opacity: 0;
    transform: translateY(-30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scale-in {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slide-in-left {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 無障礙改進 */
@media (prefers-reduced-motion: reduce) {
  *,
  ::before,
  ::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 高對比度模式支援 */
@media (prefers-contrast: high) {
  :root {
    --text-primary: 0 0% 0%;
    --text-secondary: 0 0% 20%;
    --border-medium: 0 0% 50%;
  }
  
  .dark {
    --text-primary: 0 0% 100%;
    --text-secondary: 0 0% 80%;
  }
}

/* 印刷樣式優化 */
@media print {
  * {
    color: black !important;
    background: white !important;
    box-shadow: none !important;
  }
  
  body {
    font-size: 12pt;
    line-height: 1.4;
  }
  
  h1 { font-size: 18pt; }
  h2 { font-size: 16pt; }
  h3 { font-size: 14pt; }
  
  a {
    text-decoration: underline;
  }
  
  a[href]:after {
    content: " (" attr(href) ")";
  }
}
