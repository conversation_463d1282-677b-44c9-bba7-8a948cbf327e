import { Metadata } from 'next';
import SEOComprehensiveReport from '@/components/seo/SEOComprehensiveReport';

export const metadata: Metadata = {
  title: 'SEO 綜合分析報告 | AI SEO 優化王',
  description: '生成詳細的 SEO 分析結果和優化建議報告，包含技術分析、內容優化、競爭對手分析和可執行的改進計劃，適合台灣、香港、澳門市場。',
  keywords: [
    'SEO 分析報告',
    'SEO 優化建議',
    '網站 SEO 檢測',
    'Core Web Vitals',
    '競爭對手分析',
    '關鍵字分析',
    'AI SEO 工具',
    '台灣 SEO',
    '香港 SEO',
    '澳門 SEO'
  ],
  openGraph: {
    title: 'SEO 綜合分析報告 | AI SEO 優化王',
    description: '使用 AI 技術生成專業的 SEO 分析報告，提供具體可執行的優化建議和 ROI 預測',
    type: 'website',
    locale: 'zh_TW',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'SEO 綜合分析報告 | AI SEO 優化王',
    description: '使用 AI 技術生成專業的 SEO 分析報告，提供具體可執行的優化建議和 ROI 預測',
  },
  alternates: {
    canonical: '/seo-report',
  },
};

export default function SEOReportPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <SEOComprehensiveReport />
    </div>
  );
}
