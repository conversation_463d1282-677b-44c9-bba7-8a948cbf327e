'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Download, FileText, BarChart3, Settings, Wand2 } from 'lucide-react';

interface ContentRequest {
  id: string;
  prompt: string;
  type: 'article' | 'product-description' | 'meta-description' | 'title' | 'summary';
  targetKeywords: string[];
  wordCount: number;
  tone: 'professional' | 'casual' | 'academic' | 'marketing';
  language: string;
}

interface ContentResult {
  id: string;
  content: string;
  qualityScore: number;
  qualityIssues: string[];
  suggestions: string[];
  formats: Record<string, string>;
  status: 'completed' | 'failed' | 'partial';
  error?: string;
}

export default function AIContentGeneratorPage() {
  const [requests, setRequests] = useState<ContentRequest[]>([{
    id: 'req-1',
    prompt: '',
    type: 'article',
    targetKeywords: [],
    wordCount: 500,
    tone: 'professional',
    language: '繁體中文'
  }]);

  const [results, setResults] = useState<ContentResult[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentKeywordInput, setCurrentKeywordInput] = useState('');
  const [selectedResult, setSelectedResult] = useState<ContentResult | null>(null);
  const [assessmentResult, setAssessmentResult] = useState<any>(null);

  const addRequest = () => {
    const newRequest: ContentRequest = {
      id: `req-${Date.now()}`,
      prompt: '',
      type: 'article',
      targetKeywords: [],
      wordCount: 500,
      tone: 'professional',
      language: '繁體中文'
    };
    setRequests([...requests, newRequest]);
  };

  const updateRequest = (index: number, field: keyof ContentRequest, value: any) => {
    const updated = [...requests];
    updated[index] = { ...updated[index], [field]: value };
    setRequests(updated);
  };

  const addKeyword = (index: number) => {
    if (currentKeywordInput.trim()) {
      const updated = [...requests];
      updated[index].targetKeywords.push(currentKeywordInput.trim());
      setRequests(updated);
      setCurrentKeywordInput('');
    }
  };

  const removeKeyword = (reqIndex: number, keywordIndex: number) => {
    const updated = [...requests];
    updated[reqIndex].targetKeywords.splice(keywordIndex, 1);
    setRequests(updated);
  };

  const generateContent = async () => {
    setIsGenerating(true);
    setProgress(0);
    
    try {
      const response = await fetch('/api/batch-content', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ requests })
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setResults(data.results);
        setProgress(100);
      } else {
        console.error('生成失敗:', data.error);
      }
    } catch (error) {
      console.error('請求失敗:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const assessQuality = async (content: string) => {
    try {
      const response = await fetch('/api/quality-assessment', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ contents: content })
      });
      
      const data = await response.json();
      setAssessmentResult(data);
    } catch (error) {
      console.error('質量評估失敗:', error);
    }
  };

  const standardizeFormat = async (content: string, outputFormats: string[]) => {
    try {
      const response = await fetch('/api/format-standardization', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content,
          outputFormats,
          templateId: 'default'
        })
      });
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('格式標準化失敗:', error);
      return null;
    }
  };

  return (
    <div className="min-h-screen pt-20 bg-gradient-hero">
      <div className="container-section space-y-section">
        {/* 增強的標題區域 */}
        <div className="text-center max-w-4xl mx-auto space-y-6">
          <div className="inline-flex items-center gap-2 px-5 py-2.5 rounded-full bg-surface-1/60 border border-border-light/60 backdrop-blur-md shadow-sm">
            <div className="w-2 h-2 rounded-full bg-gradient-to-r from-primary to-accent-green animate-pulse" />
            <span className="text-sm font-medium text-gradient">
              AI 驅動的內容創作
            </span>
          </div>

          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-text-primary text-balance">
            AI <span className="text-gradient">內容生成器</span>
          </h1>

          <p className="text-xl text-text-secondary leading-relaxed text-balance">
            批量生成高質量內容，自動評估品質，並支援多種格式輸出，
            讓您的內容創作更加高效和專業
          </p>
        </div>

        {/* 增強的標籤頁設計 */}
        <Tabs defaultValue="generate" className="space-y-8">
          <div className="flex justify-center">
            <TabsList className="grid grid-cols-3 bg-surface-1/60 backdrop-blur-md border border-border-light/60 p-1 rounded-xl">
              <TabsTrigger
                value="generate"
                className="flex items-center gap-2 data-[state=active]:bg-card data-[state=active]:shadow-sm data-[state=active]:text-text-primary transition-all duration-200"
              >
                <Wand2 className="w-4 h-4" />
                內容生成
              </TabsTrigger>
              <TabsTrigger
                value="assess"
                className="flex items-center gap-2 data-[state=active]:bg-card data-[state=active]:shadow-sm data-[state=active]:text-text-primary transition-all duration-200"
              >
                <BarChart3 className="w-4 h-4" />
                質量評估
              </TabsTrigger>
              <TabsTrigger
                value="format"
                className="flex items-center gap-2 data-[state=active]:bg-card data-[state=active]:shadow-sm data-[state=active]:text-text-primary transition-all duration-200"
              >
                <FileText className="w-4 h-4" />
                格式標準化
              </TabsTrigger>
            </TabsList>
          </div>

        <TabsContent value="generate" className="space-y-8">
          <Card className="card-hover">
            <CardHeader className="pb-6">
              <div className="flex items-center gap-3">
                <div className="p-3 rounded-xl bg-gradient-to-br from-primary/10 to-primary/20">
                  <Wand2 className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <CardTitle className="text-2xl">批量內容生成</CardTitle>
                  <CardDescription className="text-base mt-1">
                    一次生成多個內容，支援不同類型和格式，提升您的內容創作效率
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-8">
              {requests.map((request, index) => (
                <div key={request.id} className="card-base p-6 space-y-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center text-white font-semibold text-sm">
                        {index + 1}
                      </div>
                      <h3 className="text-lg font-semibold text-text-primary">內容請求 #{index + 1}</h3>
                    </div>
                    {requests.length > 1 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setRequests(requests.filter((_, i) => i !== index))}
                        className="hover:bg-destructive/10 hover:text-destructive hover:border-destructive/30"
                      >
                        移除
                      </Button>
                    )}
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>內容提示</Label>
                      <Textarea
                        placeholder="描述您想要生成的內容..."
                        value={request.prompt}
                        onChange={(e) => updateRequest(index, 'prompt', e.target.value)}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label>內容類型</Label>
                      <Select
                        value={request.type}
                        onValueChange={(value) => updateRequest(index, 'type', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="article">文章</SelectItem>
                          <SelectItem value="product-description">產品描述</SelectItem>
                          <SelectItem value="meta-description">Meta描述</SelectItem>
                          <SelectItem value="title">標題</SelectItem>
                          <SelectItem value="summary">摘要</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label>字數</Label>
                      <Input
                        type="number"
                        value={request.wordCount}
                        onChange={(e) => updateRequest(index, 'wordCount', Math.max(50, parseInt(e.target.value) || 500))}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label>語調</Label>
                      <Select
                        value={request.tone}
                        onValueChange={(value) => updateRequest(index, 'tone', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="professional">專業</SelectItem>
                          <SelectItem value="casual">輕鬆</SelectItem>
                          <SelectItem value="academic">學術</SelectItem>
                          <SelectItem value="marketing">營銷</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>目標關鍵字</Label>
                    <div className="flex gap-2">
                      <Input
                        placeholder="輸入關鍵字"
                        value={currentKeywordInput}
                        onChange={(e) => setCurrentKeywordInput(e.target.value)}
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            addKeyword(index);
                          }
                        }}
                      />
                      <Button type="button" onClick={() => addKeyword(index)}>
                        添加
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {request.targetKeywords.map((keyword, kIndex) => (
                        <Badge key={kIndex} variant="secondary" className="cursor-pointer"
                               onClick={() => removeKeyword(index, kIndex)}>
                          {keyword} ×
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
              
              <div className="flex gap-4">
                <Button variant="outline" onClick={addRequest}>
                  添加更多請求
                </Button>
                <Button 
                  onClick={generateContent} 
                  disabled={isGenerating || requests.some(r => !r.prompt)}
                  className="flex items-center gap-2"
                >
                  <Wand2 className="w-4 h-4" />
                  {isGenerating ? '生成中...' : '開始生成'}
                </Button>
              </div>
              
              {isGenerating && (
                <Progress value={progress} className="w-full" />
              )}
            </CardContent>
          </Card>

          {results.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>生成結果</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {results.map((result, index) => (
                    <div key={result.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium">結果 #{index + 1}</h3>
                        <div className="flex items-center gap-2">
                          <Badge variant={result.status === 'completed' ? 'default' : 'destructive'}>
                            {result.status}
                          </Badge>
                          <Badge variant="secondary">
                            質量分數: {result.qualityScore}
                          </Badge>
                        </div>
                      </div>
                      
                      {result.content && (
                        <div className="bg-gray-50 p-3 rounded mb-2">
                          <p className="text-sm">{result.content}</p>
                        </div>
                      )}
                      
                      {result.error && (
                        <Alert className="mb-2">
                          <AlertDescription>{result.error}</AlertDescription>
                        </Alert>
                      )}
                      
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setSelectedResult(result);
                            assessQuality(result.content);
                          }}
                        >
                          評估質量
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => standardizeFormat(result.content, ['html', 'markdown'])}
                        >
                          格式化
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="assess">
          <Card>
            <CardHeader>
              <CardTitle>內容質量評估</CardTitle>
              <CardDescription>
                全面分析內容質量，提供改進建議
              </CardDescription>
            </CardHeader>
            <CardContent>
              {assessmentResult ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {assessmentResult.overall?.score || 0}
                      </div>
                      <div className="text-sm text-gray-600">總體分數</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {assessmentResult.overall?.grade || 'N/A'}
                      </div>
                      <div className="text-sm text-gray-600">等級</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {assessmentResult.confidence || 0}%
                      </div>
                      <div className="text-sm text-gray-600">信心度</div>
                    </div>
                  </div>
                  
                  {assessmentResult.issues?.length > 0 && (
                    <Alert>
                      <AlertDescription>
                        發現問題: {assessmentResult.issues.join(', ')}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              ) : (
                <p className="text-gray-600">請先生成內容並點擊評估質量按鈕</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="format">
          <Card>
            <CardHeader>
              <CardTitle>格式標準化</CardTitle>
              <CardDescription>
                將內容轉換為多種標準格式
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">請先生成內容並點擊格式化按鈕</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 