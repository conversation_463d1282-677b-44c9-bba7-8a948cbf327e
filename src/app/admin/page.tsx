import Link from 'next/link';
import { 
  Link as LinkIcon, 
  BarChart3, 
  <PERSON><PERSON><PERSON>, 
  <PERSON>rendingUp,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

export default function AdminDashboard() {
  return (
    <div className="space-y-section">
      {/* 增強的歡迎區域 */}
      <div className="card-hover p-8">
        <div className="flex items-start justify-between">
          <div className="space-y-4">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-surface-1/60 border border-border-light/60 backdrop-blur-sm">
              <div className="w-2 h-2 rounded-full bg-gradient-to-r from-primary to-accent animate-pulse" />
              <span className="text-sm font-medium text-gradient">
                管理中心
              </span>
            </div>

            <h1 className="text-3xl sm:text-4xl font-bold text-text-primary">
              歡迎來到 <span className="text-gradient">AI SEO 優化王</span>
            </h1>
            <p className="text-lg text-text-secondary leading-relaxed max-w-2xl">
              這裡是您的 AI SEO 管理中心，您可以監控網站健康度、查看分析報告和配置系統設置，
              全方位掌控您的 SEO 優化策略。
            </p>
          </div>

          {/* 裝飾性元素 */}
          <div className="hidden lg:block">
            <div className="w-32 h-32 bg-gradient-primary rounded-2xl flex items-center justify-center shadow-glow">
              <BarChart3 className="h-16 w-16 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* 增強的快速統計 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card-hover p-6 group">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-4 rounded-xl bg-gradient-to-br from-success/10 to-success/20 group-hover:from-success/20 group-hover:to-success/30 transition-colors duration-200">
                <CheckCircle className="h-6 w-6 text-success" />
              </div>
              <div>
                <p className="text-sm font-medium text-text-secondary">系統狀態</p>
                <p className="text-2xl font-bold text-success">正常運行</p>
              </div>
            </div>
            <div className="w-2 h-12 bg-gradient-to-b from-success to-success/50 rounded-full" />
          </div>
        </div>

        <div className="card-hover p-6 group">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-4 rounded-xl bg-gradient-to-br from-primary/10 to-primary/20 group-hover:from-primary/20 group-hover:to-primary/30 transition-colors duration-200">
                <TrendingUp className="h-6 w-6 text-primary" />
              </div>
              <div>
                <p className="text-sm font-medium text-text-secondary">連結健康度</p>
                <p className="text-2xl font-bold text-primary">檢查中...</p>
              </div>
            </div>
            <div className="w-2 h-12 bg-gradient-to-b from-primary to-primary/50 rounded-full animate-pulse" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-yellow-100 mr-4">
              <AlertTriangle className="h-6 w-6 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">待處理問題</p>
              <p className="text-2xl font-bold text-yellow-600">-</p>
            </div>
          </div>
        </div>
      </div>

      {/* 主要功能 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Link
          href="/admin/link-health"
          className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow group"
        >
          <div className="flex items-center mb-4">
            <div className="p-3 rounded-full bg-blue-100 group-hover:bg-blue-200 transition-colors mr-4">
              <LinkIcon className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">連結健康度</h3>
          </div>
          <p className="text-gray-600 mb-4">
            監控網站所有連結的狀態，包括內部連結、外部連結、資產檔案和 SEO 相關連結。
          </p>
          <div className="flex items-center text-blue-600 font-medium">
            <span>查看詳情</span>
            <svg className="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </div>
        </Link>

        <Link
          href="/admin/analytics"
          className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow group"
        >
          <div className="flex items-center mb-4">
            <div className="p-3 rounded-full bg-green-100 group-hover:bg-green-200 transition-colors mr-4">
              <BarChart3 className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">分析報告</h3>
          </div>
          <p className="text-gray-600 mb-4">
            查看詳細的分析報告，包括趨勢分析、性能指標和優化建議。
          </p>
          <div className="flex items-center text-green-600 font-medium">
            <span>查看報告</span>
            <svg className="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </div>
        </Link>

        <Link
          href="/admin/settings"
          className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow group"
        >
          <div className="flex items-center mb-4">
            <div className="p-3 rounded-full bg-purple-100 group-hover:bg-purple-200 transition-colors mr-4">
              <Settings className="h-6 w-6 text-purple-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">系統設置</h3>
          </div>
          <p className="text-gray-600 mb-4">
            配置系統參數，包括驗證選項、通知設置和排程任務。
          </p>
          <div className="flex items-center text-purple-600 font-medium">
            <span>進入設置</span>
            <svg className="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </div>
        </Link>
      </div>

      {/* 最近活動 */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">最近活動</h2>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            <div className="flex items-center">
              <div className="p-2 rounded-full bg-blue-100 mr-4">
                <LinkIcon className="h-4 w-4 text-blue-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">連結驗證系統已啟動</p>
                <p className="text-sm text-gray-500">剛剛</p>
              </div>
            </div>
            
            <div className="flex items-center">
              <div className="p-2 rounded-full bg-green-100 mr-4">
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">API 端點創建完成</p>
                <p className="text-sm text-gray-500">5 分鐘前</p>
              </div>
            </div>
            
            <div className="flex items-center">
              <div className="p-2 rounded-full bg-purple-100 mr-4">
                <Settings className="h-4 w-4 text-purple-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">SEO 檔案配置更新</p>
                <p className="text-sm text-gray-500">10 分鐘前</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
