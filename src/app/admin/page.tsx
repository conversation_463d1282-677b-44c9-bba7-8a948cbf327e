'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';

export default function AdminDashboard() {
  const [mounted, setMounted] = useState(false);
  const [systemStats, setSystemStats] = useState({
    brands: 12,
    visibility: 73.5,
    experiments: 8,
    issues: 3
  });
  const { user, isAuthenticated } = useAuth();

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">載入中...</p>
        </div>
      </div>
    );
  }

  const quickStats = [
    {
      title: '追蹤品牌',
      value: systemStats.brands,
      change: '+2',
      icon: '🎯',
      color: 'blue',
      description: '本月新增'
    },
    {
      title: '可見度',
      value: `${systemStats.visibility}%`,
      change: '+5.3%',
      icon: '👁️',
      color: 'green',
      description: '整體平均'
    },
    {
      title: '活躍實驗',
      value: systemStats.experiments,
      change: '+1',
      icon: '⚡',
      color: 'purple',
      description: '進行中'
    },
    {
      title: '待處理問題',
      value: systemStats.issues,
      change: '-2',
      icon: '⚠️',
      color: 'orange',
      description: '需要關注'
    }
  ];

  const mainFeatures = [
    {
      title: '查詢管理',
      description: '分析和管理用戶查詢，優化查詢理解和回應品質',
      icon: '🔍',
      href: '/admin/queries',
      gradient: 'from-indigo-500 to-indigo-600',
      stats: '12,847 查詢',
      metrics: '本週新增 324'
    },
    {
      title: '產品研究',
      description: '基於 AI 驅動的品牌可見度分析和競爭情報平台',
      icon: '🔬',
      href: '/admin/product-research',
      gradient: 'from-blue-500 to-blue-600',
      stats: '12 個追蹤品牌',
      metrics: '248 次分析'
    },
    {
      title: '測量功能',
      description: '全面測量您的品牌在 AI 搜尋引擎中的表現',
      icon: '📏',
      href: '/admin/measure',
      gradient: 'from-green-500 to-green-600',
      stats: '73.5% 平均可見度',
      metrics: '156 次測量'
    },
    {
      title: 'AI 摘要監測',
      description: 'Google AI 摘要監測器，追蹤品牌在AI搜尋中的表現',
      icon: '🔍',
      href: '/admin/google-ai-monitor',
      gradient: 'from-indigo-500 to-indigo-600',
      stats: '24 次搜尋',
      metrics: '67.5% 出現率'
    },
    {
      title: '優化功能',
      description: 'AI 驅動的網站優化建議和實驗追蹤',
      icon: '⚡',
      href: '/admin/optimize',
      gradient: 'from-orange-500 to-orange-600',
      stats: '45 個建議',
      metrics: '8 個實驗'
    }
  ];

  const performanceMetrics = [
    { title: 'CPU 使用率', value: 45, status: 'normal', icon: '💻', color: 'blue' },
    { title: '記憶體使用', value: 62, status: 'normal', icon: '🧠', color: 'green' },
    { title: '網路流量', value: 78, status: 'warning', icon: '🌐', color: 'yellow' },
    { title: 'API 回應時間', value: 85, status: 'good', icon: '📡', color: 'purple' }
  ];

  const getStatColor = (color: string) => {
    const colors = {
      blue: 'bg-blue-50 text-blue-600',
      green: 'bg-green-50 text-green-600',
      purple: 'bg-purple-50 text-purple-600',
      orange: 'bg-orange-50 text-orange-600'
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 歡迎區域 */}
        <div className="mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  歡迎回來，{user?.full_name || '管理員'}！
                </h1>
                <p className="text-gray-600 mt-1">
                  {isAuthenticated ? '您已成功登入管理系統' : '請登入以訪問完整功能'}
                </p>
              </div>
              <div className="text-right">
                <div className="text-sm text-gray-500">
                  {new Date().toLocaleDateString('zh-TW', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    weekday: 'long'
                  })}
                </div>
                <div className="text-lg font-semibold text-gray-900">
                  AI SEO 管理台
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 快速統計 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {quickStats.map((stat, index) => (
            <div key={index} className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  <div className="flex items-center mt-1">
                    <span className={`text-sm font-medium ${
                      stat.change.startsWith('+') ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {stat.change}
                    </span>
                    <span className="text-sm text-gray-500 ml-1">{stat.description}</span>
                  </div>
                </div>
                <div className={`p-3 rounded-full ${getStatColor(stat.color)}`}>
                  <span className="text-2xl">{stat.icon}</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 主要功能 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {mainFeatures.map((feature, index) => (
            <Link key={index} href={feature.href}>
              <div className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 p-6 cursor-pointer">
                <div className="flex items-center mb-4">
                  <div className={`p-3 rounded-lg bg-gradient-to-r ${feature.gradient} text-white`}>
                    <span className="text-2xl">{feature.icon}</span>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-semibold text-gray-900">{feature.title}</h3>
                    <p className="text-sm text-gray-600">{feature.description}</p>
                  </div>
                </div>
                <div className="border-t pt-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">{feature.stats}</span>
                    <span className="text-gray-500">{feature.metrics}</span>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* 系統性能 */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">系統性能監控</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {performanceMetrics.map((metric, index) => (
              <div key={index} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-600">{metric.title}</span>
                  <span className="text-lg">{metric.icon}</span>
                </div>
                <div className="flex items-center">
                  <div className="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                    <div 
                      className={`h-2 rounded-full ${
                        metric.value < 50 ? 'bg-green-500' :
                        metric.value < 75 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${metric.value}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900">{metric.value}%</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
