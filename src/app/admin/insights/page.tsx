'use client';

import React from 'react';
import Link from 'next/link';
import { 
  TrendingUp, 
  Users, 
  Brain, 
  BarChart3, 
  ArrowRight,
  LineChart,
  PieChart,
  Activity,
  Target,
  Eye,
  Lightbulb
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export default function InsightsPage() {
  const insightModules = [
    {
      title: '趨勢分析',
      description: '分析數據趨勢變化，預測未來發展方向',
      icon: TrendingUp,
      href: '/admin/insights/trends',
      gradient: 'from-blue-500 to-blue-600',
      stats: '15 項趨勢',
      metrics: '準確率 89%'
    },
    {
      title: '用戶細分',
      description: '深入分析用戶群體特徵和行為模式',
      icon: Users,
      href: '/admin/insights/segments',
      gradient: 'from-green-500 to-green-600',
      stats: '8 個細分',
      metrics: '覆蓋率 94%'
    }
  ];

  const keyInsights = [
    {
      title: 'AI SEO 查詢激增',
      impact: 'high',
      category: '趨勢洞察',
      description: 'AI SEO 相關查詢在過去 30 天內增長了 185%',
      actionItems: ['優化 AI SEO 內容', '增加相關文檔', '擴展功能範圍'],
      confidence: 95
    },
    {
      title: '企業用戶偏好變化',
      impact: 'medium',
      category: '用戶行為',
      description: '企業用戶更傾向於使用批量分析功能',
      actionItems: ['改進批量處理 UI', '增加企業級功能', '優化性能'],
      confidence: 87
    },
    {
      title: '移動端使用率上升',
      impact: 'medium',
      category: '設備趨勢',
      description: '移動端用戶使用率較上月增長 45%',
      actionItems: ['優化移動端體驗', '響應式設計改進', '移動端專屬功能'],
      confidence: 92
    }
  ];

  const metricsSummary = [
    {
      title: '總洞察數量',
      value: '247',
      change: '+23',
      icon: Lightbulb,
      color: 'blue'
    },
    {
      title: '高置信度洞察',
      value: '189',
      change: '+18',
      icon: Target,
      color: 'green'
    },
    {
      title: '可行動建議',
      value: '156',
      change: '+12',
      icon: Activity,
      color: 'purple'
    },
    {
      title: '已實施建議',
      value: '89',
      change: '+15',
      icon: Eye,
      color: 'orange'
    }
  ];

  const getStatColor = (color: string) => {
    switch (color) {
      case 'blue': return 'from-blue-500/20 to-blue-600/20 text-blue-600';
      case 'green': return 'from-green-500/20 to-green-600/20 text-green-600';
      case 'purple': return 'from-purple-500/20 to-purple-600/20 text-purple-600';
      case 'orange': return 'from-orange-500/20 to-orange-600/20 text-orange-600';
      default: return 'from-primary/20 to-accent/20 text-primary';
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getImpactText = (impact: string) => {
    switch (impact) {
      case 'high': return '高影響';
      case 'medium': return '中等影響';
      case 'low': return '低影響';
      default: return '未知';
    }
  };

  return (
    <div className="space-y-8">
      {/* 頁面標題 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <Brain className="h-8 w-8" />
              洞察報告
            </h1>
            <p className="text-lg text-muted-foreground mt-2">
              基於數據分析的深度洞察和趨勢預測
            </p>
          </div>
          <Button variant="default" size="lg" className="group">
            <BarChart3 className="w-4 h-4 mr-2" />
            生成新報告
          </Button>
        </div>
      </div>

      {/* 洞察總覽指標 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metricsSummary.map((metric, index) => (
          <Card key={index} className="bg-card/60 backdrop-blur-sm border-border-light hover:shadow-glow transition-all duration-300 group">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-muted-foreground">{metric.title}</p>
                  <p className="text-2xl font-bold mt-1">{metric.value}</p>
                  <p className="text-sm font-medium mt-2 text-green-600">
                    {metric.change}
                  </p>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-br ${getStatColor(metric.color)} group-hover:scale-110 transition-transform duration-300`}>
                  <metric.icon className="h-6 w-6" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 功能模組 */}
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold mb-2">洞察分析功能</h2>
          <p className="text-muted-foreground">深入分析數據，發現有價值的洞察</p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {insightModules.map((module, index) => (
            <Card key={index} className="group bg-card/60 backdrop-blur-sm border-border-light hover:shadow-glow transition-all duration-300 hover:scale-[1.02]">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className={`p-3 rounded-2xl bg-gradient-to-br ${module.gradient} shadow-md group-hover:shadow-glow transition-shadow duration-300`}>
                      <module.icon className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-xl group-hover:text-gradient transition-colors duration-300">
                        {module.title}
                      </CardTitle>
                      <div className="flex gap-2 mt-1">
                        <Badge variant="secondary" className="bg-surface-1/60 text-muted-foreground border-border-light">
                          {module.stats}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {module.metrics}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4 leading-relaxed">
                  {module.description}
                </p>
                <Button asChild variant="outline" className="w-full group">
                  <Link href={module.href}>
                    進入模組
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* 關鍵洞察 */}
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold mb-2">關鍵洞察</h2>
          <p className="text-muted-foreground">最新發現的重要洞察和建議</p>
        </div>
        
        <div className="space-y-4">
          {keyInsights.map((insight, index) => (
            <Card key={index} className="bg-card/60 backdrop-blur-sm border-border-light hover:shadow-glow transition-all duration-300">
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-semibold">{insight.title}</h3>
                        <Badge className={getImpactColor(insight.impact)}>
                          {getImpactText(insight.impact)}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {insight.category}
                        </Badge>
                      </div>
                      <p className="text-muted-foreground mb-3">{insight.description}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold">{insight.confidence}%</div>
                      <div className="text-xs text-muted-foreground">置信度</div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium mb-2">建議行動：</h4>
                    <div className="flex flex-wrap gap-2">
                      {insight.actionItems.map((action, actionIndex) => (
                        <Badge key={actionIndex} variant="secondary" className="bg-blue-50 text-blue-700 border-blue-200">
                          {action}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* 快速操作 */}
      <Card className="bg-gradient-to-r from-primary/5 to-accent/5 border-border-light/60">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <LineChart className="h-5 w-5" />
            快速分析
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button asChild variant="outline" className="h-auto p-4 group justify-start">
              <Link href="/admin/insights/trends">
                <div className="flex items-center gap-4">
                  <TrendingUp className="h-8 w-8 text-primary" />
                  <div className="text-left">
                    <div className="font-medium">查看趨勢分析</div>
                    <div className="text-sm text-muted-foreground">分析數據趨勢</div>
                  </div>
                </div>
                <ArrowRight className="ml-auto h-4 w-4 group-hover:translate-x-1 transition-transform duration-200" />
              </Link>
            </Button>
            
            <Button asChild variant="outline" className="h-auto p-4 group justify-start">
              <Link href="/admin/insights/segments">
                <div className="flex items-center gap-4">
                  <Users className="h-8 w-8 text-primary" />
                  <div className="text-left">
                    <div className="font-medium">用戶細分分析</div>
                    <div className="text-sm text-muted-foreground">分析用戶群體</div>
                  </div>
                </div>
                <ArrowRight className="ml-auto h-4 w-4 group-hover:translate-x-1 transition-transform duration-200" />
              </Link>
            </Button>
            
            <Button variant="outline" className="h-auto p-4 group justify-start">
              <div className="flex items-center gap-4">
                <PieChart className="h-8 w-8 text-primary" />
                <div className="text-left">
                  <div className="font-medium">自定義報告</div>
                  <div className="text-sm text-muted-foreground">創建專屬分析</div>
                </div>
              </div>
              <ArrowRight className="ml-auto h-4 w-4 group-hover:translate-x-1 transition-transform duration-200" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}