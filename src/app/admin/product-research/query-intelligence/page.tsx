'use client';

import React, { useState } from 'react';
import { 
  Brain, 
  Search, 
  History, 
  TrendingUp, 
  Filter,
  Download,
  Plus,
  Eye,
  Calendar,
  Target,
  LogIn,
  AlertCircle
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { QueryIntelligencePanel } from '@/components/product-research/QueryIntelligencePanel';
import { useAuth } from '@/contexts/AuthContext';

// 模擬查詢歷史數據
const mockQueryHistory = [
  {
    id: '1',
    query: 'best HR software for small teams',
    brandId: '1',
    brandName: 'TechCorp Solutions',
    intentLevel: 'high',
    conversionRate: 68.5,
    searchVolume: 825,
    competitionLevel: 'low',
    clickPotential: 'high',
    analyzedAt: '2024-01-15T10:30:00Z',
    status: 'completed'
  },
  {
    id: '2',
    query: 'project management tools comparison',
    brandId: '1',
    brandName: 'TechCorp Solutions',
    intentLevel: 'medium',
    conversionRate: 45.2,
    searchVolume: 1250,
    competitionLevel: 'high',
    clickPotential: 'medium',
    analyzedAt: '2024-01-14T15:45:00Z',
    status: 'completed'
  },
  {
    id: '3',
    query: 'healthcare management system features',
    brandId: '2',
    brandName: 'HealthPlus Medical',
    intentLevel: 'high',
    conversionRate: 72.8,
    searchVolume: 650,
    competitionLevel: 'medium',
    clickPotential: 'high',
    analyzedAt: '2024-01-13T09:15:00Z',
    status: 'completed'
  },
  {
    id: '4',
    query: 'renewable energy solutions for businesses',
    brandId: '3',
    brandName: 'EcoGreen Energy',
    intentLevel: 'medium',
    conversionRate: 55.3,
    searchVolume: 920,
    competitionLevel: 'medium',
    clickPotential: 'high',
    analyzedAt: '2024-01-12T14:20:00Z',
    status: 'completed'
  }
];

const brands = [
  { id: '1', name: 'TechCorp Solutions' },
  { id: '2', name: 'HealthPlus Medical' },
  { id: '3', name: 'EcoGreen Energy' }
];

export default function QueryIntelligencePage() {
  const { isAuthenticated, isLoading, user, userRole, isAdmin } = useAuth();
  const [selectedBrand, setSelectedBrand] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedIntent, setSelectedIntent] = useState('all');

  // 開發模式認證繞過
  const isDevelopment = process.env.NODE_ENV === 'development';
  const bypassAuth = isDevelopment; // 開發模式下允許訪問

  // 載入中狀態
  if (isLoading && !bypassAuth) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入中...</p>
        </div>
      </div>
    );
  }

  // 未認證狀態（在開發模式下跳過）
  if (!isAuthenticated && !bypassAuth) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
          <AlertCircle className="h-16 w-16 text-yellow-500 mx-auto mb-6" />
          <h1 className="text-2xl font-bold text-gray-900 mb-4">需要登入</h1>
          <p className="text-gray-600 mb-6">
            您需要登入才能訪問查詢智能分析功能
          </p>
          
          <Alert className="mb-6 text-left">
            <AlertDescription>
              <strong>測試帳戶：</strong><br />
              Email: <EMAIL><br />
              Password: demo123456
            </AlertDescription>
          </Alert>

          <div className="flex flex-col space-y-3">
            <Button asChild className="w-full">
              <a href="/auth/login">
                <LogIn className="h-4 w-4 mr-2" />
                前往登入
              </a>
            </Button>
            <Button variant="outline" asChild className="w-full">
              <a href="/">返回首頁</a>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // 權限檢查（在開發模式下跳過）
  if (!bypassAuth && !isAdmin && userRole !== 'admin') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
          <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-6" />
          <h1 className="text-2xl font-bold text-gray-900 mb-4">權限不足</h1>
          <p className="text-gray-600 mb-6">
            您需要管理員權限才能訪問查詢智能分析功能
          </p>
          <Button variant="outline" asChild className="w-full">
            <a href="/">返回首頁</a>
          </Button>
        </div>
      </div>
    );
  }

  // 篩選查詢歷史
  const filteredHistory = mockQueryHistory.filter(item => {
    const brandMatch = selectedBrand === 'all' || item.brandId === selectedBrand;
    const searchMatch = item.query.toLowerCase().includes(searchTerm.toLowerCase());
    const intentMatch = selectedIntent === 'all' || item.intentLevel === selectedIntent;
    return brandMatch && searchMatch && intentMatch;
  });

  const getIntentColor = (level: string) => {
    switch (level) {
      case 'high': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCompetitionColor = (level: string) => {
    switch (level) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-TW', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-6">
      {/* 開發模式提示 */}
      {bypassAuth && (
        <Alert className="bg-blue-50 border-blue-200">
          <AlertCircle className="h-4 w-4 text-blue-600" />
          <AlertDescription className="text-blue-800">
            <strong>開發模式：</strong>認證檢查已暫時繞過，您可以直接使用查詢智能分析功能。
          </AlertDescription>
        </Alert>
      )}

      {/* 頁面標題 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">查詢智能分析</h1>
          <p className="text-gray-600 mt-2">
            深度分析用戶查詢意圖，獲取競爭情報和解決方案推薦
          </p>
        </div>
        <Button variant="outline">
          <Download className="h-4 w-4 mr-2" />
          匯出報告
        </Button>
      </div>

      {/* 統計概覽 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">總查詢數</p>
                <p className="text-2xl font-bold text-gray-900">{mockQueryHistory.length}</p>
              </div>
              <Search className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">高意圖查詢</p>
                <p className="text-2xl font-bold text-green-600">
                  {mockQueryHistory.filter(q => q.intentLevel === 'high').length}
                </p>
              </div>
              <Target className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">平均轉換率</p>
                <p className="text-2xl font-bold text-purple-600">
                  {(mockQueryHistory.reduce((sum, q) => sum + q.conversionRate, 0) / mockQueryHistory.length).toFixed(1)}%
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">總搜尋量</p>
                <p className="text-2xl font-bold text-orange-600">
                  {mockQueryHistory.reduce((sum, q) => sum + q.searchVolume, 0).toLocaleString()}
                </p>
              </div>
              <Brain className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 主要內容 */}
      <Tabs defaultValue="analyze" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="analyze" className="flex items-center space-x-2">
            <Brain className="h-4 w-4" />
            <span>新查詢分析</span>
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center space-x-2">
            <History className="h-4 w-4" />
            <span>查詢歷史</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="analyze">
          <QueryIntelligencePanel 
            brandId={selectedBrand !== 'all' ? selectedBrand : undefined}
            onAnalysisComplete={(data) => {
              console.log('Analysis completed:', data);
            }}
          />
        </TabsContent>

        <TabsContent value="history">
          <div className="space-y-6">
            {/* 篩選控制 */}
            <Card>
              <CardContent className="pt-6">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      品牌篩選
                    </label>
                    <select
                      value={selectedBrand}
                      onChange={(e) => setSelectedBrand(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="all">所有品牌</option>
                      {brands.map(brand => (
                        <option key={brand.id} value={brand.id}>{brand.name}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      意圖級別
                    </label>
                    <select
                      value={selectedIntent}
                      onChange={(e) => setSelectedIntent(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="all">所有級別</option>
                      <option value="high">高意圖</option>
                      <option value="medium">中意圖</option>
                      <option value="low">低意圖</option>
                    </select>
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      搜尋查詢
                    </label>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <Input
                        placeholder="搜尋查詢內容..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 查詢歷史列表 */}
            <div className="grid grid-cols-1 gap-4">
              {filteredHistory.map((query) => (
                <Card key={query.id} className="hover:shadow-lg transition-shadow">
                  <CardContent className="pt-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">
                            "{query.query}"
                          </h3>
                          <Badge variant="outline">{query.brandName}</Badge>
                        </div>
                        
                        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
                          <div>
                            <div className="text-sm text-gray-500">意圖級別</div>
                            <Badge className={getIntentColor(query.intentLevel)}>
                              {query.intentLevel === 'high' ? '高意圖' : 
                               query.intentLevel === 'medium' ? '中意圖' : '低意圖'}
                            </Badge>
                          </div>
                          <div>
                            <div className="text-sm text-gray-500">轉換率</div>
                            <div className="font-semibold text-green-600">{query.conversionRate}%</div>
                          </div>
                          <div>
                            <div className="text-sm text-gray-500">搜尋量</div>
                            <div className="font-semibold">{query.searchVolume.toLocaleString()}</div>
                          </div>
                          <div>
                            <div className="text-sm text-gray-500">競爭程度</div>
                            <Badge className={getCompetitionColor(query.competitionLevel)}>
                              {query.competitionLevel === 'low' ? '低競爭' : 
                               query.competitionLevel === 'medium' ? '中競爭' : '高競爭'}
                            </Badge>
                          </div>
                          <div>
                            <div className="text-sm text-gray-500">分析時間</div>
                            <div className="text-sm flex items-center space-x-1">
                              <Calendar className="h-3 w-3" />
                              <span>{formatDate(query.analyzedAt)}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          查看詳情
                        </Button>
                        <Button variant="outline" size="sm">
                          <TrendingUp className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* 空狀態 */}
            {filteredHistory.length === 0 && (
              <Card>
                <CardContent className="text-center py-12">
                  <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">沒有找到查詢記錄</h3>
                  <p className="text-gray-600 mb-4">
                    {searchTerm || selectedBrand !== 'all' || selectedIntent !== 'all'
                      ? '請調整篩選條件或搜尋關鍵字'
                      : '開始分析您的第一個查詢'}
                  </p>
                  <Button onClick={() => {
                    setSearchTerm('');
                    setSelectedBrand('all');
                    setSelectedIntent('all');
                  }}>
                    <Plus className="h-4 w-4 mr-2" />
                    清除篩選
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
