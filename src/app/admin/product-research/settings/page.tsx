'use client';

import React, { useState, useEffect } from 'react';
import {
  Settings,
  Key,
  Database,
  Bell,
  Clock,
  Shield,
  Activity,
  AlertCircle,
  CheckCircle,
  Save,
  RefreshCw,
  TestTube,
  Zap,
  Loader2
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';

interface SystemSettings {
  openai: {
    apiKey: string;
    model: string;
    maxTokens: number;
    temperature: number;
    isConfigured: boolean;
  };
  analysis: {
    autoAnalysisEnabled: boolean;
    analysisInterval: number;
    maxConcurrentAnalyses: number;
    retryAttempts: number;
    timeoutSeconds: number;
  };
  notifications: {
    emailEnabled: boolean;
    webhookEnabled: boolean;
    webhookUrl: string;
    notificationTypes: string[];
  };
  cache: {
    enabled: boolean;
    ttlSeconds: number;
    maxSize: number;
    compressionEnabled: boolean;
  };
  security: {
    rateLimitEnabled: boolean;
    maxRequestsPerMinute: number;
    ipWhitelistEnabled: boolean;
    ipWhitelist: string[];
  };
}

const defaultSettings: SystemSettings = {
  openai: {
    apiKey: '',
    model: 'gpt-4o-mini',
    maxTokens: 4000,
    temperature: 0.7,
    isConfigured: false
  },
  analysis: {
    autoAnalysisEnabled: true,
    analysisInterval: 24,
    maxConcurrentAnalyses: 3,
    retryAttempts: 3,
    timeoutSeconds: 300
  },
  notifications: {
    emailEnabled: true,
    webhookEnabled: false,
    webhookUrl: '',
    notificationTypes: ['analysis_completed', 'analysis_failed', 'report_generated']
  },
  cache: {
    enabled: true,
    ttlSeconds: 3600,
    maxSize: 1000,
    compressionEnabled: true
  },
  security: {
    rateLimitEnabled: true,
    maxRequestsPerMinute: 60,
    ipWhitelistEnabled: false,
    ipWhitelist: []
  }
};

export default function ProductResearchSettingsPage() {
  const [settings, setSettings] = useState<SystemSettings>(defaultSettings);
  const [isLoading, setIsLoading] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [testResults, setTestResults] = useState<Record<string, any>>({});

  // 安全的數字轉換函數
  const safeParseInt = (value: string, defaultValue: number = 0, min?: number, max?: number): number => {
    const parsed = parseInt(value);
    if (isNaN(parsed)) return defaultValue;
    if (min !== undefined && parsed < min) return min;
    if (max !== undefined && parsed > max) return max;
    return parsed;
  };

  // 載入設定
  useEffect(() => {
    const loadSettings = async () => {
      try {
        // 這裡將來會調用 API 載入設定
        await new Promise(resolve => setTimeout(resolve, 500));
        // 模擬載入的設定
        setSettings({
          ...defaultSettings,
          openai: {
            ...defaultSettings.openai,
            isConfigured: true,
            apiKey: 'sk-***************************'
          }
        });
      } catch (error) {
        toast.error('載入設定失敗');
      }
    };

    loadSettings();
  }, []);

  const handleSaveSettings = async () => {
    setIsLoading(true);
    
    try {
      // 這裡將來會調用 API 儲存設定
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success('設定儲存成功！');
    } catch (error) {
      toast.error('設定儲存失敗，請重試');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestConnection = async () => {
    setIsTesting(true);
    
    try {
      // 這裡將來會調用 API 測試連接
      await new Promise(resolve => setTimeout(resolve, 2000));
      setTestResults({
        openai: { status: 'success', latency: '245ms' },
        database: { status: 'success', connections: 15 },
        cache: { status: 'success', hit_rate: '94.5%' }
      });
      toast.success('連接測試完成');
    } catch (error) {
      toast.error('連接測試失敗');
    } finally {
      setIsTesting(false);
    }
  };

  const updateSettings = (section: keyof SystemSettings, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const resetToDefaults = () => {
    setSettings(defaultSettings);
    toast.info('設定已重置為預設值');
  };

  return (
    <div className="space-y-6">
      {/* 頁面標題和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">系統設定</h1>
          <p className="text-gray-600 mt-1">配置產品研究系統的各項參數</p>
        </div>
        <div className="flex items-center gap-4">
          <Button onClick={handleTestConnection} disabled={isTesting} variant="outline">
            <TestTube className={`w-4 h-4 mr-2 ${isTesting ? 'animate-spin' : ''}`} />
            {isTesting ? '測試中...' : '測試連接'}
          </Button>
          <Button onClick={resetToDefaults} variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            重置設定
          </Button>
          <Button onClick={handleSaveSettings} disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                儲存中...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                儲存設定
              </>
            )}
          </Button>
        </div>
      </div>

      {/* 系統狀態概覽 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">API 狀態</p>
                <p className="text-2xl font-bold text-green-600">
                  {settings.openai.isConfigured ? '已配置' : '未配置'}
                </p>
              </div>
              <Key className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">自動分析</p>
                <p className="text-2xl font-bold text-gray-900">
                  {settings.analysis.autoAnalysisEnabled ? '啟用' : '停用'}
                </p>
              </div>
              <Activity className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">快取狀態</p>
                <p className="text-2xl font-bold text-gray-900">
                  {settings.cache.enabled ? '啟用' : '停用'}
                </p>
              </div>
              <Database className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">安全設定</p>
                <p className="text-2xl font-bold text-gray-900">
                  {settings.security.rateLimitEnabled ? '啟用' : '停用'}
                </p>
              </div>
              <Shield className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 設定選項卡 */}
      <Tabs defaultValue="openai" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="openai">AI 設定</TabsTrigger>
          <TabsTrigger value="analysis">分析設定</TabsTrigger>
          <TabsTrigger value="notifications">通知設定</TabsTrigger>
          <TabsTrigger value="cache">快取設定</TabsTrigger>
          <TabsTrigger value="security">安全設定</TabsTrigger>
        </TabsList>

        <TabsContent value="openai">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Key className="h-5 w-5" />
                <span>OpenAI 設定</span>
              </CardTitle>
              <CardDescription>
                配置 OpenAI API 金鑰和模型參數
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label htmlFor="apiKey">API 金鑰</Label>
                <Input
                  id="apiKey"
                  type="password"
                  value={settings.openai.apiKey}
                  onChange={(e) => updateSettings('openai', 'apiKey', e.target.value)}
                  placeholder="請輸入您的 OpenAI API 金鑰"
                />
                <p className="text-sm text-gray-600 mt-1">
                  您的 API 金鑰將被安全地加密儲存
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="model">模型</Label>
                  <select
                    id="model"
                    value={settings.openai.model}
                    onChange={(e) => updateSettings('openai', 'model', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="gpt-4o">GPT-4o</option>
                    <option value="gpt-4o-mini">GPT-4o Mini</option>
                    <option value="gpt-4-turbo">GPT-4 Turbo</option>
                    <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="maxTokens">最大 Token 數</Label>
                  <Input
                    id="maxTokens"
                    type="number"
                    value={settings.openai.maxTokens}
                    onChange={(e) => updateSettings('openai', 'maxTokens', safeParseInt(e.target.value, 4000, 100, 8000))}
                    min="100"
                    max="8000"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="temperature">溫度: {settings.openai.temperature}</Label>
                <input
                  id="temperature"
                  type="range"
                  min="0"
                  max="2"
                  step="0.1"
                  value={settings.openai.temperature}
                  onChange={(e) => updateSettings('openai', 'temperature', parseFloat(e.target.value))}
                  className="w-full mt-2"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>保守</span>
                  <span>創新</span>
                </div>
              </div>

              {/* 測試結果 */}
              {testResults.openai && (
                <div className="border rounded-lg p-4 bg-gray-50">
                  <h4 className="font-medium text-gray-900 mb-3">連接測試結果</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">OpenAI API</span>
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm text-gray-600">{testResults.openai.latency}</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analysis">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="h-5 w-5" />
                <span>分析設定</span>
              </CardTitle>
              <CardDescription>
                配置自動分析和性能參數
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="autoAnalysis">自動分析</Label>
                  <p className="text-sm text-gray-600">定期自動執行品牌分析</p>
                </div>
                <Switch
                  id="autoAnalysis"
                  checked={settings.analysis.autoAnalysisEnabled}
                  onCheckedChange={(checked) => updateSettings('analysis', 'autoAnalysisEnabled', checked)}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="analysisInterval">分析間隔 (小時)</Label>
                  <Input
                    id="analysisInterval"
                    type="number"
                    value={settings.analysis.analysisInterval}
                    onChange={(e) => updateSettings('analysis', 'analysisInterval', safeParseInt(e.target.value, 24, 1, 168))}
                    min="1"
                    max="168"
                  />
                </div>
                <div>
                  <Label htmlFor="maxConcurrent">最大並發數</Label>
                  <Input
                    id="maxConcurrent"
                    type="number"
                    value={settings.analysis.maxConcurrentAnalyses}
                    onChange={(e) => updateSettings('analysis', 'maxConcurrentAnalyses', safeParseInt(e.target.value, 3, 1, 10))}
                    min="1"
                    max="10"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="retryAttempts">重試次數</Label>
                  <Input
                    id="retryAttempts"
                    type="number"
                    value={settings.analysis.retryAttempts}
                    onChange={(e) => updateSettings('analysis', 'retryAttempts', safeParseInt(e.target.value, 3, 0, 10))}
                    min="0"
                    max="10"
                  />
                </div>
                <div>
                  <Label htmlFor="timeout">超時時間 (秒)</Label>
                  <Input
                    id="timeout"
                    type="number"
                    value={settings.analysis.timeoutSeconds}
                    onChange={(e) => updateSettings('analysis', 'timeoutSeconds', safeParseInt(e.target.value, 300, 30, 600))}
                    min="30"
                    max="600"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Bell className="h-5 w-5" />
                <span>通知設定</span>
              </CardTitle>
              <CardDescription>
                配置系統通知和警報設定
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="emailNotifications">電子郵件通知</Label>
                    <p className="text-sm text-gray-600">接收系統通知郵件</p>
                  </div>
                  <Switch
                    id="emailNotifications"
                    checked={settings.notifications.emailEnabled}
                    onCheckedChange={(checked) => updateSettings('notifications', 'emailEnabled', checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="webhookNotifications">Webhook 通知</Label>
                    <p className="text-sm text-gray-600">發送到自定義端點</p>
                  </div>
                  <Switch
                    id="webhookNotifications"
                    checked={settings.notifications.webhookEnabled}
                    onCheckedChange={(checked) => updateSettings('notifications', 'webhookEnabled', checked)}
                  />
                </div>
              </div>
              
              {settings.notifications.webhookEnabled && (
                <div>
                  <Label htmlFor="webhookUrl">Webhook URL</Label>
                  <Input
                    id="webhookUrl"
                    type="url"
                    value={settings.notifications.webhookUrl}
                    onChange={(e) => updateSettings('notifications', 'webhookUrl', e.target.value)}
                    placeholder="https://your-webhook-url.com/endpoint"
                  />
                </div>
              )}
              
              <div>
                <Label className="text-base font-medium">通知類型</Label>
                <div className="space-y-3 mt-3">
                  {[
                    { id: 'analysis_completed', label: '分析完成', description: '當分析完成時通知' },
                    { id: 'analysis_failed', label: '分析失敗', description: '當分析失敗時通知' },
                    { id: 'report_generated', label: '報告生成', description: '當新報告生成時通知' }
                  ].map((type) => (
                    <div key={type.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <Label htmlFor={type.id} className="font-medium text-gray-900">
                          {type.label}
                        </Label>
                        <p className="text-sm text-gray-600">{type.description}</p>
                      </div>
                      <Switch
                        id={type.id}
                        checked={settings.notifications.notificationTypes.includes(type.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            updateSettings('notifications', 'notificationTypes', [
                              ...settings.notifications.notificationTypes,
                              type.id
                            ]);
                          } else {
                            updateSettings('notifications', 'notificationTypes',
                              settings.notifications.notificationTypes.filter(id => id !== type.id)
                            );
                          }
                        }}
                      />
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cache">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="h-5 w-5" />
                <span>快取設定</span>
              </CardTitle>
              <CardDescription>
                配置系統快取和性能優化
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="cacheEnabled">啟用快取</Label>
                  <p className="text-sm text-gray-600">提升系統響應速度</p>
                </div>
                <Switch
                  id="cacheEnabled"
                  checked={settings.cache.enabled}
                  onCheckedChange={(checked) => updateSettings('cache', 'enabled', checked)}
                />
              </div>
              
              {settings.cache.enabled && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="ttl">快取過期時間 (秒)</Label>
                    <Input
                      id="ttl"
                      type="number"
                      value={settings.cache.ttlSeconds}
                      onChange={(e) => updateSettings('cache', 'ttlSeconds', safeParseInt(e.target.value, 3600, 60, 86400))}
                      min="60"
                      max="86400"
                    />
                  </div>
                  <div>
                    <Label htmlFor="maxSize">最大快取大小</Label>
                    <Input
                      id="maxSize"
                      type="number"
                      value={settings.cache.maxSize}
                      onChange={(e) => updateSettings('cache', 'maxSize', safeParseInt(e.target.value, 1000, 100, 10000))}
                      min="100"
                      max="10000"
                    />
                  </div>
                </div>
              )}
              
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="compression">啟用壓縮</Label>
                  <p className="text-sm text-gray-600">減少記憶體使用量</p>
                </div>
                <Switch
                  id="compression"
                  checked={settings.cache.compressionEnabled}
                  onCheckedChange={(checked) => updateSettings('cache', 'compressionEnabled', checked)}
                />
              </div>

              {/* 快取測試結果 */}
              {testResults.cache && (
                <div className="border rounded-lg p-4 bg-gray-50">
                  <h4 className="font-medium text-gray-900 mb-3">快取狀態</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">命中率</span>
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        {testResults.cache.hit_rate}
                      </Badge>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5" />
                <span>安全設定</span>
              </CardTitle>
              <CardDescription>
                配置系統安全和存取控制
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="rateLimit">啟用限流</Label>
                  <p className="text-sm text-gray-600">防止過量請求</p>
                </div>
                <Switch
                  id="rateLimit"
                  checked={settings.security.rateLimitEnabled}
                  onCheckedChange={(checked) => updateSettings('security', 'rateLimitEnabled', checked)}
                />
              </div>
              
              {settings.security.rateLimitEnabled && (
                <div>
                  <Label htmlFor="maxRequests">每分鐘最大請求數</Label>
                  <Input
                    id="maxRequests"
                    type="number"
                    value={settings.security.maxRequestsPerMinute}
                    onChange={(e) => updateSettings('security', 'maxRequestsPerMinute', safeParseInt(e.target.value, 60, 10, 1000))}
                    min="10"
                    max="1000"
                  />
                </div>
              )}
              
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="ipWhitelist">IP 白名單</Label>
                  <p className="text-sm text-gray-600">僅允許特定 IP 存取</p>
                </div>
                <Switch
                  id="ipWhitelist"
                  checked={settings.security.ipWhitelistEnabled}
                  onCheckedChange={(checked) => updateSettings('security', 'ipWhitelistEnabled', checked)}
                />
              </div>
              
              {settings.security.ipWhitelistEnabled && (
                <div>
                  <Label htmlFor="ipList">允許的 IP 地址</Label>
                  <textarea
                    id="ipList"
                    rows={4}
                    value={settings.security.ipWhitelist.join('\n')}
                    onChange={(e) => updateSettings('security', 'ipWhitelist', e.target.value.split('\n').filter(ip => ip.trim()))}
                    placeholder="***********&#10;********&#10;..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <p className="text-sm text-gray-600 mt-1">
                    每行輸入一個 IP 地址
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 系統狀態顯示 */}
      {Object.keys(testResults).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <span>系統狀態</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {testResults.openai && (
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-green-600 mb-1">
                    ✓
                  </div>
                  <div className="text-sm font-medium text-gray-900">OpenAI API</div>
                  <div className="text-xs text-gray-600">{testResults.openai.latency}</div>
                </div>
              )}
              
              {testResults.database && (
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-green-600 mb-1">
                    ✓
                  </div>
                  <div className="text-sm font-medium text-gray-900">資料庫</div>
                  <div className="text-xs text-gray-600">{testResults.database.connections} 連線</div>
                </div>
              )}
              
              {testResults.cache && (
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-green-600 mb-1">
                    ✓
                  </div>
                  <div className="text-sm font-medium text-gray-900">快取系統</div>
                  <div className="text-xs text-gray-600">{testResults.cache.hit_rate} 命中率</div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
