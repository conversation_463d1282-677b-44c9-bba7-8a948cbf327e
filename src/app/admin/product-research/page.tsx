'use client';

import React from 'react';
import Link from 'next/link';
import {
  Search,
  Target,
  TrendingUp,
  Users,
  BarChart3,
  Eye,
  Brain,
  Layers,
  ArrowRight,
  Plus,
  Settings,
  LogIn,
  AlertCircle,
  Sparkles
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/contexts/AuthContext';
import { useResponsive } from '@/hooks/useProductResearch';
import {
  ResponsiveContainer,
  ResponsiveGrid,
  ResponsiveCard,
  ResponsiveStack,
  ResponsiveText,
  ResponsiveShow
} from '@/components/layout/ResponsiveLayout';
import { RealTimeNotifications } from '@/components/notifications/RealTimeNotifications';

export default function ProductResearchDashboard() {
  const { isMobile, isTablet, isDesktop } = useResponsive();
  const { user, isAuthenticated, isLoading, hasRole } = useAuth();
  const modules = [
    {
      title: '品牌可見度分析',
      description: '分析品牌在不同地區和人群中的可見度表現，追蹤時間趨勢和競爭對手比較',
      icon: Eye,
      href: '/admin/product-research/visibility',
      gradient: 'from-blue-500 to-blue-600',
      features: ['時間趨勢分析', '地區分布', '受眾分割', '競爭對手比較']
    },
    {
      title: '查詢智能分析',
      description: '深度分析用戶查詢意圖，提供搜尋量、競爭度、轉換率等關鍵指標',
      icon: Brain,
      href: '/admin/product-research/query-intelligence',
      gradient: 'from-green-500 to-green-600',
      features: ['意圖識別', '競爭分析', '解決方案推薦', '關鍵字建議']
    },
    {
      title: '主題分析',
      description: '識別和分析相關主題，了解用戶興趣分布和主題相關性',
      icon: Layers,
      href: '/admin/product-research/topics',
      gradient: 'from-purple-500 to-purple-600',
      features: ['主題聚類', '相關性評分', '興趣地圖', 'AI 洞察']
    },
    {
      title: '戰略分割',
      description: '按產品、地區、角色、垂直領域進行戰略分割，制定個人化策略',
      icon: Users,
      href: '/admin/product-research/segments',
      gradient: 'from-orange-500 to-orange-600',
      features: ['受眾分割', '地區分析', '角色定位', '機會評估']
    }
  ];

  const quickActions = [
    {
      title: '創建新品牌',
      description: '添加新品牌進行分析',
      href: '/admin/product-research/brands/new',
      icon: Target,
      gradient: 'from-primary to-primary-light'
    },
    {
      title: '執行查詢分析',
      description: '分析特定查詢的智能洞察',
      href: '/admin/product-research/query-intelligence',
      icon: Search,
      gradient: 'from-accent to-accent-green'
    },
    {
      title: '查看分析報告',
      description: '瀏覽最新的研究報告',
      href: '/admin/product-research/reports',
      icon: BarChart3,
      gradient: 'from-purple-500 to-purple-600'
    },
    {
      title: '系統設定',
      description: '配置產品研究模組',
      href: '/admin/product-research/settings',
      icon: Settings,
      gradient: 'from-text-secondary to-text-primary'
    }
  ];

  // 載入中狀態
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background to-surface-1/30 flex items-center justify-center">
        <div className="text-center">
          <div className="relative">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-border-light border-t-primary mx-auto mb-6"></div>
            <div className="absolute inset-0 rounded-full h-16 w-16 border-4 border-primary/20 mx-auto"></div>
          </div>
          <p className="text-text-secondary text-lg">載入中...</p>
        </div>
      </div>
    );
  }

  // 未認證狀態
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background to-surface-1/30 pt-20">
        <ResponsiveContainer>
          <ResponsiveStack spacing={{ xs: 4, sm: 4, md: 6, lg: 6 }}>
            <div className="text-center py-16">
              <div className="relative mb-8">
                <div className="w-24 h-24 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-3xl flex items-center justify-center mx-auto shadow-glow">
                  <AlertCircle className="h-12 w-12 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">!</span>
                </div>
              </div>
              
              <h1 className="text-3xl sm:text-4xl font-bold text-text-primary mb-4">需要登入</h1>
              <p className="text-text-secondary mb-8 max-w-md mx-auto leading-relaxed">
                您需要登入才能訪問產品研究中心。請使用您的帳戶登入或使用以下測試帳戶。
              </p>
              
              <Alert className="max-w-md mx-auto mb-8 bg-surface-1/60 border-border-light backdrop-blur-sm">
                <AlertCircle className="h-4 w-4 text-accent" />
                <AlertDescription className="text-text-secondary">
                  <strong className="text-text-primary">測試帳戶：</strong><br />
                  Email: <EMAIL><br />
                  Password: demo123456
                </AlertDescription>
              </Alert>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" variant="gradient" className="group">
                  <Link href="/auth/login">
                    <LogIn className="h-4 w-4 mr-2" />
                    前往登入頁面
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200" />
                  </Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/">
                    回到首頁
                  </Link>
                </Button>
              </div>
            </div>
          </ResponsiveStack>
        </ResponsiveContainer>
      </div>
    );
  }

  // 權限檢查
  if (!hasRole('admin')) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background to-surface-1/30 pt-20">
        <ResponsiveContainer>
          <ResponsiveStack spacing={{ xs: 4, sm: 4, md: 6, lg: 6 }}>
            <div className="text-center py-16">
              <div className="w-24 h-24 bg-gradient-to-br from-red-400 to-red-500 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-glow">
                <AlertCircle className="h-12 w-12 text-white" />
              </div>
              
              <h1 className="text-3xl sm:text-4xl font-bold text-text-primary mb-4">權限不足</h1>
              <p className="text-text-secondary mb-8 max-w-md mx-auto leading-relaxed">
                您需要管理員權限才能訪問產品研究中心。
              </p>
              <Button variant="outline" size="lg" asChild>
                <Link href="/admin">
                  前往管理後台
                </Link>
              </Button>
            </div>
          </ResponsiveStack>
        </ResponsiveContainer>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-surface-1/30">
      <ResponsiveContainer>
        <ResponsiveStack spacing={{ xs: 4, sm: 4, md: 6, lg: 6 }}>
          {/* 頁面標題 */}
          <div className={`flex ${isMobile ? 'flex-col space-y-4' : 'justify-between items-center'} pt-6`}>
            <div>
              <div className="flex items-center gap-3 mb-3">
                <div className="p-3 rounded-2xl bg-gradient-primary shadow-glow">
                  <Target className="h-8 w-8 text-white" />
                </div>
                <ResponsiveText
                  size={{ xs: 'xl', sm: '2xl', md: '3xl' }}
                  weight="bold"
                  className="text-text-primary"
                >
                  產品研究中心
                </ResponsiveText>
              </div>
              <ResponsiveText
                size={{ xs: 'sm', sm: 'base' }}
                color="text-text-secondary"
                className="leading-relaxed"
              >
                基於 AI 驅動的品牌可見度分析和競爭情報平台
              </ResponsiveText>
            </div>
            <div className={`flex ${isMobile ? 'flex-col w-full space-y-2' : 'items-center space-x-3'}`}>
              <RealTimeNotifications className={isMobile ? 'w-full' : ''} />
              <Button size="lg" variant="gradient" className="group">
                <Sparkles className="h-4 w-4 mr-2" />
                開始新分析
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200" />
              </Button>
            </div>
          </div>

          {/* 功能模組卡片 */}
          <div className="space-y-8">
            <div>
              <h2 className="text-2xl font-bold text-text-primary mb-2">核心分析模組</h2>
              <p className="text-text-secondary">全面的品牌可見度分析工具，助您深入了解市場表現</p>
            </div>
            
            <ResponsiveGrid
              columns={{ xs: 1, sm: 1, md: 2, lg: 2 }}
              gap={{ xs: 4, sm: 4, md: 6, lg: 6 }}
            >
              {modules.map((module, index) => (
                <ResponsiveCard key={module.title} className="group">
                  <Card className="h-full bg-card/60 backdrop-blur-sm border-border-light hover:shadow-glow transition-all duration-300 group-hover:scale-[1.02]">
                    <CardHeader className="pb-4">
                      <div className="flex items-center gap-4 mb-3">
                        <div className={`p-3 rounded-2xl bg-gradient-to-br ${module.gradient} shadow-md group-hover:shadow-glow transition-shadow duration-300`}>
                          <module.icon className="h-6 w-6 text-white" />
                        </div>
                        <CardTitle className="text-text-primary text-xl group-hover:text-gradient transition-colors duration-300">
                          {module.title}
                        </CardTitle>
                      </div>
                      <CardDescription className="text-text-secondary leading-relaxed">
                        {module.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div>
                          <h4 className="text-sm font-semibold text-text-primary mb-2 flex items-center gap-2">
                            <div className="w-1 h-4 bg-gradient-to-b from-primary to-accent rounded-full" />
                            主要功能
                          </h4>
                          <ul className="space-y-2">
                            {module.features.map((feature, featureIndex) => (
                              <li key={featureIndex} className="flex items-center text-sm text-text-secondary">
                                <div className="w-1.5 h-1.5 rounded-full bg-primary/60 mr-3" />
                                {feature}
                              </li>
                            ))}
                          </ul>
                        </div>
                        <Button asChild variant="outline" className="w-full group">
                          <Link href={module.href}>
                            進入模組
                            <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200" />
                          </Link>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </ResponsiveCard>
              ))}
            </ResponsiveGrid>
          </div>

          {/* 快速操作 */}
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-text-primary mb-2">快速操作</h2>
              <p className="text-text-secondary">常用功能快速入口，提升工作效率</p>
            </div>
            
            <ResponsiveGrid
              columns={{ xs: 1, sm: 2, md: 2, lg: 4 }}
              gap={{ xs: 3, sm: 4, md: 4, lg: 4 }}
            >
              {quickActions.map((action, index) => (
                <ResponsiveCard key={action.title} className="group">
                  <Card className="h-full bg-card/60 backdrop-blur-sm border-border-light hover:shadow-glow transition-all duration-300 group-hover:scale-105">
                    <CardContent className="p-6 text-center">
                      <div className={`w-12 h-12 bg-gradient-to-br ${action.gradient} rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-md group-hover:shadow-glow transition-shadow duration-300`}>
                        <action.icon className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="font-semibold text-text-primary mb-2 group-hover:text-gradient transition-colors duration-300">
                        {action.title}
                      </h3>
                      <p className="text-sm text-text-secondary mb-4 leading-relaxed">
                        {action.description}
                      </p>
                      <Button asChild size="sm" variant="outline" className="w-full group">
                        <Link href={action.href}>
                          開始
                          <ArrowRight className="ml-1 h-3 w-3 group-hover:translate-x-1 transition-transform duration-200" />
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                </ResponsiveCard>
              ))}
            </ResponsiveGrid>
          </div>

          {/* 統計資訊 */}
          <div className="bg-gradient-to-r from-primary/5 to-accent/5 rounded-3xl p-8 border border-border-light/60">
            <div className="text-center mb-6">
              <h3 className="text-xl font-bold text-text-primary mb-2">平台統計</h3>
              <p className="text-text-secondary">實時數據概覽</p>
            </div>
            
            <ResponsiveGrid
              columns={{ xs: 2, sm: 4, md: 4, lg: 4 }}
              gap={{ xs: 4, sm: 4, md: 6, lg: 6 }}
            >
              <div className="text-center">
                <div className="text-2xl sm:text-3xl font-bold text-gradient mb-1">12+</div>
                <div className="text-sm text-text-secondary">追蹤品牌</div>
              </div>
              <div className="text-center">
                <div className="text-2xl sm:text-3xl font-bold text-gradient mb-1">2.8K+</div>
                <div className="text-sm text-text-secondary">查詢分析</div>
              </div>
              <div className="text-center">
                <div className="text-2xl sm:text-3xl font-bold text-gradient mb-1">73.5%</div>
                <div className="text-sm text-text-secondary">平均可見度</div>
              </div>
              <div className="text-center">
                <div className="text-2xl sm:text-3xl font-bold text-gradient mb-1">24+</div>
                <div className="text-sm text-text-secondary">競爭對手</div>
              </div>
            </ResponsiveGrid>
          </div>
        </ResponsiveStack>
      </ResponsiveContainer>
    </div>
  );
}
