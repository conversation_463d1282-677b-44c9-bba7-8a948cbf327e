'use client';

import React from 'react';
import Link from 'next/link';
import {
  Search,
  Target,
  TrendingUp,
  Users,
  BarChart3,
  Eye,
  Brain,
  Layers,
  ArrowRight,
  Plus,
  Settings,
  LogIn,
  AlertCircle
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/contexts/AuthContext';
import { useResponsive } from '@/hooks/useProductResearch';
import {
  ResponsiveContainer,
  ResponsiveGrid,
  ResponsiveCard,
  ResponsiveStack,
  ResponsiveText,
  ResponsiveShow
} from '@/components/layout/ResponsiveLayout';
import { RealTimeNotifications } from '@/components/notifications/RealTimeNotifications';

export default function ProductResearchDashboard() {
  const { isMobile, isTablet, isDesktop } = useResponsive();
  const { user, isAuthenticated, isLoading, hasRole } = useAuth();
  const modules = [
    {
      title: '品牌可見度分析',
      description: '分析品牌在不同地區和人群中的可見度表現，追蹤時間趨勢和競爭對手比較',
      icon: Eye,
      href: '/admin/product-research/visibility',
      color: 'bg-blue-500',
      features: ['時間趨勢分析', '地區分布', '受眾分割', '競爭對手比較']
    },
    {
      title: '查詢智能分析',
      description: '深度分析用戶查詢意圖，提供搜尋量、競爭度、轉換率等關鍵指標',
      icon: Brain,
      href: '/admin/product-research/query-intelligence',
      color: 'bg-green-500',
      features: ['意圖識別', '競爭分析', '解決方案推薦', '關鍵字建議']
    },
    {
      title: '主題分析',
      description: '識別和分析相關主題，了解用戶興趣分布和主題相關性',
      icon: Layers,
      href: '/admin/product-research/topics',
      color: 'bg-purple-500',
      features: ['主題聚類', '相關性評分', '興趣地圖', 'AI 洞察']
    },
    {
      title: '戰略分割',
      description: '按產品、地區、角色、垂直領域進行戰略分割，制定個人化策略',
      icon: Users,
      href: '/admin/product-research/segments',
      color: 'bg-orange-500',
      features: ['受眾分割', '地區分析', '角色定位', '機會評估']
    }
  ];

  const quickActions = [
    {
      title: '創建新品牌',
      description: '添加新品牌進行分析',
      href: '/admin/product-research/brands/new',
      icon: Target,
      color: 'bg-blue-600'
    },
    {
      title: '執行查詢分析',
      description: '分析特定查詢的智能洞察',
      href: '/admin/product-research/query-intelligence',
      icon: Search,
      color: 'bg-green-600'
    },
    {
      title: '查看分析報告',
      description: '瀏覽最新的研究報告',
      href: '/admin/product-research/reports',
      icon: BarChart3,
      color: 'bg-purple-600'
    },
    {
      title: '系統設定',
      description: '配置產品研究模組',
      href: '/admin/product-research/settings',
      icon: Settings,
      color: 'bg-gray-600'
    }
  ];

  // 載入中狀態
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入中...</p>
        </div>
      </div>
    );
  }

  // 未認證狀態
  if (!isAuthenticated) {
    return (
      <ResponsiveContainer>
        <ResponsiveStack spacing={{ xs: 4, sm: 4, md: 6, lg: 6 }}>
          <div className="text-center py-16">
            <AlertCircle className="h-16 w-16 text-yellow-500 mx-auto mb-6" />
            <h1 className="text-3xl font-bold text-gray-900 mb-4">需要登入</h1>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              您需要登入才能訪問產品研究中心。請使用您的帳戶登入或使用以下測試帳戶。
            </p>
            
            <Alert className="max-w-md mx-auto mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>測試帳戶：</strong><br />
                Email: <EMAIL><br />
                Password: demo123456
              </AlertDescription>
            </Alert>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg">
                <Link href="/auth/login">
                  <LogIn className="h-4 w-4 mr-2" />
                  前往登入頁面
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/">
                  回到首頁
                </Link>
              </Button>
            </div>
          </div>
        </ResponsiveStack>
      </ResponsiveContainer>
    );
  }

  // 權限檢查
  if (!hasRole('admin')) {
    return (
      <ResponsiveContainer>
        <ResponsiveStack spacing={{ xs: 4, sm: 4, md: 6, lg: 6 }}>
          <div className="text-center py-16">
            <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-6" />
            <h1 className="text-3xl font-bold text-gray-900 mb-4">權限不足</h1>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              您需要管理員權限才能訪問產品研究中心。
            </p>
            <Button variant="outline" size="lg" asChild>
              <Link href="/dashboard">
                前往用戶儀表板
              </Link>
            </Button>
          </div>
        </ResponsiveStack>
      </ResponsiveContainer>
    );
  }

  return (
    <ResponsiveContainer>
      <ResponsiveStack spacing={{ xs: 4, sm: 4, md: 6, lg: 6 }}>
        {/* 頁面標題 */}
        <div className={`flex ${isMobile ? 'flex-col space-y-4' : 'justify-between items-center'}`}>
          <div>
            <ResponsiveText
              size={{ xs: 'xl', sm: '2xl', md: '3xl' }}
              weight="bold"
              className="text-gray-900"
            >
              產品研究中心
            </ResponsiveText>
            <ResponsiveText
              size={{ xs: 'sm', sm: 'base' }}
              color="text-gray-600"
              className="mt-2"
            >
              基於 AI 驅動的品牌可見度分析和競爭情報平台
            </ResponsiveText>
          </div>
          <div className={`flex ${isMobile ? 'flex-col w-full space-y-2' : 'items-center space-x-3'}`}>
            <RealTimeNotifications className={isMobile ? 'w-full' : ''} />
            <Button asChild size={isMobile ? 'default' : 'default'} className={isMobile ? 'w-full' : ''}>
              <Link href="/admin/product-research/brands/new">
                <Plus className="h-4 w-4 mr-2" />
                創建品牌
              </Link>
            </Button>
          </div>
        </div>

        {/* 快速操作 */}
        <ResponsiveGrid
          columns={{ xs: 1, sm: 2, md: 3, lg: 4 }}
          gap={{ xs: 4, sm: 4, md: 6 }}
        >
          {quickActions.map((action, index) => (
            <ResponsiveCard key={index} hover className="cursor-pointer">
              <Link href={action.href}>
                <div className={`${isMobile ? 'p-4' : 'p-6'}`}>
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${action.color}`}>
                      <action.icon className="h-5 w-5 text-white" />
                    </div>
                    <div className="flex-1">
                      <ResponsiveText
                        size={{ xs: 'base', sm: 'lg' }}
                        weight="semibold"
                        className="text-gray-900"
                      >
                        {action.title}
                      </ResponsiveText>
                      <ResponsiveText
                        size={{ xs: 'xs', sm: 'sm' }}
                        color="text-gray-600"
                        className="mt-1"
                      >
                        {action.description}
                      </ResponsiveText>
                    </div>
                  </div>
                </div>
              </Link>
            </ResponsiveCard>
          ))}
        </ResponsiveGrid>

        {/* 功能模組 */}
        <ResponsiveGrid
          columns={{ xs: 1, sm: 1, md: 1, lg: 2 }}
          gap={{ xs: 4, sm: 4, md: 6 }}
        >
          {modules.map((module, index) => (
            <ResponsiveCard key={index} hover className="transition-all duration-300">
              <div className={`${isMobile ? 'p-4' : 'p-6'}`}>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className={`p-3 rounded-lg ${module.color}`}>
                      <module.icon className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <ResponsiveText
                        size={{ xs: 'lg', sm: 'xl' }}
                        weight="semibold"
                        className="text-gray-900"
                      >
                        {module.title}
                      </ResponsiveText>
                      <ResponsiveText
                        size={{ xs: 'sm', sm: 'base' }}
                        color="text-gray-600"
                        className="mt-1"
                      >
                        {module.description}
                      </ResponsiveText>
                    </div>
                  </div>
                  <ResponsiveShow hide={['xs']}>
                    <ArrowRight className="h-5 w-5 text-gray-400" />
                  </ResponsiveShow>
                </div>
                <div className="space-y-3">
                  <ResponsiveText
                    size={{ xs: 'sm', sm: 'base' }}
                    weight="medium"
                    className="text-gray-900"
                  >
                    核心功能：
                  </ResponsiveText>
                  <ResponsiveGrid
                    columns={{ xs: 1, sm: 2 }}
                    gap={{ xs: 2, sm: 2 }}
                  >
                    {module.features.map((feature, idx) => (
                      <div key={idx} className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-gray-300 rounded-full flex-shrink-0"></div>
                        <ResponsiveText
                          size={{ xs: 'xs', sm: 'sm' }}
                          color="text-gray-600"
                        >
                          {feature}
                        </ResponsiveText>
                      </div>
                    ))}
                  </ResponsiveGrid>
                  <div className="pt-3">
                    <Button asChild variant="outline" className="w-full">
                      <Link href={module.href}>
                        開始使用
                        <ArrowRight className="h-4 w-4 ml-2" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </ResponsiveCard>
          ))}
        </ResponsiveGrid>

      {/* 系統狀態 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>系統概覽</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">0</div>
              <div className="text-sm text-gray-500">活躍品牌</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">0</div>
              <div className="text-sm text-gray-500">完成分析</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">0</div>
              <div className="text-sm text-gray-500">查詢智能</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">0</div>
              <div className="text-sm text-gray-500">生成報告</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 使用指南 */}
      <Card>
        <CardHeader>
          <CardTitle>快速開始指南</CardTitle>
          <CardDescription>
            按照以下步驟開始使用產品研究功能
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                1
              </div>
              <div>
                <h4 className="font-medium">創建品牌檔案</h4>
                <p className="text-sm text-gray-600">
                  添加您的品牌信息，包括行業、目標地區、競爭對手等基本資料
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                2
              </div>
              <div>
                <h4 className="font-medium">執行可見度分析</h4>
                <p className="text-sm text-gray-600">
                  使用 AI 分析品牌在不同地區和受眾中的可見度表現
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                3
              </div>
              <div>
                <h4 className="font-medium">分析查詢智能</h4>
                <p className="text-sm text-gray-600">
                  深入了解用戶查詢意圖，獲取競爭情報和解決方案推薦
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                4
              </div>
              <div>
                <h4 className="font-medium">制定戰略計劃</h4>
                <p className="text-sm text-gray-600">
                  基於分析結果制定針對性的市場策略和優化方案
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      </ResponsiveStack>
    </ResponsiveContainer>
  );
}
