/**
 * 優化功能主頁面測試
 */

import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { jest } from '@jest/globals';
import OptimizePage from '../page';

// Mock Next.js router
const mockPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}));

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Mock fetch
global.fetch = jest.fn() as jest.MockedFunction<typeof fetch>;

const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

describe('OptimizePage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue('mock-token');
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('應該正確渲染頁面標題和描述', () => {
    // Mock successful API responses
    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          total_recommendations: 10,
          active_recommendations: 5,
          completed_recommendations: 3,
          total_experiments: 4,
          running_experiments: 2,
          total_actions: 8,
          completed_actions: 6,
          average_completion_rate: 75.5,
          top_categories: [
            { category: 'SEO', count: 5 },
            { category: '性能', count: 3 }
          ],
          recent_activities: [
            {
              type: 'recommendation',
              title: '測試建議',
              status: 'completed',
              updated_at: '2024-01-01T00:00:00Z'
            }
          ]
        }),
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          items: [
            {
              id: '1',
              title: '測試建議',
              description: '測試描述',
              type: 'seo',
              status: 'pending',
              priority: 8,
              impact_score: 7.5,
              progress: 25,
              created_at: '2024-01-01T00:00:00Z'
            }
          ],
          total: 1,
          page: 1,
          size: 5,
          pages: 1
        }),
      } as Response);

    render(<OptimizePage />);

    expect(screen.getByText('優化功能')).toBeInTheDocument();
    expect(screen.getByText('AI 驅動的網站優化建議和實驗追蹤')).toBeInTheDocument();
  });

  it('應該顯示載入狀態', () => {
    // Mock pending fetch
    mockFetch.mockImplementation(
      () => new Promise(() => {}) // Never resolves
    );

    render(<OptimizePage />);

    expect(screen.getByText('載入中...')).toBeInTheDocument();
    expect(screen.getAllByRole('generic')).toHaveLength(expect.any(Number));
  });

  it('應該正確顯示統計數據', async () => {
    const mockStats = {
      total_recommendations: 15,
      active_recommendations: 8,
      completed_recommendations: 7,
      total_experiments: 5,
      running_experiments: 2,
      total_actions: 12,
      completed_actions: 9,
      average_completion_rate: 80.5,
      top_categories: [],
      recent_activities: []
    };

    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ items: [], total: 0, page: 1, size: 5, pages: 0 }),
      } as Response);

    render(<OptimizePage />);

    await waitFor(() => {
      expect(screen.getByText('15')).toBeInTheDocument(); // total_recommendations
      expect(screen.getByText('5')).toBeInTheDocument();  // total_experiments
      expect(screen.getByText('12')).toBeInTheDocument(); // total_actions
      expect(screen.getByText('80.5%')).toBeInTheDocument(); // completion_rate
    });
  });

  it('應該正確顯示功能模組卡片', async () => {
    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          total_recommendations: 0,
          active_recommendations: 0,
          completed_recommendations: 0,
          total_experiments: 0,
          running_experiments: 0,
          total_actions: 0,
          completed_actions: 0,
          average_completion_rate: 0,
          top_categories: [],
          recent_activities: []
        }),
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ items: [], total: 0, page: 1, size: 5, pages: 0 }),
      } as Response);

    render(<OptimizePage />);

    await waitFor(() => {
      expect(screen.getByText('自定義建議')).toBeInTheDocument();
      expect(screen.getByText('實驗追蹤')).toBeInTheDocument();
      expect(screen.getByText('AI 搜尋劇本')).toBeInTheDocument();
      expect(screen.getByText('執行行動')).toBeInTheDocument();
    });

    expect(screen.getByText('個人化優化建議')).toBeInTheDocument();
    expect(screen.getByText('A/B 測試和影響測量')).toBeInTheDocument();
    expect(screen.getByText('經過驗證的優化策略')).toBeInTheDocument();
    expect(screen.getByText('內容優化和實施支援')).toBeInTheDocument();
  });

  it('應該正確顯示最新建議', async () => {
    const mockRecommendations = [
      {
        id: '1',
        title: '優化頁面載入速度',
        description: '減少頁面載入時間',
        type: 'performance',
        status: 'in_progress',
        priority: 9,
        impact_score: 8.5,
        progress: 60,
        created_at: '2024-01-01T00:00:00Z'
      },
      {
        id: '2',
        title: '改善 SEO 標題',
        description: '優化頁面標題標籤',
        type: 'seo',
        status: 'pending',
        priority: 7,
        impact_score: 6.0,
        progress: 0,
        created_at: '2024-01-02T00:00:00Z'
      }
    ];

    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          total_recommendations: 2,
          active_recommendations: 2,
          completed_recommendations: 0,
          total_experiments: 0,
          running_experiments: 0,
          total_actions: 0,
          completed_actions: 0,
          average_completion_rate: 0,
          top_categories: [],
          recent_activities: []
        }),
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          items: mockRecommendations,
          total: 2,
          page: 1,
          size: 5,
          pages: 1
        }),
      } as Response);

    render(<OptimizePage />);

    await waitFor(() => {
      expect(screen.getByText('優化頁面載入速度')).toBeInTheDocument();
      expect(screen.getByText('改善 SEO 標題')).toBeInTheDocument();
    });

    expect(screen.getByText('優先級 9')).toBeInTheDocument();
    expect(screen.getByText('優先級 7')).toBeInTheDocument();
  });

  it('應該處理 API 錯誤', async () => {
    const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;
    mockFetch.mockRejectedValue(new Error('API Error'));

    render(<OptimizePage />);

    await waitFor(() => {
      expect(screen.getByText('載入數據失敗，請稍後重試')).toBeInTheDocument();
    });

    const retryButton = screen.getByText('重新載入');
    expect(retryButton).toBeInTheDocument();
  });

  it('應該正確處理重新載入', async () => {
    // First call fails
    const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;
    mockFetch
      .mockRejectedValueOnce(new Error('API Error'))
      // Second call succeeds
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          total_recommendations: 0,
          active_recommendations: 0,
          completed_recommendations: 0,
          total_experiments: 0,
          running_experiments: 0,
          total_actions: 0,
          completed_actions: 0,
          average_completion_rate: 0,
          top_categories: [],
          recent_activities: []
        }),
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ items: [], total: 0, page: 1, size: 5, pages: 0 }),
      } as Response);

    render(<OptimizePage />);

    await waitFor(() => {
      expect(screen.getByText('載入數據失敗，請稍後重試')).toBeInTheDocument();
    });

    const retryButton = screen.getByText('重新載入');
    fireEvent.click(retryButton);

    await waitFor(() => {
      expect(screen.getByText('優化功能')).toBeInTheDocument();
      expect(screen.queryByText('載入數據失敗，請稍後重試')).not.toBeInTheDocument();
    });
  });

  it('應該正確顯示狀態顏色', async () => {
    const mockRecommendations = [
      {
        id: '1',
        title: '已完成建議',
        type: 'seo',
        status: 'completed',
        priority: 5,
        progress: 100,
        created_at: '2024-01-01T00:00:00Z'
      },
      {
        id: '2',
        title: '進行中建議',
        type: 'performance',
        status: 'in_progress',
        priority: 8,
        progress: 50,
        created_at: '2024-01-02T00:00:00Z'
      }
    ];

    const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;
    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          total_recommendations: 2,
          active_recommendations: 1,
          completed_recommendations: 1,
          total_experiments: 0,
          running_experiments: 0,
          total_actions: 0,
          completed_actions: 0,
          average_completion_rate: 50,
          top_categories: [],
          recent_activities: []
        }),
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          items: mockRecommendations,
          total: 2,
          page: 1,
          size: 5,
          pages: 1
        }),
      } as Response);

    render(<OptimizePage />);

    await waitFor(() => {
      const completedBadge = screen.getByText('completed');
      const inProgressBadge = screen.getByText('in_progress');
      
      expect(completedBadge).toBeInTheDocument();
      expect(inProgressBadge).toBeInTheDocument();
    });
  });

  it('應該正確顯示類型圖標', async () => {
    const mockRecommendations = [
      {
        id: '1',
        title: 'SEO 建議',
        type: 'seo',
        status: 'pending',
        priority: 5,
        progress: 0,
        created_at: '2024-01-01T00:00:00Z'
      },
      {
        id: '2',
        title: '性能建議',
        type: 'performance',
        status: 'pending',
        priority: 8,
        progress: 0,
        created_at: '2024-01-02T00:00:00Z'
      }
    ];

    const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;
    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          total_recommendations: 2,
          active_recommendations: 2,
          completed_recommendations: 0,
          total_experiments: 0,
          running_experiments: 0,
          total_actions: 0,
          completed_actions: 0,
          average_completion_rate: 0,
          top_categories: [],
          recent_activities: []
        }),
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          items: mockRecommendations,
          total: 2,
          page: 1,
          size: 5,
          pages: 1
        }),
      } as Response);

    render(<OptimizePage />);

    await waitFor(() => {
      expect(screen.getByText('SEO 建議')).toBeInTheDocument();
      expect(screen.getByText('性能建議')).toBeInTheDocument();
    });
  });

  it('應該正確處理空數據狀態', async () => {
    const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;
    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          total_recommendations: 0,
          active_recommendations: 0,
          completed_recommendations: 0,
          total_experiments: 0,
          running_experiments: 0,
          total_actions: 0,
          completed_actions: 0,
          average_completion_rate: 0,
          top_categories: [],
          recent_activities: []
        }),
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ items: [], total: 0, page: 1, size: 5, pages: 0 }),
      } as Response);

    render(<OptimizePage />);

    await waitFor(() => {
      expect(screen.getByText('暫無建議')).toBeInTheDocument();
    });
  });
});
