'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  Target, 
  TrendingUp, 
  Zap, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  BarChart3,
  Lightbulb,
  PlayCircle,
  Activity,
  Plus,
  ArrowRight,
  Sparkles
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';

// 類型定義
interface DashboardStats {
  total_recommendations: number;
  active_recommendations: number;
  completed_recommendations: number;
  total_experiments: number;
  running_experiments: number;
  total_actions: number;
  completed_actions: number;
  average_completion_rate: number;
  top_categories: Array<{ category: string; count: number }>;
  recent_activities: Array<{
    type: string;
    title: string;
    status: string;
    updated_at: string;
  }>;
}

interface OptimizationRecommendation {
  id: string;
  title: string;
  description: string;
  type: string;
  status: string;
  priority: number;
  impact_score: number;
  progress: number;
  created_at: string;
  due_date?: string;
}

// 模擬數據
const mockStats: DashboardStats = {
  total_recommendations: 45,
  active_recommendations: 12,
  completed_recommendations: 28,
  total_experiments: 8,
  running_experiments: 3,
  total_actions: 156,
  completed_actions: 132,
  average_completion_rate: 84.6,
  top_categories: [
    { category: '內容優化', count: 18 },
    { category: '技術修復', count: 15 },
    { category: '性能提升', count: 12 },
  ],
  recent_activities: [
    { type: 'recommendation', title: '優化頁面載入速度', status: 'completed', updated_at: '2024-06-24 14:30' },
    { type: 'experiment', title: 'A/B 測試標題優化', status: 'running', updated_at: '2024-06-24 13:45' },
    { type: 'action', title: '修復內部連結', status: 'pending', updated_at: '2024-06-24 12:15' },
  ]
};

const mockRecommendations: OptimizationRecommendation[] = [
  {
    id: '1',
    title: '優化網站載入速度',
    description: '壓縮圖片並啟用快取以提升頁面載入性能',
    type: 'performance',
    status: 'active',
    priority: 1,
    impact_score: 8.5,
    progress: 75,
    created_at: '2024-06-20'
  },
  {
    id: '2',
    title: '改善標題標籤結構',
    description: '優化 H1-H6 標籤的層次結構，提升 SEO 效果',
    type: 'content',
    status: 'pending',
    priority: 2,
    impact_score: 7.2,
    progress: 0,
    created_at: '2024-06-22'
  },
  {
    id: '3',
    title: '修復斷裂連結',
    description: '檢測並修復網站上的 404 錯誤連結',
    type: 'technical',
    status: 'completed',
    priority: 3,
    impact_score: 6.8,
    progress: 100,
    created_at: '2024-06-18'
  }
];

export default function OptimizePage() {
  const [stats, setStats] = useState<DashboardStats>(mockStats);
  const [recommendations, setRecommendations] = useState<OptimizationRecommendation[]>(mockRecommendations);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // 在實際使用時會呼叫 loadDashboardData()
    setLoading(false);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 border-green-200';
      case 'active': 
      case 'running': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'failed': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-surface-1/60 text-text-secondary border-border-light';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'content': return <Lightbulb className="h-4 w-4" />;
      case 'technical': return <Zap className="h-4 w-4" />;
      case 'performance': return <TrendingUp className="h-4 w-4" />;
      case 'seo': return <Target className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const getTypeGradient = (type: string) => {
    switch (type) {
      case 'content': return 'from-blue-500 to-blue-600';
      case 'technical': return 'from-purple-500 to-purple-600';
      case 'performance': return 'from-green-500 to-green-600';
      case 'seo': return 'from-orange-500 to-orange-600';
      default: return 'from-primary to-primary-light';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background to-surface-1/30 flex items-center justify-center">
        <div className="text-center">
          <div className="relative">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-border-light border-t-primary mx-auto mb-6"></div>
            <div className="absolute inset-0 rounded-full h-16 w-16 border-4 border-primary/20 mx-auto"></div>
          </div>
          <p className="text-text-secondary text-lg">載入優化數據中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background to-surface-1/30 flex items-center justify-center">
        <Alert variant="destructive" className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-surface-1/30">
      <div className="container-section py-8 space-y-8">
        {/* 頁面標題 */}
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
          <div>
            <div className="flex items-center gap-3 mb-3">
              <div className="p-3 rounded-2xl bg-gradient-primary shadow-glow">
                <Zap className="h-8 w-8 text-white" />
              </div>
              <h1 className="text-3xl sm:text-4xl font-bold text-text-primary">
                優化功能
              </h1>
            </div>
            <p className="text-text-secondary text-lg leading-relaxed max-w-2xl">
              AI 驅動的網站優化建議和實驗追蹤，助您提升網站表現和用戶體驗
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3">
            <Button variant="outline" size="lg" className="group">
              <PlayCircle className="h-4 w-4 mr-2" />
              新實驗
            </Button>
            <Button variant="gradient" size="lg" className="group">
              <Sparkles className="h-4 w-4 mr-2" />
              生成建議
              <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200" />
            </Button>
          </div>
        </div>

        {/* 統計卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-card/60 backdrop-blur-sm border-border-light hover:shadow-glow transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-text-secondary">總建議數</p>
                  <p className="text-2xl font-bold text-text-primary">{stats.total_recommendations}</p>
                </div>
                <div className="p-2 rounded-xl bg-gradient-to-br from-blue-500/20 to-blue-600/20">
                  <Lightbulb className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div className="mt-4">
                <div className="flex items-center text-xs text-text-secondary">
                  <span className="text-green-600 font-medium mr-1">+{stats.completed_recommendations}</span>
                  已完成
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-card/60 backdrop-blur-sm border-border-light hover:shadow-glow transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-text-secondary">進行中實驗</p>
                  <p className="text-2xl font-bold text-text-primary">{stats.running_experiments}</p>
                </div>
                <div className="p-2 rounded-xl bg-gradient-to-br from-green-500/20 to-green-600/20">
                  <PlayCircle className="h-6 w-6 text-green-600" />
                </div>
              </div>
              <div className="mt-4">
                <div className="flex items-center text-xs text-text-secondary">
                  總計 <span className="font-medium ml-1">{stats.total_experiments}</span> 個實驗
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-card/60 backdrop-blur-sm border-border-light hover:shadow-glow transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-text-secondary">執行行動</p>
                  <p className="text-2xl font-bold text-text-primary">{stats.completed_actions}</p>
                </div>
                <div className="p-2 rounded-xl bg-gradient-to-br from-purple-500/20 to-purple-600/20">
                  <Target className="h-6 w-6 text-purple-600" />
                </div>
              </div>
              <div className="mt-4">
                <div className="flex items-center text-xs text-text-secondary">
                  / {stats.total_actions} 總行動
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-card/60 backdrop-blur-sm border-border-light hover:shadow-glow transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-text-secondary">完成率</p>
                  <p className="text-2xl font-bold text-gradient">{stats.average_completion_rate}%</p>
                </div>
                <div className="p-2 rounded-xl bg-gradient-to-br from-primary/20 to-accent/20">
                  <CheckCircle className="h-6 w-6 text-primary" />
                </div>
              </div>
              <div className="mt-4">
                <Progress value={stats.average_completion_rate} className="h-2" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 最新建議和分類統計 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 最新優化建議 */}
          <Card className="bg-card/60 backdrop-blur-sm border-border-light">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-text-primary flex items-center gap-2">
                  <Lightbulb className="h-5 w-5" />
                  最新優化建議
                </CardTitle>
                <Button variant="outline" size="sm" className="group">
                  查看全部
                  <ArrowRight className="ml-2 h-3 w-3 group-hover:translate-x-1 transition-transform duration-200" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recommendations.map((rec) => (
                  <div key={rec.id} className="p-4 rounded-xl bg-surface-1/40 border border-border-light/60 hover:shadow-md transition-all duration-300">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-lg bg-gradient-to-br ${getTypeGradient(rec.type)} shadow-md`}>
                          {getTypeIcon(rec.type)}
                          <span className="sr-only">{rec.type}</span>
                        </div>
                        <div>
                          <h4 className="font-semibold text-text-primary">{rec.title}</h4>
                          <p className="text-sm text-text-secondary mt-1">{rec.description}</p>
                        </div>
                      </div>
                      <Badge className={getStatusColor(rec.status)}>
                        {rec.status === 'completed' ? '已完成' : 
                         rec.status === 'active' ? '進行中' : '待處理'}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="text-sm">
                          <span className="text-text-secondary">影響評分: </span>
                          <span className="font-medium text-gradient">{rec.impact_score}/10</span>
                        </div>
                        <div className="text-sm">
                          <span className="text-text-secondary">進度: </span>
                          <span className="font-medium text-text-primary">{rec.progress}%</span>
                        </div>
                      </div>
                      <Button size="sm" variant="outline">
                        查看詳情
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 分類統計和最近活動 */}
          <div className="space-y-6">
            {/* 優化分類統計 */}
            <Card className="bg-card/60 backdrop-blur-sm border-border-light">
              <CardHeader>
                <CardTitle className="text-text-primary flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  優化分類統計
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {stats.top_categories.map((category, index) => (
                    <div key={category.category} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`w-3 h-3 rounded-full ${
                          index === 0 ? 'bg-blue-500' :
                          index === 1 ? 'bg-green-500' : 'bg-purple-500'
                        }`} />
                        <span className="text-sm font-medium text-text-primary">{category.category}</span>
                      </div>
                      <div className="text-sm font-bold text-text-primary">{category.count}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 最近活動 */}
            <Card className="bg-card/60 backdrop-blur-sm border-border-light">
              <CardHeader>
                <CardTitle className="text-text-primary flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  最近活動
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {stats.recent_activities.map((activity, index) => (
                    <div key={index} className="flex items-center gap-3 p-3 rounded-lg bg-surface-1/40 border border-border-light/60">
                      <div className="w-2 h-2 rounded-full bg-primary" />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-text-primary">{activity.title}</p>
                        <p className="text-xs text-text-secondary">{activity.updated_at}</p>
                      </div>
                      <Badge className={getStatusColor(activity.status)}>
                        {activity.status === 'completed' ? '完成' : 
                         activity.status === 'running' ? '進行中' : '待處理'}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* 快速操作 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="group bg-card/60 backdrop-blur-sm border-border-light hover:shadow-glow transition-all duration-300 hover:scale-105 cursor-pointer">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-md group-hover:shadow-glow transition-shadow duration-300">
                <Lightbulb className="h-6 w-6 text-white" />
              </div>
              <h3 className="font-semibold text-text-primary mb-2 group-hover:text-gradient transition-colors duration-300">
                內容優化
              </h3>
              <p className="text-sm text-text-secondary mb-4 leading-relaxed">
                優化標題、內容結構和關鍵字
              </p>
              <Button size="sm" variant="outline" className="w-full">
                開始優化
              </Button>
            </CardContent>
          </Card>

          <Card className="group bg-card/60 backdrop-blur-sm border-border-light hover:shadow-glow transition-all duration-300 hover:scale-105 cursor-pointer">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-md group-hover:shadow-glow transition-shadow duration-300">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <h3 className="font-semibold text-text-primary mb-2 group-hover:text-gradient transition-colors duration-300">
                性能提升
              </h3>
              <p className="text-sm text-text-secondary mb-4 leading-relaxed">
                提升頁面載入速度和性能
              </p>
              <Button size="sm" variant="outline" className="w-full">
                開始優化
              </Button>
            </CardContent>
          </Card>

          <Card className="group bg-card/60 backdrop-blur-sm border-border-light hover:shadow-glow transition-all duration-300 hover:scale-105 cursor-pointer">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-md group-hover:shadow-glow transition-shadow duration-300">
                <Zap className="h-6 w-6 text-white" />
              </div>
              <h3 className="font-semibold text-text-primary mb-2 group-hover:text-gradient transition-colors duration-300">
                技術修復
              </h3>
              <p className="text-sm text-text-secondary mb-4 leading-relaxed">
                修復技術問題和錯誤
              </p>
              <Button size="sm" variant="outline" className="w-full">
                開始修復
              </Button>
            </CardContent>
          </Card>

          <Card className="group bg-card/60 backdrop-blur-sm border-border-light hover:shadow-glow transition-all duration-300 hover:scale-105 cursor-pointer">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-md group-hover:shadow-glow transition-shadow duration-300">
                <PlayCircle className="h-6 w-6 text-white" />
              </div>
              <h3 className="font-semibold text-text-primary mb-2 group-hover:text-gradient transition-colors duration-300">
                A/B 測試
              </h3>
              <p className="text-sm text-text-secondary mb-4 leading-relaxed">
                運行實驗測試不同策略
              </p>
              <Button size="sm" variant="outline" className="w-full">
                開始實驗
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
