'use client';

import React, { useState } from 'react';

export default function TestSave() {
  const [apiKey, setApiKey] = useState('');
  const [isSaving, setSaving] = useState(false);
  const [message, setMessage] = useState('');

  const handleSave = async () => {
    console.log('🔧 測試保存功能...');
    setSaving(true);
    setMessage('');

    try {
      const config = {
        openai: {
          api_key: apiKey,
          model: 'gpt-4o-mini',
          max_tokens: 4000,
          temperature: 0.7,
          timeout: 30,
          max_retries: 3
        },
        database_url: 'sqlite:///./test.db',
        redis_url: 'redis://localhost:6379',
        debug: true
      };

      const response = await fetch('http://localhost:8000/api/v1/system/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config)
      });

      const result = await response.json();
      
      if (result.status === 'success') {
        setMessage('✅ 保存成功！');
      } else {
        setMessage('❌ 保存失敗：' + result.message);
      }
    } catch (error) {
      setMessage('❌ 錯誤：' + error);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="max-w-md mx-auto mt-10 p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-xl font-bold mb-4">🧪 保存功能測試</h2>
      
      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">API Key:</label>
        <input
          type="text"
          value={apiKey}
          onChange={(e) => setApiKey(e.target.value)}
          placeholder="輸入測試API密鑰..."
          className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      <button
        onClick={handleSave}
        disabled={isSaving}
        className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
      >
        {isSaving ? '保存中...' : '測試保存'}
      </button>

      {message && (
        <div className="mt-4 p-3 rounded-lg bg-gray-100">
          {message}
        </div>
      )}
    </div>
  );
} 