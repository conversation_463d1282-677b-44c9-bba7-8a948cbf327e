'use client';

import { useState, useEffect } from 'react';
import { 
  Save, 
  TestTube, 
  <PERSON>tings, 
  CheckCircle, 
  XCircle,
  Loader2,
  Eye,
  EyeOff,
  Shield,
  Zap,
  BarChart3,
  Clock,
  RotateCcw
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'sonner';

// 接口定義
interface OpenAISettings {
  api_key_masked?: string;
  model?: string;
  max_tokens?: number;
  temperature?: number;
  timeout?: number;
  max_retries?: number;
  is_configured?: boolean;
}

interface FormData {
  api_key?: string;
  model?: string;
  max_tokens?: number;
  temperature?: number;
  timeout?: number;
  max_retries?: number;
}

interface TestResult {
  success: boolean;
  error_message?: string;
  response_text?: string;
  response_time?: number;
  model_used?: string;
  tokens_used?: number;
}

interface UsageStats {
  total_requests?: number;
  successful_requests?: number;
  failed_requests?: number;
  average_response_time?: number;
  today?: {
    requests: number;
    tokens: number;
    estimated_cost: number;
  };
  this_month?: {
    requests: number;
    tokens: number;
    estimated_cost: number;
  };
}

// 模型選項
const MODEL_OPTIONS = [
  { value: 'gpt-4o-mini', label: 'GPT-4o Mini', description: '最具成本效益' },
  { value: 'gpt-4o', label: 'GPT-4o', description: '最新最強模型' },
  { value: 'gpt-4-turbo', label: 'GPT-4 Turbo', description: '高性能模型' },
  { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo', description: '快速且經濟' }
];

export default function OpenAISettingsPage() {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [settings, setSettings] = useState<OpenAISettings>({});
  const [usageStats, setUsageStats] = useState<UsageStats>({});
  const [formData, setFormData] = useState<FormData>({
    model: 'gpt-4o-mini',
    max_tokens: 4000,
    temperature: 0.7,
    timeout: 30000,
    max_retries: 3
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showApiKey, setShowApiKey] = useState(false);
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [testPrompt, setTestPrompt] = useState('Hello, this is a test message.');

  // 載入設置
  useEffect(() => {
    loadSettings();
    loadUsageStats();
  }, []);

  const loadSettings = async () => {
    try {
      const response = await fetch('/api/v1/admin/openai/settings');
      const data = await response.json();
      console.log('載入設置:', data);
      
      if (data.success && data.data) {
        setSettings(data.data);
        setFormData(prev => ({
          ...prev,
          model: data.data.model || prev.model,
          max_tokens: data.data.max_tokens || prev.max_tokens,
          temperature: data.data.temperature || prev.temperature,
          timeout: data.data.timeout || prev.timeout,
          max_retries: data.data.max_retries || prev.max_retries
        }));
      } else {
        setSettings(data || {});
      }
    } catch (error) {
      console.error('載入設置失敗:', error);
      toast.error('載入設置失敗');
    } finally {
      setLoading(false);
    }
  };

  const loadUsageStats = async () => {
    try {
      const response = await fetch('/api/v1/admin/openai/usage-stats');
      const data = await response.json();
      console.log('載入使用統計:', data);
      
      if (data.success && data.data) {
        setUsageStats(data.data);
      }
    } catch (error) {
      console.error('載入使用統計失敗:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setErrors({});

    try {
      const response = await fetch('/api/v1/admin/openai/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success('設置保存成功');
        await loadSettings();
        setFormData(prev => ({ ...prev, api_key: '' })); // 清除表單中的密鑰
      } else {
        toast.error(data.message || '保存失敗');
      }
    } catch (error) {
      console.error('保存設置失敗:', error);
      toast.error('保存設置失敗');
    } finally {
      setSaving(false);
    }
  };

  const handleTest = async () => {
    if (!formData.api_key && !settings?.is_configured) {
      toast.error('請先輸入 API 密鑰');
      return;
    }

    setTesting(true);
    setTestResult(null);

    try {
      const response = await fetch('/api/v1/admin/openai/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          api_key: formData.api_key,
          model: formData.model,
          test_prompt: testPrompt,
          use_real_api: true
        }),
      });

      const data = await response.json();
      
      if (data.success && data.data) {
        setTestResult(data.data);
        if (data.data.success) {
          toast.success('連接測試成功');
        } else {
          toast.error(`連接測試失敗: ${data.data.error_message}`);
        }
      } else {
        toast.error(data.message || '測試失敗');
      }
    } catch (error) {
      console.error('測試連接失敗:', error);
      toast.error('測試連接失敗');
    } finally {
      setTesting(false);
    }
  };

  const handleReset = () => {
    if (confirm('確定要重置設置嗎？')) {
      setFormData({
        model: 'gpt-4o-mini',
        max_tokens: 4000,
        temperature: 0.7,
        timeout: 30000,
        max_retries: 3
      });
      setErrors({});
      setTestResult(null);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
          <span className="ml-2">載入 OpenAI 設置中...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">OpenAI 設置</h1>
          <p className="text-muted-foreground">
            配置和管理 OpenAI API 設置，用於 AI SEO 分析功能
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {settings?.is_configured && (
            <Badge variant="outline" className="text-green-600 border-green-600">
              <CheckCircle className="w-3 h-3 mr-1" />
              已配置
            </Badge>
          )}
        </div>
      </div>

      <Tabs defaultValue="settings" className="space-y-6">
        <TabsList>
          <TabsTrigger value="settings" className="flex items-center space-x-2">
            <Settings className="w-4 h-4" />
            <span>基本設置</span>
          </TabsTrigger>
          <TabsTrigger value="test" className="flex items-center space-x-2">
            <TestTube className="w-4 h-4" />
            <span>連接測試</span>
          </TabsTrigger>
          <TabsTrigger value="stats" className="flex items-center space-x-2">
            <BarChart3 className="w-4 h-4" />
            <span>使用統計</span>
          </TabsTrigger>
        </TabsList>

        {/* 基本設置 */}
        <TabsContent value="settings">
          <form onSubmit={handleSubmit} className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Shield className="w-5 h-5" />
                  <span>API 配置</span>
                </CardTitle>
                <CardDescription>
                  配置 OpenAI API 密鑰和基本參數
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* API 密鑰 */}
                <div className="space-y-2">
                  <Label htmlFor="api_key">API 密鑰</Label>
                  <div className="relative">
                    <Input
                      id="api_key"
                      type={showApiKey ? "text" : "password"}
                      placeholder={settings?.api_key_masked || "輸入 OpenAI API 密鑰"}
                      value={formData.api_key || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, api_key: e.target.value }))}
                      className={errors.api_key ? "border-red-500" : ""}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-2 top-1/2 -translate-y-1/2"
                      onClick={() => setShowApiKey(!showApiKey)}
                    >
                      {showApiKey ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </Button>
                  </div>
                  {errors.api_key && (
                    <p className="text-sm text-red-500">{errors.api_key}</p>
                  )}
                  <p className="text-sm text-muted-foreground">
                    API 密鑰將被加密存儲，僅用於 AI SEO 分析功能
                  </p>
                </div>

                {/* 模型選擇 */}
                <div className="space-y-2">
                  <Label htmlFor="model">模型</Label>
                  <Select
                    value={formData.model}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, model: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="選擇 OpenAI 模型" />
                    </SelectTrigger>
                    <SelectContent>
                      {MODEL_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <div>
                            <div className="font-medium">{option.label}</div>
                            <div className="text-sm text-muted-foreground">{option.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Zap className="w-5 h-5" />
                  <span>進階參數</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Max Tokens */}
                <div className="space-y-2">
                  <Label>最大 Token 數: {formData.max_tokens}</Label>
                  <Slider
                    value={[formData.max_tokens || 4000]}
                    onValueChange={([value]) => setFormData(prev => ({ ...prev, max_tokens: value }))}
                    min={100}
                    max={32000}
                    step={100}
                    className="w-full"
                  />
                </div>

                {/* Temperature */}
                <div className="space-y-2">
                  <Label>溫度參數: {formData.temperature}</Label>
                  <Slider
                    value={[formData.temperature || 0.7]}
                    onValueChange={([value]) => setFormData(prev => ({ ...prev, temperature: value }))}
                    min={0}
                    max={2}
                    step={0.1}
                    className="w-full"
                  />
                </div>

                {/* Timeout */}
                <div className="space-y-2">
                  <Label>超時時間: {((formData.timeout || 30000) / 1000).toFixed(0)}s</Label>
                  <Slider
                    value={[formData.timeout || 30000]}
                    onValueChange={([value]) => setFormData(prev => ({ ...prev, timeout: value }))}
                    min={5000}
                    max={300000}
                    step={5000}
                    className="w-full"
                  />
                </div>
              </CardContent>
            </Card>

            {/* 操作按鈕 */}
            <div className="flex items-center justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={handleReset}
                disabled={saving}
                className="flex items-center space-x-2"
              >
                <RotateCcw className="w-4 h-4" />
                <span>重置</span>
              </Button>

              <Button
                type="submit"
                disabled={saving}
                className="flex items-center space-x-2"
              >
                {saving ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Save className="w-4 h-4" />
                )}
                <span>{saving ? '保存中...' : '保存設置'}</span>
              </Button>
            </div>
          </form>
        </TabsContent>

        {/* 連接測試 */}
        <TabsContent value="test">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TestTube className="w-5 h-5" />
                <span>連接測試</span>
              </CardTitle>
              <CardDescription>
                測試 OpenAI API 連接和配置是否正常工作
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="test_prompt">測試提示</Label>
                <Input
                  id="test_prompt"
                  value={testPrompt}
                  onChange={(e) => setTestPrompt(e.target.value)}
                  placeholder="輸入測試提示..."
                />
              </div>

              <Button
                onClick={handleTest}
                disabled={testing || (!formData.api_key && !settings?.is_configured)}
                className="flex items-center space-x-2"
              >
                {testing ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <TestTube className="w-4 h-4" />
                )}
                <span>{testing ? '測試中...' : '開始測試'}</span>
              </Button>

              {!formData.api_key && !settings?.is_configured && (
                <p className="text-sm text-muted-foreground">
                  請先在基本設置中輸入 API 密鑰
                </p>
              )}

              {/* 測試結果 */}
              {testResult && (
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    {testResult.success ? (
                      <CheckCircle className="w-5 h-5 text-green-500" />
                    ) : (
                      <XCircle className="w-5 h-5 text-red-500" />
                    )}
                    <span className={testResult.success ? "text-green-600" : "text-red-600"}>
                      {testResult.success ? '連接成功' : '連接失敗'}
                    </span>
                  </div>

                  {testResult.success && testResult.response_text && (
                    <div className="p-3 bg-muted rounded-lg">
                      <Label className="text-sm font-medium">API 響應:</Label>
                      <p className="text-sm mt-1">{testResult.response_text}</p>
                    </div>
                  )}

                  {!testResult.success && testResult.error_message && (
                    <Alert variant="destructive">
                      <XCircle className="h-4 w-4" />
                      <AlertDescription>
                        {testResult.error_message}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 使用統計 */}
        <TabsContent value="stats">
          <div className="grid gap-6">
            {/* 統計卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <BarChart3 className="w-4 h-4 text-blue-500" />
                    <p className="text-sm font-medium">總請求數</p>
                  </div>
                  <p className="text-2xl font-bold">{usageStats?.total_requests || 0}</p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <p className="text-sm font-medium">成功請求</p>
                  </div>
                  <p className="text-2xl font-bold">{usageStats?.successful_requests || 0}</p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <XCircle className="w-4 h-4 text-red-500" />
                    <p className="text-sm font-medium">失敗請求</p>
                  </div>
                  <p className="text-2xl font-bold">{usageStats?.failed_requests || 0}</p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-orange-500" />
                    <p className="text-sm font-medium">平均響應時間</p>
                  </div>
                  <p className="text-2xl font-bold">
                    {usageStats?.average_response_time ? 
                      `${usageStats.average_response_time.toFixed(2)}s` : 
                      '0s'
                    }
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* 詳細統計 */}
            <Card>
              <CardHeader>
                <CardTitle>使用統計詳情</CardTitle>
                <CardDescription>
                  OpenAI API 使用的詳細統計信息
                </CardDescription>
              </CardHeader>
              <CardContent>
                {(usageStats?.total_requests || 0) === 0 ? (
                  <Alert>
                    <BarChart3 className="h-4 w-4" />
                    <AlertDescription>
                      尚無 API 使用記錄。配置完成後開始使用 AI SEO 功能即可看到統計數據。
                    </AlertDescription>
                  </Alert>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium mb-2">今日使用量</h4>
                      <p>請求數: {usageStats?.today?.requests || 0}</p>
                      <p>Token 使用: {usageStats?.today?.tokens || 0}</p>
                      <p>預估費用: ${(usageStats?.today?.estimated_cost || 0).toFixed(4)}</p>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">本月使用量</h4>
                      <p>請求數: {usageStats?.this_month?.requests || 0}</p>
                      <p>Token 使用: {usageStats?.this_month?.tokens || 0}</p>
                      <p>預估費用: ${(usageStats?.this_month?.estimated_cost || 0).toFixed(4)}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
} 