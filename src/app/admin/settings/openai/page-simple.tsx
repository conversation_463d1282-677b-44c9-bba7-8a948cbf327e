'use client';

import { useState, useEffect } from 'react';
import { Save, TestTube, Settings as SettingsIcon, CheckCircle } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';

interface Settings {
  api_key_masked?: string;
  model?: string;
  max_tokens?: number;
  temperature?: number;
  is_configured?: boolean;
}

export default function SimpleOpenAISettings() {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState<Settings>({});
  const [formData, setFormData] = useState({
    api_key: '',
    model: 'gpt-4o-mini',
    max_tokens: 4000,
    temperature: 0.7
  });

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const response = await fetch('/api/v1/admin/openai/settings');
      const data = await response.json();
      
      if (data.success && data.data) {
        setSettings(data.data);
        setFormData(prev => ({
          ...prev,
          model: data.data.model || 'gpt-4o-mini',
          max_tokens: data.data.max_tokens || 4000,
          temperature: data.data.temperature || 0.7
        }));
      }
    } catch (error) {
      console.error('載入失敗:', error);
      toast.error('載入設置失敗');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const response = await fetch('/api/v1/admin/openai/settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success('設置更新成功');
        loadSettings();
      } else {
        toast.error('更新失敗');
      }
    } catch (error) {
      toast.error('更新失敗');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p>載入中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <SettingsIcon className="h-8 w-8 text-gray-600 mr-3" />
        <div>
          <h1 className="text-2xl font-bold">OpenAI 設置</h1>
          <p className="text-gray-600">配置 OpenAI API 設置</p>
        </div>
        {settings.is_configured && (
          <CheckCircle className="h-6 w-6 text-green-500 ml-auto" />
        )}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>API 配置</CardTitle>
          <CardDescription>配置您的 OpenAI API 金鑰和參數</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="api_key">API 金鑰</Label>
            <Input
              id="api_key"
              type="password"
              placeholder="sk-..."
              value={formData.api_key}
              onChange={(e) => setFormData(prev => ({ ...prev, api_key: e.target.value }))}
            />
            {settings.api_key_masked && (
              <p className="text-sm text-gray-500 mt-1">
                當前: {settings.api_key_masked}
              </p>
            )}
          </div>

          <div>
            <Label htmlFor="model">模型</Label>
            <Select value={formData.model} onValueChange={(value) => setFormData(prev => ({ ...prev, model: value }))}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="gpt-4o-mini">GPT-4o Mini</SelectItem>
                <SelectItem value="gpt-4o">GPT-4o</SelectItem>
                <SelectItem value="gpt-4">GPT-4</SelectItem>
                <SelectItem value="gpt-4-turbo">GPT-4 Turbo</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="max_tokens">最大 Token 數</Label>
            <Input
              id="max_tokens"
              type="number"
              value={formData.max_tokens}
              onChange={(e) => setFormData(prev => ({ ...prev, max_tokens: parseInt(e.target.value) }))}
              min={100}
              max={16000}
            />
          </div>

          <div>
            <Label htmlFor="temperature">溫度參數</Label>
            <Input
              id="temperature"
              type="number"
              value={formData.temperature}
              onChange={(e) => setFormData(prev => ({ ...prev, temperature: parseFloat(e.target.value) }))}
              min={0}
              max={2}
              step={0.1}
            />
          </div>

          <div className="flex justify-end">
            <Button onClick={handleSave} disabled={saving}>
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                  保存中...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  保存設置
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 