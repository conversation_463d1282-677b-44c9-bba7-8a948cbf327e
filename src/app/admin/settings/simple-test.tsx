'use client';

import React, { useState } from 'react';

export default function SimpleSettingsTest() {
  const [apiKey, setApiKey] = useState('');
  const [model, setModel] = useState('gpt-4o-mini');
  const [maxTokens, setMaxTokens] = useState(4000);
  const [isSaving, setSaving] = useState(false);
  const [message, setMessage] = useState('');

  const handleSave = async () => {
    console.log('🔧 測試保存功能...');
    setSaving(true);
    setMessage('');

    try {
      const config = {
        openai: {
          api_key: apiKey,
          model: model,
          max_tokens: maxTokens,
          temperature: 0.7,
          timeout: 30,
          max_retries: 3
        },
        database_url: 'sqlite:///./test.db',
        redis_url: 'redis://localhost:6379',
        debug: true
      };

      console.log('發送請求:', config);

      const response = await fetch('http://localhost:8000/api/v1/system/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config)
      });

      console.log('響應狀態:', response.status);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('響應結果:', result);

      if (result.status === 'success') {
        setMessage('✅ 配置保存成功！');
      } else {
        setMessage('❌ 保存失敗：' + (result.message || '未知錯誤'));
      }
    } catch (error) {
      console.error('保存錯誤:', error);
      setMessage('❌ 保存失敗：' + (error.message || '網路錯誤'));
    } finally {
      setSaving(false);
    }
  };

  return (
    <div style={{ 
      padding: '20px', 
      maxWidth: '600px', 
      margin: '0 auto',
      fontFamily: 'system-ui, -apple-system, sans-serif'
    }}>
      <h1 style={{ color: '#333', marginBottom: '30px' }}>
        🧪 系統設置保存測試
      </h1>

      {message && (
        <div style={{
          padding: '15px',
          marginBottom: '20px',
          borderRadius: '5px',
          backgroundColor: message.includes('✅') ? '#d4edda' : '#f8d7da',
          border: message.includes('✅') ? '1px solid #c3e6cb' : '1px solid #f5c6cb',
          color: message.includes('✅') ? '#155724' : '#721c24'
        }}>
          {message}
        </div>
      )}

      <div style={{ marginBottom: '20px' }}>
        <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
          🔑 OpenAI API Key:
        </label>
        <input
          type="text"
          value={apiKey}
          onChange={(e) => setApiKey(e.target.value)}
          placeholder="輸入您的 OpenAI API Key"
          style={{
            width: '100%',
            padding: '10px',
            border: '1px solid #ddd',
            borderRadius: '4px',
            fontSize: '14px'
          }}
        />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
          🤖 模型:
        </label>
        <select
          value={model}
          onChange={(e) => setModel(e.target.value)}
          style={{
            width: '100%',
            padding: '10px',
            border: '1px solid #ddd',
            borderRadius: '4px',
            fontSize: '14px'
          }}
        >
          <option value="gpt-4o-mini">gpt-4o-mini</option>
          <option value="gpt-4o">gpt-4o</option>
          <option value="gpt-4-turbo">gpt-4-turbo</option>
          <option value="gpt-3.5-turbo">gpt-3.5-turbo</option>
        </select>
      </div>

      <div style={{ marginBottom: '30px' }}>
        <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
          📊 最大 Token 數:
        </label>
        <input
          type="number"
          value={maxTokens}
          onChange={(e) => setMaxTokens(parseInt(e.target.value) || 4000)}
          min={100}
          max={8000}
          style={{
            width: '100%',
            padding: '10px',
            border: '1px solid #ddd',
            borderRadius: '4px',
            fontSize: '14px'
          }}
        />
      </div>

      <button
        onClick={handleSave}
        disabled={isSaving}
        style={{
          width: '100%',
          padding: '15px',
          backgroundColor: isSaving ? '#6c757d' : '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '5px',
          fontSize: '16px',
          fontWeight: 'bold',
          cursor: isSaving ? 'not-allowed' : 'pointer',
          transition: 'background-color 0.3s'
        }}
        onMouseOver={(e) => {
          if (!isSaving) {
            e.target.style.backgroundColor = '#0056b3';
          }
        }}
        onMouseOut={(e) => {
          if (!isSaving) {
            e.target.style.backgroundColor = '#007bff';
          }
        }}
      >
        {isSaving ? '💾 保存中...' : '💾 保存設置'}
      </button>

      <div style={{ 
        marginTop: '20px', 
        padding: '15px', 
        backgroundColor: '#f8f9fa', 
        borderRadius: '5px',
        border: '1px solid #dee2e6'
      }}>
        <h3 style={{ margin: '0 0 10px 0', color: '#495057' }}>🔍 測試說明：</h3>
        <ul style={{ margin: 0, paddingLeft: '20px', color: '#6c757d' }}>
          <li>填寫 OpenAI API Key（或保持空白進行測試）</li>
          <li>選擇想要的模型</li>
          <li>調整 Token 數量</li>
          <li>點擊「保存設置」按鈕</li>
          <li>觀察控制台輸出和狀態消息</li>
        </ul>
      </div>
    </div>
  );
} 