'use client';

import React, { useState, useEffect } from 'react';

interface SystemConfig {
  openai: {
    api_key: string | null;
    model: string;
    max_tokens: number;
    temperature: number;
    timeout: number;
    max_retries: number;
  };
  database_url: string;
  redis_url: string;
  debug: boolean;
}

interface SystemStatus {
  backend: {
    status: string;
    version: string;
    uptime: number;
  };
  database: {
    status: string;
    type: string;
    url: string;
  };
  redis: {
    status: string;
    url: string;
  };
  openai: {
    status: string;
    model: string;
  };
}

interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
  };
  memory: {
    usage: number;
    used: number;
    total: number;
  };
  disk: {
    usage: number;
    used: number;
    total: number;
  };
}

export default function SystemSettings() {
  const [mounted, setMounted] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setSaving] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showApi<PERSON>ey, setShowApi<PERSON>ey] = useState(false);
  const [message, setMessage] = useState<{type: 'success' | 'error', text: string} | null>(null);
  
  const [config, setConfig] = useState<SystemConfig>({
    openai: {
      api_key: '',
      model: 'gpt-4o-mini',
      max_tokens: 4000,
      temperature: 0.7,
      timeout: 30,
      max_retries: 3
    },
    database_url: 'sqlite:///./test.db',
    redis_url: 'redis://localhost:6379',
    debug: true
  });

  const [status, setStatus] = useState<SystemStatus>({
    backend: { status: 'unknown', version: '2.0.0', uptime: 0 },
    database: { status: 'unknown', type: 'sqlite', url: '' },
    redis: { status: 'unknown', url: '' },
    openai: { status: 'unknown', model: 'gpt-4o-mini' }
  });

  const [metrics, setMetrics] = useState<SystemMetrics>({
    cpu: { usage: 0, cores: 8 },
    memory: { usage: 0, used: 0, total: 0 },
    disk: { usage: 0, used: 0, total: 0 }
  });

  useEffect(() => {
    setMounted(true);
    fetchSystemData();
  }, []);

  const fetchSystemData = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('http://localhost:8000/api/v1/system/config');
      const result = await response.json();
      
      if (result.status === 'success') {
        const configData = result.data;
        if (configData.openai && configData.openai.api_key_full) {
          configData.openai.api_key = configData.openai.api_key_full;
        }
        setConfig(configData);
      }
    } catch (error) {
      console.error('獲取系統數據失敗:', error);
      setMessage({ type: 'error', text: '無法連接到後端服務器' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveConfig = async () => {
    console.log('🔧 開始保存配置...', config);
    setSaving(true);
    setMessage(null);
    
    try {
      const response = await fetch('http://localhost:8000/api/v1/system/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config)
      });

      console.log('API響應狀態:', response.status);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('API響應結果:', result);

      if (result.status === 'success') {
        setMessage({ type: 'success', text: '✅ 系統配置已成功保存！' });
        setTimeout(() => setMessage(null), 5000);
      } else {
        setMessage({ type: 'error', text: result.message || '❌ 保存失敗，請重試' });
        setTimeout(() => setMessage(null), 5000);
      }
    } catch (error) {
      console.error('保存配置時發生錯誤:', error);
      setMessage({ 
        type: 'error', 
        text: `❌ 保存配置時發生錯誤: ${error instanceof Error ? error.message : '未知錯誤'}` 
      });
      setTimeout(() => setMessage(null), 5000);
    } finally {
      setSaving(false);
      console.log('✅ 保存操作完成');
    }
  };

  const handleTestOpenAI = async () => {
    if (!config.openai.api_key) {
      setMessage({ type: 'error', text: '請先設置 OpenAI API Key' });
      return;
    }

    try {
      const response = await fetch('http://localhost:8000/api/v1/openai/test', {
        method: 'POST'
      });

      const result = await response.json();

      if (result.status === 'success') {
        setMessage({ type: 'success', text: `OpenAI API 連接測試成功！回應：${result.data.test_response}` });
      } else {
        setMessage({ type: 'error', text: result.detail || 'API 測試失敗' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'API 測試失敗' });
    }
  };

  const handleDemoTest = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/v1/openai/test-demo', {
        method: 'POST'
      });

      const result = await response.json();

      if (result.status === 'success') {
        setMessage({ 
          type: 'success', 
          text: `${result.message} - ${result.data.test_response}${result.data.note ? ` (${result.data.note})` : ''}` 
        });
      } else {
        setMessage({ type: 'error', text: '演示測試失敗' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: '演示測試失敗' });
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (!mounted) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">載入系統設置中...</p>
          <button 
            onClick={() => setIsLoading(false)}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            跳過載入
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* 頁面標題 */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900">🔧 系統設置</h1>
        <p className="text-gray-600 mt-2">管理 OpenAI API 配置和系統設定</p>
      </div>

      {/* 消息提示 */}
      {message && (
        <div className={`p-4 rounded-lg mb-6 ${
          message.type === 'success' ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200'
        }`}>
          <div className="font-medium">{message.text}</div>
        </div>
      )}

      {/* OpenAI API 配置 */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">🤖 OpenAI API 配置</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* API Key */}
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              🔑 API Key
            </label>
            <div className="flex items-center space-x-2">
              <input
                type={showApiKey ? 'text' : 'password'}
                value={config.openai.api_key || ''}
                onChange={(e) => setConfig({
                  ...config,
                  openai: { ...config.openai, api_key: e.target.value }
                })}
                placeholder="sk-proj-您的OpenAI密鑰..."
                className="flex-1 px-4 py-3 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <button
                onClick={() => setShowApiKey(!showApiKey)}
                className="px-4 py-3 border border-gray-300 rounded-lg text-sm hover:bg-gray-50 transition-colors"
              >
                {showApiKey ? '🙈' : '👁️'}
              </button>
            </div>
            {(config.openai.api_key?.includes('示例密鑰') || config.openai.api_key?.includes('請替換')) && (
              <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <p className="text-sm text-yellow-800">
                  📝 <strong>如何獲取OpenAI API密鑰：</strong>
                </p>
                <ol className="text-xs text-yellow-700 mt-1 ml-4 list-decimal">
                  <li>訪問 <a href="https://platform.openai.com/api-keys" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">OpenAI API Keys 頁面</a></li>
                  <li>點擊「Create new secret key」</li>
                  <li>複製生成的密鑰（以 sk- 開頭）</li>
                  <li>貼上到上方輸入框並點擊保存</li>
                </ol>
              </div>
            )}
          </div>

          {/* 模型 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              🧠 模型
            </label>
            <select
              value={config.openai.model}
              onChange={(e) => setConfig({
                ...config,
                openai: { ...config.openai, model: e.target.value }
              })}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="gpt-4o-mini">GPT-4o Mini</option>
              <option value="gpt-4o">GPT-4o</option>
              <option value="gpt-4-turbo">GPT-4 Turbo</option>
              <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
            </select>
          </div>

          {/* 最大 Token 數 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              📊 最大 Token 數
            </label>
            <input
              type="number"
              value={config.openai.max_tokens}
              onChange={(e) => setConfig({
                ...config,
                openai: { ...config.openai, max_tokens: parseInt(e.target.value) || 4000 }
              })}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* 溫度 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              🌡️ 溫度 (0-1)
            </label>
            <input
              type="number"
              step="0.1"
              min="0"
              max="1"
              value={config.openai.temperature}
              onChange={(e) => setConfig({
                ...config,
                openai: { ...config.openai, temperature: parseFloat(e.target.value) || 0.7 }
              })}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* 超時時間 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              ⏱️ 超時時間 (秒)
            </label>
            <input
              type="number"
              value={config.openai.timeout}
              onChange={(e) => setConfig({
                ...config,
                openai: { ...config.openai, timeout: parseInt(e.target.value) || 30 }
              })}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
      </div>

      {/* 保存按鈕 */}
      <div className="bg-blue-50 border-2 border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-4">💾 保存配置</h3>
        <p className="text-sm text-blue-700 mb-4">點擊下方按鈕保存您的設置更改</p>
        <div className="flex justify-center">
          <button 
            onClick={handleSaveConfig}
            disabled={isSaving}
            className="px-8 py-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 active:scale-95 font-semibold text-lg shadow-lg"
          >
            {isSaving ? '💾 保存中...' : '💾 保存設置'}
          </button>
        </div>
      </div>
    </div>
  );
}
