'use client';

import { useState, useEffect } from 'react';
import { 
  Save, 
  TestTube, 
  Clock, 
  Mail, 
  AlertTriangle,
  CheckCircle,
  Settings as SettingsIcon,
  Play,
  Pause
} from 'lucide-react';

interface ScheduleConfig {
  enabled: boolean;
  frequency: 'daily' | 'weekly' | 'monthly';
  time: string;
  emailNotifications: boolean;
  notificationEmails: string[];
  healthThreshold: number;
}

export default function SettingsPage() {
  const [config, setConfig] = useState<ScheduleConfig>({
    enabled: false,
    frequency: 'daily',
    time: '09:00',
    emailNotifications: true,
    notificationEmails: ['<EMAIL>'],
    healthThreshold: 70
  });
  
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  // 載入配置
  useEffect(() => {
    loadConfig();
  }, []);

  const loadConfig = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/schedule-validation');
      const data = await response.json();
      
      if (data.success) {
        setConfig(data.config);
      }
    } catch (error) {
      console.error('載入配置失敗:', error);
      setMessage({ type: 'error', text: '載入配置失敗' });
    } finally {
      setLoading(false);
    }
  };

  const saveConfig = async () => {
    setSaving(true);
    try {
      const response = await fetch('/api/schedule-validation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'update-config',
          config
        })
      });
      
      const data = await response.json();
      
      if (data.success) {
        setMessage({ type: 'success', text: '配置已保存' });
        setTimeout(() => setMessage(null), 3000);
      } else {
        setMessage({ type: 'error', text: data.error || '保存失敗' });
      }
    } catch (error) {
      console.error('保存配置失敗:', error);
      setMessage({ type: 'error', text: '保存配置失敗' });
    } finally {
      setSaving(false);
    }
  };

  const testNotification = async () => {
    setTesting(true);
    try {
      const response = await fetch('/api/schedule-validation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'test-notification'
        })
      });
      
      const data = await response.json();
      
      if (data.success) {
        setMessage({ type: 'success', text: '測試通知已發送，請檢查您的郵箱' });
      } else {
        setMessage({ type: 'error', text: data.error || '發送測試通知失敗' });
      }
    } catch (error) {
      console.error('發送測試通知失敗:', error);
      setMessage({ type: 'error', text: '發送測試通知失敗' });
    } finally {
      setTesting(false);
    }
  };

  const triggerValidation = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/schedule-validation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'trigger-validation'
        })
      });
      
      const data = await response.json();
      
      if (data.success) {
        setMessage({ 
          type: 'success', 
          text: `驗證完成！健康度評分: ${data.summary.healthScore}${data.notificationSent ? '，已發送通知郵件' : ''}` 
        });
      } else {
        setMessage({ type: 'error', text: data.error || '驗證失敗' });
      }
    } catch (error) {
      console.error('觸發驗證失敗:', error);
      setMessage({ type: 'error', text: '觸發驗證失敗' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* 頁面標題 */}
      <div className="flex items-center">
        <SettingsIcon className="h-8 w-8 text-gray-600 mr-3" />
        <div>
          <h1 className="text-2xl font-bold text-gray-900">系統設置</h1>
          <p className="text-gray-600">配置自動化排程和通知系統</p>
        </div>
      </div>

      {/* 消息提示 */}
      {message && (
        <div className={`p-4 rounded-lg ${
          message.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          <div className="flex items-center">
            {message.type === 'success' ? (
              <CheckCircle className="h-5 w-5 mr-2" />
            ) : (
              <AlertTriangle className="h-5 w-5 mr-2" />
            )}
            {message.text}
          </div>
        </div>
      )}

      {/* 排程設置 */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Clock className="h-6 w-6 text-blue-600 mr-3" />
              <h2 className="text-lg font-medium text-gray-900">自動化排程</h2>
            </div>
            <div className="flex items-center">
              <span className="mr-3 text-sm text-gray-600">
                {config.enabled ? '已啟用' : '已禁用'}
              </span>
              <button
                onClick={() => setConfig({ ...config, enabled: !config.enabled })}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  config.enabled ? 'bg-blue-600' : 'bg-gray-200'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    config.enabled ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>
        </div>
        
        <div className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                執行頻率
              </label>
              <select
                value={config.frequency}
                onChange={(e) => setConfig({ ...config, frequency: e.target.value as any })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="daily">每日</option>
                <option value="weekly">每週</option>
                <option value="monthly">每月</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                執行時間
              </label>
              <input
                type="time"
                value={config.time}
                onChange={(e) => setConfig({ ...config, time: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              健康度警告閾值
            </label>
            <div className="flex items-center space-x-4">
              <input
                type="range"
                min="0"
                max="100"
                value={config.healthThreshold}
                onChange={(e) => setConfig({ ...config, healthThreshold: Math.max(0, Math.min(100, parseInt(e.target.value) || 50)) })}
                className="flex-1"
              />
              <span className="text-lg font-medium text-gray-900 w-12">
                {config.healthThreshold}
              </span>
            </div>
            <p className="text-sm text-gray-500 mt-1">
              當健康度評分低於此值時將發送警告通知
            </p>
          </div>
        </div>
      </div>

      {/* 郵件通知設置 */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Mail className="h-6 w-6 text-green-600 mr-3" />
              <h2 className="text-lg font-medium text-gray-900">郵件通知</h2>
            </div>
            <button
              onClick={() => setConfig({ ...config, emailNotifications: !config.emailNotifications })}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                config.emailNotifications ? 'bg-green-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  config.emailNotifications ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        </div>
        
        <div className="p-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              通知郵箱地址
            </label>
            <textarea
              value={config.notificationEmails.join('\n')}
              onChange={(e) => setConfig({ 
                ...config, 
                notificationEmails: e.target.value.split('\n').filter(email => email.trim()) 
              })}
              placeholder="每行一個郵箱地址"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p className="text-sm text-gray-500 mt-1">
              每行輸入一個郵箱地址，將接收健康度警告和報告
            </p>
          </div>
        </div>
      </div>

      {/* 操作按鈕 */}
      <div className="flex flex-wrap gap-4">
        <button
          onClick={saveConfig}
          disabled={saving}
          className="flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          <Save className={`h-5 w-5 mr-2 ${saving ? 'animate-spin' : ''}`} />
          {saving ? '保存中...' : '保存設置'}
        </button>
        
        <button
          onClick={testNotification}
          disabled={testing || !config.emailNotifications}
          className="flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
        >
          <TestTube className={`h-5 w-5 mr-2 ${testing ? 'animate-spin' : ''}`} />
          {testing ? '發送中...' : '測試通知'}
        </button>
        
        <button
          onClick={triggerValidation}
          disabled={loading}
          className="flex items-center px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
        >
          <Play className={`h-5 w-5 mr-2 ${loading ? 'animate-spin' : ''}`} />
          {loading ? '執行中...' : '手動執行驗證'}
        </button>
      </div>
    </div>
  );
}
