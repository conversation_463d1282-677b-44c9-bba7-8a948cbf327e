'use client';

import { Monitor, Activity, Server, Database, Cpu, HardDrive } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export default function MonitoringSettingsPage() {
  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <Monitor className="h-8 w-8" />
              系統監控設置
            </h1>
            <p className="text-lg text-muted-foreground mt-2">
              配置系統監控、警報規則和效能追蹤設置
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">系統狀態</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Activity className="h-5 w-5 text-green-600" />
              <Badge className="bg-green-100 text-green-800">健康</Badge>
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              運行時間: 2天 14小時 32分鐘
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Cpu className="h-4 w-4" />
              CPU 使用率
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-blue-600">45.2%</div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-blue-600 h-2 rounded-full" style={{ width: '45.2%' }}></div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <HardDrive className="h-4 w-4" />
              記憶體使用率
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-green-600">67.8%</div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-green-600 h-2 rounded-full" style={{ width: '67.8%' }}></div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Server className="h-4 w-4" />
              活躍連接
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-purple-600">156</div>
              <p className="text-sm text-muted-foreground">
                回應時間: 145ms
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>系統詳細資訊</CardTitle>
          <CardDescription>
            最後檢查時間: {new Date().toLocaleString('zh-TW')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <Server className="h-4 w-4" />
                  服務狀態
                </span>
                <Badge className="bg-green-100 text-green-800">運行中</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <Database className="h-4 w-4" />
                  資料庫連接
                </span>
                <Badge className="bg-green-100 text-green-800">正常</Badge>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span>磁碟使用率</span>
                <span className="text-green-600">34.1%</span>
              </div>
              <div className="flex items-center justify-between">
                <span>監控狀態</span>
                <span className="text-green-600">啟用</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>開發進度</CardTitle>
          <CardDescription>
            監控設置功能開發狀態
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Monitor className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-lg font-medium mb-2">監控設置功能開發中</p>
            <p className="text-muted-foreground">
              完整的監控配置、警報設置和通知規則功能正在開發中。
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
