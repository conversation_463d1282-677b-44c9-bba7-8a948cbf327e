'use client';

import React, { useState } from 'react';
import { 
  Brain, 
  <PERSON><PERSON>, 
  <PERSON><PERSON>s, 
  Plus,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Edit,
  Trash2,
  Play,
  Pause,
  BarChart3,
  Zap,
  Clock,
  DollarSign
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Progress } from '@/components/ui/progress';
import { Slider } from '@/components/ui/slider';

export default function ModelsPage() {
  const [temperature, setTemperature] = useState([0.7]);
  const [maxTokens, setMaxTokens] = useState([2048]);

  const aiModels = [
    {
      id: '1',
      name: 'GPT-4 Turbo',
      provider: 'OpenAI',
      version: 'gpt-4-1106-preview',
      status: 'active',
      usage: 87,
      cost: '$127.45',
      requests: '12,847',
      avgResponseTime: '1.2s',
      successRate: '99.2%',
      description: '主要用於內容生成和分析'
    },
    {
      id: '2',
      name: 'Claude 3 Opus',
      provider: 'Anthropic',
      version: 'claude-3-opus-20240229',
      status: 'active',
      usage: 65,
      cost: '$89.32',
      requests: '8,254',
      avgResponseTime: '1.8s',
      successRate: '98.7%',
      description: '用於複雜的推理和分析任務'
    },
    {
      id: '3',
      name: 'GPT-3.5 Turbo',
      provider: 'OpenAI',
      version: 'gpt-3.5-turbo-1106',
      status: 'standby',
      usage: 23,
      cost: '$15.67',
      requests: '3,421',
      avgResponseTime: '0.8s',
      successRate: '97.8%',
      description: '用於簡單查詢和快速回應'
    },
    {
      id: '4',
      name: 'Gemini Pro',
      provider: 'Google',
      version: 'gemini-pro',
      status: 'inactive',
      usage: 0,
      cost: '$0.00',
      requests: '0',
      avgResponseTime: '-',
      successRate: '-',
      description: '測試中的多模態模型'
    }
  ];

  const modelProviders = [
    { value: 'openai', label: 'OpenAI' },
    { value: 'anthropic', label: 'Anthropic' },
    { value: 'google', label: 'Google' },
    { value: 'microsoft', label: 'Microsoft' },
    { value: 'meta', label: 'Meta' }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200';
      case 'standby': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'inactive': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '運行中';
      case 'standby': return '待機';
      case 'inactive': return '停用';
      default: return '未知';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return CheckCircle;
      case 'standby': return AlertTriangle;
      case 'inactive': return XCircle;
      default: return AlertTriangle;
    }
  };

  const getProviderIcon = (provider: string) => {
    return Brain; // 統一使用 Brain 圖標，也可以根據不同提供商使用不同圖標
  };

  return (
    <div className="space-y-8">
      {/* 頁面標題 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <Brain className="h-8 w-8" />
              AI 模型管理
            </h1>
            <p className="text-lg text-muted-foreground mt-2">
              管理和配置系統使用的 AI 模型和參數
            </p>
          </div>
          <Button variant="default" size="lg" className="group">
            <Plus className="w-4 h-4 mr-2" />
            新增模型
          </Button>
        </div>
      </div>

      {/* 模型狀態概覽 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-card/60 backdrop-blur-sm border-border-light">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-text-secondary">總模型數</p>
                <p className="text-2xl font-bold text-text-primary mt-1">4</p>
                <p className="text-sm text-text-secondary mt-1">已配置</p>
              </div>
              <div className="p-3 rounded-xl bg-gradient-to-br from-blue-500/20 to-blue-600/20 text-blue-600">
                <Brain className="h-6 w-6" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card/60 backdrop-blur-sm border-border-light">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-text-secondary">運行中</p>
                <p className="text-2xl font-bold text-text-primary mt-1">2</p>
                <p className="text-sm text-green-600 mt-1">正常運行</p>
              </div>
              <div className="p-3 rounded-xl bg-gradient-to-br from-green-500/20 to-green-600/20 text-green-600">
                <CheckCircle className="h-6 w-6" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card/60 backdrop-blur-sm border-border-light">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-text-secondary">本月費用</p>
                <p className="text-2xl font-bold text-text-primary mt-1">$232.44</p>
                <p className="text-sm text-orange-600 mt-1">+12.5%</p>
              </div>
              <div className="p-3 rounded-xl bg-gradient-to-br from-orange-500/20 to-orange-600/20 text-orange-600">
                <DollarSign className="h-6 w-6" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card/60 backdrop-blur-sm border-border-light">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-text-secondary">總請求數</p>
                <p className="text-2xl font-bold text-text-primary mt-1">24,522</p>
                <p className="text-sm text-green-600 mt-1">+8.3%</p>
              </div>
              <div className="p-3 rounded-xl bg-gradient-to-br from-purple-500/20 to-purple-600/20 text-purple-600">
                <BarChart3 className="h-6 w-6" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 模型列表 */}
      <div className="space-y-4">
        <div>
          <h2 className="text-2xl font-bold text-text-primary mb-2">已配置的 AI 模型</h2>
          <p className="text-text-secondary">管理您的 AI 模型配置和使用狀態</p>
        </div>

        <div className="grid grid-cols-1 gap-4">
          {aiModels.map((model) => {
            const StatusIcon = getStatusIcon(model.status);
            const ProviderIcon = getProviderIcon(model.provider);
            
            return (
              <Card key={model.id} className="bg-card/60 backdrop-blur-sm border-border-light hover:shadow-glow transition-all duration-300">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {/* 模型基本資訊 */}
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-4 flex-1">
                        <div className="p-3 rounded-xl bg-gradient-to-br from-primary/20 to-accent/20 text-primary">
                          <ProviderIcon className="h-6 w-6" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-lg font-semibold text-text-primary">{model.name}</h3>
                            <Badge className={getStatusColor(model.status)}>
                              {getStatusText(model.status)}
                            </Badge>
                            <Badge variant="outline">{model.provider}</Badge>
                          </div>
                          <p className="text-text-secondary mb-2">{model.description}</p>
                          <p className="text-sm text-text-secondary">版本: {model.version}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 ml-4">
                        <StatusIcon className={`h-5 w-5 ${model.status === 'active' ? 'text-green-600' : model.status === 'standby' ? 'text-yellow-600' : 'text-red-600'}`} />
                        <Button variant="outline" size="sm">
                          <Edit className="w-4 h-4 mr-1" />
                          編輯
                        </Button>
                        <Button variant="outline" size="sm">
                          {model.status === 'active' ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                        </Button>
                        <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    {/* 使用統計 */}
                    <div className="grid grid-cols-2 lg:grid-cols-5 gap-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-text-secondary">使用率</span>
                          <span className="text-sm font-bold text-text-primary">{model.usage}%</span>
                        </div>
                        <Progress value={model.usage} className="h-2" />
                      </div>
                      <div className="text-center">
                        <p className="text-lg font-bold text-text-primary">{model.cost}</p>
                        <p className="text-xs text-text-secondary">本月費用</p>
                      </div>
                      <div className="text-center">
                        <p className="text-lg font-bold text-text-primary">{model.requests}</p>
                        <p className="text-xs text-text-secondary">總請求數</p>
                      </div>
                      <div className="text-center">
                        <p className="text-lg font-bold text-text-primary">{model.avgResponseTime}</p>
                        <p className="text-xs text-text-secondary">平均回應時間</p>
                      </div>
                      <div className="text-center">
                        <p className="text-lg font-bold text-text-primary">{model.successRate}</p>
                        <p className="text-xs text-text-secondary">成功率</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {/* 模型配置 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 新增模型 */}
        <Card className="bg-card/60 backdrop-blur-sm border-border-light">
          <CardHeader>
            <CardTitle className="text-text-primary flex items-center gap-2">
              <Plus className="h-5 w-5" />
              新增 AI 模型
            </CardTitle>
            <CardDescription>
              配置新的 AI 模型連接
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label htmlFor="model-name">模型名稱</Label>
                <Input id="model-name" placeholder="輸入模型名稱" />
              </div>
              
              <div>
                <Label htmlFor="provider">提供商</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="選擇提供商" />
                  </SelectTrigger>
                  <SelectContent>
                    {modelProviders.map((provider) => (
                      <SelectItem key={provider.value} value={provider.value}>
                        {provider.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="api-key">API 金鑰</Label>
                <Input id="api-key" type="password" placeholder="輸入 API 金鑰" />
              </div>

              <div>
                <Label htmlFor="endpoint">API 端點</Label>
                <Input id="endpoint" placeholder="https://api.example.com/v1" />
              </div>

              <div>
                <Label htmlFor="model-description">描述</Label>
                <Textarea id="model-description" placeholder="輸入模型描述" rows={2} />
              </div>

              <div className="flex items-center space-x-2">
                <Switch id="auto-enable" />
                <Label htmlFor="auto-enable">自動啟用</Label>
              </div>

              <div className="flex justify-end gap-3">
                <Button variant="outline">取消</Button>
                <Button variant="outline">測試連接</Button>
                <Button variant="default">保存配置</Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 全局模型參數 */}
        <Card className="bg-card/60 backdrop-blur-sm border-border-light">
          <CardHeader>
            <CardTitle className="text-text-primary flex items-center gap-2">
              <Settings className="h-5 w-5" />
              全局模型參數
            </CardTitle>
            <CardDescription>
              設置默認的模型調用參數
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <Label>溫度設置 (Temperature): {temperature[0]}</Label>
                <Slider
                  value={temperature}
                  onValueChange={setTemperature}
                  max={2}
                  min={0}
                  step={0.1}
                  className="mt-2"
                />
                <p className="text-xs text-text-secondary mt-1">
                  控制回應的隨機性，0 為確定性，2 為最大隨機性
                </p>
              </div>

              <div>
                <Label>最大 Token 數: {maxTokens[0]}</Label>
                <Slider
                  value={maxTokens}
                  onValueChange={setMaxTokens}
                  max={4096}
                  min={100}
                  step={100}
                  className="mt-2"
                />
                <p className="text-xs text-text-secondary mt-1">
                  控制回應的最大長度
                </p>
              </div>

              <div>
                <Label htmlFor="system-prompt">系統提示詞</Label>
                <Textarea 
                  id="system-prompt" 
                  placeholder="輸入默認的系統提示詞" 
                  rows={4}
                  defaultValue="你是一個專業的 AI SEO 分析助手，請提供準確、有用的建議。"
                />
              </div>

              <div className="flex items-center justify-between p-3 rounded-lg bg-surface-1/40 border border-border-light/60">
                <div>
                  <p className="font-medium text-text-primary">啟用自動重試</p>
                  <p className="text-sm text-text-secondary">API 調用失敗時自動重試</p>
                </div>
                <Switch defaultChecked />
              </div>

              <div className="flex items-center justify-between p-3 rounded-lg bg-surface-1/40 border border-border-light/60">
                <div>
                  <p className="font-medium text-text-primary">使用費用限制</p>
                  <p className="text-sm text-text-secondary">每月 $500 費用上限</p>
                </div>
                <Switch defaultChecked />
              </div>

              <Button className="w-full">保存全局設置</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 