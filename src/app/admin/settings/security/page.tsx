'use client';

import { <PERSON>, Key, Lock, Users, Alert<PERSON>riangle, CheckCircle, Eye } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export default function SecuritySettingsPage() {
  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <Shield className="h-8 w-8" />
              安全設置
            </h1>
            <p className="text-lg text-muted-foreground mt-2">
              配置訪問控制、認證和安全策略
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">安全狀態</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <Badge className="bg-green-100 text-green-800">安全</Badge>
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              最後掃描: 1小時前
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Key className="h-4 w-4" />
              API 金鑰
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-blue-600">5</div>
              <p className="text-sm text-muted-foreground">
                活躍金鑰數量
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Users className="h-4 w-4" />
              活躍用戶
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-green-600">12</div>
              <p className="text-sm text-muted-foreground">
                當前在線: 3人
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Eye className="h-4 w-4" />
              安全事件
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-orange-600">2</div>
              <p className="text-sm text-muted-foreground">
                今日警告數量
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>訪問控制</CardTitle>
            <CardDescription>
              用戶權限和角色管理
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span>管理員</span>
                <Badge className="bg-red-100 text-red-800">2人</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>編輯者</span>
                <Badge className="bg-blue-100 text-blue-800">5人</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>檢視者</span>
                <Badge className="bg-gray-100 text-gray-800">5人</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>認證設置</CardTitle>
            <CardDescription>
              登入安全和認證策略
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span>雙因子認證</span>
                <Badge className="bg-green-100 text-green-800">啟用</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>密碼複雜度</span>
                <Badge className="bg-blue-100 text-blue-800">高</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>Session 超時</span>
                <span className="text-sm">24小時</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>安全策略</CardTitle>
            <CardDescription>
              系統安全規則和限制
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span>IP 白名單</span>
                <Badge className="bg-blue-100 text-blue-800">啟用</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>API 請求限制</span>
                <span className="text-sm">1000/小時</span>
              </div>
              <div className="flex items-center justify-between">
                <span>資料加密</span>
                <Badge className="bg-green-100 text-green-800">AES-256</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>審計日誌</CardTitle>
            <CardDescription>
              系統活動記錄和追蹤
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span>日誌記錄</span>
                <Badge className="bg-green-100 text-green-800">啟用</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>保留期限</span>
                <span className="text-sm">90天</span>
              </div>
              <div className="flex items-center justify-between">
                <span>今日事件</span>
                <span className="text-sm">247筆</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>最近安全事件</CardTitle>
          <CardDescription>
            最近的安全警告和異常活動
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <AlertTriangle className="h-5 w-5 text-yellow-600" />
                <div>
                  <p className="font-medium text-yellow-800">異常登入嘗試</p>
                  <p className="text-sm text-yellow-600">來自 IP: *************</p>
                </div>
              </div>
              <span className="text-sm text-yellow-600">2小時前</span>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium text-blue-800">密碼重設</p>
                  <p className="text-sm text-blue-600">用戶: <EMAIL></p>
                </div>
              </div>
              <span className="text-sm text-blue-600">4小時前</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>開發進度</CardTitle>
          <CardDescription>
            安全設置功能開發狀態
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Shield className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-lg font-medium mb-2">安全設置功能開發中</p>
            <p className="text-muted-foreground">
              完整的訪問控制、認證設置和安全策略管理功能正在開發中。
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
