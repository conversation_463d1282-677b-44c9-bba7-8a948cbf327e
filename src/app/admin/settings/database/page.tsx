'use client';

import { Database, Server, HardDrive, Shield, Activity } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export default function DatabaseSettingsPage() {
  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <Database className="h-8 w-8" />
              資料庫設置
            </h1>
            <p className="text-lg text-muted-foreground mt-2">
              管理資料庫連接、備份和效能監控
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">連接狀態</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Activity className="h-5 w-5 text-green-600" />
              <Badge className="bg-green-100 text-green-800">已連接</Badge>
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              PostgreSQL 15.4
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Server className="h-4 w-4" />
              連接數
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-blue-600">23/100</div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-blue-600 h-2 rounded-full" style={{ width: '23%' }}></div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <HardDrive className="h-4 w-4" />
              資料庫大小
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-green-600">2.4GB</div>
              <p className="text-sm text-muted-foreground">
                增長: +156MB/月
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Shield className="h-4 w-4" />
              最後備份
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-lg font-bold text-purple-600">2小時前</div>
              <p className="text-sm text-muted-foreground">
                自動備份: 啟用
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>連接管理</CardTitle>
            <CardDescription>
              資料庫連接配置和狀態監控
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span>主資料庫</span>
                <Badge className="bg-green-100 text-green-800">在線</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>讀取副本</span>
                <Badge className="bg-green-100 text-green-800">在線</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>Redis 緩存</span>
                <Badge className="bg-green-100 text-green-800">在線</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>備份設置</CardTitle>
            <CardDescription>
              自動備份和恢復配置
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span>自動備份</span>
                <Badge className="bg-blue-100 text-blue-800">每日 02:00</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>保留期限</span>
                <span className="text-sm">30天</span>
              </div>
              <div className="flex items-center justify-between">
                <span>備份存儲</span>
                <span className="text-sm">AWS S3</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>開發進度</CardTitle>
          <CardDescription>
            資料庫設置功能開發狀態
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Database className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-lg font-medium mb-2">資料庫設置功能開發中</p>
            <p className="text-muted-foreground">
              完整的資料庫配置、備份管理和效能監控功能正在開發中。
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
