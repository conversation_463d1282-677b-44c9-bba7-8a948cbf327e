'use client';

import { Bell, Mail, Webhook, Settings } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function NotificationsSettingsPage() {
  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <Bell className="h-8 w-8" />
              通知設置
            </h1>
            <p className="text-lg text-muted-foreground mt-2">
              配置郵件、Webhook 和即時通知設置
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              郵件配置
            </CardTitle>
            <CardDescription>
              SMTP 服務器和郵件發送設置
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              配置 SMTP 服務器、認證信息和發件人設置，支持 Gmail、Outlook 等主流郵件服務。
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Webhook className="h-5 w-5" />
              Webhook 設置
            </CardTitle>
            <CardDescription>
              集成第三方服務通知
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              配置 Slack、Discord、Microsoft Teams 等平台的 Webhook 通知。
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              通知規則
            </CardTitle>
            <CardDescription>
              定義何時發送通知
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              設置系統警報、性能監控、安全事件等通知觸發條件。
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>開發進度</CardTitle>
          <CardDescription>
            通知設置功能開發狀態
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="text-center py-8">
              <Bell className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <p className="text-lg font-medium mb-2">通知設置功能開發中</p>
              <p className="text-muted-foreground">
                此功能正在積極開發中，將包含完整的郵件配置、Webhook 設置、即時通知和警報規則管理功能。
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
