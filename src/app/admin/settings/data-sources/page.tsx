'use client';

import React, { useState } from 'react';
import { 
  Database, 
  Server, 
  Cloud, 
  Key, 
  Settings, 
  Plus,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Edit,
  Trash2,
  Eye,
  EyeOff
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';

export default function DataSourcesPage() {
  const [showPassword, setShowPassword] = useState(false);
  const [editingSource, setEditingSource] = useState<string | null>(null);

  const dataSources = [
    {
      id: '1',
      name: 'PostgreSQL 主資料庫',
      type: 'postgresql',
      status: 'connected',
      host: 'localhost:5432',
      database: 'aiseoking_db',
      lastSync: '2 分鐘前',
      description: '主要業務數據存儲'
    },
    {
      id: '2',
      name: 'Redis 快取',
      type: 'redis',
      status: 'connected',
      host: 'localhost:6379',
      database: '0',
      lastSync: '即時',
      description: '快取和會話數據'
    },
    {
      id: '3',
      name: 'Elasticsearch 搜尋',
      type: 'elasticsearch',
      status: 'warning',
      host: 'localhost:9200',
      database: 'seo_analytics',
      lastSync: '1 小時前',
      description: '搜尋和分析數據索引'
    },
    {
      id: '4',
      name: 'Google Analytics',
      type: 'google_analytics',
      status: 'disconnected',
      host: 'analytics.google.com',
      database: 'GA4',
      lastSync: '3 天前',
      description: '網站流量和用戶行為數據'
    }
  ];

  const dataSourceTypes = [
    { value: 'postgresql', label: 'PostgreSQL', icon: Database },
    { value: 'mysql', label: 'MySQL', icon: Database },
    { value: 'redis', label: 'Redis', icon: Server },
    { value: 'elasticsearch', label: 'Elasticsearch', icon: Cloud },
    { value: 'google_analytics', label: 'Google Analytics', icon: Cloud },
    { value: 'mongodb', label: 'MongoDB', icon: Database }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'bg-green-100 text-green-800 border-green-200';
      case 'warning': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'disconnected': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'connected': return '已連接';
      case 'warning': return '警告';
      case 'disconnected': return '已斷開';
      default: return '未知';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected': return CheckCircle;
      case 'warning': return AlertTriangle;
      case 'disconnected': return XCircle;
      default: return AlertTriangle;
    }
  };

  const getTypeIcon = (type: string) => {
    const typeInfo = dataSourceTypes.find(t => t.value === type);
    return typeInfo?.icon || Database;
  };

  return (
    <div className="space-y-8">
      {/* 頁面標題 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <Database className="h-8 w-8" />
              數據源配置
            </h1>
            <p className="text-lg text-muted-foreground mt-2">
              管理和配置系統使用的各種數據源連接
            </p>
          </div>
          <Button variant="default" size="lg" className="group">
            <Plus className="w-4 h-4 mr-2" />
            新增數據源
          </Button>
        </div>
      </div>

      {/* 數據源狀態概覽 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-card/60 backdrop-blur-sm border-border-light">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-text-secondary">總數據源</p>
                <p className="text-2xl font-bold text-text-primary mt-1">4</p>
                <p className="text-sm text-text-secondary mt-1">已配置</p>
              </div>
              <div className="p-3 rounded-xl bg-gradient-to-br from-blue-500/20 to-blue-600/20 text-blue-600">
                <Database className="h-6 w-6" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card/60 backdrop-blur-sm border-border-light">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-text-secondary">正常連接</p>
                <p className="text-2xl font-bold text-text-primary mt-1">2</p>
                <p className="text-sm text-green-600 mt-1">運行正常</p>
              </div>
              <div className="p-3 rounded-xl bg-gradient-to-br from-green-500/20 to-green-600/20 text-green-600">
                <CheckCircle className="h-6 w-6" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card/60 backdrop-blur-sm border-border-light">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-text-secondary">需要關注</p>
                <p className="text-2xl font-bold text-text-primary mt-1">2</p>
                <p className="text-sm text-orange-600 mt-1">需要處理</p>
              </div>
              <div className="p-3 rounded-xl bg-gradient-to-br from-orange-500/20 to-orange-600/20 text-orange-600">
                <AlertTriangle className="h-6 w-6" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 數據源列表 */}
      <div className="space-y-4">
        <div>
          <h2 className="text-2xl font-bold text-text-primary mb-2">已配置的數據源</h2>
          <p className="text-text-secondary">管理您的數據源連接和配置</p>
        </div>

        <div className="grid grid-cols-1 gap-4">
          {dataSources.map((source) => {
            const StatusIcon = getStatusIcon(source.status);
            const TypeIcon = getTypeIcon(source.type);
            
            return (
              <Card key={source.id} className="bg-card/60 backdrop-blur-sm border-border-light hover:shadow-glow transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-4 flex-1">
                      <div className="p-3 rounded-xl bg-gradient-to-br from-primary/20 to-accent/20 text-primary">
                        <TypeIcon className="h-6 w-6" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold text-text-primary">{source.name}</h3>
                          <Badge className={getStatusColor(source.status)}>
                            {getStatusText(source.status)}
                          </Badge>
                        </div>
                        <p className="text-text-secondary mb-2">{source.description}</p>
                        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-text-secondary">主機：</span>
                            <span className="text-text-primary font-medium">{source.host}</span>
                          </div>
                          <div>
                            <span className="text-text-secondary">資料庫：</span>
                            <span className="text-text-primary font-medium">{source.database}</span>
                          </div>
                          <div>
                            <span className="text-text-secondary">類型：</span>
                            <span className="text-text-primary font-medium">{source.type}</span>
                          </div>
                          <div>
                            <span className="text-text-secondary">最後同步：</span>
                            <span className="text-text-primary font-medium">{source.lastSync}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 ml-4">
                      <StatusIcon className={`h-5 w-5 ${source.status === 'connected' ? 'text-green-600' : source.status === 'warning' ? 'text-yellow-600' : 'text-red-600'}`} />
                      <Button variant="outline" size="sm">
                        <Edit className="w-4 h-4 mr-1" />
                        編輯
                      </Button>
                      <Button variant="outline" size="sm">
                        測試連接
                      </Button>
                      <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {/* 新增數據源表單 */}
      <Card className="bg-card/60 backdrop-blur-sm border-border-light">
        <CardHeader>
          <CardTitle className="text-text-primary flex items-center gap-2">
            <Plus className="h-5 w-5" />
            新增數據源
          </CardTitle>
          <CardDescription>
            配置新的數據源連接
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">數據源名稱</Label>
                <Input id="name" placeholder="輸入數據源名稱" />
              </div>
              
              <div>
                <Label htmlFor="type">數據源類型</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="選擇數據源類型" />
                  </SelectTrigger>
                  <SelectContent>
                    {dataSourceTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="host">主機地址</Label>
                <Input id="host" placeholder="localhost:5432" />
              </div>

              <div>
                <Label htmlFor="database">資料庫名稱</Label>
                <Input id="database" placeholder="database_name" />
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="username">用戶名</Label>
                <Input id="username" placeholder="輸入用戶名" />
              </div>

              <div>
                <Label htmlFor="password">密碼</Label>
                <div className="relative">
                  <Input 
                    id="password" 
                    type={showPassword ? "text" : "password"} 
                    placeholder="輸入密碼" 
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              <div>
                <Label htmlFor="description">描述</Label>
                <Textarea id="description" placeholder="輸入數據源描述" rows={3} />
              </div>

              <div className="flex items-center space-x-2">
                <Switch id="ssl" />
                <Label htmlFor="ssl">啟用 SSL 連接</Label>
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-3 mt-6">
            <Button variant="outline">取消</Button>
            <Button variant="outline">測試連接</Button>
            <Button variant="default">保存配置</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 