'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Bar<PERSON>hart3, 
  <PERSON><PERSON>hart as LineChartIcon, 
  <PERSON><PERSON>hart as PieChartIcon,
  TrendingUp,
  Target,
  Zap
} from 'lucide-react';

// 導入圖表組件
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Radar<PERSON>hart,
} from '@/components/charts';

import {
  VisibilityDashboard,
  CompetitiveDashboard,
  JourneyDashboard,
  SentimentDashboard,
} from '@/components/charts/measure';

import {
  TrendAnalysisDashboard,
  PredictionDashboard,
} from '@/components/charts/trends';

import { RealTimeChart } from '@/components/charts/RealTimeChart';
import { simulateApiCalls } from '@/lib/realTimeSimulator';

// 模擬數據
const mockLineData = [
  { date: '1月', value: 65, predicted: null },
  { date: '2月', value: 72, predicted: null },
  { date: '3月', value: 68, predicted: null },
  { date: '4月', value: 75, predicted: null },
  { date: '5月', value: 82, predicted: null },
  { date: '6月', value: null, predicted: 85 },
  { date: '7月', value: null, predicted: 88 },
];

const mockBarData = [
  { name: 'ChatGPT', value: 78 },
  { name: 'Google Gemini', value: 72 },
  { name: 'Perplexity', value: 69 },
  { name: 'Claude', value: 75 },
];

const mockPieData = [
  { name: '正面', value: 65 },
  { name: '中性', value: 25 },
  { name: '負面', value: 10 },
];

const mockVisibilityData = {
  overall: {
    score: 73.5,
    change: '+5.3%',
    trend: 'up' as const,
    queries: 2847,
    mentions: 1892
  },
  by_engine: [
    { engine: 'ChatGPT', score: 78.2, queries: 892, mentions: 697 },
    { engine: 'Google Gemini', score: 71.8, queries: 756, mentions: 543 },
    { engine: 'Perplexity', score: 69.4, queries: 634, mentions: 440 },
    { engine: 'Claude', score: 75.1, queries: 565, mentions: 212 }
  ],
  top_queries: [
    { query: 'AI SEO 優化工具', mentions: 156, visibility: 89.2 },
    { query: 'SEO 自動化平台', mentions: 134, visibility: 76.8 },
    { query: '智能 SEO 分析', mentions: 98, visibility: 82.4 },
    { query: 'AI 內容優化', mentions: 87, visibility: 71.3 }
  ]
};

const mockTrendData = {
  current: mockLineData.filter(d => d.value !== null).map(d => ({
    date: d.date,
    value: d.value!,
    predicted: false
  })),
  historical: [],
  prediction: mockLineData.filter(d => d.predicted !== null).map(d => ({
    date: d.date,
    value: d.predicted!,
    predicted: true,
    confidence: 0.85
  }))
};

export default function ChartsDemo() {
  const [activeTab, setActiveTab] = useState('basic');

  return (
    <div className="space-y-6">
      {/* 頁面標題 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <BarChart3 className="h-8 w-8 text-blue-600 mr-3" />
            圖表組件展示
          </h1>
          <p className="text-gray-600 mt-2">
            展示 AI SEO 優化王系統中的所有圖表組件和數據視覺化功能
          </p>
        </div>
        <div className="flex space-x-2">
          <Badge variant="outline">Chart.js</Badge>
          <Badge variant="outline">Recharts</Badge>
          <Badge className="bg-green-100 text-green-800">互動式</Badge>
        </div>
      </div>

      {/* 圖表展示 */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="basic">基礎圖表</TabsTrigger>
          <TabsTrigger value="measure">測量功能</TabsTrigger>
          <TabsTrigger value="trends">趨勢分析</TabsTrigger>
          <TabsTrigger value="prediction">預測分析</TabsTrigger>
          <TabsTrigger value="realtime">實時數據</TabsTrigger>
          <TabsTrigger value="interactive">互動功能</TabsTrigger>
        </TabsList>

        {/* 基礎圖表 */}
        <TabsContent value="basic">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <LineChart
              title="折線圖示例"
              description="顯示時間序列數據的變化趨勢"
              data={mockLineData}
              xAxisKey="date"
              yAxisKey="value"
              height={300}
              showGrid={true}
              showTooltip={true}
              showLegend={false}
            />

            <BarChart
              title="柱狀圖示例"
              description="比較不同類別的數值大小"
              data={mockBarData}
              xAxisKey="name"
              yAxisKey="value"
              height={300}
              showGrid={true}
              showTooltip={true}
              showLegend={false}
            />

            <PieChart
              title="圓餅圖示例"
              description="顯示各部分佔整體的比例"
              data={mockPieData}
              height={300}
              showTooltip={true}
              showLegend={true}
            />

            <AreaChart
              title="面積圖示例"
              description="強調數據的累積效果"
              data={mockLineData.filter(d => d.value !== null)}
              xAxisKey="date"
              yAxisKey="value"
              height={300}
              showGrid={true}
              showTooltip={true}
              showLegend={false}
            />
          </div>
        </TabsContent>

        {/* 測量功能圖表 */}
        <TabsContent value="measure">
          <div className="space-y-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <LineChartIcon className="h-5 w-5 mr-2" />
                  品牌可見度儀表板
                </CardTitle>
                <CardDescription>
                  完整的品牌可見度分析圖表組合
                </CardDescription>
              </CardHeader>
              <CardContent>
                <VisibilityDashboard 
                  data={mockVisibilityData}
                  timeRange="7d"
                  loading={false}
                  error={null}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Target className="h-5 w-5 mr-2" />
                  競爭分析儀表板
                </CardTitle>
                <CardDescription>
                  競爭對手分析和市場機會識別
                </CardDescription>
              </CardHeader>
              <CardContent>
                <CompetitiveDashboard 
                  data={{
                    competitors: [
                      { id: 1, name: 'SEO 大師', visibility: 82.4, change: '+3.2%', rank: 1, queries: 1200, mentions: 800, shareOfVoice: 25 },
                      { id: 2, name: 'AI SEO 優化王', visibility: 73.5, change: '+5.3%', rank: 2, queries: 1000, mentions: 650, shareOfVoice: 20 },
                      { id: 3, name: 'SEO 工具專家', visibility: 68.9, change: '-1.2%', rank: 3, queries: 900, mentions: 580, shareOfVoice: 18 },
                    ],
                    opportunities: [
                      { title: 'AI 內容優化', description: '競爭對手在此領域表現較弱', impact: 'high' as const, effort: 'medium' as const },
                      { title: '語音搜索優化', description: '新興領域，競爭較少', impact: 'medium' as const, effort: 'low' as const },
                    ]
                  }}
                  timeRange="30d"
                  loading={false}
                  error={null}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 趨勢分析 */}
        <TabsContent value="trends">
          <TrendAnalysisDashboard
            data={mockTrendData}
            title="趨勢分析展示"
            showIndicators={true}
            loading={false}
            error={null}
          />
        </TabsContent>

        {/* 預測分析 */}
        <TabsContent value="prediction">
          <PredictionDashboard
            data={mockLineData.map(d => ({
              date: d.date,
              actual: d.value || undefined,
              predicted: d.predicted || d.value || 0,
              confidence: 0.85,
              upperBound: (d.predicted || d.value || 0) * 1.1,
              lowerBound: (d.predicted || d.value || 0) * 0.9,
            }))}
            analysis={{
              accuracy: 87.5,
              confidence: 85.2,
              trend: 'increasing' as const,
              volatility: 'low' as const,
              nextPeriodPrediction: 90,
              riskLevel: 'low' as const,
            }}
            title="預測分析展示"
            loading={false}
            error={null}
          />
        </TabsContent>

        {/* 實時數據 */}
        <TabsContent value="realtime">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <RealTimeChart
              title="品牌可見度實時監控"
              description="每 3 秒更新一次品牌可見度分數"
              fetchData={() => simulateApiCalls.getMetricValue('visibility')}
              chartType="line"
              xAxisKey="time"
              yAxisKey="data"
              height={300}
              updateInterval={3000}
              maxDataPoints={20}
              showControls={true}
              showStatus={true}
            />

            <RealTimeChart
              title="競爭排名實時追蹤"
              description="每 5 秒更新一次競爭排名數據"
              fetchData={() => simulateApiCalls.getMetricValue('competitive')}
              chartType="area"
              xAxisKey="time"
              yAxisKey="data"
              height={300}
              updateInterval={5000}
              maxDataPoints={15}
              showControls={true}
              showStatus={true}
            />

            <RealTimeChart
              title="情感分析實時監測"
              description="每 4 秒更新一次情感分析結果"
              fetchData={() => simulateApiCalls.getMetricValue('sentiment')}
              chartType="bar"
              xAxisKey="time"
              yAxisKey="data"
              height={300}
              updateInterval={4000}
              maxDataPoints={12}
              showControls={true}
              showStatus={true}
            />

            <RealTimeChart
              title="轉換率實時統計"
              description="每 6 秒更新一次轉換率數據"
              fetchData={() => simulateApiCalls.getMetricValue('conversion')}
              chartType="line"
              xAxisKey="time"
              yAxisKey="data"
              height={300}
              updateInterval={6000}
              maxDataPoints={18}
              showControls={true}
              showStatus={true}
            />
          </div>
        </TabsContent>

        {/* 互動功能 */}
        <TabsContent value="interactive">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Zap className="h-5 w-5 mr-2" />
                  互動功能展示
                </CardTitle>
                <CardDescription>
                  圖表的互動功能和工具欄
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex flex-wrap gap-2">
                    <Badge>數據篩選</Badge>
                    <Badge>縮放功能</Badge>
                    <Badge>工具提示</Badge>
                    <Badge>圖表下載</Badge>
                    <Badge>全屏顯示</Badge>
                  </div>
                  
                  <LineChart
                    title="互動式折線圖"
                    description="支援縮放、篩選和下載功能"
                    data={mockLineData}
                    xAxisKey="date"
                    yAxisKey="value"
                    height={250}
                    showToolbar={true}
                    showRefresh={true}
                    showDownload={true}
                    showFullscreen={true}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2" />
                  響應式設計
                </CardTitle>
                <CardDescription>
                  圖表自動適應不同螢幕尺寸
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="outline">手機適配</Badge>
                    <Badge variant="outline">平板適配</Badge>
                    <Badge variant="outline">桌面適配</Badge>
                    <Badge variant="outline">自動縮放</Badge>
                  </div>
                  
                  <BarChart
                    title="響應式柱狀圖"
                    description="自動調整高度和佈局"
                    data={mockBarData}
                    xAxisKey="name"
                    yAxisKey="value"
                    height="auto"
                    responsive={true}
                    minHeight={200}
                    maxHeight={400}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
