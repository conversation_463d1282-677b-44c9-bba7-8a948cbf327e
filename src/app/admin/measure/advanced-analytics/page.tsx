'use client';

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Wand2, 
  Layers, 
  Filter,
  BarChart3,
  TrendingUp,
  Settings,
  Download,
  Eye
} from 'lucide-react';

import {
  CustomChartBuilder,
  DataDrillDown,
  MultiDimensionalAnalysis,
  advancedAnalysisUtils,
  type ChartConfig,
  type DrillLevel,
  type Dimension,
  type Measure,
} from '@/components/charts/advanced';

// 模擬數據
const generateMockData = () => {
  const categories = ['AI SEO', 'SEO 工具', '內容優化', '關鍵字分析', '競爭分析'];
  const engines = ['ChatGPT', 'Google Gemini', 'Perplexity', 'Claude'];
  const regions = ['台灣', '香港', '新加坡', '馬來西亞'];
  const timeRanges = ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06'];

  return Array.from({ length: 500 }, (_, i) => ({
    id: i + 1,
    category: categories[Math.floor(Math.random() * categories.length)],
    engine: engines[Math.floor(Math.random() * engines.length)],
    region: regions[Math.floor(Math.random() * regions.length)],
    month: timeRanges[Math.floor(Math.random() * timeRanges.length)],
    visibility: Math.floor(Math.random() * 100) + 1,
    queries: Math.floor(Math.random() * 1000) + 100,
    mentions: Math.floor(Math.random() * 500) + 50,
    sentiment: Math.random() * 100,
    conversion: Math.random() * 10,
    revenue: Math.floor(Math.random() * 10000) + 1000,
  }));
};

export default function AdvancedAnalyticsPage() {
  const [activeTab, setActiveTab] = useState('builder');
  const [chartConfig, setChartConfig] = useState<ChartConfig | null>(null);

  // 生成模擬數據
  const mockData = useMemo(() => generateMockData(), []);

  // 維度定義
  const dimensions: Dimension[] = [
    advancedAnalysisUtils.createDimension('category', '產品類別', 'category', 'categorical'),
    advancedAnalysisUtils.createDimension('engine', 'AI 引擎', 'engine', 'categorical'),
    advancedAnalysisUtils.createDimension('region', '地區', 'region', 'categorical'),
    advancedAnalysisUtils.createDimension('month', '月份', 'month', 'temporal'),
    advancedAnalysisUtils.createDimension('visibility_range', '可見度範圍', 'visibility', 'numerical', {
      range: [0, 100]
    }),
  ];

  // 度量定義
  const measures: Measure[] = [
    advancedAnalysisUtils.createMeasure('visibility', '品牌可見度', 'visibility', 'avg', 'percentage'),
    advancedAnalysisUtils.createMeasure('queries', '查詢數量', 'queries', 'sum', 'number'),
    advancedAnalysisUtils.createMeasure('mentions', '提及次數', 'mentions', 'sum', 'number'),
    advancedAnalysisUtils.createMeasure('sentiment', '情感分數', 'sentiment', 'avg', 'number'),
    advancedAnalysisUtils.createMeasure('conversion', '轉換率', 'conversion', 'avg', 'percentage'),
    advancedAnalysisUtils.createMeasure('revenue', '收入', 'revenue', 'sum', 'currency'),
  ];

  // 鑽取層級定義
  const drillLevels: DrillLevel[] = [
    advancedAnalysisUtils.createDrillLevel('category', '產品類別', 'category', {
      description: '按產品類別分組的頂層視圖',
      aggregateBy: 'visibility',
      chartType: 'bar',
    }),
    advancedAnalysisUtils.createDrillLevel('engine', 'AI 引擎', 'engine', {
      description: '按 AI 引擎分組的詳細視圖',
      aggregateBy: 'visibility',
      chartType: 'pie',
    }),
    advancedAnalysisUtils.createDrillLevel('region', '地區分佈', 'region', {
      description: '按地區分組的最詳細視圖',
      aggregateBy: 'visibility',
      chartType: 'bar',
    }),
  ];

  // 數據統計
  const dataStatistics = useMemo(() => {
    return advancedAnalysisUtils.generateDataSummary(mockData, dimensions, measures);
  }, [mockData, dimensions, measures]);

  // 分析建議
  const analysisSuggestions = useMemo(() => {
    return advancedAnalysisUtils.generateAnalysisSuggestions(mockData, dimensions, measures);
  }, [mockData, dimensions, measures]);

  return (
    <div className="space-y-6">
      {/* 頁面標題 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Wand2 className="h-8 w-8 text-purple-600 mr-3" />
            高級數據分析
          </h1>
          <p className="text-gray-600 mt-2">
            使用自定義圖表、數據鑽取和多維度分析來深入探索您的數據
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Badge variant="outline">
            {dataStatistics.totalRecords.toLocaleString()} 記錄
          </Badge>
          <Badge variant="outline">
            {dimensions.length} 維度
          </Badge>
          <Badge variant="outline">
            {measures.length} 度量
          </Badge>
        </div>
      </div>

      {/* 數據概覽 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">總記錄數</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dataStatistics.totalRecords.toLocaleString()}
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">平均可見度</p>
                <p className="text-2xl font-bold text-gray-900">
                  {(mockData.reduce((sum, item) => sum + item.visibility, 0) / mockData.length).toFixed(1)}%
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">總查詢數</p>
                <p className="text-2xl font-bold text-gray-900">
                  {mockData.reduce((sum, item) => sum + item.queries, 0).toLocaleString()}
                </p>
              </div>
              <Filter className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">總收入</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${mockData.reduce((sum, item) => sum + item.revenue, 0).toLocaleString()}
                </p>
              </div>
              <Layers className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 分析建議 */}
      {analysisSuggestions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Eye className="h-5 w-5 mr-2" />
              智能分析建議
            </CardTitle>
            <CardDescription>
              基於您的數據特徵生成的分析建議
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {analysisSuggestions.map((suggestion, index) => (
                <div key={index} className="p-4 border rounded-lg">
                  <div className="flex items-start justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900">{suggestion.title}</h4>
                      <p className="text-sm text-gray-600 mt-1">{suggestion.description}</p>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {suggestion.type}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 分析工具 */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="builder">自定義圖表</TabsTrigger>
          <TabsTrigger value="drilldown">數據鑽取</TabsTrigger>
          <TabsTrigger value="multidim">多維度分析</TabsTrigger>
        </TabsList>

        <TabsContent value="builder">
          <CustomChartBuilder
            data={mockData}
            onConfigChange={setChartConfig}
            onSave={(config) => {
              console.log('保存圖表配置:', config);
              // 這裡可以實現保存到後端的邏輯
            }}
          />
        </TabsContent>

        <TabsContent value="drilldown">
          <DataDrillDown
            data={mockData as any}
            levels={drillLevels}
            title="SEO 數據鑽取分析"
            description="從產品類別開始，逐層深入分析 AI 引擎和地區表現"
            onDrillDown={(path) => {
              console.log('鑽取路徑:', path);
            }}
          />
        </TabsContent>

        <TabsContent value="multidim">
          <MultiDimensionalAnalysis
            data={mockData}
            dimensions={dimensions}
            measures={measures}
            title="多維度 SEO 分析"
            description="同時分析多個維度和度量，發現數據中的模式和關聯"
            onConfigChange={(config) => {
              console.log('多維度分析配置:', config);
            }}
          />
        </TabsContent>
      </Tabs>

      {/* 當前配置信息 */}
      {chartConfig && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              當前圖表配置
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="font-medium">圖表類型:</span>
                <span className="ml-2">{chartConfig.type}</span>
              </div>
              <div>
                <span className="font-medium">X 軸:</span>
                <span className="ml-2">{chartConfig.xAxisKey}</span>
              </div>
              <div>
                <span className="font-medium">Y 軸:</span>
                <span className="ml-2">{chartConfig.yAxisKey}</span>
              </div>
              <div>
                <span className="font-medium">高度:</span>
                <span className="ml-2">{chartConfig.height}px</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
