'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Gauge, 
  Eye, 
  Zap, 
  TrendingUp, 
  Heart, 
  ArrowRight,
  BarChart3,
  Target,
  Users,
  AlertCircle,
  CheckCircle,
  Clock,
  Sparkles
} from 'lucide-react';

// 模擬數據
const mockStats = {
  totalBrands: 12,
  activeCampaigns: 8,
  totalQueries: 2847,
  avgVisibility: 73.5,
  competitorTracked: 24,
  sentimentScore: 8.2
};

const mockRecentAnalysis = [
  {
    id: 1,
    brand: 'AI SEO 優化',
    type: 'visibility',
    status: 'completed',
    score: 85.2,
    timestamp: '2025-06-22 14:30',
    change: '+5.3%'
  },
  {
    id: 2,
    brand: 'SEO 工具王',
    type: 'competitive',
    status: 'running',
    score: 72.8,
    timestamp: '2025-06-22 13:45',
    change: '-2.1%'
  },
  {
    id: 3,
    brand: '智能分析師',
    type: 'sentiment',
    status: 'completed',
    score: 91.5,
    timestamp: '2025-06-22 12:15',
    change: '+8.7%'
  }
];

const featureModules = [
  {
    href: '/admin/measure/visibility',
    title: '品牌可見度分析',
    description: '測量您的品牌在主要 AI 搜尋引擎中的可見度和聲量份額',
    icon: Eye,
    gradient: 'from-blue-500 to-blue-600',
    stats: '73.5% 平均可見度',
    features: ['AI 可見度追蹤', '品牌提及分析', '聲量份額追蹤']
  },
  {
    href: '/admin/measure/competitive',
    title: '競爭定位分析',
    description: '與競爭對手進行基準比較，識別市場機會和定位策略',
    icon: Zap,
    gradient: 'from-purple-500 to-purple-600',
    stats: '24 個競爭對手追蹤',
    features: ['競爭基準比較', '頭對頭分析', '機會識別']
  },
  {
    href: '/admin/measure/journey',
    title: '購買旅程分析',
    description: '分析不同人群在購買旅程中的行為模式和轉換機會',
    icon: TrendingUp,
    gradient: 'from-green-500 to-green-600',
    stats: '8 個活躍行銷活動',
    features: ['客戶旅程映射', '人群特定分析', '轉換漏斗優化']
  },
  {
    href: '/admin/measure/sentiment',
    title: '準確性與情感分析',
    description: '監控品牌情感並識別 AI 搜尋回應中的不準確資訊',
    icon: Heart,
    gradient: 'from-red-500 to-red-600',
    stats: '8.2/10 情感評分',
    features: ['情感分析', '幻覺檢測', '品牌安全監控']
  }
];

export default function MeasurePage() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // 模擬數據載入
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'running':
        return <Clock className="h-4 w-4 text-blue-500 animate-spin" />;
      default:
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'running':
        return '執行中';
      default:
        return '待處理';
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'completed':
        return 'default';
      case 'running':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background to-surface-1/30 flex items-center justify-center">
        <div className="text-center">
          <div className="relative">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-border-light border-t-primary mx-auto mb-6"></div>
            <div className="absolute inset-0 rounded-full h-16 w-16 border-4 border-primary/20 mx-auto"></div>
          </div>
          <p className="text-text-secondary text-lg">載入測量數據中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-surface-1/30">
      <div className="container-section py-8 space-y-8">
        {/* 頁面標題 */}
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
          <div>
            <div className="flex items-center gap-3 mb-3">
              <div className="p-3 rounded-2xl bg-gradient-primary shadow-glow">
                <Gauge className="h-8 w-8 text-white" />
              </div>
              <h1 className="text-3xl sm:text-4xl font-bold text-text-primary">
                測量功能
              </h1>
            </div>
            <p className="text-text-secondary text-lg leading-relaxed max-w-2xl">
              全面測量您的品牌在 AI 搜尋引擎中的表現，包括可見度、競爭定位、購買旅程和情感分析
            </p>
          </div>
          <Button variant="gradient" size="lg" className="group">
            <Sparkles className="h-4 w-4 mr-2" />
            開始新分析
            <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200" />
          </Button>
        </div>

        {/* 統計概覽 */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
          <Card className="bg-card/60 backdrop-blur-sm border-border-light hover:shadow-glow transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-text-secondary">追蹤品牌</p>
                  <p className="text-2xl font-bold text-text-primary">{mockStats.totalBrands}</p>
                </div>
                <div className="p-2 rounded-xl bg-gradient-to-br from-blue-500/20 to-blue-600/20">
                  <Target className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-card/60 backdrop-blur-sm border-border-light hover:shadow-glow transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-text-secondary">活躍活動</p>
                  <p className="text-2xl font-bold text-text-primary">{mockStats.activeCampaigns}</p>
                </div>
                <div className="p-2 rounded-xl bg-gradient-to-br from-green-500/20 to-green-600/20">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-card/60 backdrop-blur-sm border-border-light hover:shadow-glow transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-text-secondary">總查詢</p>
                  <p className="text-2xl font-bold text-text-primary">{mockStats.totalQueries.toLocaleString()}</p>
                </div>
                <div className="p-2 rounded-xl bg-gradient-to-br from-purple-500/20 to-purple-600/20">
                  <BarChart3 className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-card/60 backdrop-blur-sm border-border-light hover:shadow-glow transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-text-secondary">平均可見度</p>
                  <p className="text-2xl font-bold text-gradient">{mockStats.avgVisibility}%</p>
                </div>
                <div className="p-2 rounded-xl bg-gradient-to-br from-primary/20 to-accent/20">
                  <Eye className="h-6 w-6 text-primary" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-card/60 backdrop-blur-sm border-border-light hover:shadow-glow transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-text-secondary">競爭對手</p>
                  <p className="text-2xl font-bold text-text-primary">{mockStats.competitorTracked}</p>
                </div>
                <div className="p-2 rounded-xl bg-gradient-to-br from-orange-500/20 to-orange-600/20">
                  <Users className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-card/60 backdrop-blur-sm border-border-light hover:shadow-glow transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-text-secondary">情感評分</p>
                  <p className="text-2xl font-bold text-gradient">{mockStats.sentimentScore}/10</p>
                </div>
                <div className="p-2 rounded-xl bg-gradient-to-br from-red-500/20 to-red-600/20">
                  <Heart className="h-6 w-6 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 功能模組 */}
        <div className="space-y-6">
          <div>
            <h2 className="text-2xl font-bold text-text-primary mb-2">核心測量模組</h2>
            <p className="text-text-secondary">全方位品牌表現測量工具，深度了解市場定位</p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {featureModules.map((module, index) => (
              <Card key={module.title} className="group bg-card/60 backdrop-blur-sm border-border-light hover:shadow-glow transition-all duration-300 hover:scale-[1.02]">
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-4 mb-3">
                    <div className={`p-3 rounded-2xl bg-gradient-to-br ${module.gradient} shadow-md group-hover:shadow-glow transition-shadow duration-300`}>
                      <module.icon className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <CardTitle className="text-text-primary text-xl group-hover:text-gradient transition-colors duration-300">
                        {module.title}
                      </CardTitle>
                      <Badge variant="secondary" className="mt-1 bg-surface-1/60 text-text-secondary border-border-light">
                        {module.stats}
                      </Badge>
                    </div>
                  </div>
                  <CardDescription className="text-text-secondary leading-relaxed">
                    {module.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-sm font-semibold text-text-primary mb-2 flex items-center gap-2">
                        <div className="w-1 h-4 bg-gradient-to-b from-primary to-accent rounded-full" />
                        核心功能
                      </h4>
                      <ul className="space-y-2">
                        {module.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-center text-sm text-text-secondary">
                            <div className="w-1.5 h-1.5 rounded-full bg-primary/60 mr-3" />
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                    <Button asChild variant="outline" className="w-full group">
                      <Link href={module.href}>
                        進入模組
                        <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* 最近分析 */}
        <div className="space-y-6">
          <div>
            <h2 className="text-2xl font-bold text-text-primary mb-2">最近分析</h2>
            <p className="text-text-secondary">查看最新的測量分析結果和進行中的項目</p>
          </div>
          
          <Card className="bg-card/60 backdrop-blur-sm border-border-light">
            <CardHeader>
              <CardTitle className="text-text-primary flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                分析歷史
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockRecentAnalysis.map((analysis) => (
                  <div key={analysis.id} className="flex items-center justify-between p-4 rounded-xl bg-surface-1/40 border border-border-light/60 hover:shadow-md transition-all duration-300">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(analysis.status)}
                        <div>
                          <p className="font-semibold text-text-primary">{analysis.brand}</p>
                          <p className="text-sm text-text-secondary">{analysis.type}</p>
                        </div>
                      </div>
                      <Badge variant={getStatusBadgeVariant(analysis.status)} className="bg-surface-1/60 text-text-secondary border-border-light">
                        {getStatusText(analysis.status)}
                      </Badge>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-text-primary">{analysis.score}%</p>
                      <p className={`text-sm ${analysis.change.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                        {analysis.change}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-6 pt-4 border-t border-border-light">
                <Button variant="outline" className="w-full group">
                  查看所有分析
                  <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
