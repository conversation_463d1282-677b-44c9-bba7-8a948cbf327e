'use client';

import React, { useState } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  Calendar, 
  Search, 
  Filter,
  Download,
  RefreshCw,
  Eye,
  Clock,
  Users,
  Target
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

export default function QueryAnalyticsPage() {
  const [timeRange, setTimeRange] = useState('7d');
  const [queryType, setQueryType] = useState('all');

  const analyticsData = [
    {
      title: '總查詢量',
      value: '12,847',
      change: '+15.3%',
      icon: Search,
      color: 'blue'
    },
    {
      title: '平均回應時間',
      value: '1.2s',
      change: '-0.3s',
      icon: Clock,
      color: 'green'
    },
    {
      title: '成功率',
      value: '94.7%',
      change: '+2.1%',
      icon: Target,
      color: 'purple'
    },
    {
      title: '活躍用戶',
      value: '3,247',
      change: '+8.9%',
      icon: Users,
      color: 'orange'
    }
  ];

  const topQueries = [
    { query: 'AI SEO 最佳實踐', count: 1247, growth: '+25%' },
    { query: '如何優化品牌可見度', count: 982, growth: '+18%' },
    { query: '競爭對手分析工具', count: 756, growth: '+12%' },
    { query: '內容策略規劃', count: 543, growth: '+8%' },
    { query: '技術SEO檢查清單', count: 421, growth: '+5%' }
  ];

  const queryCategories = [
    { category: 'SEO優化', count: 3247, percentage: 28.5 },
    { category: '品牌分析', count: 2891, percentage: 25.3 },
    { category: '競爭情報', count: 2145, percentage: 18.8 },
    { category: '內容策略', count: 1756, percentage: 15.4 },
    { category: '技術問題', count: 1382, percentage: 12.0 }
  ];

  const getStatColor = (color: string) => {
    switch (color) {
      case 'blue': return 'from-blue-500/20 to-blue-600/20 text-blue-600';
      case 'green': return 'from-green-500/20 to-green-600/20 text-green-600';
      case 'purple': return 'from-purple-500/20 to-purple-600/20 text-purple-600';
      case 'orange': return 'from-orange-500/20 to-orange-600/20 text-orange-600';
      default: return 'from-primary/20 to-accent/20 text-primary';
    }
  };

  return (
    <div className="space-y-8">
      {/* 頁面標題和控制項 */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <BarChart3 className="h-8 w-8" />
            查詢分析
          </h1>
          <p className="text-lg text-muted-foreground mt-2">
            深入分析用戶查詢趨勢、模式和性能指標
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-3">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[140px]">
              <Calendar className="w-4 h-4 mr-2" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="24h">過去 24 小時</SelectItem>
              <SelectItem value="7d">過去 7 天</SelectItem>
              <SelectItem value="30d">過去 30 天</SelectItem>
              <SelectItem value="90d">過去 90 天</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={queryType} onValueChange={setQueryType}>
            <SelectTrigger className="w-[140px]">
              <Filter className="w-4 h-4 mr-2" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有查詢</SelectItem>
              <SelectItem value="seo">SEO相關</SelectItem>
              <SelectItem value="brand">品牌分析</SelectItem>
              <SelectItem value="competitor">競爭分析</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="lg">
            <RefreshCw className="w-4 h-4 mr-2" />
            刷新
          </Button>
          
          <Button variant="outline" size="lg">
            <Download className="w-4 h-4 mr-2" />
            導出
          </Button>
        </div>
      </div>

      {/* 核心指標 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {analyticsData.map((metric, index) => (
          <Card key={index} className="bg-card/60 backdrop-blur-sm border-border-light hover:shadow-glow transition-all duration-300 group">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-text-secondary">{metric.title}</p>
                  <p className="text-2xl font-bold text-text-primary mt-1">{metric.value}</p>
                  <p className={`text-sm font-medium mt-2 ${metric.change.startsWith('+') || metric.change.startsWith('-') && metric.title.includes('時間') ? 'text-green-600' : 'text-red-600'}`}>
                    {metric.change}
                  </p>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-br ${getStatColor(metric.color)} group-hover:scale-110 transition-transform duration-300`}>
                  <metric.icon className="h-6 w-6" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 分析圖表區域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 熱門查詢 */}
        <Card className="bg-card/60 backdrop-blur-sm border-border-light">
          <CardHeader>
            <CardTitle className="text-text-primary flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              熱門查詢
            </CardTitle>
            <CardDescription>
              根據查詢頻率和增長率排序
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topQueries.map((query, index) => (
                <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-surface-1/40 border border-border-light/60">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-lg bg-gradient-primary flex items-center justify-center">
                      <span className="text-white text-sm font-bold">{index + 1}</span>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-text-primary">{query.query}</p>
                      <p className="text-xs text-text-secondary">{query.count} 次查詢</p>
                    </div>
                  </div>
                  <Badge className={`${query.growth.startsWith('+') ? 'bg-green-100 text-green-800 border-green-200' : 'bg-red-100 text-red-800 border-red-200'}`}>
                    {query.growth}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 查詢分類 */}
        <Card className="bg-card/60 backdrop-blur-sm border-border-light">
          <CardHeader>
            <CardTitle className="text-text-primary flex items-center gap-2">
              <Eye className="h-5 w-5" />
              查詢分類分布
            </CardTitle>
            <CardDescription>
              按類別分析查詢分布情況
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {queryCategories.map((category, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-text-primary">{category.category}</span>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-bold text-text-primary">{category.count}</span>
                      <span className="text-xs text-text-secondary">({category.percentage}%)</span>
                    </div>
                  </div>
                  <div className="w-full bg-surface-1/40 rounded-full h-2">
                    <div 
                      className="bg-gradient-primary h-2 rounded-full transition-all duration-500"
                      style={{ width: `${category.percentage}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 詳細分析表格 */}
      <Card className="bg-card/60 backdrop-blur-sm border-border-light">
        <CardHeader>
          <CardTitle className="text-text-primary">查詢詳細分析</CardTitle>
          <CardDescription>
            查看所有查詢的詳細統計信息
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-lg border border-border-light/60 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-surface-1/40">
                  <tr>
                    <th className="px-4 py-3 text-left text-sm font-medium text-text-primary">查詢內容</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-text-primary">頻率</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-text-primary">增長率</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-text-primary">成功率</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-text-primary">平均時間</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-border-light/60">
                  {topQueries.map((query, index) => (
                    <tr key={index} className="hover:bg-surface-1/20 transition-colors">
                      <td className="px-4 py-3 text-sm text-text-primary">{query.query}</td>
                      <td className="px-4 py-3 text-sm text-text-secondary">{query.count}</td>
                      <td className="px-4 py-3">
                        <Badge className={`${query.growth.startsWith('+') ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                          {query.growth}
                        </Badge>
                      </td>
                      <td className="px-4 py-3 text-sm text-text-secondary">
                        {Math.floor(Math.random() * 10) + 90}%
                      </td>
                      <td className="px-4 py-3 text-sm text-text-secondary">
                        {(Math.random() * 2 + 0.5).toFixed(1)}s
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 