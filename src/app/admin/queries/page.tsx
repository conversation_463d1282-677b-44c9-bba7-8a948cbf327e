'use client';

import React from 'react';
import Link from 'next/link';
import { 
  Search, 
  TrendingUp, 
  Brain, 
  Hash, 
  ArrowRight,
  Activity,
  BarChart3,
  Users,
  Target
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export default function QueriesPage() {
  const queryModules = [
    {
      title: '查詢分析',
      description: '深入分析用戶查詢趨勢、模式和性能指標',
      icon: BarChart3,
      href: '/admin/queries/analytics',
      gradient: 'from-blue-500 to-blue-600',
      stats: '2,847 查詢',
      metrics: '本週新增 324'
    },
    {
      title: '意圖管理',
      description: '分類和管理不同的查詢意圖，優化理解準確度',
      icon: Brain,
      href: '/admin/queries/intents',
      gradient: 'from-green-500 to-green-600',
      stats: '45 意圖類別',
      metrics: '85% 準確率'
    },
    {
      title: '主題管理',
      description: '組織和管理查詢相關的主題標籤和分組',
      icon: Hash,
      href: '/admin/queries/topics',
      gradient: 'from-purple-500 to-purple-600',
      stats: '128 主題標籤',
      metrics: '覆蓋率 92%'
    }
  ];

  const quickStats = [
    {
      title: '今日查詢',
      value: '1,247',
      change: '+12.5%',
      icon: Search,
      color: 'blue'
    },
    {
      title: '熱門查詢',
      value: '89',
      change: '+5.2%',
      icon: TrendingUp,
      color: 'green'
    },
    {
      title: '新意圖',
      value: '7',
      change: '+2',
      icon: Brain,
      color: 'purple'
    },
    {
      title: '活躍主題',
      value: '45',
      change: '+8.1%',
      icon: Hash,
      color: 'orange'
    }
  ];

  const getStatColor = (color: string) => {
    switch (color) {
      case 'blue': return 'from-blue-500/20 to-blue-600/20 text-blue-600';
      case 'green': return 'from-green-500/20 to-green-600/20 text-green-600';
      case 'purple': return 'from-purple-500/20 to-purple-600/20 text-purple-600';
      case 'orange': return 'from-orange-500/20 to-orange-600/20 text-orange-600';
      default: return 'from-primary/20 to-accent/20 text-primary';
    }
  };

  return (
    <div className="space-y-8">
      {/* 頁面標題 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <Search className="h-8 w-8" />
              查詢管理
            </h1>
            <p className="text-lg text-muted-foreground mt-2">
              分析和管理用戶查詢，優化查詢理解和回應品質
            </p>
          </div>
          <Button variant="default" size="lg" className="group">
            <Target className="w-4 h-4 mr-2" />
            新建分析
          </Button>
        </div>
      </div>

      {/* 快速統計 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {quickStats.map((stat, index) => (
          <Card key={index} className="bg-card/60 backdrop-blur-sm border-border-light hover:shadow-glow transition-all duration-300 group">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-text-secondary">{stat.title}</p>
                  <p className="text-2xl font-bold text-text-primary mt-1">{stat.value}</p>
                  <p className={`text-sm font-medium mt-2 ${stat.change.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                    {stat.change}
                  </p>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-br ${getStatColor(stat.color)} group-hover:scale-110 transition-transform duration-300`}>
                  <stat.icon className="h-6 w-6" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 功能模組 */}
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold text-text-primary mb-2">查詢管理功能</h2>
          <p className="text-text-secondary">選擇下列功能進行查詢分析和管理</p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {queryModules.map((module, index) => (
            <Card key={index} className="group bg-card/60 backdrop-blur-sm border-border-light hover:shadow-glow transition-all duration-300 hover:scale-[1.02]">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className={`p-3 rounded-2xl bg-gradient-to-br ${module.gradient} shadow-md group-hover:shadow-glow transition-shadow duration-300`}>
                      <module.icon className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-text-primary text-xl group-hover:text-gradient transition-colors duration-300">
                        {module.title}
                      </CardTitle>
                      <div className="flex gap-2 mt-1">
                        <Badge variant="secondary" className="bg-surface-1/60 text-text-secondary border-border-light">
                          {module.stats}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {module.metrics}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-text-secondary mb-4 leading-relaxed">
                  {module.description}
                </p>
                <Button asChild variant="outline" className="w-full group">
                  <Link href={module.href}>
                    進入模組
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* 最近活動 */}
      <Card className="bg-card/60 backdrop-blur-sm border-border-light">
        <CardHeader>
          <CardTitle className="text-text-primary flex items-center gap-2">
            <Activity className="h-5 w-5" />
            最近查詢活動
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 rounded-lg bg-surface-1/40 border border-border-light/60">
              <div className="flex items-center gap-3">
                <Search className="h-8 w-8 text-primary" />
                <div>
                  <div className="font-medium text-text-primary">AI SEO 最佳實踐</div>
                  <div className="text-sm text-text-secondary">查詢頻率增長 25%</div>
                </div>
              </div>
              <Badge className="bg-green-100 text-green-800 border-green-200">熱門</Badge>
            </div>
            
            <div className="flex items-center justify-between p-4 rounded-lg bg-surface-1/40 border border-border-light/60">
              <div className="flex items-center gap-3">
                <Brain className="h-8 w-8 text-primary" />
                <div>
                  <div className="font-medium text-text-primary">新意圖分類: 產品比較</div>
                  <div className="text-sm text-text-secondary">系統自動識別新意圖類別</div>
                </div>
              </div>
              <Badge className="bg-blue-100 text-blue-800 border-blue-200">新增</Badge>
            </div>
            
            <div className="flex items-center justify-between p-4 rounded-lg bg-surface-1/40 border border-border-light/60">
              <div className="flex items-center gap-3">
                <Hash className="h-8 w-8 text-primary" />
                <div>
                  <div className="font-medium text-text-primary">主題標籤更新</div>
                  <div className="text-sm text-text-secondary">15 個主題標籤重新分類</div>
                </div>
              </div>
              <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">更新</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 