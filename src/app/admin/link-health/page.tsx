'use client';

import { useState, useEffect } from 'react';
import {
  Refresh<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>gle,
  CheckCircle,
  XCircle,
  TrendingUp,
  TrendingDown,
  Activity,
  Globe,
  Image,
  Shield,
  Search,
  BarChart3,
  Download,
  Minus
} from 'lucide-react';
import {
  saveHealthRecord,
  getHealthHistory,
  calculateHealthTrend,
  getHealthSummary,
  exportHealthHistory,
  type HealthRecord
} from '@/lib/link-health-storage';
import { LinkHealthTrendChart } from '@/components/charts';

interface LinkValidationResult {
  link: string;
  type: string;
  isValid: boolean;
  statusCode?: number;
  errorMessage?: string;
  suggestedFix?: string;
  loadTime?: number;
  fileSize?: number;
}

interface ValidationSummary {
  total: number;
  valid: number;
  invalid: number;
  byType: Record<string, { valid: number; invalid: number }>;
  healthScore: number;
}

interface ValidationData {
  success: boolean;
  results: LinkValidationResult[];
  summary: ValidationSummary;
  timestamp: string;
  message?: string;
}

const typeIcons = {
  internal: Globe,
  external: Globe,
  asset: Image,
  protected: Shield,
  seo: Search,
  image: Image
};

const typeColors = {
  internal: 'text-blue-600 bg-blue-100',
  external: 'text-green-600 bg-green-100',
  asset: 'text-purple-600 bg-purple-100',
  protected: 'text-orange-600 bg-orange-100',
  seo: 'text-red-600 bg-red-100',
  image: 'text-pink-600 bg-pink-100'
};

export default function LinkHealthPage() {
  const [validationData, setValidationData] = useState<ValidationData | null>(null);
  const [loading, setLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [selectedType, setSelectedType] = useState<string>('all');
  const [healthHistory, setHealthHistory] = useState<HealthRecord[]>([]);
  const [showTrends, setShowTrends] = useState(false);

  // 防抖和請求去重
  const [isRequestInProgress, setIsRequestInProgress] = useState(false);
  const [lastRequestKey, setLastRequestKey] = useState<string>('');

  // 載入驗證數據
  const loadValidationData = async (type: string = 'all', quick: boolean = false) => {
    // 防止重複請求
    const requestKey = `${type}-${quick}`;
    if (isRequestInProgress || lastRequestKey === requestKey) {
      console.log('🚫 跳過重複請求:', requestKey);
      return;
    }

    setIsRequestInProgress(true);
    setLoading(true);
    setLastRequestKey(requestKey);

    try {
      const params = new URLSearchParams();
      if (type !== 'all') params.append('type', type);
      if (quick) params.append('quick', 'true');

      console.log('🔍 開始載入連結驗證數據...', { type, quick, requestKey });

      const response = await fetch(`/api/validate-links?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('📊 收到驗證數據:', data);

      if (data.success) {
        setValidationData(data);
        setLastUpdated(new Date());

        // 保存健康度記錄到歷史
        if (data.summary) {
          console.log('💾 保存健康度記錄:', data.summary);
          saveHealthRecord({
            healthScore: data.summary.healthScore,
            totalLinks: data.summary.total,
            validLinks: data.summary.valid,
            invalidLinks: data.summary.invalid,
            byType: data.summary.byType
          });

          // 更新歷史記錄 (延遲以避免 hydration 錯誤)
          setTimeout(() => {
            setHealthHistory(getHealthHistory());
          }, 50);
        }
      } else {
        throw new Error(data.message || '驗證失敗');
      }
    } catch (error) {
      console.error('❌ 載入驗證數據失敗:', error);
      // 顯示用戶友好的錯誤信息
      setValidationData({
        success: false,
        message: error instanceof Error ? error.message : '未知錯誤',
        results: [],
        summary: {
          total: 0,
          valid: 0,
          invalid: 0,
          healthScore: 0,
          byType: {}
        },
        timestamp: new Date().toISOString()
      });
    } finally {
      setLoading(false);
      setIsRequestInProgress(false);
      // 清理請求標記（延遲以避免快速連續請求）
      setTimeout(() => {
        setLastRequestKey('');
      }, 1000);
    }
  };

  // 初始載入（僅在組件掛載時執行一次）
  useEffect(() => {
    loadValidationData('all', true);
    // 延遲載入歷史記錄以避免 hydration 錯誤
    const timer = setTimeout(() => {
      setHealthHistory(getHealthHistory());
    }, 100);

    return () => clearTimeout(timer);
  }, []); // 移除 selectedType 依賴，避免重複請求

  // 當選擇類型改變時，僅更新 UI 狀態，不自動重新請求
  const handleTypeChange = (newType: string) => {
    setSelectedType(newType);
    // 用戶需要手動點擊「重新檢查」來獲取新數據
  };

  // 導出歷史記錄
  const handleExportHistory = () => {
    const data = exportHealthHistory();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `link-health-history-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 健康度評分顏色
  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  // 健康度評分背景色
  const getHealthScoreBg = (score: number) => {
    if (score >= 80) return 'bg-green-100';
    if (score >= 60) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  return (
    <div className="space-y-6">
      {/* 頁面標題和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">連結健康度監控</h1>
          <p className="text-gray-600 mt-1">
            監控網站所有連結的狀態和性能
            {lastUpdated && (
              <span className="ml-2 text-sm">
                最後更新: {lastUpdated.toLocaleString('zh-TW')}
              </span>
            )}
          </p>
        </div>
        
        <div className="flex gap-3">
          <select
            value={selectedType}
            onChange={(e) => handleTypeChange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="all">所有類型</option>
            <option value="seo">SEO 連結</option>
            <option value="internal">內部連結</option>
            <option value="external">外部連結</option>
            <option value="assets">資產檔案</option>
            <option value="protected">受保護路由</option>
          </select>
          
          <div className="flex gap-2">
            <button
              onClick={() => setShowTrends(!showTrends)}
              className="flex items-center px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              <BarChart3 className="h-4 w-4 mr-2" />
              {showTrends ? '隱藏' : '顯示'}趨勢
            </button>

            <button
              onClick={handleExportHistory}
              className="flex items-center px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              <Download className="h-4 w-4 mr-2" />
              導出
            </button>

            <button
              onClick={() => loadValidationData(selectedType, false)}
              disabled={loading || isRequestInProgress}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              {loading ? '檢查中...' : '重新檢查'}
            </button>
          </div>
        </div>
      </div>

      {validationData && validationData.summary && (
        <>
          {/* 健康度評分卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className={`p-6 rounded-lg ${getHealthScoreBg(validationData.summary.healthScore)}`}>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">健康度評分</p>
                  <p className={`text-3xl font-bold ${getHealthScoreColor(validationData.summary.healthScore)}`}>
                    {validationData.summary.healthScore}
                  </p>
                </div>
                <Activity className={`h-8 w-8 ${getHealthScoreColor(validationData.summary.healthScore)}`} />
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">總連結數</p>
                  <p className="text-3xl font-bold text-gray-900">{validationData.summary.total}</p>
                </div>
                <Globe className="h-8 w-8 text-gray-400" />
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">有效連結</p>
                  <p className="text-3xl font-bold text-green-600">{validationData.summary.valid}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-400" />
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">問題連結</p>
                  <p className="text-3xl font-bold text-red-600">{validationData.summary.invalid}</p>
                </div>
                <XCircle className="h-8 w-8 text-red-400" />
              </div>
            </div>
          </div>

          {/* 趨勢分析 */}
          {showTrends && healthHistory.length > 0 && (
            <div className="bg-white rounded-lg shadow">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">健康度趨勢</h2>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                  {(() => {
                    const trend = calculateHealthTrend();
                    const summary = getHealthSummary();

                    return (
                      <>
                        <div className="text-center">
                          <div className="flex items-center justify-center mb-2">
                            {trend.trend === 'up' ? (
                              <TrendingUp className="h-8 w-8 text-green-600" />
                            ) : trend.trend === 'down' ? (
                              <TrendingDown className="h-8 w-8 text-red-600" />
                            ) : (
                              <Minus className="h-8 w-8 text-gray-600" />
                            )}
                          </div>
                          <p className="text-sm text-gray-600">趨勢</p>
                          <p className={`text-lg font-semibold ${
                            trend.trend === 'up' ? 'text-green-600' :
                            trend.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                          }`}>
                            {trend.trend === 'up' ? `+${trend.change}` :
                             trend.trend === 'down' ? `-${trend.change}` : '穩定'}
                          </p>
                          <p className="text-xs text-gray-500">{trend.period}</p>
                        </div>

                        <div className="text-center">
                          <p className="text-sm text-gray-600">平均評分</p>
                          <p className="text-2xl font-bold text-blue-600">{summary.averageScore}</p>
                          <p className="text-xs text-gray-500">基於 {summary.totalChecks} 次檢查</p>
                        </div>

                        <div className="text-center">
                          <p className="text-sm text-gray-600">最佳評分</p>
                          <p className="text-2xl font-bold text-green-600">{summary.bestScore}</p>
                          <p className="text-xs text-gray-500">歷史最高</p>
                        </div>
                      </>
                    );
                  })()}
                </div>

                {/* 趨勢圖表 */}
                <div className="border-t pt-6">
                  <h3 className="text-md font-medium text-gray-900 mb-4">健康度趨勢圖表</h3>
                  <div className="mb-6">
                    <LinkHealthTrendChart
                      data={healthHistory}
                      height={400}
                      showGrid={true}
                      showLegend={true}
                      timeRange="30d"
                    />
                  </div>
                </div>

                {/* 簡單的歷史記錄列表 */}
                <div className="border-t pt-6">
                  <h3 className="text-md font-medium text-gray-900 mb-4">最近檢查記錄</h3>
                  <div className="space-y-3 max-h-64 overflow-y-auto">
                    {healthHistory.slice(0, 10).map((record, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center">
                          <div className={`w-3 h-3 rounded-full mr-3 ${
                            record.healthScore >= 80 ? 'bg-green-500' :
                            record.healthScore >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}></div>
                          <div>
                            <p className="text-sm font-medium text-gray-900">
                              評分: {record.healthScore}
                            </p>
                            <p className="text-xs text-gray-500">
                              {new Date(record.timestamp).toLocaleString('zh-TW')}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-gray-600">
                            {record.validLinks}/{record.totalLinks} 有效
                          </p>
                          <p className="text-xs text-gray-500">
                            {Math.round((record.validLinks / record.totalLinks) * 100)}%
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 按類型統計 */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">按類型統計</h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(validationData.summary.byType || {}).map(([type, stats]) => {
                  const Icon = typeIcons[type as keyof typeof typeIcons] || Globe;
                  const colorClass = typeColors[type as keyof typeof typeColors] || 'text-gray-600 bg-gray-100';
                  const total = stats.valid + stats.invalid;
                  const percentage = total > 0 ? Math.round((stats.valid / total) * 100) : 0;
                  
                  return (
                    <div key={type} className="flex items-center p-4 border rounded-lg">
                      <div className={`p-2 rounded-lg ${colorClass} mr-4`}>
                        <Icon className="h-5 w-5" />
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between items-center mb-1">
                          <span className="font-medium capitalize">{type}</span>
                          <span className="text-sm text-gray-500">{percentage}%</span>
                        </div>
                        <div className="flex text-sm text-gray-600">
                          <span className="text-green-600">{stats.valid} 有效</span>
                          <span className="mx-2">•</span>
                          <span className="text-red-600">{stats.invalid} 問題</span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* 詳細連結列表 */}
          {validationData.results && validationData.results.length > 0 && (
            <div className="bg-white rounded-lg shadow">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">連結詳情</h2>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        連結
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        類型
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        狀態
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        載入時間
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        建議
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {validationData.results.map((result, index) => {
                      const Icon = typeIcons[result.type as keyof typeof typeIcons] || Globe;
                      const colorClass = typeColors[result.type as keyof typeof typeColors] || 'text-gray-600 bg-gray-100';

                      return (
                        <tr key={index} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className={`p-1 rounded ${colorClass} mr-3`}>
                                <Icon className="h-4 w-4" />
                              </div>
                              <div>
                                <div className="text-sm font-medium text-gray-900 max-w-xs truncate">
                                  {result.link}
                                </div>
                                {result.errorMessage && (
                                  <div className="text-sm text-red-600 max-w-xs truncate">
                                    {result.errorMessage}
                                  </div>
                                )}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="capitalize text-sm text-gray-900">
                              {result.type}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              result.isValid
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {result.isValid ? (
                                <>
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                  有效
                                </>
                              ) : (
                                <>
                                  <XCircle className="h-3 w-3 mr-1" />
                                  問題
                                </>
                              )}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {result.loadTime ? (
                              <span className={`${
                                result.loadTime <= 1000 ? 'text-green-600' :
                                result.loadTime <= 3000 ? 'text-yellow-600' : 'text-red-600'
                              }`}>
                                {result.loadTime}ms
                              </span>
                            ) : (
                              <span className="text-gray-400">-</span>
                            )}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-500 max-w-xs">
                            <div className="truncate" title={result.suggestedFix}>
                              {result.suggestedFix || '-'}
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* 快速操作 */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">快速操作</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button
                onClick={() => {
                  setSelectedType('seo');
                  loadValidationData('seo', false);
                }}
                disabled={loading || isRequestInProgress}
                className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors disabled:opacity-50"
              >
                <Search className="h-6 w-6 text-gray-400 mr-2" />
                <span className="text-gray-600">檢查 SEO 連結</span>
              </button>

              <button
                onClick={() => {
                  setSelectedType('external');
                  loadValidationData('external', false);
                }}
                disabled={loading || isRequestInProgress}
                className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors disabled:opacity-50"
              >
                <Globe className="h-6 w-6 text-gray-400 mr-2" />
                <span className="text-gray-600">檢查外部連結</span>
              </button>

              <button
                onClick={() => {
                  setSelectedType('all');
                  loadValidationData('all', false);
                }}
                disabled={loading || isRequestInProgress}
                className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors disabled:opacity-50"
              >
                <Activity className="h-6 w-6 text-gray-400 mr-2" />
                <span className="text-gray-600">完整檢查</span>
              </button>
            </div>
          </div>
        </>
      )}

      {/* 載入狀態 */}
      {loading && !validationData && (
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-600 mr-3" />
          <span className="text-lg text-gray-600">正在檢查連結...</span>
        </div>
      )}

      {/* 空狀態 */}
      {!loading && !validationData && (
        <div className="text-center py-12">
          <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">尚未進行連結檢查</h3>
          <p className="text-gray-600 mb-4">點擊上方的「重新檢查」按鈕開始檢查連結健康度</p>
          <button
            onClick={() => loadValidationData('all', true)}
            disabled={loading || isRequestInProgress}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            開始檢查
          </button>
        </div>
      )}
    </div>
  );
}
