'use client';

import React, { useState } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';

const navigationItems = [
  {
    name: '儀表板',
    href: '/admin',
    description: '總覽儀表板',
    icon: '📊'
  },
  {
    name: '查詢管理',
    href: '/admin/queries',
    description: '查詢分析與管理',
    icon: '🔍'
  },
  {
    name: '產品研究',
    href: '/admin/product-research',
    description: 'AI 搜尋品牌分析',
    icon: '🔬'
  },
  {
    name: '測量功能',
    href: '/admin/measure',
    description: '品牌可見度測量',
    icon: '📏'
  },
  {
    name: 'AI 摘要監測',
    href: '/admin/google-ai-monitor',
    description: 'Google AI 摘要監測器',
    icon: '🔍'
  },
  {
    name: '優化功能',
    href: '/admin/optimize',
    description: 'AI 驅動優化建議',
    icon: '⚡'
  },
  {
    name: '系統設置',
    href: '/admin/settings',
    description: '系統配置與模型管理',
    icon: '⚙️'
  }
];

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const router = useRouter();
  const { user, logout, isAuthenticated } = useAuth();
  const [showUserMenu, setShowUserMenu] = useState(false);

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/auth/login');
    } catch (error) {
      console.error('登出失敗:', error);
    }
  };

  const handleLogin = () => {
    router.push('/auth/login');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 頂部導航欄 */}
      <nav className="bg-white border-b border-gray-200 sticky top-0 z-50 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              {/* Logo */}
              <Link href="/admin" className="flex items-center">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold">A</span>
                  </div>
                  <span className="text-xl font-bold text-gray-900">
                    AI SEO 管理台
                  </span>
                </div>
              </Link>
            </div>

            {/* 桌面端導航 */}
            <div className="hidden lg:flex items-center space-x-1">
              {navigationItems.slice(0, 6).map((item) => {
                const isActive = pathname === item.href;
                
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                      isActive
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                    }`}
                  >
                    <span className="mr-1">{item.icon}</span>
                    {item.name}
                  </Link>
                );
              })}
            </div>

            {/* 用戶菜單 */}
            <div className="flex items-center space-x-4">
              {/* 系統設置快捷按鈕 */}
              <Link
                href="/admin/settings"
                className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  pathname === '/admin/settings'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                <span className="mr-1">⚙️</span>
                <span className="hidden sm:inline">設置</span>
              </Link>

              {/* 用戶信息和登入/登出 */}
              <div className="relative">
                <button
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  className="flex items-center space-x-2 text-sm text-gray-600 hover:text-gray-900"
                >
                  <span>{user?.full_name || '訪客'}</span>
                  <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                    <span className="text-gray-600 text-sm">👤</span>
                  </div>
                </button>

                {/* 下拉菜單 */}
                {showUserMenu && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                    {isAuthenticated ? (
                      <>
                        <div className="px-4 py-2 text-sm text-gray-700 border-b">
                          <div className="font-medium">{user?.full_name}</div>
                          <div className="text-gray-500">{user?.email}</div>
                        </div>
                        <Link
                          href="/admin/profile"
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          onClick={() => setShowUserMenu(false)}
                        >
                          👤 個人資料
                        </Link>
                        <Link
                          href="/admin/settings"
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          onClick={() => setShowUserMenu(false)}
                        >
                          ⚙️ 系統設置
                        </Link>
                        <button
                          onClick={() => {
                            setShowUserMenu(false);
                            handleLogout();
                          }}
                          className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                        >
                          🚪 登出
                        </button>
                      </>
                    ) : (
                      <button
                        onClick={() => {
                          setShowUserMenu(false);
                          handleLogin();
                        }}
                        className="block w-full text-left px-4 py-2 text-sm text-blue-600 hover:bg-gray-100"
                      >
                        🔑 登入
                      </button>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* 移動端導航 */}
      <div className="lg:hidden bg-white border-b border-gray-200">
        <div className="px-4 py-2">
          <div className="grid grid-cols-4 gap-2">
            {navigationItems.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`flex flex-col items-center p-2 rounded-lg text-xs ${
                    isActive
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <span className="text-lg mb-1">{item.icon}</span>
                  <span className="text-center">{item.name}</span>
                </Link>
              );
            })}
          </div>
        </div>
      </div>

      {/* 主要內容區域 */}
      <main className="min-h-screen">
        {children}
      </main>

      {/* 點擊外部關閉用戶菜單 */}
      {showUserMenu && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowUserMenu(false)}
        />
      )}
    </div>
  );
}
