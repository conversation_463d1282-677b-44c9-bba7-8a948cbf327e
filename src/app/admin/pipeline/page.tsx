'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { 
  Play, 
  Pause, 
  RotateCcw, 
  Activity, 
  Database, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Clock,
  TrendingUp,
  Settings,
  RefreshCw
} from 'lucide-react';
import { toast } from 'sonner';

interface PipelineStatus {
  is_running: boolean;
  current_stage: string;
  progress: number;
  last_update: string;
  total_queries_processed: number;
  success_rate: number;
  avg_processing_time: number;
}

interface CollectorStatus {
  name: string;
  status: 'active' | 'idle' | 'error';
  last_update: string;
  total_collected: number;
  error_count: number;
}

interface ProcessorStatus {
  name: string;
  status: 'active' | 'idle' | 'error';
  last_update: string;
  total_processed: number;
  queue_size: number;
}

interface PerformanceMetrics {
  throughput: number;
  latency: number;
  error_rate: number;
  memory_usage: number;
  cpu_usage: number;
}

export default function DataPipelinePage() {
  const [pipelineStatus, setPipelineStatus] = useState<PipelineStatus | null>(null);
  const [collectors, setCollectors] = useState<CollectorStatus[]>([]);
  const [processors, setProcessors] = useState<ProcessorStatus[]>([]);
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const fetchPipelineData = async () => {
    try {
      setRefreshing(true);
      
      const [statusRes, collectorsRes, processorsRes, metricsRes] = await Promise.all([
        fetch('/api/v1/pipeline/status'),
        fetch('/api/v1/pipeline/collectors'),
        fetch('/api/v1/pipeline/processors'),
        fetch('/api/v1/pipeline/metrics')
      ]);

      if (statusRes.ok) {
        const statusData = await statusRes.json();
        setPipelineStatus(statusData.data);
      }

      if (collectorsRes.ok) {
        const collectorsData = await collectorsRes.json();
        setCollectors(collectorsData.data);
      }

      if (processorsRes.ok) {
        const processorsData = await processorsRes.json();
        setProcessors(processorsData.data);
      }

      if (metricsRes.ok) {
        const metricsData = await metricsRes.json();
        setMetrics(metricsData.data);
      }
    } catch (error) {
      console.error('Failed to fetch pipeline data:', error);
      toast.error('無法獲取管道數據');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const controlPipeline = async (action: 'start' | 'stop' | 'restart') => {
    try {
      const response = await fetch(`/api/v1/pipeline/${action}`, {
        method: 'POST'
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(result.message);
        await fetchPipelineData();
      } else {
        throw new Error('Pipeline control failed');
      }
    } catch (error) {
      console.error(`Failed to ${action} pipeline:`, error);
      toast.error(`無法${action === 'start' ? '啟動' : action === 'stop' ? '停止' : '重啟'}管道`);
    }
  };

  const runTestCollection = async () => {
    try {
      const response = await fetch('/api/v1/pipeline/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query: "SEO test", samples: 5 })
      });

      if (response.ok) {
        const result = await response.json();
        toast.success('測試收集完成');
        await fetchPipelineData();
      } else {
        throw new Error('Test collection failed');
      }
    } catch (error) {
      console.error('Failed to run test collection:', error);
      toast.error('測試收集失敗');
    }
  };

  useEffect(() => {
    fetchPipelineData();
    
    // 設置自動刷新
    const interval = setInterval(fetchPipelineData, 30000); // 每30秒刷新
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'idle': return 'bg-yellow-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="h-4 w-4" />;
      case 'idle': return <Clock className="h-4 w-4" />;
      case 'error': return <XCircle className="h-4 w-4" />;
      default: return <AlertTriangle className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>載入管道數據中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">數據收集管道管理</h1>
          <p className="text-muted-foreground">
            監控和管理 AI SEO 數據收集管道系統
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={fetchPipelineData}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            刷新
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={runTestCollection}
          >
            <Activity className="h-4 w-4 mr-2" />
            測試收集
          </Button>
        </div>
      </div>

      {/* 管道狀態控制 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            管道狀態控制
          </CardTitle>
          <CardDescription>
            管理數據收集管道的運行狀態
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {pipelineStatus && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {pipelineStatus.is_running ? (
                    <Badge variant="default" className="bg-green-500">
                      <CheckCircle className="h-4 w-4 mr-1" />
                      運行中
                    </Badge>
                  ) : (
                    <Badge variant="secondary">
                      <Pause className="h-4 w-4 mr-1" />
                      已停止
                    </Badge>
                  )}
                </div>
                <p className="text-sm text-muted-foreground">狀態</p>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold">{pipelineStatus.current_stage}</div>
                <p className="text-sm text-muted-foreground">當前階段</p>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold">{pipelineStatus.total_queries_processed}</div>
                <p className="text-sm text-muted-foreground">處理查詢數</p>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold">{(pipelineStatus.success_rate * 100).toFixed(1)}%</div>
                <p className="text-sm text-muted-foreground">成功率</p>
              </div>
            </div>
          )}

          {pipelineStatus && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>處理進度</span>
                <span>{pipelineStatus.progress}%</span>
              </div>
              <Progress value={pipelineStatus.progress} className="h-2" />
            </div>
          )}

          <Separator />

          <div className="flex gap-2">
            <Button
              onClick={() => controlPipeline('start')}
              disabled={pipelineStatus?.is_running}
              className="flex-1"
            >
              <Play className="h-4 w-4 mr-2" />
              啟動管道
            </Button>
            
            <Button
              variant="outline"
              onClick={() => controlPipeline('stop')}
              disabled={!pipelineStatus?.is_running}
              className="flex-1"
            >
              <Pause className="h-4 w-4 mr-2" />
              停止管道
            </Button>
            
            <Button
              variant="outline"
              onClick={() => controlPipeline('restart')}
              className="flex-1"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              重啟管道
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 收集器狀態 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              數據收集器
            </CardTitle>
            <CardDescription>
              各個數據收集器的運行狀態
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {collectors.map((collector) => (
                <div key={collector.name} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className={`w-3 h-3 rounded-full ${getStatusColor(collector.status)}`} />
                    <div>
                      <p className="font-medium">{collector.name}</p>
                      <p className="text-sm text-muted-foreground">
                        收集: {collector.total_collected} | 錯誤: {collector.error_count}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    {getStatusIcon(collector.status)}
                    <p className="text-xs text-muted-foreground">
                      {new Date(collector.last_update).toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 處理器狀態 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              數據處理器
            </CardTitle>
            <CardDescription>
              各個數據處理器的運行狀態
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {processors.map((processor) => (
                <div key={processor.name} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className={`w-3 h-3 rounded-full ${getStatusColor(processor.status)}`} />
                    <div>
                      <p className="font-medium">{processor.name}</p>
                      <p className="text-sm text-muted-foreground">
                        處理: {processor.total_processed} | 隊列: {processor.queue_size}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    {getStatusIcon(processor.status)}
                    <p className="text-xs text-muted-foreground">
                      {new Date(processor.last_update).toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 性能指標 */}
      {metrics && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              性能指標
            </CardTitle>
            <CardDescription>
              系統性能和資源使用情況
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{metrics.throughput}</div>
                <p className="text-sm text-muted-foreground">吞吐量/分鐘</p>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold">{metrics.latency}ms</div>
                <p className="text-sm text-muted-foreground">平均延遲</p>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold">{(metrics.error_rate * 100).toFixed(1)}%</div>
                <p className="text-sm text-muted-foreground">錯誤率</p>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold">{metrics.memory_usage}%</div>
                <p className="text-sm text-muted-foreground">內存使用</p>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold">{metrics.cpu_usage}%</div>
                <p className="text-sm text-muted-foreground">CPU 使用</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 警告信息 */}
      {metrics && metrics.error_rate > 0.1 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            錯誤率過高 ({(metrics.error_rate * 100).toFixed(1)}%)，建議檢查管道配置和數據源連接。
          </AlertDescription>
        </Alert>
      )}

      {metrics && metrics.memory_usage > 80 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            內存使用率過高 ({metrics.memory_usage}%)，建議檢查內存洩漏或調整配置。
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
} 