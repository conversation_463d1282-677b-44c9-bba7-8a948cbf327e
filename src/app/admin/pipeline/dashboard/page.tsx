'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar 
} from 'recharts';
import { 
  Activity, 
  TrendingUp, 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  Download,
  Calendar,
  Filter
} from 'lucide-react';
import { toast } from 'sonner';

interface TimeSeriesData {
  timestamp: string;
  throughput: number;
  latency: number;
  error_rate: number;
  memory_usage: number;
  cpu_usage: number;
}

interface AlertRule {
  id: string;
  name: string;
  condition: string;
  threshold: number;
  enabled: boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

interface DashboardOverview {
  total_throughput: number;
  avg_latency: number;
  total_errors: number;
  active_collectors: number;
  active_processors: number;
  system_health_score: number;
}

export default function PipelineDashboardPage() {
  const [timeSeriesData, setTimeSeriesData] = useState<TimeSeriesData[]>([]);
  const [alerts, setAlerts] = useState<AlertRule[]>([]);
  const [overview, setOverview] = useState<DashboardOverview | null>(null);
  const [timeRange, setTimeRange] = useState('1h');
  const [refreshInterval, setRefreshInterval] = useState(30);
  const [loading, setLoading] = useState(true);

  const fetchDashboardData = async () => {
    try {
      const [overviewRes, timeSeriesRes, alertsRes] = await Promise.all([
        fetch('/api/v1/pipeline/dashboard/overview'),
        fetch(`/api/v1/pipeline/dashboard/timeseries?time_range=${timeRange}`),
        fetch('/api/v1/pipeline/dashboard/alerts')
      ]);

      if (overviewRes.ok) {
        const overviewData = await overviewRes.json();
        setOverview(overviewData.data);
      }

      if (timeSeriesRes.ok) {
        const timeSeriesData = await timeSeriesRes.json();
        setTimeSeriesData(timeSeriesData.data);
      }

      if (alertsRes.ok) {
        const alertsData = await alertsRes.json();
        setAlerts(alertsData.data);
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      toast.error('無法獲取儀表板數據');
    } finally {
      setLoading(false);
    }
  };

  const exportMetrics = async (format: 'json' | 'csv' | 'prometheus') => {
    try {
      const response = await fetch(`/api/v1/pipeline/dashboard/export?format=${format}&time_range=${timeRange}`);
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `pipeline_metrics_${new Date().toISOString().split('T')[0]}.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        toast.success('指標導出完成');
      }
    } catch (error) {
      console.error('Failed to export metrics:', error);
      toast.error('指標導出失敗');
    }
  };

  const toggleAlert = async (alertId: string, enabled: boolean) => {
    try {
      const response = await fetch(`/api/v1/pipeline/dashboard/alerts/${alertId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ enabled })
      });

      if (response.ok) {
        setAlerts(prev => prev.map(alert => 
          alert.id === alertId ? { ...alert, enabled } : alert
        ));
        toast.success(`告警規則已${enabled ? '啟用' : '停用'}`);
      }
    } catch (error) {
      console.error('Failed to toggle alert:', error);
      toast.error('告警規則更新失敗');
    }
  };

  useEffect(() => {
    fetchDashboardData();
    
    const interval = setInterval(fetchDashboardData, refreshInterval * 1000);
    return () => clearInterval(interval);
  }, [timeRange, refreshInterval]);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-500';
    if (score >= 70) return 'text-yellow-500';
    return 'text-red-500';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Activity className="h-8 w-8 animate-pulse mx-auto mb-4" />
          <p>載入儀表板數據中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">管道監控儀表板</h1>
          <p className="text-muted-foreground">
            實時監控數據收集管道的性能指標和系統狀態
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-24">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="15m">15分鐘</SelectItem>
              <SelectItem value="1h">1小時</SelectItem>
              <SelectItem value="6h">6小時</SelectItem>
              <SelectItem value="24h">24小時</SelectItem>
              <SelectItem value="7d">7天</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={refreshInterval.toString()} onValueChange={(v) => setRefreshInterval(parseInt(v))}>
            <SelectTrigger className="w-28">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10秒</SelectItem>
              <SelectItem value="30">30秒</SelectItem>
              <SelectItem value="60">1分鐘</SelectItem>
              <SelectItem value="300">5分鐘</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" onClick={() => exportMetrics('json')}>
            <Download className="h-4 w-4 mr-2" />
            導出數據
          </Button>
        </div>
      </div>

      {/* 概覽指標 */}
      {overview && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">總吞吐量</p>
                  <p className="text-2xl font-bold">{overview.total_throughput}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">平均延遲</p>
                  <p className="text-2xl font-bold">{overview.avg_latency}ms</p>
                </div>
                <Clock className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">總錯誤數</p>
                  <p className="text-2xl font-bold">{overview.total_errors}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">活躍收集器</p>
                  <p className="text-2xl font-bold">{overview.active_collectors}</p>
                </div>
                <Activity className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">活躍處理器</p>
                  <p className="text-2xl font-bold">{overview.active_processors}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-indigo-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">系統健康分數</p>
                  <p className={`text-2xl font-bold ${getHealthScoreColor(overview.system_health_score)}`}>
                    {overview.system_health_score}%
                  </p>
                </div>
                <div className={`h-8 w-8 rounded-full ${getHealthScoreColor(overview.system_health_score)}`}>
                  <CheckCircle className="h-8 w-8" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="performance" className="space-y-4">
        <TabsList>
          <TabsTrigger value="performance">性能監控</TabsTrigger>
          <TabsTrigger value="resources">資源使用</TabsTrigger>
          <TabsTrigger value="alerts">告警管理</TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 吞吐量趨勢 */}
            <Card>
              <CardHeader>
                <CardTitle>吞吐量趨勢</CardTitle>
                <CardDescription>每分鐘處理的查詢數量</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={timeSeriesData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="timestamp" 
                      tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                    />
                    <YAxis />
                    <Tooltip 
                      labelFormatter={(value) => new Date(value).toLocaleString()}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="throughput" 
                      stroke="#8884d8" 
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* 延遲趨勢 */}
            <Card>
              <CardHeader>
                <CardTitle>延遲趨勢</CardTitle>
                <CardDescription>平均響應時間（毫秒）</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={timeSeriesData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="timestamp" 
                      tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                    />
                    <YAxis />
                    <Tooltip 
                      labelFormatter={(value) => new Date(value).toLocaleString()}
                    />
                    <Area 
                      type="monotone" 
                      dataKey="latency" 
                      stroke="#82ca9d" 
                      fill="#82ca9d"
                      fillOpacity={0.3}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* 錯誤率趨勢 */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>錯誤率趨勢</CardTitle>
                <CardDescription>系統錯誤率百分比</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={timeSeriesData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="timestamp" 
                      tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                    />
                    <YAxis />
                    <Tooltip 
                      labelFormatter={(value) => new Date(value).toLocaleString()}
                      formatter={(value) => [`${(value as number * 100).toFixed(2)}%`, '錯誤率']}
                    />
                    <Bar 
                      dataKey="error_rate" 
                      fill="#ff7300"
                    />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="resources" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 內存使用 */}
            <Card>
              <CardHeader>
                <CardTitle>內存使用率</CardTitle>
                <CardDescription>系統內存使用情況</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={timeSeriesData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="timestamp" 
                      tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                    />
                    <YAxis />
                    <Tooltip 
                      labelFormatter={(value) => new Date(value).toLocaleString()}
                      formatter={(value) => [`${value}%`, '內存使用率']}
                    />
                    <Area 
                      type="monotone" 
                      dataKey="memory_usage" 
                      stroke="#ffc658" 
                      fill="#ffc658"
                      fillOpacity={0.3}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* CPU 使用 */}
            <Card>
              <CardHeader>
                <CardTitle>CPU 使用率</CardTitle>
                <CardDescription>系統 CPU 使用情況</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={timeSeriesData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="timestamp" 
                      tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                    />
                    <YAxis />
                    <Tooltip 
                      labelFormatter={(value) => new Date(value).toLocaleString()}
                      formatter={(value) => [`${value}%`, 'CPU 使用率']}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="cpu_usage" 
                      stroke="#ff7300" 
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>告警規則管理</CardTitle>
              <CardDescription>
                配置和管理系統監控告警規則
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {alerts.map((alert) => (
                  <div key={alert.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded-full ${getSeverityColor(alert.severity)}`} />
                      <div>
                        <p className="font-medium">{alert.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {alert.condition} (閾值: {alert.threshold})
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={alert.severity === 'critical' ? 'destructive' : 'secondary'}>
                        {alert.severity}
                      </Badge>
                      <Button
                        variant={alert.enabled ? "default" : "outline"}
                        size="sm"
                        onClick={() => toggleAlert(alert.id, !alert.enabled)}
                      >
                        {alert.enabled ? '已啟用' : '已停用'}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 