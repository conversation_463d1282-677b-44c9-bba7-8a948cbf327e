/**
 * 產品分析主儀表板頁面
 * 顯示品牌可見度總覽和關鍵指標
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  TrendingUp,
  TrendingDown,
  Eye,
  MessageSquare,
  Link as LinkIcon,
  BarChart3,
  RefreshCw,
  Download,
  Plus,
  Search,
  Target,
  Sparkles,
  ArrowRight
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useProductAnalysis } from '@/services/product-analysis';
import AnalysisProgressDialog from '@/components/dialogs/AnalysisProgressDialog';
import SEOAnalysisConfigDialog from '@/components/dialogs/SEOAnalysisConfigDialog';
import SEOAnalysisExecutor from '@/components/analysis/SEOAnalysisExecutor';
import SEOAnalysisResults from '@/components/analysis/SEOAnalysisResults';
import type { SEOAnalysisRequest, SEOAnalysisResult } from '@/services/openai-seo-analysis';
import { toast } from 'sonner';

// 模擬數據 - 實際使用時會從 API 獲取
const mockDashboardData = {
  overview: {
    totalBrands: 12,
    totalAnalyses: 248,
    averageVisibility: 78.5,
    activeReports: 6,
    lastUpdated: new Date().toISOString(),
  },
  visibilityTrends: [
    { date: '2024-01', visibility: 72.3, change: 2.1 },
    { date: '2024-02', visibility: 75.8, change: 3.5 },
    { date: '2024-03', visibility: 78.5, change: 2.7 },
    { date: '2024-04', visibility: 76.2, change: -2.3 },
    { date: '2024-05', visibility: 81.4, change: 5.2 },
    { date: '2024-06', visibility: 78.5, change: -2.9 },
  ],
  aiEngineDistribution: [
    { engine: 'ChatGPT', visibility: 85.2, share: 28.5 },
    { engine: 'Gemini', visibility: 79.8, share: 24.3 },
    { engine: 'Perplexity', visibility: 76.4, share: 22.1 },
    { engine: 'Copilot', visibility: 72.1, share: 15.8 },
    { engine: 'Claude', visibility: 68.9, share: 9.3 },
  ],
  topBrands: [
    { name: 'TechCorp', visibility: 92.3, change: 5.2, industry: '科技' },
    { name: 'HealthPlus', visibility: 88.7, change: -1.8, industry: '醫療' },
    { name: 'EcoGreen', visibility: 85.1, change: 3.4, industry: '環保' },
    { name: 'FinanceMax', visibility: 82.9, change: 2.1, industry: '金融' },
    { name: 'EduSmart', visibility: 79.6, change: -0.5, industry: '教育' },
  ],
  recentAnalyses: [
    { id: '1', brand: 'TechCorp', type: '回應分析', status: '完成', date: '2024-06-24' },
    { id: '2', brand: 'HealthPlus', type: '引用分析', status: '進行中', date: '2024-06-24' },
    { id: '3', brand: 'EcoGreen', type: '主題分析', status: '完成', date: '2024-06-23' },
    { id: '4', brand: 'FinanceMax', type: '內容分析', status: '排隊中', date: '2024-06-23' },
  ],
};

export default function ProductAnalysisDashboard() {
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState(mockDashboardData);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [showProgressDialog, setShowProgressDialog] = useState(false);
  const [currentAnalysisId, setCurrentAnalysisId] = useState<string | null>(null);

  // 新的 OpenAI 分析狀態
  const [showConfigDialog, setShowConfigDialog] = useState(false);
  const [showAnalysisExecutor, setShowAnalysisExecutor] = useState(false);
  const [showAnalysisResults, setShowAnalysisResults] = useState(false);
  const [currentAnalysisRequest, setCurrentAnalysisRequest] = useState<SEOAnalysisRequest | null>(null);
  const [currentAnalysisResult, setCurrentAnalysisResult] = useState<SEOAnalysisResult | null>(null);

  // 使用產品分析服務
  const { startQuickAnalysis, refreshDashboard, exportDashboardReport } = useProductAnalysis();

  // 刷新數據
  const handleRefresh = async () => {
    setIsLoading(true);
    try {
      const newData = await refreshDashboard();
      if (newData) {
        // 確保數據結構完整，使用默認值填充缺失的屬性
        const safeData = {
          overview: newData.overview || mockDashboardData.overview,
          visibilityTrends: newData.visibilityTrends || mockDashboardData.visibilityTrends,
          aiEngineDistribution: newData.aiEngineDistribution || mockDashboardData.aiEngineDistribution,
          topBrands: newData.topBrands || mockDashboardData.topBrands,
          recentAnalyses: newData.recentAnalyses || mockDashboardData.recentAnalyses,
        };
        setData(safeData);
        toast.success('數據已成功刷新！');
      }
    } catch (error) {
      console.error('刷新數據失敗:', error);
      // 發生錯誤時保持使用模擬數據
      toast.error('刷新數據失敗，顯示模擬數據');
    } finally {
      setIsLoading(false);
    }
  };

  // 開始分析（舊版本 - 模擬分析）
  const handleStartAnalysis = async () => {
    setIsAnalyzing(true);
    try {
      const result = await startQuickAnalysis({
        brandName: '示例品牌',
        analysisType: 'full',
        includeCompetitors: true,
        onProgress: (status) => {
          console.log('分析進度:', status);
        }
      });

      if (result) {
        setCurrentAnalysisId(result.analysisId);
        setShowProgressDialog(true);
        toast.success(`分析已開始！預估時間：${result.estimatedTime}`);
      }
    } catch (error) {
      console.error('啟動分析失敗:', error);
      toast.error('啟動分析失敗，請稍後重試');
    } finally {
      setIsAnalyzing(false);
    }
  };

  // 開始真實 OpenAI 分析
  const handleStartOpenAIAnalysis = () => {
    setShowConfigDialog(true);
  };

  // 處理分析配置完成
  const handleAnalysisConfigComplete = (request: SEOAnalysisRequest) => {
    setCurrentAnalysisRequest(request);
    setShowAnalysisExecutor(true);
    setShowConfigDialog(false);
    toast.success('開始執行 OpenAI SEO 分析...');
  };

  // 處理分析完成
  const handleAnalysisComplete = (result: SEOAnalysisResult) => {
    setCurrentAnalysisResult(result);
    setShowAnalysisExecutor(false);
    setShowAnalysisResults(true);
    toast.success('SEO 分析完成！');
  };

  // 處理分析取消
  const handleAnalysisCancel = () => {
    setShowAnalysisExecutor(false);
    setCurrentAnalysisRequest(null);
    toast.info('分析已取消');
  };

  // 開始新分析
  const handleNewAnalysis = () => {
    setShowAnalysisResults(false);
    setCurrentAnalysisResult(null);
    setShowConfigDialog(true);
  };

  // 導出報告
  const handleExportReport = async () => {
    try {
      await exportDashboardReport('pdf');
      toast.success('報告導出成功！');
    } catch (error) {
      console.error('導出報告失敗:', error);
      toast.error('導出報告失敗，請稍後重試');
    }
  };

  // 模擬分析完成處理
  const handleMockAnalysisComplete = (results: any) => {
    console.log('模擬分析完成:', results);
    setShowProgressDialog(false);
    setCurrentAnalysisId(null);
    setIsAnalyzing(false);
    toast.success('模擬分析已完成！正在刷新數據...');

    // 刷新儀表板數據
    setTimeout(() => {
      handleRefresh();
    }, 1000);
  };

  // 分析錯誤處理
  const handleAnalysisError = (error: string) => {
    console.error('分析錯誤:', error);
    setShowProgressDialog(false);
    setCurrentAnalysisId(null);
    setIsAnalyzing(false);
    toast.error(`分析失敗：${error}`);
  };

  // 格式化數字
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('zh-TW').format(num);
  };

  // 格式化百分比
  const formatPercentage = (num: number) => {
    return `${num.toFixed(1)}%`;
  };

  // 獲取變化趨勢圖標和顏色
  const getTrendIcon = (change: number) => {
    if (change > 0) {
      return <TrendingUp className="w-4 h-4 text-green-500" />;
    } else if (change < 0) {
      return <TrendingDown className="w-4 h-4 text-red-500" />;
    }
    return null;
  };

  const getTrendColor = (change: number) => {
    if (change > 0) return 'text-green-600';
    if (change < 0) return 'text-red-600';
    return 'text-gray-500';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case '完成':
        return 'bg-green-100 text-green-800 border-green-200';
      case '進行中':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case '排隊中':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8">
        {/* 頁面標題 */}
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
          <div>
            <div className="flex items-center gap-3 mb-3">
              <div className="p-3 rounded-xl bg-blue-600 shadow-lg">
                <BarChart3 className="h-8 w-8 text-white" />
              </div>
              <h1 className="text-3xl sm:text-4xl font-bold text-gray-900">
                產品分析
              </h1>
            </div>
            <p className="text-gray-600 text-lg max-w-2xl">
              深入分析品牌在 AI 搜尋引擎中的表現，包括回應、引用、主題和內容分析
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3">
            <Button 
              onClick={handleRefresh} 
              disabled={isLoading} 
              variant="outline" 
              size="lg" 
              className="whitespace-nowrap"
            >
              <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
              刷新數據
            </Button>
            <Button
              onClick={handleExportReport}
              variant="outline"
              size="lg"
              className="whitespace-nowrap"
            >
              <Download className="w-4 h-4 mr-2" />
              導出報告
            </Button>
            <Button
              onClick={handleStartAnalysis}
              disabled={isAnalyzing}
              variant="outline"
              size="lg"
              className="whitespace-nowrap"
            >
              {isAnalyzing ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  模擬分析中...
                </>
              ) : (
                <>
                  <BarChart3 className="w-4 h-4 mr-2" />
                  模擬分析
                </>
              )}
            </Button>
            <Button
              onClick={handleStartOpenAIAnalysis}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
              size="lg"
            >
              <Sparkles className="w-4 h-4 mr-2" />
              AI 分析
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* 關鍵指標卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-white border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">總品牌數</CardTitle>
              <div className="p-2 rounded-lg bg-blue-100">
                <Eye className="h-4 w-4 text-blue-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{formatNumber(data.overview.totalBrands)}</div>
              <p className="text-xs text-gray-500 mt-1">
                管理中的品牌總數
              </p>
            </CardContent>
          </Card>

          <Card className="bg-white border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">總分析次數</CardTitle>
              <div className="p-2 rounded-lg bg-green-100">
                <BarChart3 className="h-4 w-4 text-green-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{formatNumber(data.overview.totalAnalyses)}</div>
              <p className="text-xs text-gray-500 mt-1">
                本月執行的分析總數
              </p>
            </CardContent>
          </Card>

          <Card className="bg-white border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">平均可見度</CardTitle>
              <div className="p-2 rounded-lg bg-purple-100">
                <Target className="h-4 w-4 text-purple-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{formatPercentage(data.overview.averageVisibility)}</div>
              <p className="text-xs text-gray-500 mt-1">
                所有品牌的平均可見度分數
              </p>
            </CardContent>
          </Card>

          <Card className="bg-white border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">活躍報告</CardTitle>
              <div className="p-2 rounded-lg bg-orange-100">
                <MessageSquare className="h-4 w-4 text-orange-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{formatNumber(data.overview.activeReports)}</div>
              <p className="text-xs text-gray-500 mt-1">
                正在生成或處理中的報告
              </p>
            </CardContent>
          </Card>
        </div>

        {/* AI 引擎分佈和頂級品牌 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* AI 引擎分佈 */}
          <Card className="bg-white border border-gray-200 shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-900">AI 引擎分佈</CardTitle>
              <p className="text-sm text-gray-600">各 AI 引擎的可見度表現</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {(data.aiEngineDistribution || []).map((engine, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">{engine.engine}</span>
                      <span className="text-sm text-gray-600">{formatPercentage(engine.visibility)}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${engine.share}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 頂級品牌 */}
          <Card className="bg-white border border-gray-200 shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-900">頂級品牌</CardTitle>
              <p className="text-sm text-gray-600">表現最佳的品牌排行</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {(data.topBrands || []).map((brand, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-gray-50">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-sm font-medium text-blue-600">
                        {index + 1}
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">{brand.name}</div>
                        <div className="text-sm text-gray-500">{brand.industry}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium text-gray-900">{formatPercentage(brand.visibility)}</div>
                      <div className={cn("text-sm flex items-center", getTrendColor(brand.change))}>
                        {getTrendIcon(brand.change)}
                        <span className="ml-1">{brand.change > 0 ? '+' : ''}{brand.change}%</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 最近分析 */}
        <Card className="bg-white border border-gray-200 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-900">最近分析</CardTitle>
            <p className="text-sm text-gray-600">最新的分析活動</p>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {(data.recentAnalyses || []).map((analysis) => (
                <div key={analysis.id} className="flex items-center justify-between p-3 rounded-lg bg-gray-50">
                  <div className="flex items-center gap-3">
                    <div>
                      <div className="font-medium text-gray-900">{analysis.brand}</div>
                      <div className="text-sm text-gray-500">{analysis.type}</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Badge className={cn("text-xs", getStatusColor(analysis.status))}>
                      {analysis.status}
                    </Badge>
                    <span className="text-sm text-gray-500">{analysis.date}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 分析進度對話框（舊版本） */}
        <AnalysisProgressDialog
          open={showProgressDialog}
          onOpenChange={setShowProgressDialog}
          analysisId={currentAnalysisId || undefined}
          onComplete={handleMockAnalysisComplete}
          onError={handleAnalysisError}
        />

        {/* OpenAI 分析配置對話框 */}
        <SEOAnalysisConfigDialog
          open={showConfigDialog}
          onOpenChange={setShowConfigDialog}
          onStartAnalysis={handleAnalysisConfigComplete}
          defaultUrl="https://example.com"
        />

        {/* OpenAI 分析執行器 */}
        {showAnalysisExecutor && currentAnalysisRequest && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <SEOAnalysisExecutor
                request={currentAnalysisRequest}
                onComplete={handleAnalysisComplete}
                onCancel={handleAnalysisCancel}
                className="p-6"
              />
            </div>
          </div>
        )}

        {/* OpenAI 分析結果 */}
        {showAnalysisResults && currentAnalysisResult && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
              <SEOAnalysisResults
                result={currentAnalysisResult}
                onExport={(format) => toast.success(`正在導出 ${format.toUpperCase()} 報告...`)}
                onShare={() => toast.success('分享功能開發中...')}
                onNewAnalysis={handleNewAnalysis}
                className="p-6"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
