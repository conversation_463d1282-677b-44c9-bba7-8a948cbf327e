'use client';

import React, { useState, useEffect } from 'react';
import {
  Settings,
  Key,
  Database,
  Bell,
  Clock,
  Shield,
  Activity,
  AlertCircle,
  CheckCircle,
  Save,
  RefreshCw,
  TestTube,
  Zap,
  Loader2,
  Eye,
  EyeOff,
  Brain,
  Target,
  BarChart3
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { toast } from 'sonner';

interface ProductAnalysisSettings {
  ai: {
    openaiApiKey: string;
    model: string;
    maxTokens: number;
    temperature: number;
    isConfigured: boolean;
  };
  analysis: {
    autoAnalysisEnabled: boolean;
    analysisInterval: number;
    maxConcurrentAnalyses: number;
    retryAttempts: number;
    timeoutSeconds: number;
    enabledEngines: string[];
    visibilityThreshold: number;
  };
  notifications: {
    emailEnabled: boolean;
    slackEnabled: boolean;
    webhookEnabled: boolean;
    webhookUrl: string;
    notificationTypes: string[];
    alertThresholds: {
      lowVisibility: number;
      negativeResponse: number;
      citationDrop: number;
    };
  };
  content: {
    contentAnalysisEnabled: boolean;
    topicTrackingEnabled: boolean;
    competitorMonitoring: boolean;
    brandSafetyEnabled: boolean;
    hallucinationDetection: boolean;
  };
  reports: {
    autoReportGeneration: boolean;
    reportFrequency: string;
    retentionDays: number;
    exportFormats: string[];
  };
}

const defaultSettings: ProductAnalysisSettings = {
  ai: {
    openaiApiKey: '',
    model: 'gpt-4o-mini',
    maxTokens: 4000,
    temperature: 0.7,
    isConfigured: false
  },
  analysis: {
    autoAnalysisEnabled: true,
    analysisInterval: 24,
    maxConcurrentAnalyses: 5,
    retryAttempts: 3,
    timeoutSeconds: 300,
    enabledEngines: ['chatgpt', 'gemini', 'perplexity', 'copilot', 'claude'],
    visibilityThreshold: 70
  },
  notifications: {
    emailEnabled: true,
    slackEnabled: false,
    webhookEnabled: false,
    webhookUrl: '',
    notificationTypes: ['visibility_change', 'response_analysis', 'citation_update', 'report_ready'],
    alertThresholds: {
      lowVisibility: 50,
      negativeResponse: 30,
      citationDrop: 20
    }
  },
  content: {
    contentAnalysisEnabled: true,
    topicTrackingEnabled: true,
    competitorMonitoring: true,
    brandSafetyEnabled: true,
    hallucinationDetection: true
  },
  reports: {
    autoReportGeneration: true,
    reportFrequency: 'weekly',
    retentionDays: 90,
    exportFormats: ['pdf', 'csv', 'json']
  }
};

const aiEngines = [
  { id: 'chatgpt', name: 'ChatGPT', icon: '🤖' },
  { id: 'gemini', name: 'Google Gemini', icon: '🔍' },
  { id: 'perplexity', name: 'Perplexity AI', icon: '🧠' },
  { id: 'copilot', name: 'Microsoft Copilot', icon: '💻' },
  { id: 'claude', name: 'Anthropic Claude', icon: '📝' }
];

export default function ProductAnalysisSettingsPage() {
  // 強制初始化所有數值為安全的預設值
  const createSafeSettings = (): ProductAnalysisSettings => {
    console.log('🔧 Creating safe settings with force defaults');
    return {
      ai: {
        openaiApiKey: '',
        model: 'gpt-4o-mini', // 強制設定為有效的模型字串
        maxTokens: 4000,      // 強制設定為數字
        temperature: 0.7,     // 強制設定為數字
        isConfigured: false
      },
      analysis: {
        autoAnalysisEnabled: true,
        analysisInterval: 24,        // 強制設定為數字
        maxConcurrentAnalyses: 5,    // 強制設定為數字
        retryAttempts: 3,            // 強制設定為數字
        timeoutSeconds: 300,         // 強制設定為數字
        enabledEngines: ['chatgpt', 'gemini', 'perplexity', 'copilot', 'claude'],
        visibilityThreshold: 70      // 強制設定為數字
      },
      notifications: {
        emailEnabled: true,
        slackEnabled: false,
        webhookEnabled: false,
        webhookUrl: '',
        notificationTypes: ['visibility_change', 'response_analysis', 'citation_update', 'report_ready'],
        alertThresholds: {
          lowVisibility: 50,    // 強制設定為數字
          negativeResponse: 30, // 強制設定為數字
          citationDrop: 20      // 強制設定為數字
        }
      },
      content: {
        contentAnalysisEnabled: true,
        topicTrackingEnabled: true,
        competitorMonitoring: true,
        brandSafetyEnabled: true,
        hallucinationDetection: true
      },
      reports: {
        autoReportGeneration: true,
        reportFrequency: 'weekly',    // 強制設定為有效的頻率字串
        retentionDays: 90,            // 強制設定為數字
        exportFormats: ['pdf', 'csv', 'json']
      }
    };
  };

  const [settings, setSettings] = useState<ProductAnalysisSettings>(createSafeSettings);
  
  const [isLoading, setIsLoading] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [showApiKey, setShowApiKey] = useState(false);
  const [testResults, setTestResults] = useState<Record<string, any>>({});

  // 強制 NaN 清理 - 每次渲染都檢查
  useEffect(() => {
    const forceCleanNaN = () => {
      setSettings(current => {
        const hasNaN = JSON.stringify(current).includes('NaN');
        if (hasNaN) {
          console.warn('🚨 Force cleaning detected NaN values in settings');
          return createSafeSettings();
        }
        
        // 深度檢查數字屬性和字串屬性
        const validModels = ['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-3.5-turbo'];
        const validFrequencies = ['daily', 'weekly', 'monthly', 'quarterly'];
        const needsCleaning = 
          !validModels.includes(current.ai.model) ||
          !validFrequencies.includes(current.reports.reportFrequency) ||
          isNaN(current.ai.maxTokens) ||
          isNaN(current.ai.temperature) ||
          isNaN(current.analysis.analysisInterval) ||
          isNaN(current.analysis.maxConcurrentAnalyses) ||
          isNaN(current.analysis.retryAttempts) ||
          isNaN(current.analysis.timeoutSeconds) ||
          isNaN(current.analysis.visibilityThreshold) ||
          isNaN(current.notifications.alertThresholds.lowVisibility) ||
          isNaN(current.notifications.alertThresholds.negativeResponse) ||
          isNaN(current.notifications.alertThresholds.citationDrop) ||
          isNaN(current.reports.retentionDays);
          
        if (needsCleaning) {
          console.warn('🚨 Force cleaning: Found invalid values in properties');
          return {
            ...current,
            ai: {
              ...current.ai,
              model: validModels.includes(current.ai.model) ? current.ai.model : 'gpt-4o-mini',
              maxTokens: isNaN(current.ai.maxTokens) ? 4000 : current.ai.maxTokens,
              temperature: isNaN(current.ai.temperature) ? 0.7 : current.ai.temperature,
            },
            analysis: {
              ...current.analysis,
              analysisInterval: isNaN(current.analysis.analysisInterval) ? 24 : current.analysis.analysisInterval,
              maxConcurrentAnalyses: isNaN(current.analysis.maxConcurrentAnalyses) ? 5 : current.analysis.maxConcurrentAnalyses,
              retryAttempts: isNaN(current.analysis.retryAttempts) ? 3 : current.analysis.retryAttempts,
              timeoutSeconds: isNaN(current.analysis.timeoutSeconds) ? 300 : current.analysis.timeoutSeconds,
              visibilityThreshold: isNaN(current.analysis.visibilityThreshold) ? 70 : current.analysis.visibilityThreshold,
            },
            notifications: {
              ...current.notifications,
              alertThresholds: {
                lowVisibility: isNaN(current.notifications.alertThresholds.lowVisibility) ? 50 : current.notifications.alertThresholds.lowVisibility,
                negativeResponse: isNaN(current.notifications.alertThresholds.negativeResponse) ? 30 : current.notifications.alertThresholds.negativeResponse,
                citationDrop: isNaN(current.notifications.alertThresholds.citationDrop) ? 20 : current.notifications.alertThresholds.citationDrop,
              }
            },
            reports: {
              ...current.reports,
              reportFrequency: validFrequencies.includes(current.reports.reportFrequency) ? current.reports.reportFrequency : 'weekly',
              retentionDays: isNaN(current.reports.retentionDays) ? 90 : current.reports.retentionDays,
            }
          };
        }
        
        return current;
      });
    };
    
    // 立即執行一次
    forceCleanNaN();
    
    // 設定定時器每秒檢查一次（開發環境）
    if (process.env.NODE_ENV === 'development') {
      const interval = setInterval(forceCleanNaN, 1000);
      return () => clearInterval(interval);
    }
  }, []);

  // 安全的數字轉換函數 - 增強版
  const safeParseInt = (value: string, defaultValue: number = 0, min?: number, max?: number): number => {
    // 處理空字串和undefined
    if (value === '' || value === undefined || value === null) {
      return defaultValue;
    }
    
    const parsed = parseInt(String(value));
    if (isNaN(parsed)) {
      console.warn(`Failed to parse "${value}" as integer, using default ${defaultValue}`);
      return defaultValue;
    }
    
    if (min !== undefined && parsed < min) return min;
    if (max !== undefined && parsed > max) return max;
    return parsed;
  };

  // 安全的數字轉換函數（浮點數）
  const safeParseFloat = (value: string | number, defaultValue: number = 0, min?: number, max?: number): number => {
    if (value === '' || value === undefined || value === null) {
      return defaultValue;
    }
    
    const parsed = parseFloat(String(value));
    if (isNaN(parsed)) {
      console.warn(`Failed to parse "${value}" as float, using default ${defaultValue}`);
      return defaultValue;
    }
    
    if (min !== undefined && parsed < min) return min;
    if (max !== undefined && parsed > max) return max;
    return parsed;
  };

  // 載入設定
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const response = await fetch('/api/product-analysis/settings');
        const result = await response.json();
        
        if (result.success) {
          console.log('🔧 Loading settings from API:', result.data);
          
          // 使用更激進的清理策略 - 強制轉換所有數值
          const cleanedSettings: ProductAnalysisSettings = {
            ...createSafeSettings(), // 從安全預設值開始
            ...result.data,          // 覆蓋 API 資料
            ai: {
              ...createSafeSettings().ai,
              ...result.data.ai,
              model: (() => {
                const loadedModel = result.data.ai?.model;
                const validModels = ['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-3.5-turbo'];
                if (typeof loadedModel === 'string' && validModels.includes(loadedModel)) {
                  return loadedModel;
                }
                console.warn('🚨 AI Model: Invalid loaded model, using default:', loadedModel);
                return 'gpt-4o-mini';
              })(),
              maxTokens: Number(result.data.ai?.maxTokens) || 4000,
              temperature: Number(result.data.ai?.temperature) || 0.7,
            },
            analysis: {
              ...createSafeSettings().analysis,
              ...result.data.analysis,
              analysisInterval: Number(result.data.analysis?.analysisInterval) || 24,
              maxConcurrentAnalyses: Number(result.data.analysis?.maxConcurrentAnalyses) || 5,
              retryAttempts: Number(result.data.analysis?.retryAttempts) || 3,
              timeoutSeconds: Number(result.data.analysis?.timeoutSeconds) || 300,
              visibilityThreshold: Number(result.data.analysis?.visibilityThreshold) || 70,
            },
            notifications: {
              ...createSafeSettings().notifications,
              ...result.data.notifications,
              alertThresholds: {
                lowVisibility: Number(result.data.notifications?.alertThresholds?.lowVisibility) || 50,
                negativeResponse: Number(result.data.notifications?.alertThresholds?.negativeResponse) || 30,
                citationDrop: Number(result.data.notifications?.alertThresholds?.citationDrop) || 20,
              }
            },
            reports: {
              ...createSafeSettings().reports,
              ...result.data.reports,
              reportFrequency: (() => {
                const loadedFrequency = result.data.reports?.reportFrequency;
                const validFrequencies = ['daily', 'weekly', 'monthly', 'quarterly'];
                if (typeof loadedFrequency === 'string' && validFrequencies.includes(loadedFrequency)) {
                  return loadedFrequency;
                }
                console.warn('🚨 Report Frequency: Invalid loaded frequency, using default:', loadedFrequency);
                return 'weekly';
              })(),
              retentionDays: Number(result.data.reports?.retentionDays) || 90,
            }
          };
          
          console.log('🔧 Cleaned settings:', cleanedSettings);
          setSettings(cleanedSettings);
        } else {
          console.warn('🔧 Failed to load settings, using defaults');
          throw new Error(result.error?.message || '載入設定失敗');
        }
      } catch (error) {
        console.error('載入設定錯誤:', error);
        console.log('🔧 Using safe defaults due to load error');
        // 確保即使載入失敗也使用安全的預設值
        setSettings(createSafeSettings());
        toast.error('載入設定失敗，使用預設值');
      }
    };

    loadSettings();
  }, []);

  const handleSaveSettings = async () => {
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/product-analysis/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      const result = await response.json();
      
      if (result.success) {
        toast.success('設定儲存成功！');
      } else {
        throw new Error(result.error?.message || '設定儲存失敗');
      }
    } catch (error) {
      console.error('保存設定錯誤:', error);
      toast.error('設定儲存失敗，請重試');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestConnection = async () => {
    setIsTesting(true);
    
    try {
      const response = await fetch('/api/product-analysis/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ testType: 'all' }),
      });

      const result = await response.json();
      
      if (result.success) {
        setTestResults(result.data);
        toast.success('連接測試完成');
      } else {
        throw new Error(result.error?.message || '連接測試失敗');
      }
    } catch (error) {
      console.error('測試連接錯誤:', error);
      toast.error('連接測試失敗');
      setTestResults({});
    } finally {
      setIsTesting(false);
    }
  };

  const updateSettings = (section: keyof ProductAnalysisSettings, field: string, value: any) => {
    setSettings(prev => {
      // 詳細的 NaN 檢查和處理
      let processedValue = value;
      
      console.log(`Updating ${section}.${field} with value:`, value, `(type: ${typeof value})`);
      
      if (typeof value === 'number') {
        if (isNaN(value)) {
          console.warn(`⚠️ NaN value detected for ${section}.${field}, using default`);
          // 根據欄位提供合理的預設值
          const defaults: Record<string, number> = {
            maxTokens: 4000,
            temperature: 0.7,
            analysisInterval: 24,
            maxConcurrentAnalyses: 5,
            retryAttempts: 3,
            timeoutSeconds: 300,
            visibilityThreshold: 70,
            lowVisibility: 50,
            negativeResponse: 30,
            citationDrop: 20,
            retentionDays: 90
          };
          processedValue = defaults[field] || 0;
          console.log(`🔧 Using default value ${processedValue} for ${field}`);
        } else if (!isFinite(value)) {
          console.warn(`⚠️ Non-finite value detected for ${section}.${field}:`, value);
          processedValue = 0;
        }
      }
      
      // 確保處理後的值是有效的
      if (typeof processedValue === 'number' && (isNaN(processedValue) || !isFinite(processedValue))) {
        console.error(`❌ Still invalid number after processing for ${section}.${field}:`, processedValue);
        processedValue = 0;
      }
      
      let newState;
      
      // 特殊處理巢狀的 alertThresholds
      if (section === 'notifications' && field === 'alertThresholds') {
        newState = {
          ...prev,
          notifications: {
            ...prev.notifications,
            alertThresholds: {
              ...prev.notifications.alertThresholds,
              ...processedValue // processedValue 應該是一個物件
            }
          }
        };
      } else {
        newState = {
          ...prev,
          [section]: {
            ...prev[section],
            [field]: processedValue
          }
        };
      }
      
      // 強制驗證整個狀態樹，清理任何遺漏的 NaN 值
      const cleanState = cleanAllNaNValues(newState);
      
      console.log(`✅ Updated ${section}.${field} to:`, processedValue);
      return cleanState;
    });
  };

  // 強制清理所有 NaN 值的函數
  const cleanAllNaNValues = (obj: any): any => {
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }
    
    if (Array.isArray(obj)) {
      return obj.map(cleanAllNaNValues);
    }
    
    const cleaned: any = {};
    Object.keys(obj).forEach(key => {
      const value = obj[key];
      
      if (typeof value === 'number') {
        if (isNaN(value) || !isFinite(value)) {
          console.warn(`🧹 Cleaning NaN/Infinite value at ${key}:`, value);
          // 根據欄位名稱提供合理的預設值
          const fieldDefaults: Record<string, number> = {
            maxTokens: 4000,
            temperature: 0.7,
            analysisInterval: 24,
            maxConcurrentAnalyses: 5,
            retryAttempts: 3,
            timeoutSeconds: 300,
            visibilityThreshold: 70,
            lowVisibility: 50,
            negativeResponse: 30,
            citationDrop: 20,
            retentionDays: 90
          };
          cleaned[key] = fieldDefaults[key] || 0;
        } else {
          cleaned[key] = value;
        }
      } else if (typeof value === 'object') {
        cleaned[key] = cleanAllNaNValues(value);
      } else {
        cleaned[key] = value;
      }
    });
    
    return cleaned;
  };

  const toggleEngine = (engineId: string) => {
    const currentEngines = settings.analysis.enabledEngines;
    const newEngines = currentEngines.includes(engineId)
      ? currentEngines.filter(id => id !== engineId)
      : [...currentEngines, engineId];
    
    updateSettings('analysis', 'enabledEngines', newEngines);
  };

  const resetToDefaults = () => {
    setSettings(defaultSettings);
    toast.info('設定已重置為預設值');
  };

  // 渲染守護 - 確保所有數字值都是有效的
  const safeSetting = (value: any, fallback: any) => {
    if (typeof value === 'number' && (isNaN(value) || !isFinite(value))) {
      console.warn('Invalid number detected in render, using fallback:', fallback);
      return fallback;
    }
    return value;
  };

  return (
    <div className="space-y-6">
      {/* 頁面操作 */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <Button onClick={handleTestConnection} disabled={isTesting} variant="outline">
            <TestTube className={`w-4 h-4 mr-2 ${isTesting ? 'animate-spin' : ''}`} />
            {isTesting ? '測試中...' : '測試連接'}
          </Button>
          <Button onClick={resetToDefaults} variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            重置設定
          </Button>
        </div>
        <Button onClick={handleSaveSettings} disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              儲存中...
            </>
          ) : (
            <>
              <Save className="w-4 h-4 mr-2" />
              儲存設定
            </>
          )}
        </Button>
      </div>

      {/* 系統狀態概覽 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">AI 引擎狀態</p>
                <p className="text-2xl font-bold text-green-600">
                  {settings.analysis.enabledEngines.length}/5
                </p>
              </div>
              <Brain className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">自動分析</p>
                <p className="text-2xl font-bold text-gray-900">
                  {settings.analysis.autoAnalysisEnabled ? '啟用' : '停用'}
                </p>
              </div>
              <Activity className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">通知類型</p>
                <p className="text-2xl font-bold text-gray-900">
                  {settings.notifications.notificationTypes.length}
                </p>
              </div>
              <Bell className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">品牌安全</p>
                <p className="text-2xl font-bold text-gray-900">
                  {settings.content.brandSafetyEnabled ? '啟用' : '停用'}
                </p>
              </div>
              <Shield className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 設定標籤 */}
      <Tabs defaultValue="ai" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="ai">AI 設定</TabsTrigger>
          <TabsTrigger value="analysis">分析設定</TabsTrigger>
          <TabsTrigger value="notifications">通知設定</TabsTrigger>
          <TabsTrigger value="content">內容設定</TabsTrigger>
          <TabsTrigger value="reports">報告設定</TabsTrigger>
        </TabsList>

        <TabsContent value="ai">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Key className="h-5 w-5" />
                <span>AI 引擎設定</span>
              </CardTitle>
              <CardDescription>
                配置 OpenAI API 和其他 AI 引擎的設定
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="openai-key">OpenAI API 金鑰</Label>
                  <div className="flex space-x-2">
                    <div className="relative flex-1">
                      <Input
                        id="openai-key"
                        type={showApiKey ? 'text' : 'password'}
                        value={settings.ai.openaiApiKey}
                        onChange={(e) => updateSettings('ai', 'openaiApiKey', e.target.value)}
                        placeholder="sk-..."
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3"
                        onClick={() => setShowApiKey(!showApiKey)}
                      >
                        {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                    {settings.ai.isConfigured && (
                      <Badge variant="outline" className="text-green-600 border-green-600">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        已配置
                      </Badge>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="model">AI 模型</Label>
                    <select
                      id="model"
                      value={(() => {
                        // 🚨 AI 模型安全值檢查
                        const modelValue = settings.ai.model;
                        const validModels = ['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-3.5-turbo'];
                        
                        if (!modelValue || typeof modelValue !== 'string' || !validModels.includes(modelValue)) {
                          console.warn('🚨 AI Model: Invalid model value, using default:', modelValue);
                          return 'gpt-4o-mini';
                        }
                        
                        console.log('🔧 AI Model: Valid model selected:', modelValue);
                        return modelValue;
                      })()}
                      onChange={(e) => {
                        const selectedValue = e.target.value;
                        console.log('🔧 AI Model onChange:', selectedValue);
                        
                        // 驗證選擇的值
                        const validModels = ['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-3.5-turbo'];
                        if (!validModels.includes(selectedValue)) {
                          console.warn('🚨 AI Model: Invalid selection, using default');
                          updateSettings('ai', 'model', 'gpt-4o-mini');
                          return;
                        }
                        
                        updateSettings('ai', 'model', selectedValue);
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="gpt-4o">GPT-4o</option>
                      <option value="gpt-4o-mini">GPT-4o Mini</option>
                      <option value="gpt-4-turbo">GPT-4 Turbo</option>
                      <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="max-tokens">最大 Token 數</Label>
                    <Input
                      id="max-tokens"
                      type="number"
                      value={(() => {
                        // 🚨 最終極保護 - 專門針對最大 Token 數
                        let value = settings.ai.maxTokens;
                        
                        // 多重檢查
                        if (value === undefined || value === null || isNaN(value) || !isFinite(value)) {
                          console.warn('🚨 Max Tokens: Using fallback 4000, original was:', value);
                          return '4000';
                        }
                        
                        // 確保是有效數字
                        const numValue = Number(value);
                        if (isNaN(numValue) || !isFinite(numValue)) {
                          console.warn('🚨 Max Tokens: Number conversion failed, using 4000');
                          return '4000';
                        }
                        
                        // 範圍檢查
                        if (numValue < 100 || numValue > 8000) {
                          console.warn('🚨 Max Tokens: Out of range, clamping:', numValue);
                          return String(Math.max(100, Math.min(8000, numValue)));
                        }
                        
                        const finalValue = String(numValue);
                        console.log('🔧 Max Tokens final value:', finalValue);
                        return finalValue;
                      })()}
                      onChange={(e) => {
                        console.log('🔧 Max Tokens onChange:', e.target.value);
                        
                        // 立即驗證輸入值
                        let inputValue = e.target.value;
                        if (inputValue === '' || inputValue === 'NaN' || inputValue === 'null' || inputValue === 'undefined') {
                          console.warn('🚨 Max Tokens: Invalid input, using 4000');
                          updateSettings('ai', 'maxTokens', 4000);
                          return;
                        }
                        
                        const parsed = safeParseInt(inputValue, 4000, 100, 8000);
                        console.log('🔧 Max Tokens parsed:', parsed);
                        
                        // 再次驗證解析後的值
                        if (isNaN(parsed) || !isFinite(parsed)) {
                          console.warn('🚨 Max Tokens: Parse result invalid, using 4000');
                          updateSettings('ai', 'maxTokens', 4000);
                          return;
                        }
                        
                        updateSettings('ai', 'maxTokens', parsed);
                      }}
                      min="100"
                      max="8000"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="temperature">溫度設定: {safeSetting(settings.ai.temperature, 0.7)}</Label>
                  <Slider
                    id="temperature"
                    min={0}
                    max={2}
                    step={0.1}
                    value={[safeSetting(settings.ai.temperature, 0.7)]}
                    onValueChange={([value]) => updateSettings('ai', 'temperature', safeParseFloat(value, 0.7, 0, 2))}
                    className="mt-2"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>更準確</span>
                    <span>更創意</span>
                  </div>
                </div>
              </div>

              {/* 測試結果 */}
              {Object.keys(testResults).length > 0 && (
                <div className="border rounded-lg p-4 bg-gray-50">
                  <h4 className="font-medium text-gray-900 mb-3">連接測試結果</h4>
                  <div className="space-y-2">
                    {testResults.openai && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm">OpenAI API</span>
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm text-gray-600">{testResults.openai.latency}</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analysis">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5" />
                <span>分析設定</span>
              </CardTitle>
              <CardDescription>
                配置自動分析和 AI 引擎參數
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="auto-analysis">自動分析</Label>
                  <p className="text-sm text-gray-600">啟用定期自動分析</p>
                </div>
                <Switch
                  id="auto-analysis"
                  checked={settings.analysis.autoAnalysisEnabled}
                  onCheckedChange={(checked) => updateSettings('analysis', 'autoAnalysisEnabled', checked)}
                />
              </div>

              {settings.analysis.autoAnalysisEnabled && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <Label htmlFor="analysis-interval">分析間隔 (小時)</Label>
                    <Input
                      id="analysis-interval"
                      type="number"
                      value={(() => {
                        // 🚨 分析間隔安全值檢查
                        const value = settings.analysis.analysisInterval;
                        if (value === undefined || value === null || isNaN(value) || !isFinite(value)) {
                          console.warn('🚨 Analysis Interval: Using fallback 24, original was:', value);
                          return '24';
                        }
                        const numValue = Number(value);
                        if (isNaN(numValue) || numValue < 1 || numValue > 168) {
                          console.warn('🚨 Analysis Interval: Out of range, using 24:', numValue);
                          return '24';
                        }
                        return String(numValue);
                      })()}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        if (inputValue === '' || inputValue === 'NaN') {
                          updateSettings('analysis', 'analysisInterval', 24);
                          return;
                        }
                        const parsed = safeParseInt(inputValue, 24, 1, 168);
                        updateSettings('analysis', 'analysisInterval', parsed);
                      }}
                      min="1"
                      max="168"
                    />
                  </div>
                  <div>
                    <Label htmlFor="max-concurrent">最大並發數</Label>
                    <Input
                      id="max-concurrent"
                      type="number"
                      value={(() => {
                        // 🚨 最大並發數安全值檢查
                        const value = settings.analysis.maxConcurrentAnalyses;
                        if (value === undefined || value === null || isNaN(value) || !isFinite(value)) {
                          console.warn('🚨 Max Concurrent: Using fallback 5, original was:', value);
                          return '5';
                        }
                        const numValue = Number(value);
                        if (isNaN(numValue) || numValue < 1 || numValue > 10) {
                          console.warn('🚨 Max Concurrent: Out of range, using 5:', numValue);
                          return '5';
                        }
                        return String(numValue);
                      })()}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        if (inputValue === '' || inputValue === 'NaN') {
                          updateSettings('analysis', 'maxConcurrentAnalyses', 5);
                          return;
                        }
                        const parsed = safeParseInt(inputValue, 5, 1, 10);
                        updateSettings('analysis', 'maxConcurrentAnalyses', parsed);
                      }}
                      min="1"
                      max="10"
                    />
                  </div>
                  <div>
                    <Label htmlFor="timeout">超時時間 (秒)</Label>
                    <Input
                      id="timeout"
                      type="number"
                      value={(() => {
                        // 🚨 超時時間安全值檢查
                        const value = settings.analysis.timeoutSeconds;
                        if (value === undefined || value === null || isNaN(value) || !isFinite(value)) {
                          console.warn('🚨 Timeout: Using fallback 300, original was:', value);
                          return '300';
                        }
                        const numValue = Number(value);
                        if (isNaN(numValue) || numValue < 30 || numValue > 600) {
                          console.warn('🚨 Timeout: Out of range, using 300:', numValue);
                          return '300';
                        }
                        return String(numValue);
                      })()}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        if (inputValue === '' || inputValue === 'NaN') {
                          updateSettings('analysis', 'timeoutSeconds', 300);
                          return;
                        }
                        const parsed = safeParseInt(inputValue, 300, 30, 600);
                        updateSettings('analysis', 'timeoutSeconds', parsed);
                      }}
                      min="30"
                      max="600"
                    />
                  </div>
                </div>
              )}

              <div>
                <Label>啟用的 AI 引擎</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-2">
                  {aiEngines.map((engine) => (
                    <div
                      key={engine.id}
                      className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                        settings.analysis.enabledEngines.includes(engine.id)
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => toggleEngine(engine.id)}
                    >
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{engine.icon}</span>
                        <div>
                          <h4 className="font-medium text-gray-900">{engine.name}</h4>
                          <div className="flex items-center space-x-2 mt-1">
                            <input
                              type="checkbox"
                              checked={settings.analysis.enabledEngines.includes(engine.id)}
                              onChange={() => toggleEngine(engine.id)}
                              className="rounded"
                            />
                            <span className="text-sm text-gray-600">
                              {settings.analysis.enabledEngines.includes(engine.id) ? '已啟用' : '已停用'}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <Label htmlFor="visibility-threshold">可見度警告閾值: {safeSetting(settings.analysis.visibilityThreshold, 70)}%</Label>
                <Slider
                  id="visibility-threshold"
                  min={0}
                  max={100}
                  step={5}
                  value={[safeSetting(settings.analysis.visibilityThreshold, 70)]}
                  onValueChange={([value]) => updateSettings('analysis', 'visibilityThreshold', safeParseInt(String(value), 70, 0, 100))}
                  className="mt-2"
                />
                <p className="text-sm text-gray-600 mt-1">
                  當可見度低於此值時將發送警告通知
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Bell className="h-5 w-5" />
                <span>通知設定</span>
              </CardTitle>
              <CardDescription>
                配置分析結果和警報的通知方式
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="email-notifications">電子郵件通知</Label>
                    <p className="text-sm text-gray-600">接收分析報告和警報的電子郵件</p>
                  </div>
                  <Switch
                    id="email-notifications"
                    checked={settings.notifications.emailEnabled}
                    onCheckedChange={(checked) => updateSettings('notifications', 'emailEnabled', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="slack-notifications">Slack 通知</Label>
                    <p className="text-sm text-gray-600">發送通知到 Slack 頻道</p>
                  </div>
                  <Switch
                    id="slack-notifications"
                    checked={settings.notifications.slackEnabled}
                    onCheckedChange={(checked) => updateSettings('notifications', 'slackEnabled', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="webhook-notifications">Webhook 通知</Label>
                    <p className="text-sm text-gray-600">發送通知到指定的 Webhook URL</p>
                  </div>
                  <Switch
                    id="webhook-notifications"
                    checked={settings.notifications.webhookEnabled}
                    onCheckedChange={(checked) => updateSettings('notifications', 'webhookEnabled', checked)}
                  />
                </div>
              </div>

              {settings.notifications.webhookEnabled && (
                <div>
                  <Label htmlFor="webhook-url">Webhook URL</Label>
                  <Input
                    id="webhook-url"
                    type="url"
                    value={settings.notifications.webhookUrl}
                    onChange={(e) => updateSettings('notifications', 'webhookUrl', e.target.value)}
                    placeholder="https://hooks.slack.com/services/..."
                  />
                </div>
              )}

              <div>
                <Label>通知類型</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  {[
                    { id: 'visibility_change', label: '可見度變化', description: '品牌可見度顯著變化時通知' },
                    { id: 'response_analysis', label: '回應分析', description: '新的 AI 回應分析完成時通知' },
                    { id: 'citation_update', label: '引用更新', description: '引用狀態變化時通知' },
                    { id: 'report_ready', label: '報告就緒', description: '新報告生成完成時通知' }
                  ].map((type) => (
                    <div key={type.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                      <input
                        type="checkbox"
                        id={type.id}
                        checked={settings.notifications.notificationTypes.includes(type.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            updateSettings('notifications', 'notificationTypes', [
                              ...settings.notifications.notificationTypes,
                              type.id
                            ]);
                          } else {
                            updateSettings('notifications', 'notificationTypes',
                              settings.notifications.notificationTypes.filter(id => id !== type.id)
                            );
                          }
                        }}
                        className="mt-1 rounded"
                      />
                      <div>
                        <label htmlFor={type.id} className="font-medium text-gray-900">
                          {type.label}
                        </label>
                        <p className="text-sm text-gray-600">{type.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <Label className="text-base font-medium">警報閾值</Label>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-4">
                  <div>
                    <Label htmlFor="low-visibility">低可見度警報: {safeSetting(settings.notifications.alertThresholds.lowVisibility, 50)}%</Label>
                    <Slider
                      id="low-visibility"
                      min={0}
                      max={100}
                      step={5}
                      value={[safeSetting(settings.notifications.alertThresholds.lowVisibility, 50)]}
                      onValueChange={([value]) => {
                        const safeValue = safeParseInt(String(value), 50, 0, 100);
                        updateSettings('notifications', 'alertThresholds', {
                          ...settings.notifications.alertThresholds,
                          lowVisibility: safeValue
                        });
                      }}
                      className="mt-2"
                    />
                  </div>
                  <div>
                    <Label htmlFor="negative-response">負面回應警報: {safeSetting(settings.notifications.alertThresholds.negativeResponse, 30)}%</Label>
                    <Slider
                      id="negative-response"
                      min={0}
                      max={100}
                      step={5}
                      value={[safeSetting(settings.notifications.alertThresholds.negativeResponse, 30)]}
                      onValueChange={([value]) => {
                        const safeValue = safeParseInt(String(value), 30, 0, 100);
                        updateSettings('notifications', 'alertThresholds', {
                          ...settings.notifications.alertThresholds,
                          negativeResponse: safeValue
                        });
                      }}
                      className="mt-2"
                    />
                  </div>
                  <div>
                    <Label htmlFor="citation-drop">引用下降警報: {safeSetting(settings.notifications.alertThresholds.citationDrop, 20)}%</Label>
                    <Slider
                      id="citation-drop"
                      min={0}
                      max={100}
                      step={5}
                      value={[safeSetting(settings.notifications.alertThresholds.citationDrop, 20)]}
                      onValueChange={([value]) => {
                        const safeValue = safeParseInt(String(value), 20, 0, 100);
                        updateSettings('notifications', 'alertThresholds', {
                          ...settings.notifications.alertThresholds,
                          citationDrop: safeValue
                        });
                      }}
                      className="mt-2"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="content">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5" />
                <span>內容設定</span>
              </CardTitle>
              <CardDescription>
                配置內容分析和品牌安全監控
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="content-analysis">內容分析</Label>
                    <p className="text-sm text-gray-600">自動分析內容的 AI 可見度優化潛力</p>
                  </div>
                  <Switch
                    id="content-analysis"
                    checked={settings.content.contentAnalysisEnabled}
                    onCheckedChange={(checked) => updateSettings('content', 'contentAnalysisEnabled', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="topic-tracking">主題追蹤</Label>
                    <p className="text-sm text-gray-600">追蹤品牌在特定主題的表現變化</p>
                  </div>
                  <Switch
                    id="topic-tracking"
                    checked={settings.content.topicTrackingEnabled}
                    onCheckedChange={(checked) => updateSettings('content', 'topicTrackingEnabled', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="competitor-monitoring">競爭對手監控</Label>
                    <p className="text-sm text-gray-600">監控競爭對手的可見度變化</p>
                  </div>
                  <Switch
                    id="competitor-monitoring"
                    checked={settings.content.competitorMonitoring}
                    onCheckedChange={(checked) => updateSettings('content', 'competitorMonitoring', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="brand-safety">品牌安全監控</Label>
                    <p className="text-sm text-gray-600">檢測對品牌有害的內容和回應</p>
                  </div>
                  <Switch
                    id="brand-safety"
                    checked={settings.content.brandSafetyEnabled}
                    onCheckedChange={(checked) => updateSettings('content', 'brandSafetyEnabled', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="hallucination-detection">幻覺檢測</Label>
                    <p className="text-sm text-gray-600">自動檢測 AI 回應中的不準確資訊</p>
                  </div>
                  <Switch
                    id="hallucination-detection"
                    checked={settings.content.hallucinationDetection}
                    onCheckedChange={(checked) => updateSettings('content', 'hallucinationDetection', checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="h-5 w-5" />
                <span>報告設定</span>
              </CardTitle>
              <CardDescription>
                配置自動報告生成和資料保留政策
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="auto-reports">自動報告生成</Label>
                  <p className="text-sm text-gray-600">定期自動生成分析報告</p>
                </div>
                <Switch
                  id="auto-reports"
                  checked={settings.reports.autoReportGeneration}
                  onCheckedChange={(checked) => updateSettings('reports', 'autoReportGeneration', checked)}
                />
              </div>

              {settings.reports.autoReportGeneration && (
                <div>
                  <Label htmlFor="report-frequency">報告頻率</Label>
                  <select
                    id="report-frequency"
                    value={(() => {
                      // 🚨 報告頻率安全值檢查
                      const frequency = settings.reports.reportFrequency;
                      const validFrequencies = ['daily', 'weekly', 'monthly', 'quarterly'];
                      if (!frequency || typeof frequency !== 'string' || !validFrequencies.includes(frequency)) {
                        console.warn('🚨 Report Frequency: Invalid frequency value, using default:', frequency);
                        return 'weekly';
                      }
                      return frequency;
                    })()}
                    onChange={(e) => {
                      const selectedValue = e.target.value;
                      const validFrequencies = ['daily', 'weekly', 'monthly', 'quarterly'];
                      if (!validFrequencies.includes(selectedValue)) {
                        console.warn('🚨 Report Frequency: Invalid selection, using default');
                        updateSettings('reports', 'reportFrequency', 'weekly');
                        return;
                      }
                      updateSettings('reports', 'reportFrequency', selectedValue);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mt-2"
                  >
                    <option value="daily">每日</option>
                    <option value="weekly">每週</option>
                    <option value="monthly">每月</option>
                    <option value="quarterly">每季</option>
                  </select>
                </div>
              )}

              <div>
                <Label htmlFor="retention-days">資料保留天數</Label>
                <Input
                  id="retention-days"
                  type="number"
                  value={(() => {
                    // 🚨 資料保留天數安全值檢查
                    const value = settings.reports.retentionDays;
                    if (value === undefined || value === null || isNaN(value) || !isFinite(value)) {
                      console.warn('🚨 Retention Days: Using fallback 90, original was:', value);
                      return '90';
                    }
                    const numValue = Number(value);
                    if (isNaN(numValue) || numValue < 7 || numValue > 365) {
                      console.warn('🚨 Retention Days: Out of range, using 90:', numValue);
                      return '90';
                    }
                    return String(numValue);
                  })()}
                  onChange={(e) => {
                    const inputValue = e.target.value;
                    if (inputValue === '' || inputValue === 'NaN') {
                      updateSettings('reports', 'retentionDays', 90);
                      return;
                    }
                    const parsed = safeParseInt(inputValue, 90, 7, 365);
                    updateSettings('reports', 'retentionDays', parsed);
                  }}
                  min="7"
                  max="365"
                  className="mt-2"
                />
                <p className="text-sm text-gray-600 mt-1">
                  報告和分析資料將保留指定天數後自動刪除
                </p>
              </div>

              <div>
                <Label>支援的匯出格式</Label>
                <div className="flex flex-wrap gap-4 mt-2">
                  {[
                    { id: 'pdf', label: 'PDF', description: '適合列印和分享' },
                    { id: 'csv', label: 'CSV', description: '適合資料分析' },
                    { id: 'json', label: 'JSON', description: '適合 API 整合' },
                    { id: 'xlsx', label: 'Excel', description: '適合進階分析' }
                  ].map((format) => (
                    <div key={format.id} className="flex items-center space-x-2 p-3 border rounded-lg">
                      <input
                        type="checkbox"
                        id={format.id}
                        checked={settings.reports.exportFormats.includes(format.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            updateSettings('reports', 'exportFormats', [
                              ...settings.reports.exportFormats,
                              format.id
                            ]);
                          } else {
                            updateSettings('reports', 'exportFormats',
                              settings.reports.exportFormats.filter(id => id !== format.id)
                            );
                          }
                        }}
                        className="rounded"
                      />
                      <div>
                        <label htmlFor={format.id} className="font-medium text-gray-900">
                          {format.label}
                        </label>
                        <p className="text-xs text-gray-600">{format.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 