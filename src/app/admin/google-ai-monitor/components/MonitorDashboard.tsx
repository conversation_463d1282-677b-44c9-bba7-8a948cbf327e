/**
 * 監測儀表板組件
 * 顯示監測概覽、統計數據和最近活動
 */

'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  TrendingUp,
  TrendingDown,
  Eye,
  Search,
  AlertTriangle,
  CheckCircle,
  Clock,
  Globe,
  Users,
  BarChart3,
  Zap,
  RefreshCw,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { DashboardData, MonitoringStatus } from '@/types/google-ai-monitor';

interface MonitorDashboardProps {
  data: DashboardData;
  status: MonitoringStatus;
  isLoading: boolean;
  onRefresh: () => void;
}

export default function MonitorDashboard({
  data,
  status,
  isLoading,
  onRefresh,
}: MonitorDashboardProps) {
  // 獲取趨勢方向圖標
  const getTrendIcon = (value: number, threshold: number = 0) => {
    if (value > threshold) {
      return <TrendingUp className="w-4 h-4 text-green-600" />;
    } else if (value < threshold) {
      return <TrendingDown className="w-4 h-4 text-red-600" />;
    }
    return <div className="w-4 h-4" />; // 空白佔位
  };

  // 獲取警告類型樣式
  const getAlertStyle = (type: 'info' | 'warning' | 'error') => {
    switch (type) {
      case 'info':
        return 'border-blue-200 bg-blue-50 text-blue-800';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50 text-yellow-800';
      case 'error':
        return 'border-red-200 bg-red-50 text-red-800';
      default:
        return 'border-gray-200 bg-gray-50 text-gray-800';
    }
  };

  // 獲取警告圖標
  const getAlertIcon = (type: 'info' | 'warning' | 'error') => {
    switch (type) {
      case 'info':
        return <CheckCircle className="w-4 h-4 text-blue-600" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      case 'error':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      default:
        return <CheckCircle className="w-4 h-4 text-gray-600" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* 核心指標卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* 總關鍵字數 */}
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">總關鍵字數</p>
                <div className="flex items-center gap-2 mt-1">
                  <p className="text-3xl font-bold text-gray-900">
                    {data.overview.totalKeywords}
                  </p>
                  {getTrendIcon(data.overview.activeKeywords - data.overview.totalKeywords + data.overview.activeKeywords)}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  活躍: {data.overview.activeKeywords}
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <Search className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 今日搜尋次數 */}
        <Card className="border-l-4 border-l-green-500">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">今日搜尋次數</p>
                <div className="flex items-center gap-2 mt-1">
                  <p className="text-3xl font-bold text-gray-900">
                    {data.overview.totalSearchesToday}
                  </p>
                  {getTrendIcon(data.overview.totalSearchesToday, 20)}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  完成: {status.completedToday} | 失敗: {status.failedToday}
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <Eye className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* AI 摘要出現率 */}
        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">AI 摘要出現率</p>
                <div className="flex items-center gap-2 mt-1">
                  <p className="text-3xl font-bold text-gray-900">
                    {data.overview.aiSummaryRate.toFixed(1)}%
                  </p>
                  {getTrendIcon(data.overview.aiSummaryRate, 50)}
                </div>
                <Progress 
                  value={data.overview.aiSummaryRate} 
                  className="mt-2 h-2"
                />
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <BarChart3 className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 用戶提及率 */}
        <Card className="border-l-4 border-l-orange-500">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">用戶提及率</p>
                <div className="flex items-center gap-2 mt-1">
                  <p className="text-3xl font-bold text-gray-900">
                    {data.overview.userMentionRate.toFixed(1)}%
                  </p>
                  {getTrendIcon(data.overview.userMentionRate, 10)}
                </div>
                <Progress 
                  value={data.overview.userMentionRate} 
                  className="mt-2 h-2"
                />
              </div>
              <div className="p-3 bg-orange-100 rounded-full">
                <Users className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 熱門關鍵字 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Zap className="h-5 w-5 text-yellow-600" />
              熱門關鍵字
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.trends.topKeywords.map((keyword, index) => {
                // 使用關鍵字名稱生成確定的出現率，避免 hydration error
                const getKeywordAppearanceRate = (keyword: string) => {
                  const hash = keyword.split('').reduce((a, b) => {
                    a = ((a << 5) - a) + b.charCodeAt(0);
                    return a & a;
                  }, 0);
                  return Math.abs(hash % 40) + 50; // 50-89% 的範圍
                };
                
                const appearanceRate = getKeywordAppearanceRate(keyword);
                
                return (
                  <div key={keyword} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className={cn(
                        'flex items-center justify-center w-6 h-6 rounded-full text-xs font-bold',
                        index === 0 ? 'bg-yellow-500 text-white' :
                        index === 1 ? 'bg-gray-400 text-white' :
                        index === 2 ? 'bg-orange-500 text-white' :
                        'bg-gray-200 text-gray-600'
                      )}>
                        {index + 1}
                      </div>
                      <span className="font-medium">{keyword}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">
                        {appearanceRate}% 出現率
                      </Badge>
                      <TrendingUp className="w-4 h-4 text-green-600" />
                    </div>
                  </div>
                );
              })}
              {data.trends.topKeywords.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <Search className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                  <p>暫無關鍵字數據</p>
                  <p className="text-sm">開始監測後將顯示熱門關鍵字</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 系統警告 */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-yellow-600" />
                系統通知
              </CardTitle>
              <Button 
                onClick={onRefresh} 
                disabled={isLoading}
                variant="ghost" 
                size="sm"
              >
                <RefreshCw className={cn('w-4 h-4', isLoading && 'animate-spin')} />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.alerts.map((alert, index) => (
                <div 
                  key={index} 
                  className={cn(
                    'flex items-start gap-3 p-3 rounded-lg border',
                    getAlertStyle(alert.type)
                  )}
                >
                  {getAlertIcon(alert.type)}
                  <div className="flex-1">
                    <p className="text-sm font-medium">{alert.message}</p>
                    <p className="text-xs opacity-75 mt-1">
                      {alert.timestamp.toLocaleString('zh-TW')}
                    </p>
                  </div>
                </div>
              ))}
              {data.alerts.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <CheckCircle className="w-12 h-12 mx-auto mb-3 text-green-300" />
                  <p>系統運行正常</p>
                  <p className="text-sm">暫無警告或通知</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* API 配額使用情況 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Globe className="h-5 w-5 text-blue-600" />
            API 配額使用情況
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-600">今日使用量</span>
                <span className="text-sm font-bold">
                  {status.apiQuotaUsed}%
                </span>
              </div>
              <Progress 
                value={status.apiQuotaUsed} 
                className={cn(
                  'h-3',
                  status.apiQuotaUsed > 90 ? 'bg-red-100' :
                  status.apiQuotaUsed > 75 ? 'bg-yellow-100' :
                  'bg-green-100'
                )}
              />
              <p className="text-xs text-gray-500">
                剩餘: {status.apiQuotaRemaining} 次請求
              </p>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-600">請求成功率</span>
                <span className="text-sm font-bold text-green-600">
                  {((status.completedToday / (status.completedToday + status.failedToday)) * 100 || 0).toFixed(1)}%
                </span>
              </div>
              <Progress 
                value={(status.completedToday / (status.completedToday + status.failedToday)) * 100 || 0}
                className="h-3"
              />
              <p className="text-xs text-gray-500">
                成功: {status.completedToday} | 失敗: {status.failedToday}
              </p>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-600">任務處理率</span>
                <span className="text-sm font-bold text-blue-600">
                  {status.activeTasks + status.pendingTasks > 0 ? 
                    ((status.activeTasks / (status.activeTasks + status.pendingTasks)) * 100).toFixed(1) : 
                    0}%
                </span>
              </div>
              <Progress 
                value={status.activeTasks + status.pendingTasks > 0 ? 
                  (status.activeTasks / (status.activeTasks + status.pendingTasks)) * 100 : 
                  0}
                className="h-3"
              />
              <p className="text-xs text-gray-500">
                執行中: {status.activeTasks} | 等待: {status.pendingTasks}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 下次執行時間 */}
      {status.nextScheduledTask && (
        <Card className="border-dashed border-2 border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <div className="flex items-center justify-center gap-3 text-blue-800">
              <Clock className="w-5 h-5" />
              <span className="font-medium">
                下次自動監測時間: {new Date(status.nextScheduledTask).toLocaleString('zh-TW')}
              </span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
