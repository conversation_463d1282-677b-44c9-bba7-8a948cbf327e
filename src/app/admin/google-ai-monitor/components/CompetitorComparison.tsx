/**
 * 競爭對手比較組件
 * 分析和比較競爭對手在 AI 摘要中的表現
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Users,
  Plus,
  TrendingUp,
  TrendingDown,
  Globe,
  Target,
  BarChart3,
  Crown,
  Award,
  Zap,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

export default function CompetitorComparison() {
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [newCompetitor, setNewCompetitor] = useState({ domain: '', name: '' });

  // 模擬競爭對手數據
  const competitorData = [
    {
      id: '1',
      domain: 'competitor1.com',
      name: '競爭對手 A',
      mentionCount: 45,
      shareOfVoice: 32.1,
      averagePosition: 1.8,
      trend: 'up',
      topKeywords: ['AI工具', '機器學習', '自動化'],
    },
    {
      id: '2',
      domain: 'competitor2.com',
      name: '競爭對手 B',
      mentionCount: 38,
      shareOfVoice: 27.3,
      averagePosition: 2.2,
      trend: 'stable',
      topKeywords: ['電動車', '新能源', '智能駕駛'],
    },
    {
      id: '3',
      domain: 'competitor3.com',
      name: '競爭對手 C',
      mentionCount: 29,
      shareOfVoice: 20.8,
      averagePosition: 2.9,
      trend: 'down',
      topKeywords: ['5G網路', '物聯網', '智慧城市'],
    },
    {
      id: 'user',
      domain: 'example.com',
      name: '您的網站',
      mentionCount: 28,
      shareOfVoice: 19.8,
      averagePosition: 3.1,
      trend: 'up',
      topKeywords: ['AI工具', '數據分析', 'SEO優化'],
    },
  ];

  // 添加競爭對手
  const handleAddCompetitor = () => {
    if (!newCompetitor.domain.trim()) {
      toast.error('請輸入競爭對手網域');
      return;
    }

    // 模擬添加邏輯
    toast.success('競爭對手已添加');
    setShowAddDialog(false);
    setNewCompetitor({ domain: '', name: '' });
  };

  // 獲取趨勢圖標
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-red-600" />;
      default:
        return <div className="w-4 h-4 bg-gray-300 rounded-full" />;
    }
  };

  // 獲取排名徽章
  const getRankBadge = (index: number) => {
    if (index === 0) {
      return <Crown className="w-4 h-4 text-yellow-500" />;
    } else if (index === 1) {
      return <Award className="w-4 h-4 text-gray-400" />;
    } else if (index === 2) {
      return <Award className="w-4 h-4 text-orange-500" />;
    }
    return null;
  };

  // 按提及次數排序
  const sortedCompetitors = [...competitorData].sort((a, b) => b.mentionCount - a.mentionCount);

  return (
    <div className="space-y-6">
      {/* 頁面標題 */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">競爭對手分析</h2>
          <p className="text-gray-600 mt-1">
            比較您與競爭對手在 Google AI 摘要中的表現
          </p>
        </div>
        <Button onClick={() => setShowAddDialog(true)}>
          <Plus className="w-4 h-4 mr-2" />
          添加競爭對手
        </Button>
      </div>

      {/* 競爭概覽 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">
                {competitorData.length - 1}
              </div>
              <div className="text-sm text-gray-600 mt-1">追蹤競爭對手</div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">
                {sortedCompetitors.findIndex(c => c.id === 'user') + 1}
              </div>
              <div className="text-sm text-gray-600 mt-1">您的排名</div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600">
                {competitorData.find(c => c.id === 'user')?.shareOfVoice.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600 mt-1">聲量佔比</div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-600">
                {competitorData.find(c => c.id === 'user')?.averagePosition.toFixed(1)}
              </div>
              <div className="text-sm text-gray-600 mt-1">平均位置</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 競爭對手排行榜 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-purple-600" />
            AI 摘要提及排行榜
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {sortedCompetitors.map((competitor, index) => (
              <div 
                key={competitor.id} 
                className={cn(
                  'flex items-center justify-between p-4 rounded-lg border',
                  competitor.id === 'user' ? 'bg-blue-50 border-blue-200' : 'bg-gray-50'
                )}
              >
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    {getRankBadge(index)}
                    <div className={cn(
                      'flex items-center justify-center w-8 h-8 rounded-full text-sm font-bold',
                      index === 0 ? 'bg-yellow-500 text-white' :
                      index === 1 ? 'bg-gray-400 text-white' :
                      index === 2 ? 'bg-orange-500 text-white' :
                      'bg-gray-200 text-gray-600'
                    )}>
                      {index + 1}
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center gap-2">
                      <h4 className={cn(
                        'font-medium',
                        competitor.id === 'user' ? 'text-blue-900' : 'text-gray-900'
                      )}>
                        {competitor.name}
                      </h4>
                      {competitor.id === 'user' && (
                        <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                          您的網站
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Globe className="w-3 h-3" />
                      {competitor.domain}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-6">
                  <div className="text-center">
                    <div className="text-lg font-bold text-gray-900">
                      {competitor.mentionCount}
                    </div>
                    <div className="text-xs text-gray-600">提及次數</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="text-lg font-bold text-gray-900">
                      {competitor.shareOfVoice.toFixed(1)}%
                    </div>
                    <div className="text-xs text-gray-600">聲量佔比</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="text-lg font-bold text-gray-900">
                      {competitor.averagePosition.toFixed(1)}
                    </div>
                    <div className="text-xs text-gray-600">平均位置</div>
                  </div>
                  
                  <div className="text-center">
                    {getTrendIcon(competitor.trend)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 詳細比較表格 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5 text-green-600" />
            詳細比較分析
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>競爭對手</TableHead>
                  <TableHead>網域</TableHead>
                  <TableHead>提及次數</TableHead>
                  <TableHead>聲量佔比</TableHead>
                  <TableHead>平均位置</TableHead>
                  <TableHead>趨勢</TableHead>
                  <TableHead>主要關鍵字</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedCompetitors.map((competitor) => (
                  <TableRow 
                    key={competitor.id}
                    className={competitor.id === 'user' ? 'bg-blue-50' : ''}
                  >
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">
                        {competitor.name}
                        {competitor.id === 'user' && (
                          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                            您
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Globe className="w-3 h-3 text-gray-400" />
                        {competitor.domain}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{competitor.mentionCount}</div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">{competitor.shareOfVoice.toFixed(1)}%</div>
                        <Progress value={competitor.shareOfVoice} className="h-1" />
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={competitor.averagePosition <= 2 ? 'default' : 'secondary'}>
                        {competitor.averagePosition.toFixed(1)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {getTrendIcon(competitor.trend)}
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {competitor.topKeywords.slice(0, 2).map((keyword) => (
                          <Badge key={keyword} variant="outline" className="text-xs">
                            {keyword}
                          </Badge>
                        ))}
                        {competitor.topKeywords.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{competitor.topKeywords.length - 2}
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* 競爭洞察 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-yellow-600" />
              競爭優勢分析
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                <h4 className="font-medium text-green-900 mb-2">您的優勢</h4>
                <ul className="text-sm text-green-800 space-y-1">
                  <li>• 在 AI工具 領域有較高提及率</li>
                  <li>• 內容品質獲得 AI 摘要青睞</li>
                  <li>• 近期趨勢呈現上升態勢</li>
                </ul>
              </div>
              
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <h4 className="font-medium text-red-900 mb-2">改進機會</h4>
                <ul className="text-sm text-red-800 space-y-1">
                  <li>• 平均位置需要提升至前 2 名</li>
                  <li>• 聲量佔比可以進一步增加</li>
                  <li>• 需要擴展更多關鍵字領域</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5 text-blue-600" />
              策略建議
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">短期目標 (1-3個月)</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• 優化現有內容以提升 AI 摘要排名</li>
                  <li>• 增加高品質的原創內容</li>
                  <li>• 強化在主要關鍵字的權威性</li>
                </ul>
              </div>
              
              <div className="p-3 bg-purple-50 border border-purple-200 rounded-lg">
                <h4 className="font-medium text-purple-900 mb-2">長期策略 (3-12個月)</h4>
                <ul className="text-sm text-purple-800 space-y-1">
                  <li>• 建立更廣泛的主題權威性</li>
                  <li>• 開發新的內容領域和關鍵字</li>
                  <li>• 建立更多高品質的外部連結</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 添加競爭對手對話框 */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>添加競爭對手</DialogTitle>
            <DialogDescription>
              添加競爭對手網站來比較 AI 摘要表現
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="domain">網域 *</Label>
              <Input
                id="domain"
                placeholder="例如：competitor.com"
                value={newCompetitor.domain}
                onChange={(e) => setNewCompetitor({ ...newCompetitor, domain: e.target.value })}
              />
            </div>
            
            <div>
              <Label htmlFor="name">公司名稱</Label>
              <Input
                id="name"
                placeholder="例如：競爭對手公司"
                value={newCompetitor.name}
                onChange={(e) => setNewCompetitor({ ...newCompetitor, name: e.target.value })}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddDialog(false)}>
              取消
            </Button>
            <Button onClick={handleAddCompetitor}>
              添加競爭對手
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
