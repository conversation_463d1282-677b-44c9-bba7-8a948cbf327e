/**
 * 歷史趨勢組件
 * 顯示 AI 摘要監測的歷史數據和趨勢分析
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Clock,
  TrendingUp,
  TrendingDown,
  Calendar,
  BarChart3,
  LineChart,
  PieChart,
  Download,
  Filter,
} from 'lucide-react';
import { cn } from '@/lib/utils';

export default function HistoryTrends() {
  const [timeRange, setTimeRange] = useState('30d');
  const [granularity, setGranularity] = useState('daily');
  const [selectedMetric, setSelectedMetric] = useState('appearances');

  // 模擬歷史數據
  const trendData = {
    daily: [
      { date: '2024-06-01', appearances: 12, mentions: 2, searches: 18 },
      { date: '2024-06-02', appearances: 15, mentions: 1, searches: 20 },
      { date: '2024-06-03', appearances: 18, mentions: 3, searches: 22 },
      { date: '2024-06-04', appearances: 14, mentions: 2, searches: 19 },
      { date: '2024-06-05', appearances: 20, mentions: 4, searches: 25 },
      { date: '2024-06-06', appearances: 16, mentions: 2, searches: 21 },
      { date: '2024-06-07', appearances: 22, mentions: 5, searches: 28 },
    ],
    weekly: [
      { date: '2024-05-06', appearances: 89, mentions: 12, searches: 125 },
      { date: '2024-05-13', appearances: 95, mentions: 15, searches: 132 },
      { date: '2024-05-20', appearances: 102, mentions: 18, searches: 140 },
      { date: '2024-05-27', appearances: 117, mentions: 19, searches: 156 },
    ],
    monthly: [
      { date: '2024-03', appearances: 345, mentions: 45, searches: 520 },
      { date: '2024-04', appearances: 389, mentions: 52, searches: 580 },
      { date: '2024-05', appearances: 403, mentions: 64, searches: 553 },
      { date: '2024-06', appearances: 117, mentions: 19, searches: 156 },
    ],
  };

  const keywordTrends = [
    { keyword: 'AI工具', trend: 'up', change: 23.5, appearances: 45 },
    { keyword: '電動車', trend: 'up', change: 12.8, appearances: 32 },
    { keyword: '5G網路', trend: 'down', change: -8.2, appearances: 12 },
    { keyword: 'SEO優化', trend: 'stable', change: 2.1, appearances: 28 },
  ];

  const summaryTypes = [
    { type: 'AI Overview', count: 89, percentage: 67.4 },
    { type: 'Featured Snippet', count: 28, percentage: 21.2 },
    { type: 'Knowledge Panel', count: 12, percentage: 9.1 },
    { type: 'Other', count: 3, percentage: 2.3 },
  ];

  // 獲取當前數據
  const currentData = trendData[granularity as keyof typeof trendData] || trendData.daily;

  // 計算趨勢變化
  const calculateTrend = (data: any[], metric: string) => {
    if (data.length < 2) return { change: 0, direction: 'stable' };
    
    const latest = data[data.length - 1][metric];
    const previous = data[data.length - 2][metric];
    const change = ((latest - previous) / previous) * 100;
    
    return {
      change: Math.abs(change),
      direction: change > 5 ? 'up' : change < -5 ? 'down' : 'stable'
    };
  };

  const appearancesTrend = calculateTrend(currentData, 'appearances');
  const mentionsTrend = calculateTrend(currentData, 'mentions');

  // 獲取趨勢圖標
  const getTrendIcon = (direction: string) => {
    switch (direction) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-red-600" />;
      default:
        return <div className="w-4 h-4 bg-gray-300 rounded-full" />;
    }
  };

  // 格式化日期
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    switch (granularity) {
      case 'daily':
        return date.toLocaleDateString('zh-TW', { month: 'short', day: 'numeric' });
      case 'weekly':
        return `${date.toLocaleDateString('zh-TW', { month: 'short', day: 'numeric' })} 週`;
      case 'monthly':
        return date.toLocaleDateString('zh-TW', { year: 'numeric', month: 'short' });
      default:
        return dateStr;
    }
  };

  return (
    <div className="space-y-6">
      {/* 頁面標題和控制 */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">歷史趨勢分析</h2>
          <p className="text-gray-600 mt-1">
            追蹤 AI 摘要監測的長期趨勢和變化模式
          </p>
        </div>
        <div className="flex gap-3">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">7 天</SelectItem>
              <SelectItem value="30d">30 天</SelectItem>
              <SelectItem value="90d">90 天</SelectItem>
              <SelectItem value="1y">1 年</SelectItem>
            </SelectContent>
          </Select>
          <Select value={granularity} onValueChange={setGranularity}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">每日</SelectItem>
              <SelectItem value="weekly">每週</SelectItem>
              <SelectItem value="monthly">每月</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            導出數據
          </Button>
        </div>
      </div>

      {/* 趨勢概覽 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">AI 摘要出現</p>
                <div className="flex items-center gap-2 mt-1">
                  <p className="text-3xl font-bold text-gray-900">
                    {currentData[currentData.length - 1]?.appearances || 0}
                  </p>
                  {getTrendIcon(appearancesTrend.direction)}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {appearancesTrend.direction === 'up' ? '↗' : appearancesTrend.direction === 'down' ? '↘' : '→'} 
                  {appearancesTrend.change.toFixed(1)}% vs 上期
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <BarChart3 className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">用戶內容提及</p>
                <div className="flex items-center gap-2 mt-1">
                  <p className="text-3xl font-bold text-gray-900">
                    {currentData[currentData.length - 1]?.mentions || 0}
                  </p>
                  {getTrendIcon(mentionsTrend.direction)}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {mentionsTrend.direction === 'up' ? '↗' : mentionsTrend.direction === 'down' ? '↘' : '→'} 
                  {mentionsTrend.change.toFixed(1)}% vs 上期
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">提及率</p>
                <div className="flex items-center gap-2 mt-1">
                  <p className="text-3xl font-bold text-gray-900">
                    {currentData.length > 0 ? 
                      ((currentData[currentData.length - 1]?.mentions / currentData[currentData.length - 1]?.appearances) * 100).toFixed(1) : 
                      0}%
                  </p>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  用戶內容被引用比例
                </p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <PieChart className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 趨勢圖表 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <LineChart className="h-5 w-5 text-blue-600" />
              趨勢圖表
            </CardTitle>
            <Select value={selectedMetric} onValueChange={setSelectedMetric}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="appearances">AI 摘要出現</SelectItem>
                <SelectItem value="mentions">用戶內容提及</SelectItem>
                <SelectItem value="searches">總搜尋次數</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {currentData.map((item, index) => {
              const value = item[selectedMetric as keyof typeof item] as number;
              const maxValue = Math.max(...currentData.map(d => d[selectedMetric as keyof typeof d] as number));
              const percentage = (value / maxValue) * 100;
              
              return (
                <div key={item.date} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 w-20">
                    {formatDate(item.date)}
                  </span>
                  <div className="flex items-center gap-3 flex-1 ml-4">
                    <div className="flex-1">
                      <Progress value={percentage} className="h-3" />
                    </div>
                    <span className="text-sm font-medium w-12 text-right">
                      {value}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 關鍵字趨勢 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              關鍵字趨勢
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {keywordTrends.map((item) => (
                <div key={item.keyword} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div>
                      <h4 className="font-medium text-gray-900">{item.keyword}</h4>
                      <p className="text-sm text-gray-600">{item.appearances} 次出現</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className={cn(
                      'text-sm font-medium',
                      item.trend === 'up' ? 'text-green-600' :
                      item.trend === 'down' ? 'text-red-600' :
                      'text-gray-600'
                    )}>
                      {item.change > 0 ? '+' : ''}{item.change}%
                    </div>
                    {getTrendIcon(item.trend)}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* AI 摘要類型分佈 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5 text-purple-600" />
              AI 摘要類型分佈
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {summaryTypes.map((item) => (
                <div key={item.type} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={cn(
                      'w-3 h-3 rounded-full',
                      item.type === 'AI Overview' ? 'bg-blue-500' :
                      item.type === 'Featured Snippet' ? 'bg-green-500' :
                      item.type === 'Knowledge Panel' ? 'bg-purple-500' :
                      'bg-gray-400'
                    )} />
                    <span className="text-sm font-medium text-gray-900">
                      {item.type}
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-32">
                      <Progress value={item.percentage} className="h-2" />
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">{item.count}</div>
                      <div className="text-xs text-gray-600">{item.percentage}%</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 時間分析 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-orange-600" />
            時間模式分析
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-3">最佳監測時間</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">上午 9-11 點</span>
                  <Badge variant="secondary">最高活躍</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">下午 2-4 點</span>
                  <Badge variant="outline">中等活躍</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">晚上 8-10 點</span>
                  <Badge variant="outline">低活躍</Badge>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900 mb-3">週期性模式</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">週一至週三</span>
                  <Badge variant="secondary">高峰期</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">週四至週五</span>
                  <Badge variant="outline">平穩期</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">週末</span>
                  <Badge variant="outline">低谷期</Badge>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900 mb-3">季節性趨勢</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Q1 (1-3月)</span>
                  <span className="text-sm font-medium">+12.5%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Q2 (4-6月)</span>
                  <span className="text-sm font-medium">+8.3%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">預測 Q3</span>
                  <span className="text-sm font-medium text-blue-600">+15.2%</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
