/**
 * AI 摘要分析組件
 * 顯示 AI 摘要的詳細分析和統計
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  BarChart3,
  TrendingUp,
  Eye,
  Download,
  Calendar,
  Globe,
  Users,
  Target,
  Zap,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SummaryAnalyticsProps {
  onExport: () => void;
}

export default function SummaryAnalytics({ onExport }: SummaryAnalyticsProps) {
  const [timeRange, setTimeRange] = useState('30d');
  const [selectedKeyword, setSelectedKeyword] = useState('all');

  // 模擬數據
  const analyticsData = {
    summary: {
      totalSearches: 156,
      aiSummaryAppearances: 89,
      appearanceRate: 57.1,
      userMentions: 12,
      mentionRate: 13.5,
      averagePosition: 2.3,
    },
    topKeywords: [
      { keyword: 'AI工具', appearances: 45, rate: 78.9, mentions: 8 },
      { keyword: '電動車', appearances: 32, rate: 64.0, mentions: 3 },
      { keyword: '5G網路', appearances: 12, rate: 30.0, mentions: 1 },
    ],
    trends: [
      { date: '2024-06-01', appearances: 12, mentions: 2 },
      { date: '2024-06-02', appearances: 15, mentions: 1 },
      { date: '2024-06-03', appearances: 18, mentions: 3 },
      { date: '2024-06-04', appearances: 14, mentions: 2 },
      { date: '2024-06-05', appearances: 20, mentions: 4 },
    ],
  };

  return (
    <div className="space-y-6">
      {/* 頁面標題和控制 */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">AI 摘要分析</h2>
          <p className="text-gray-600 mt-1">
            深入分析 AI 摘要出現情況和用戶內容提及率
          </p>
        </div>
        <div className="flex gap-3">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">7 天</SelectItem>
              <SelectItem value="30d">30 天</SelectItem>
              <SelectItem value="90d">90 天</SelectItem>
            </SelectContent>
          </Select>
          <Select value={selectedKeyword} onValueChange={setSelectedKeyword}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有關鍵字</SelectItem>
              <SelectItem value="ai-tools">AI工具</SelectItem>
              <SelectItem value="electric-car">電動車</SelectItem>
              <SelectItem value="5g">5G網路</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={onExport} variant="outline">
            <Download className="w-4 h-4 mr-2" />
            導出報告
          </Button>
        </div>
      </div>

      {/* 核心統計 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">總搜尋次數</p>
                <p className="text-3xl font-bold text-gray-900">
                  {analyticsData.summary.totalSearches}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  過去 {timeRange === '7d' ? '7' : timeRange === '30d' ? '30' : '90'} 天
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <Eye className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">AI 摘要出現</p>
                <p className="text-3xl font-bold text-gray-900">
                  {analyticsData.summary.aiSummaryAppearances}
                </p>
                <div className="flex items-center gap-1 mt-1">
                  <span className="text-xs text-gray-500">出現率:</span>
                  <Badge variant="secondary" className="text-xs">
                    {analyticsData.summary.appearanceRate}%
                  </Badge>
                </div>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <BarChart3 className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">用戶內容提及</p>
                <p className="text-3xl font-bold text-gray-900">
                  {analyticsData.summary.userMentions}
                </p>
                <div className="flex items-center gap-1 mt-1">
                  <span className="text-xs text-gray-500">提及率:</span>
                  <Badge variant="secondary" className="text-xs">
                    {analyticsData.summary.mentionRate}%
                  </Badge>
                </div>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <Target className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">平均位置</p>
                <p className="text-3xl font-bold text-gray-900">
                  {analyticsData.summary.averagePosition}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  在搜尋結果中的位置
                </p>
              </div>
              <div className="p-3 bg-orange-100 rounded-full">
                <TrendingUp className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 關鍵字表現 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-yellow-600" />
            關鍵字表現排行
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analyticsData.topKeywords.map((item, index) => (
              <div key={item.keyword} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-4">
                  <div className={cn(
                    'flex items-center justify-center w-8 h-8 rounded-full text-sm font-bold',
                    index === 0 ? 'bg-yellow-500 text-white' :
                    index === 1 ? 'bg-gray-400 text-white' :
                    index === 2 ? 'bg-orange-500 text-white' :
                    'bg-gray-200 text-gray-600'
                  )}>
                    {index + 1}
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">{item.keyword}</h4>
                    <p className="text-sm text-gray-600">
                      {item.appearances} 次出現 • {item.mentions} 次提及
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-gray-900">
                    {item.rate}%
                  </div>
                  <div className="text-sm text-gray-600">出現率</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 趨勢圖表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              AI 摘要出現趨勢
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analyticsData.trends.map((item, index) => (
                <div key={item.date} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    {new Date(item.date).toLocaleDateString('zh-TW', { 
                      month: 'short', 
                      day: 'numeric' 
                    })}
                  </span>
                  <div className="flex items-center gap-3">
                    <div className="w-32">
                      <Progress value={(item.appearances / 25) * 100} className="h-2" />
                    </div>
                    <span className="text-sm font-medium w-8 text-right">
                      {item.appearances}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-green-600" />
              用戶提及趨勢
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analyticsData.trends.map((item, index) => (
                <div key={item.date} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    {new Date(item.date).toLocaleDateString('zh-TW', { 
                      month: 'short', 
                      day: 'numeric' 
                    })}
                  </span>
                  <div className="flex items-center gap-3">
                    <div className="w-32">
                      <Progress value={(item.mentions / 5) * 100} className="h-2" />
                    </div>
                    <span className="text-sm font-medium w-8 text-right">
                      {item.mentions}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 詳細分析 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-purple-600" />
            詳細分析報告
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-3">AI 摘要特徵分析</h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">平均字數</span>
                  <span className="font-medium">156 字</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">平均來源數</span>
                  <span className="font-medium">3.2 個</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">最常見類型</span>
                  <span className="font-medium">AI Overview</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">語言分佈</span>
                  <span className="font-medium">繁中 89%</span>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900 mb-3">用戶內容表現</h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">被引用次數</span>
                  <span className="font-medium">12 次</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">平均相似度</span>
                  <span className="font-medium">78.5%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">引用位置</span>
                  <span className="font-medium">第 2.1 位</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">引用長度</span>
                  <span className="font-medium">45 字</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
