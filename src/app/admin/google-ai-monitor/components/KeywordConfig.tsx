/**
 * 關鍵字配置組件
 * 管理監測關鍵字的新增、編輯、刪除和配置
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  MoreHorizontal,
  Play,
  Pause,
  Eye,
  Calendar,
  Globe,
  Tag,
  Filter,
  RefreshCw,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import type { 
  MonitoringKeyword, 
  MonitoringFrequency,
  CreateKeywordRequest,
  UpdateKeywordRequest 
} from '@/types/google-ai-monitor';

interface KeywordConfigProps {
  onKeywordAdded: () => void;
  onKeywordUpdated: () => void;
  onKeywordDeleted: () => void;
}

// 模擬數據
const mockKeywords: MonitoringKeyword[] = [
  {
    id: '1',
    userId: 'user1',
    keyword: 'AI工具',
    category: '科技',
    targetDomain: 'example.com',
    isActive: true,
    monitoringFrequency: 'daily',
    lastMonitoredAt: new Date(Date.now() - 3600000),
    nextMonitoringAt: new Date(Date.now() + 3600000),
    monitoringConfig: {},
    createdAt: new Date(Date.now() - 86400000),
    updatedAt: new Date(Date.now() - 3600000),
  },
  {
    id: '2',
    userId: 'user1',
    keyword: '電動車',
    category: '汽車',
    targetDomain: 'example.com',
    isActive: true,
    monitoringFrequency: 'weekly',
    lastMonitoredAt: new Date(Date.now() - 86400000),
    nextMonitoringAt: new Date(Date.now() + 86400000 * 6),
    monitoringConfig: {},
    createdAt: new Date(Date.now() - 86400000 * 7),
    updatedAt: new Date(Date.now() - 86400000),
  },
];

export default function KeywordConfig({
  onKeywordAdded,
  onKeywordUpdated,
  onKeywordDeleted,
}: KeywordConfigProps) {
  const [keywords, setKeywords] = useState<MonitoringKeyword[]>(mockKeywords);
  const [isLoading, setIsLoading] = useState(false);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editingKeyword, setEditingKeyword] = useState<MonitoringKeyword | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // 表單狀態
  const [formData, setFormData] = useState<CreateKeywordRequest>({
    keyword: '',
    category: '',
    targetDomain: '',
    monitoringFrequency: 'daily',
    monitoringConfig: {},
  });

  // 載入關鍵字列表
  const loadKeywords = async () => {
    try {
      setIsLoading(true);
      // const response = await fetch('/api/google-ai-monitor/keywords');
      // const data = await response.json();
      // setKeywords(data.keywords);
      
      // 模擬載入延遲
      await new Promise(resolve => setTimeout(resolve, 500));
      setKeywords(mockKeywords);
    } catch (error) {
      console.error('載入關鍵字失敗:', error);
      toast.error('載入關鍵字失敗');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadKeywords();
  }, []);

  // 過濾關鍵字
  const filteredKeywords = keywords.filter(keyword => {
    const matchesSearch = keyword.keyword.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (keyword.category?.toLowerCase().includes(searchTerm.toLowerCase()) || false);
    const matchesCategory = categoryFilter === 'all' || keyword.category === categoryFilter;
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'active' && keyword.isActive) ||
                         (statusFilter === 'inactive' && !keyword.isActive);
    
    return matchesSearch && matchesCategory && matchesStatus;
  });

  // 獲取所有分類
  const categories = Array.from(new Set(keywords.map(k => k.category).filter(Boolean)));

  // 添加關鍵字
  const handleAddKeyword = async () => {
    try {
      if (!formData.keyword.trim()) {
        toast.error('請輸入關鍵字');
        return;
      }

      setIsLoading(true);
      
      // const response = await fetch('/api/google-ai-monitor/keywords', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(formData),
      // });

      // 模擬添加
      const newKeyword: MonitoringKeyword = {
        id: Date.now().toString(),
        userId: 'user1',
        ...formData,
        isActive: true,
        lastMonitoredAt: undefined,
        nextMonitoringAt: new Date(Date.now() + 3600000),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      setKeywords([...keywords, newKeyword]);
      setShowAddDialog(false);
      setFormData({
        keyword: '',
        category: '',
        targetDomain: '',
        monitoringFrequency: 'daily',
        monitoringConfig: {},
      });
      
      toast.success('關鍵字已添加');
      onKeywordAdded();
    } catch (error) {
      console.error('添加關鍵字失敗:', error);
      toast.error('添加關鍵字失敗');
    } finally {
      setIsLoading(false);
    }
  };

  // 編輯關鍵字
  const handleEditKeyword = async () => {
    try {
      if (!editingKeyword || !formData.keyword.trim()) {
        return;
      }

      setIsLoading(true);
      
      // const response = await fetch(`/api/google-ai-monitor/keywords/${editingKeyword.id}`, {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(formData),
      // });

      // 模擬更新
      const updatedKeywords = keywords.map(k => 
        k.id === editingKeyword.id 
          ? { ...k, ...formData, updatedAt: new Date() }
          : k
      );
      
      setKeywords(updatedKeywords);
      setShowEditDialog(false);
      setEditingKeyword(null);
      
      toast.success('關鍵字已更新');
      onKeywordUpdated();
    } catch (error) {
      console.error('更新關鍵字失敗:', error);
      toast.error('更新關鍵字失敗');
    } finally {
      setIsLoading(false);
    }
  };

  // 刪除關鍵字
  const handleDeleteKeyword = async (keywordId: string) => {
    try {
      setIsLoading(true);
      
      // const response = await fetch(`/api/google-ai-monitor/keywords/${keywordId}`, {
      //   method: 'DELETE',
      // });

      // 模擬刪除
      const updatedKeywords = keywords.filter(k => k.id !== keywordId);
      setKeywords(updatedKeywords);
      
      toast.success('關鍵字已刪除');
      onKeywordDeleted();
    } catch (error) {
      console.error('刪除關鍵字失敗:', error);
      toast.error('刪除關鍵字失敗');
    } finally {
      setIsLoading(false);
    }
  };

  // 切換關鍵字狀態
  const handleToggleKeyword = async (keywordId: string, isActive: boolean) => {
    try {
      // const response = await fetch(`/api/google-ai-monitor/keywords/${keywordId}`, {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ isActive }),
      // });

      // 模擬切換
      const updatedKeywords = keywords.map(k => 
        k.id === keywordId 
          ? { ...k, isActive, updatedAt: new Date() }
          : k
      );
      
      setKeywords(updatedKeywords);
      toast.success(isActive ? '監測已啟用' : '監測已暫停');
    } catch (error) {
      console.error('切換關鍵字狀態失敗:', error);
      toast.error('操作失敗');
    }
  };

  // 立即執行監測
  const handleExecuteNow = async (keywordId: string) => {
    try {
      setIsLoading(true);
      
      // const response = await fetch(`/api/google-ai-monitor/keywords/${keywordId}/execute`, {
      //   method: 'POST',
      // });

      toast.success('監測任務已啟動');
    } catch (error) {
      console.error('執行監測失敗:', error);
      toast.error('執行監測失敗');
    } finally {
      setIsLoading(false);
    }
  };

  // 開始編輯
  const startEdit = (keyword: MonitoringKeyword) => {
    setEditingKeyword(keyword);
    setFormData({
      keyword: keyword.keyword,
      category: keyword.category || '',
      targetDomain: keyword.targetDomain || '',
      monitoringFrequency: keyword.monitoringFrequency,
      monitoringConfig: keyword.monitoringConfig,
    });
    setShowEditDialog(true);
  };

  // 獲取頻率顯示文字
  const getFrequencyText = (frequency: MonitoringFrequency) => {
    switch (frequency) {
      case 'daily': return '每日';
      case 'weekly': return '每週';
      case 'monthly': return '每月';
      default: return frequency;
    }
  };

  return (
    <div className="space-y-6">
      {/* 頁面標題和操作 */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">關鍵字管理</h2>
          <p className="text-gray-600 mt-1">
            管理監測關鍵字，配置監測頻率和目標網域
          </p>
        </div>
        <div className="flex gap-3">
          <Button 
            onClick={loadKeywords} 
            disabled={isLoading}
            variant="outline"
          >
            <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
            刷新
          </Button>
          <Button 
            onClick={() => setShowAddDialog(true)}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Plus className="w-4 h-4 mr-2" />
            新增關鍵字
          </Button>
        </div>
      </div>

      {/* 搜尋和篩選 */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="搜尋關鍵字或分類..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="選擇分類" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有分類</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-32">
                <SelectValue placeholder="狀態" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部</SelectItem>
                <SelectItem value="active">啟用</SelectItem>
                <SelectItem value="inactive">停用</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* 關鍵字列表 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Tag className="w-5 h-5" />
            關鍵字列表 ({filteredKeywords.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>關鍵字</TableHead>
                  <TableHead>分類</TableHead>
                  <TableHead>目標網域</TableHead>
                  <TableHead>監測頻率</TableHead>
                  <TableHead>狀態</TableHead>
                  <TableHead>最後監測</TableHead>
                  <TableHead>下次監測</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredKeywords.map((keyword) => (
                  <TableRow key={keyword.id}>
                    <TableCell className="font-medium">
                      {keyword.keyword}
                    </TableCell>
                    <TableCell>
                      {keyword.category && (
                        <Badge variant="outline">{keyword.category}</Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      {keyword.targetDomain && (
                        <div className="flex items-center gap-1">
                          <Globe className="w-3 h-3 text-gray-400" />
                          <span className="text-sm">{keyword.targetDomain}</span>
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">
                        {getFrequencyText(keyword.monitoringFrequency)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={keyword.isActive}
                          onCheckedChange={(checked) => handleToggleKeyword(keyword.id, checked)}
                          size="sm"
                        />
                        <span className={cn(
                          'text-xs',
                          keyword.isActive ? 'text-green-600' : 'text-gray-400'
                        )}>
                          {keyword.isActive ? '啟用' : '停用'}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {keyword.lastMonitoredAt ? (
                        <div className="text-sm text-gray-600">
                          {keyword.lastMonitoredAt.toLocaleString('zh-TW', {
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit',
                          })}
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm">未執行</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {keyword.nextMonitoringAt && keyword.isActive ? (
                        <div className="text-sm text-gray-600">
                          {keyword.nextMonitoringAt.toLocaleString('zh-TW', {
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit',
                          })}
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm">--</span>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => startEdit(keyword)}>
                            <Edit className="w-4 h-4 mr-2" />
                            編輯
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleExecuteNow(keyword.id)}>
                            <Play className="w-4 h-4 mr-2" />
                            立即執行
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Eye className="w-4 h-4 mr-2" />
                            查看結果
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDeleteKeyword(keyword.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            刪除
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            
            {filteredKeywords.length === 0 && (
              <div className="text-center py-12">
                <Search className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {searchTerm || categoryFilter !== 'all' || statusFilter !== 'all' 
                    ? '沒有找到符合條件的關鍵字' 
                    : '還沒有監測關鍵字'}
                </h3>
                <p className="text-gray-600 mb-4">
                  {searchTerm || categoryFilter !== 'all' || statusFilter !== 'all'
                    ? '請嘗試調整搜尋條件或篩選器'
                    : '開始添加關鍵字來監測 Google AI 摘要'}
                </p>
                {(!searchTerm && categoryFilter === 'all' && statusFilter === 'all') && (
                  <Button onClick={() => setShowAddDialog(true)}>
                    <Plus className="w-4 h-4 mr-2" />
                    新增第一個關鍵字
                  </Button>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 新增關鍵字對話框 */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>新增監測關鍵字</DialogTitle>
            <DialogDescription>
              添加新的關鍵字來監測其在 Google AI 摘要中的表現
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="keyword">關鍵字 *</Label>
              <Input
                id="keyword"
                placeholder="例如：AI工具"
                value={formData.keyword}
                onChange={(e) => setFormData({ ...formData, keyword: e.target.value })}
              />
            </div>
            
            <div>
              <Label htmlFor="category">分類</Label>
              <Input
                id="category"
                placeholder="例如：科技"
                value={formData.category}
                onChange={(e) => setFormData({ ...formData, category: e.target.value })}
              />
            </div>
            
            <div>
              <Label htmlFor="targetDomain">目標網域</Label>
              <Input
                id="targetDomain"
                placeholder="例如：example.com"
                value={formData.targetDomain}
                onChange={(e) => setFormData({ ...formData, targetDomain: e.target.value })}
              />
            </div>
            
            <div>
              <Label htmlFor="frequency">監測頻率</Label>
              <Select 
                value={formData.monitoringFrequency} 
                onValueChange={(value: MonitoringFrequency) => 
                  setFormData({ ...formData, monitoringFrequency: value })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">每日</SelectItem>
                  <SelectItem value="weekly">每週</SelectItem>
                  <SelectItem value="monthly">每月</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddDialog(false)}>
              取消
            </Button>
            <Button onClick={handleAddKeyword} disabled={isLoading}>
              {isLoading ? '添加中...' : '添加關鍵字'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 編輯關鍵字對話框 */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>編輯監測關鍵字</DialogTitle>
            <DialogDescription>
              修改關鍵字的監測配置
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-keyword">關鍵字 *</Label>
              <Input
                id="edit-keyword"
                value={formData.keyword}
                onChange={(e) => setFormData({ ...formData, keyword: e.target.value })}
              />
            </div>
            
            <div>
              <Label htmlFor="edit-category">分類</Label>
              <Input
                id="edit-category"
                value={formData.category}
                onChange={(e) => setFormData({ ...formData, category: e.target.value })}
              />
            </div>
            
            <div>
              <Label htmlFor="edit-targetDomain">目標網域</Label>
              <Input
                id="edit-targetDomain"
                value={formData.targetDomain}
                onChange={(e) => setFormData({ ...formData, targetDomain: e.target.value })}
              />
            </div>
            
            <div>
              <Label htmlFor="edit-frequency">監測頻率</Label>
              <Select 
                value={formData.monitoringFrequency} 
                onValueChange={(value: MonitoringFrequency) => 
                  setFormData({ ...formData, monitoringFrequency: value })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">每日</SelectItem>
                  <SelectItem value="weekly">每週</SelectItem>
                  <SelectItem value="monthly">每月</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              取消
            </Button>
            <Button onClick={handleEditKeyword} disabled={isLoading}>
              {isLoading ? '更新中...' : '更新關鍵字'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
