/**
 * 台灣 Google AI 摘要監測器 - 主頁面
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Search,
  Plus,
  Settings,
  TrendingUp,
  Eye,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  Users,
  Globe,
  Zap,
  RefreshCw,
  Download,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

// 組件導入
import MonitorDashboard from './components/MonitorDashboard';
import KeywordConfig from './components/KeywordConfig';
import SummaryAnalytics from './components/SummaryAnalytics';
import CompetitorComparison from './components/CompetitorComparison';
import HistoryTrends from './components/HistoryTrends';

// 類型導入
import type { 
  DashboardData, 
  MonitoringStatus,
  MonitoringKeyword 
} from '@/types/google-ai-monitor';

// 模擬數據
const mockDashboardData: DashboardData = {
  overview: {
    totalKeywords: 12,
    activeKeywords: 8,
    totalSearchesToday: 24,
    aiSummaryRate: 67.5,
    userMentionRate: 12.8,
  },
  recentActivity: {
    recentSummaries: [],
    recentMentions: [],
    recentTasks: [],
  },
  trends: {
    summaryTrend: [],
    mentionTrend: [],
    topKeywords: ['AI工具', '電動車', '5G網路'],
  },
  alerts: [
    {
      type: 'info',
      message: '今日已完成 24 次搜尋監測',
      timestamp: new Date(),
    },
    {
      type: 'warning',
      message: 'API 配額使用率達到 75%',
      timestamp: new Date(),
    },
  ],
};

const mockMonitoringStatus: MonitoringStatus = {
  isRunning: true,
  activeTasks: 2,
  pendingTasks: 5,
  completedToday: 24,
  failedToday: 1,
  nextScheduledTask: new Date(Date.now() + 3600000), // 1小時後
  apiQuotaUsed: 75,
  apiQuotaRemaining: 25,
};

export default function GoogleAIMonitorPage() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [isLoading, setIsLoading] = useState(false);
  const [dashboardData, setDashboardData] = useState<DashboardData>(mockDashboardData);
  const [monitoringStatus, setMonitoringStatus] = useState<MonitoringStatus>(mockMonitoringStatus);
  const [showKeywordDialog, setShowKeywordDialog] = useState(false);

  // 載入數據
  useEffect(() => {
    loadDashboardData();
    loadMonitoringStatus();
    
    // 設置定期刷新
    const interval = setInterval(() => {
      loadMonitoringStatus();
    }, 30000); // 每30秒刷新狀態

    return () => clearInterval(interval);
  }, []);

  // 載入儀表板數據
  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      // const response = await fetch('/api/google-ai-monitor/dashboard');
      // const data = await response.json();
      // setDashboardData(data);
      
      // 模擬載入延遲
      await new Promise(resolve => setTimeout(resolve, 1000));
      setDashboardData(mockDashboardData);
    } catch (error) {
      console.error('載入儀表板數據失敗:', error);
      toast.error('載入數據失敗，請稍後重試');
    } finally {
      setIsLoading(false);
    }
  };

  // 載入監測狀態
  const loadMonitoringStatus = async () => {
    try {
      // const response = await fetch('/api/google-ai-monitor/status');
      // const status = await response.json();
      // setMonitoringStatus(status);
      
      setMonitoringStatus(mockMonitoringStatus);
    } catch (error) {
      console.error('載入監測狀態失敗:', error);
    }
  };

  // 開始監測
  const handleStartMonitoring = async () => {
    try {
      setIsLoading(true);
      // await fetch('/api/google-ai-monitor/start', { method: 'POST' });
      
      toast.success('監測已開始');
      await loadMonitoringStatus();
    } catch (error) {
      console.error('啟動監測失敗:', error);
      toast.error('啟動監測失敗');
    } finally {
      setIsLoading(false);
    }
  };

  // 停止監測
  const handleStopMonitoring = async () => {
    try {
      setIsLoading(true);
      // await fetch('/api/google-ai-monitor/stop', { method: 'POST' });
      
      toast.success('監測已停止');
      await loadMonitoringStatus();
    } catch (error) {
      console.error('停止監測失敗:', error);
      toast.error('停止監測失敗');
    } finally {
      setIsLoading(false);
    }
  };

  // 刷新數據
  const handleRefresh = async () => {
    await Promise.all([
      loadDashboardData(),
      loadMonitoringStatus(),
    ]);
    toast.success('數據已刷新');
  };

  // 導出報告
  const handleExportReport = async () => {
    try {
      setIsLoading(true);
      // const response = await fetch('/api/google-ai-monitor/export', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ format: 'pdf', dateRange: '30d' }),
      // });
      
      toast.success('報告導出成功');
    } catch (error) {
      console.error('導出報告失敗:', error);
      toast.error('導出報告失敗');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8">
        {/* 頁面標題 */}
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
          <div>
            <div className="flex items-center gap-3 mb-3">
              <div className="p-3 rounded-xl bg-gradient-to-r from-blue-600 to-purple-600 shadow-lg">
                <Search className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl sm:text-4xl font-bold text-gray-900">
                  台灣 Google AI 摘要監測器
                </h1>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                    BETA
                  </Badge>
                  <span className="text-sm text-gray-600">
                    專為 google.com.tw AI 摘要設計
                  </span>
                </div>
              </div>
            </div>
            <p className="text-gray-600 text-lg max-w-2xl">
              即時監測品牌、產品與服務在 Google AI 摘要中的表現，掌握 AI 搜尋時代的競爭優勢
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3">
            <Button 
              onClick={handleRefresh} 
              disabled={isLoading} 
              variant="outline" 
              size="lg"
            >
              <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
              刷新數據
            </Button>
            <Button
              onClick={handleExportReport}
              variant="outline"
              size="lg"
            >
              <Download className="w-4 h-4 mr-2" />
              導出報告
            </Button>
            <Button
              onClick={() => setShowKeywordDialog(true)}
              variant="outline"
              size="lg"
            >
              <Plus className="w-4 h-4 mr-2" />
              新增關鍵字
            </Button>
            {monitoringStatus.isRunning ? (
              <Button
                onClick={handleStopMonitoring}
                disabled={isLoading}
                variant="destructive"
                size="lg"
              >
                <AlertTriangle className="w-4 h-4 mr-2" />
                停止監測
              </Button>
            ) : (
              <Button
                onClick={handleStartMonitoring}
                disabled={isLoading}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
                size="lg"
              >
                <Zap className="w-4 h-4 mr-2" />
                開始監測
              </Button>
            )}
          </div>
        </div>

        {/* 監測狀態卡片 */}
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="pt-6">
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
              <div className="text-center">
                <div className={cn(
                  'inline-flex items-center justify-center w-3 h-3 rounded-full mb-2',
                  monitoringStatus.isRunning ? 'bg-green-500' : 'bg-red-500'
                )}>
                </div>
                <div className="text-sm font-medium">
                  {monitoringStatus.isRunning ? '運行中' : '已停止'}
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {monitoringStatus.activeTasks}
                </div>
                <div className="text-sm text-gray-600">執行中任務</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {monitoringStatus.pendingTasks}
                </div>
                <div className="text-sm text-gray-600">等待中任務</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {monitoringStatus.completedToday}
                </div>
                <div className="text-sm text-gray-600">今日完成</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {monitoringStatus.failedToday}
                </div>
                <div className="text-sm text-gray-600">今日失敗</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {monitoringStatus.apiQuotaUsed}%
                </div>
                <div className="text-sm text-gray-600">API 配額</div>
              </div>
              <div className="text-center">
                <div className="text-sm font-medium text-gray-900">
                  {monitoringStatus.nextScheduledTask ? 
                    new Date(monitoringStatus.nextScheduledTask).toLocaleTimeString('zh-TW', {
                      hour: '2-digit',
                      minute: '2-digit'
                    }) : 
                    '--:--'
                  }
                </div>
                <div className="text-sm text-gray-600">下次執行</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 主要內容標籤頁 */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="dashboard" className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4" />
              儀表板
            </TabsTrigger>
            <TabsTrigger value="keywords" className="flex items-center gap-2">
              <Search className="w-4 h-4" />
              關鍵字管理
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center gap-2">
              <TrendingUp className="w-4 h-4" />
              分析報告
            </TabsTrigger>
            <TabsTrigger value="competitors" className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              競爭對手
            </TabsTrigger>
            <TabsTrigger value="trends" className="flex items-center gap-2">
              <Clock className="w-4 h-4" />
              歷史趨勢
            </TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard" className="space-y-6">
            <MonitorDashboard 
              data={dashboardData}
              status={monitoringStatus}
              isLoading={isLoading}
              onRefresh={handleRefresh}
            />
          </TabsContent>

          <TabsContent value="keywords" className="space-y-6">
            <KeywordConfig 
              onKeywordAdded={() => {
                loadDashboardData();
                toast.success('關鍵字已添加');
              }}
              onKeywordUpdated={() => {
                loadDashboardData();
                toast.success('關鍵字已更新');
              }}
              onKeywordDeleted={() => {
                loadDashboardData();
                toast.success('關鍵字已刪除');
              }}
            />
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <SummaryAnalytics 
              onExport={handleExportReport}
            />
          </TabsContent>

          <TabsContent value="competitors" className="space-y-6">
            <CompetitorComparison />
          </TabsContent>

          <TabsContent value="trends" className="space-y-6">
            <HistoryTrends />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
