'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Eye, 
  EyeOff, 
  Loader2, 
  Shield, 
  Mail, 
  Lock,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { TokenManager } from '@/lib/api/client';

export default function LoginPage() {
  const router = useRouter();
  const { login, isAuthenticated, isLoading: authLoading, hasRole, user } = useAuth();
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // 檢查是否已登入
  useEffect(() => {
    if (!authLoading && isAuthenticated) {
      if (hasRole('admin')) {
        router.push('/admin');
      } else {
        router.push('/dashboard');
      }
    }
  }, [isAuthenticated, authLoading, hasRole, router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // 清除錯誤信息
    if (error) {
      setError('');
    }
  };

  // 處理登入成功後的重定向
  const handleLoginSuccess = async () => {
    console.log('✅ 登入成功，準備重定向...');

    // 等待一小段時間確保 token 已經儲存
    await new Promise(resolve => setTimeout(resolve, 100));

    // 從 token 中解析用戶角色
    const token = TokenManager.getAccessToken();
    if (token) {
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        const userRole = payload.role;
        console.log('🎭 用戶角色:', userRole);

        // 顯示成功訊息
        toast.success(`歡迎回來，${payload.email}！正在重定向...`);

        // 根據角色進行重定向
        if (userRole === 'admin') {
          console.log('👑 管理員角色，重定向到 /admin');

          // 使用 router.push 而不是 replace，確保正確的導航歷史
          await router.push('/admin');

          // 如果 Next.js 路由失敗，使用備用方案
          setTimeout(() => {
            if (window.location.pathname === '/auth/login') {
              console.log('⚠️ Next.js 導航未成功，使用直接跳轉');
              window.location.href = '/admin';
            }
          }, 1000);
        } else {
          console.log('👤 普通用戶，重定向到 /dashboard');

          await router.push('/dashboard');

          setTimeout(() => {
            if (window.location.pathname === '/auth/login') {
              console.log('⚠️ Next.js 導航未成功，使用直接跳轉');
              window.location.href = '/dashboard';
            }
          }, 1000);
        }
      } catch (tokenError) {
        console.error('Token 解析失敗:', tokenError);
        toast.error('Token 解析失敗，重定向到儀表板');

        // 回退到默認重定向
        await router.push('/dashboard');
        setTimeout(() => {
          if (window.location.pathname === '/auth/login') {
            window.location.href = '/dashboard';
          }
        }, 1000);
      }
    } else {
      console.log('⚠️ 未找到 token，重定向到 dashboard');
      toast.warning('認證信息不完整，重定向到儀表板');

      await router.push('/dashboard');
      setTimeout(() => {
        if (window.location.pathname === '/auth/login') {
          window.location.href = '/dashboard';
        }
      }, 1000);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.email || !formData.password) {
      setError('請填寫所有必填欄位');
      return;
    }

    console.log('🔍 開始普通登入流程...', { email: formData.email });
    setIsLoading(true);
    setError('');

    try {
      console.log('📡 調用登入 API...');
      const success = await login({
        email: formData.email,
        password: formData.password
      });
      console.log('📊 登入結果:', success);

      if (success) {
        await handleLoginSuccess();
      } else {
        console.log('❌ 登入失敗');
        setError('登入失敗，請檢查您的電子郵件和密碼');
      }
    } catch (error) {
      console.error('💥 登入過程中發生錯誤:', error);
      const errorMessage = error instanceof Error ? error.message : '登入過程中發生錯誤';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDevLogin = async () => {
    setIsLoading(true);
    setError('');
    
    try {
      // 在開發模式下，直接跳轉到管理台
      if (process.env.NODE_ENV === 'development') {
        router.push('/admin');
      } else {
        const success = await login({ 
          email: '<EMAIL>', 
          password: 'admin123' 
        });
        if (success) {
          router.push('/admin');
        }
      }
    } catch (error) {
      setError('開發登入失敗');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 px-4 pt-20">
      <div className="w-full max-w-md">
        {/* Logo 和標題 */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-600 rounded-full mb-4">
            <Shield className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900">AI SEO 優化</h1>
          <p className="text-gray-600 mt-2">管理員後台登入</p>
        </div>

        {/* 登入表單 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-center">歡迎回來</CardTitle>
            <CardDescription className="text-center">
              請使用您的帳號登入管理員後台
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form id="login-form" onSubmit={handleSubmit} className="space-y-4">
              {/* 錯誤提示 */}
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {/* 電子郵件 */}
              <div className="space-y-2">
                <Label htmlFor="email">電子郵件</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="請輸入您的電子郵件"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="pl-10"
                    required
                    disabled={isLoading}
                  />
                </div>
              </div>

              {/* 密碼 */}
              <div className="space-y-2">
                <Label htmlFor="password">密碼</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="請輸入您的密碼"
                    value={formData.password}
                    onChange={handleInputChange}
                    className="pl-10 pr-10"
                    required
                    disabled={isLoading}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                    disabled={isLoading}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
              </div>

              {/* 登入按鈕 */}
              <Button 
                type="submit" 
                className="w-full" 
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    登入中...
                  </>
                ) : (
                  '登入'
                )}
              </Button>

              {/* 開發模式快速登入 */}
              {process.env.NODE_ENV === 'development' && (
                <div className="pt-4 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={handleDevLogin}
                    disabled={isLoading}
                    className="w-full flex justify-center py-2 px-4 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-gray-50 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                  >
                    🔧 開發模式快速登入
                  </button>
                  <p className="mt-2 text-xs text-gray-500 text-center">
                    開發環境專用，將跳過認證直接進入管理台
                  </p>
                </div>
              )}
            </form>

            {/* 其他選項 */}
            <div className="mt-6 text-center space-y-2">
              <Link 
                href="/auth/forgot-password" 
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                忘記密碼？
              </Link>
              <div className="text-sm text-gray-600">
                還沒有帳號？{' '}
                <Link 
                  href="/auth/register" 
                  className="text-blue-600 hover:text-blue-800"
                >
                  立即註冊
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 示範帳號信息 */}
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-medium text-blue-900 mb-2">示範帳號</h3>
          <div className="text-sm text-blue-700 space-y-1">
            <p><strong>管理員：</strong> <EMAIL></p>
            <p><strong>密碼：</strong> demo123456</p>
          </div>
        </div>

        {/* 版權信息 */}
        <div className="mt-8 text-center text-sm text-gray-500">
          © 2025 AI SEO 優化王. 版權所有.
        </div>
      </div>
    </div>
  );
}
