'use client';

import React from 'react';
import RealTimeChart from '@/components/visualization/RealTimeChart';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function RealtimeChartsDemo() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          即時數據可視化演示
        </h1>
        <p className="text-lg text-gray-600">
          展示使用 D3.js 和 WebSocket 的即時數據可視化組件
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* SEO 分析進度 */}
        <Card>
          <CardHeader>
            <CardTitle>SEO 分析進度</CardTitle>
            <CardDescription>
              即時監控 SEO 分析任務的處理進度
            </CardDescription>
          </CardHeader>
          <CardContent>
            <RealTimeChart
              title="SEO 分析進度"
              topic="seo-analysis"
              chartType="line"
              color="#3b82f6"
              width={600}
              height={300}
              maxDataPoints={50}
              refreshInterval={2000}
            />
          </CardContent>
        </Card>

        {/* 關鍵字排名變化 */}
        <Card>
          <CardHeader>
            <CardTitle>關鍵字排名變化</CardTitle>
            <CardDescription>
              追蹤關鍵字在搜索引擎中的排名變化
            </CardDescription>
          </CardHeader>
          <CardContent>
            <RealTimeChart
              title="關鍵字排名"
              topic="keyword-ranking"
              chartType="area"
              color="#10b981"
              width={600}
              height={300}
              maxDataPoints={100}
              refreshInterval={3000}
            />
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* 網站流量統計 */}
        <Card>
          <CardHeader>
            <CardTitle>網站流量統計</CardTitle>
            <CardDescription>
              即時網站訪問量和用戶行為數據
            </CardDescription>
          </CardHeader>
          <CardContent>
            <RealTimeChart
              title="網站流量"
              topic="website-traffic"
              chartType="bar"
              color="#f59e0b"
              width={600}
              height={300}
              maxDataPoints={30}
              refreshInterval={5000}
            />
          </CardContent>
        </Card>

        {/* 系統性能監控 */}
        <Card>
          <CardHeader>
            <CardTitle>系統性能監控</CardTitle>
            <CardDescription>
              監控系統 CPU、內存和網絡使用情況
            </CardDescription>
          </CardHeader>
          <CardContent>
            <RealTimeChart
              title="系統性能"
              topic="seo-analysis"
              chartType="scatter"
              color="#8b5cf6"
              width={600}
              height={300}
              maxDataPoints={60}
              refreshInterval={1000}
              enableZoom={true}
            />
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 gap-8">
        {/* 競爭對手分析 */}
        <Card>
          <CardHeader>
            <CardTitle>競爭對手分析</CardTitle>
            <CardDescription>
              實時比較競爭對手的 SEO 表現和市場趨勢
            </CardDescription>
          </CardHeader>
          <CardContent>
            <RealTimeChart
              title="競爭對手分析"
              topic="competitor-analysis"
              chartType="line"
              color="#ef4444"
              width={1200}
              height={400}
              maxDataPoints={200}
              refreshInterval={4000}
              enableZoom={true}
              enableBrush={true}
            />
          </CardContent>
        </Card>
      </div>

      {/* 連接狀態說明 */}
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>連接說明</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">WebSocket 連接狀態：</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center">
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                    綠色：已連接並接收實時數據
                  </li>
                  <li className="flex items-center">
                    <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                    紅色：連接中斷，正在嘗試重連
                  </li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">互動功能：</h4>
                <ul className="space-y-1 text-sm text-gray-600">
                  <li>• 滑鼠懸停查看詳細數值</li>
                  <li>• 滾輪縮放圖表（如果啟用）</li>
                  <li>• 拖拽平移視圖</li>
                  <li>• 自動更新和數據流控制</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 mb-2">技術棧：</h4>
                <div className="flex flex-wrap gap-2">
                  <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">React</span>
                  <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">D3.js</span>
                  <span className="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm">WebSocket</span>
                  <span className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">TypeScript</span>
                  <span className="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm">Tailwind CSS</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 