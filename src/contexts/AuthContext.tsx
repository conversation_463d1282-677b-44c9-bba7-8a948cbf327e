/**
 * 認證上下文
 * 提供全局的用戶認證狀態管理
 */

'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import type { ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { authApi } from '@/lib/api/auth';
import type { UserProfile, LoginRequest, RegisterRequest } from '@/lib/api/auth';
import { TokenManager } from '@/lib/api/client';

// 認證狀態類型
interface AuthState {
  user: UserProfile | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// 認證上下文類型
interface AuthContextType extends AuthState {
  login: (credentials: LoginRequest) => Promise<boolean>;
  register: (userData: RegisterRequest) => Promise<boolean>;
  logout: () => Promise<void>;
  logoutAll: () => Promise<void>;
  refreshProfile: () => Promise<void>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<boolean>;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  clearError: () => void;
}

// 創建上下文
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// 認證提供者組件
interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [state, setState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    error: null,
  });

  const router = useRouter();

  // 初始化認證狀態
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      // 🔧 開發模式：跳過認證檢查
      if (process.env.NODE_ENV === 'development') {
        console.log('🔧 開發模式：跳過認證檢查');
        setState({
          user: {
            id: 'dev-user',
            email: '<EMAIL>',
            name: 'SEO優化王',
            roles: ['admin'],
            permissions: ['*'],
            avatar: null,
            emailVerified: true,
            lastLoginAt: new Date().toISOString(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });
        return;
      }

      // 檢查是否有有效的 token
      if (!authApi.isTokenValid()) {
        setState(prev => ({ ...prev, isLoading: false }));
        return;
      }

      // 嘗試獲取用戶資料
      const response = await authApi.getProfile();
      if (response.success && response.data) {
        setState({
          user: response.data,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });
      } else {
        // token 無效，清除本地存儲
        TokenManager.clearTokens();
        setState({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });
      }
    } catch (error) {
      console.error('初始化認證狀態失敗:', error);
      
      // 🔧 開發模式：即使發生錯誤也提供默認用戶
      if (process.env.NODE_ENV === 'development') {
        console.log('🔧 開發模式：提供默認用戶以避免認證錯誤');
        setState({
          user: {
            id: 'dev-user',
            email: '<EMAIL>',
            name: 'SEO優化王',
            roles: ['admin'],
            permissions: ['*'],
            avatar: null,
            emailVerified: true,
            lastLoginAt: new Date().toISOString(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });
        return;
      }
      
      TokenManager.clearTokens();
      setState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: '認證初始化失敗',
      });
    }
  };

  const login = async (credentials: LoginRequest): Promise<boolean> => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      const response = await authApi.login(credentials);

      if (response.success && response.data) {
        // 確保狀態更新完成
        setState({
          user: response.data.user,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });

        // 等待狀態更新完成
        await new Promise(resolve => setTimeout(resolve, 50));

        console.log('🔐 認證上下文狀態已更新:', {
          user: response.data.user,
          isAuthenticated: true
        });

        return true;
      } else {
        const errorMessage = response.error?.message || '登入失敗';
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: errorMessage,
        }));
        toast.error(errorMessage);
        return false;
      }
    } catch (error) {
      const errorMessage = '登入過程中發生錯誤';
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      toast.error(errorMessage);
      return false;
    }
  };

  const register = async (userData: RegisterRequest): Promise<boolean> => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      const response = await authApi.register(userData);
      
      if (response.success && response.data) {
        setState({
          user: response.data.user,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });

        toast.success('註冊成功');
        return true;
      } else {
        const errorMessage = response.error?.message || '註冊失敗';
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: errorMessage,
        }));
        toast.error(errorMessage);
        return false;
      }
    } catch (error) {
      const errorMessage = '註冊過程中發生錯誤';
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      toast.error(errorMessage);
      return false;
    }
  };

  const logout = async (): Promise<void> => {
    try {
      await authApi.logout();
    } catch (error) {
      console.error('登出 API 調用失敗:', error);
    } finally {
      // 無論 API 調用是否成功，都清除本地狀態
      setState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      });
      
      toast.success('已登出');
      router.push('/auth/login');
    }
  };

  const logoutAll = async (): Promise<void> => {
    try {
      await authApi.logoutAll();
    } catch (error) {
      console.error('全設備登出 API 調用失敗:', error);
    } finally {
      setState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      });
      
      toast.success('已從所有設備登出');
      router.push('/auth/login');
    }
  };

  const refreshProfile = async (): Promise<void> => {
    try {
      const response = await authApi.getProfile();
      if (response.success && response.data) {
        setState(prev => ({
          ...prev,
          user: response.data!,
          error: null,
        }));
      }
    } catch (error) {
      console.error('刷新用戶資料失敗:', error);
    }
  };

  const updateProfile = async (updates: Partial<UserProfile>): Promise<boolean> => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      const response = await authApi.updateProfile(updates);
      
      if (response.success && response.data) {
        setState(prev => ({
          ...prev,
          user: response.data!,
          isLoading: false,
          error: null,
        }));

        toast.success('資料更新成功');
        return true;
      } else {
        const errorMessage = response.error?.message || '更新失敗';
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: errorMessage,
        }));
        toast.error(errorMessage);
        return false;
      }
    } catch (error) {
      const errorMessage = '更新過程中發生錯誤';
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      toast.error(errorMessage);
      return false;
    }
  };

  const hasPermission = (permission: string): boolean => {
    // 優先使用狀態中的用戶信息
    if (state.user?.role === 'admin') {
      return true; // 管理員擁有所有權限
    }
    // 回退到 token 檢查
    return authApi.hasPermission(permission);
  };

  const hasRole = (role: string): boolean => {
    // 優先使用狀態中的用戶信息
    if (state.user?.role) {
      return state.user.role === role || state.user.role === 'admin';
    }
    // 回退到 token 檢查
    return authApi.hasRole(role);
  };

  const clearError = (): void => {
    setState(prev => ({ ...prev, error: null }));
  };

  const contextValue: AuthContextType = {
    ...state,
    login,
    register,
    logout,
    logoutAll,
    refreshProfile,
    updateProfile,
    hasPermission,
    hasRole,
    clearError,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

// 使用認證上下文的 Hook
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// 認證守衛 Hook
export function useAuthGuard(redirectTo: string = '/auth/login') {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push(redirectTo);
    }
  }, [isAuthenticated, isLoading, router, redirectTo]);

  return { isAuthenticated, isLoading };
}

// 角色守衛 Hook
export function useRoleGuard(requiredRole: string, redirectTo: string = '/unauthorized') {
  const { hasRole, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && isAuthenticated && !hasRole(requiredRole)) {
      router.push(redirectTo);
    }
  }, [hasRole, requiredRole, isAuthenticated, isLoading, router, redirectTo]);

  return { hasRole: hasRole(requiredRole), isAuthenticated, isLoading };
}

// 權限守衛 Hook
export function usePermissionGuard(requiredPermission: string, redirectTo: string = '/unauthorized') {
  const { hasPermission, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && isAuthenticated && !hasPermission(requiredPermission)) {
      router.push(redirectTo);
    }
  }, [hasPermission, requiredPermission, isAuthenticated, isLoading, router, redirectTo]);

  return { hasPermission: hasPermission(requiredPermission), isAuthenticated, isLoading };
}
