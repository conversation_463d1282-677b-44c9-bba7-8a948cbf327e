/**
 * WebSocket 配置文件
 * 用於控制 WebSocket 功能的啟用/禁用
 */

// WebSocket 功能開關
export const WEBSOCKET_CONFIG = {
  // 設置為 false 可以完全禁用 WebSocket 功能
  enabled: false,
  
  // WebSocket 服務器 URL
  url: process.env.NEXT_PUBLIC_WEBSOCKET_URL || 'ws://localhost:8003/ws',
  
  // 連接選項
  options: {
    autoConnect: true,
    autoReconnect: true,
    reconnectInterval: 3000,
    maxReconnectAttempts: 5,
    showToastNotifications: false,
  },
  
  // 調試模式
  debug: process.env.NODE_ENV === 'development',
};

/**
 * 檢查 WebSocket 是否應該啟用
 */
export function isWebSocketEnabled(): boolean {
  return WEBSOCKET_CONFIG.enabled && !!WEBSOCKET_CONFIG.url;
}

/**
 * 獲取 WebSocket URL
 */
export function getWebSocketUrl(): string | null {
  return isWebSocketEnabled() ? WEBSOCKET_CONFIG.url : null;
}

/**
 * 啟用 WebSocket 功能
 * 注意：需要確保 WebSocket 服務器正在運行
 */
export function enableWebSocket(): void {
  WEBSOCKET_CONFIG.enabled = true;
  if (WEBSOCKET_CONFIG.debug) {
    console.log('WebSocket 功能已啟用');
  }
}

/**
 * 禁用 WebSocket 功能
 */
export function disableWebSocket(): void {
  WEBSOCKET_CONFIG.enabled = false;
  if (WEBSOCKET_CONFIG.debug) {
    console.log('WebSocket 功能已禁用');
  }
}

/**
 * 切換 WebSocket 功能
 */
export function toggleWebSocket(): boolean {
  WEBSOCKET_CONFIG.enabled = !WEBSOCKET_CONFIG.enabled;
  if (WEBSOCKET_CONFIG.debug) {
    console.log(`WebSocket 功能已${WEBSOCKET_CONFIG.enabled ? '啟用' : '禁用'}`);
  }
  return WEBSOCKET_CONFIG.enabled;
}
