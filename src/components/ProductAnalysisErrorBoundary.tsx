/**
 * 產品分析頁面專用錯誤邊界組件
 * 提供優雅的錯誤處理和恢復機制
 */

'use client';

import React, { Component, ReactNode } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: any;
  retryCount: number;
}

class ProductAnalysisErrorBoundary extends Component<Props, State> {
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('產品分析頁面錯誤:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // 發送錯誤報告到分析服務
    this.reportError(error, errorInfo);
  }

  private reportError = async (error: Error, errorInfo: any) => {
    try {
      await fetch('/api/analytics/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'product_analysis_error',
          message: error.message,
          stack: error.stack,
          componentStack: errorInfo.componentStack,
          url: window.location.href,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
        }),
      });
    } catch (reportError) {
      console.error('發送錯誤報告失敗:', reportError);
    }
  };

  private handleRetry = () => {
    if (this.state.retryCount < this.maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: prevState.retryCount + 1,
      }));
    }
  };

  private handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
    });
  };

  private handleGoHome = () => {
    window.location.href = '/admin';
  };

  private handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定義 fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默認錯誤 UI
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
          <Card className="w-full max-w-2xl">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                <div className="p-3 rounded-full bg-red-100">
                  <AlertTriangle className="h-8 w-8 text-red-600" />
                </div>
              </div>
              <CardTitle className="text-2xl text-gray-900">
                產品分析頁面發生錯誤
              </CardTitle>
              <p className="text-gray-600 mt-2">
                很抱歉，產品分析功能遇到了問題。我們已經記錄了這個錯誤，請嘗試以下解決方案。
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* 錯誤詳情 */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">錯誤詳情</h4>
                <p className="text-sm text-gray-600 font-mono bg-white p-3 rounded border">
                  {this.state.error?.message || '未知錯誤'}
                </p>
                {process.env.NODE_ENV === 'development' && this.state.error?.stack && (
                  <details className="mt-3">
                    <summary className="text-sm text-gray-500 cursor-pointer hover:text-gray-700">
                      查看技術詳情
                    </summary>
                    <pre className="text-xs text-gray-500 mt-2 bg-gray-100 p-2 rounded overflow-auto max-h-40">
                      {this.state.error.stack}
                    </pre>
                  </details>
                )}
              </div>

              {/* 解決方案 */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">建議的解決方案</h4>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {/* 重試按鈕 */}
                  {this.state.retryCount < this.maxRetries && (
                    <Button
                      onClick={this.handleRetry}
                      variant="outline"
                      className="flex items-center gap-2"
                    >
                      <RefreshCw className="h-4 w-4" />
                      重試 ({this.maxRetries - this.state.retryCount} 次剩餘)
                    </Button>
                  )}

                  {/* 重新載入頁面 */}
                  <Button
                    onClick={this.handleReload}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <RefreshCw className="h-4 w-4" />
                    重新載入頁面
                  </Button>

                  {/* 返回首頁 */}
                  <Button
                    onClick={this.handleGoHome}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <Home className="h-4 w-4" />
                    返回管理首頁
                  </Button>

                  {/* 重置狀態 */}
                  <Button
                    onClick={this.handleReset}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <Bug className="h-4 w-4" />
                    重置組件狀態
                  </Button>
                </div>
              </div>

              {/* 幫助信息 */}
              <div className="bg-blue-50 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">需要幫助？</h4>
                <p className="text-sm text-blue-700">
                  如果問題持續發生，請聯繫技術支援團隊。我們已經自動記錄了這個錯誤，
                  技術團隊會盡快修復。
                </p>
                <div className="mt-3 text-xs text-blue-600">
                  錯誤 ID: {Date.now().toString(36)}
                  <br />
                  時間: {new Date().toLocaleString('zh-TW')}
                </div>
              </div>

              {/* 重試次數提示 */}
              {this.state.retryCount > 0 && (
                <div className="text-center text-sm text-gray-500">
                  已重試 {this.state.retryCount} 次
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ProductAnalysisErrorBoundary;
