/**
 * SEO 分析執行器組件
 * 提供階段化的 SEO 分析執行界面
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Play,
  Pause,
  Square,
  CheckCircle,
  AlertCircle,
  Clock,
  Loader2,
  BarChart3,
  Eye,
  Zap,
  FileText,
  Users,
  Target,
  DollarSign,
  Timer,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  openaiSEOAnalysisService,
  type SEOAnalysisRequest,
  type SEOAnalysisResult,
  type AnalysisStage,
} from '@/services/openai-seo-analysis';
import { toast } from 'sonner';

interface SEOAnalysisExecutorProps {
  request: SEOAnalysisRequest;
  onComplete?: (result: SEOAnalysisResult) => void;
  onCancel?: () => void;
  className?: string;
}

const stageIcons = {
  'content-extraction': Eye,
  'keyword-analysis': Target,
  'content-quality': FileText,
  'technical-seo': BarChart3,
  'competitor-analysis': Users,
  'ai-optimization': Zap,
  'report-generation': FileText,
};

export default function SEOAnalysisExecutor({
  request,
  onComplete,
  onCancel,
  className,
}: SEOAnalysisExecutorProps) {
  const [isRunning, setIsRunning] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [stages, setStages] = useState<AnalysisStage[]>([]);
  const [currentStageIndex, setCurrentStageIndex] = useState(-1);
  const [overallProgress, setOverallProgress] = useState(0);
  const [startTime, setStartTime] = useState<Date | null>(null);
  const [estimatedEndTime, setEstimatedEndTime] = useState<Date | null>(null);
  const [apiUsage, setApiUsage] = useState({ tokensUsed: 0, requestCount: 0, estimatedCost: 0 });
  const [error, setError] = useState<string | null>(null);

  // 計算總預估時間
  const totalEstimatedTime = stages.reduce((total, stage) => total + stage.estimatedTime, 0);

  // 更新階段狀態的回調
  const handleStageUpdate = (updatedStage: AnalysisStage) => {
    setStages(prevStages => {
      const newStages = [...prevStages];
      const stageIndex = newStages.findIndex(s => s.id === updatedStage.id);
      if (stageIndex !== -1) {
        newStages[stageIndex] = { ...updatedStage };
        
        // 更新當前階段索引
        if (updatedStage.status === 'running') {
          setCurrentStageIndex(stageIndex);
        }
        
        // 計算整體進度
        const completedStages = newStages.filter(s => s.status === 'completed').length;
        const runningStage = newStages.find(s => s.status === 'running');
        const runningProgress = runningStage ? runningStage.progress / 100 : 0;
        const totalProgress = ((completedStages + runningProgress) / newStages.length) * 100;
        setOverallProgress(totalProgress);
        
        // 更新預估結束時間
        if (startTime && totalProgress > 0) {
          const elapsed = Date.now() - startTime.getTime();
          const estimatedTotal = (elapsed / totalProgress) * 100;
          setEstimatedEndTime(new Date(startTime.getTime() + estimatedTotal));
        }
      }
      return newStages;
    });

    // 更新 API 使用統計
    setApiUsage(openaiSEOAnalysisService.getApiUsage());
  };

  // 開始分析
  const startAnalysis = async () => {
    try {
      setIsRunning(true);
      setIsPaused(false);
      setError(null);
      setStartTime(new Date());
      
      // 初始化階段
      const initialStages = [
        {
          id: 'content-extraction',
          name: '網站內容抓取',
          description: '抓取並預處理網站內容',
          estimatedTime: 30,
          status: 'pending' as const,
          progress: 0,
        },
        {
          id: 'keyword-analysis',
          name: '關鍵字分析',
          description: '分析關鍵字密度和 SEO 標籤',
          estimatedTime: 45,
          status: 'pending' as const,
          progress: 0,
        },
        {
          id: 'content-quality',
          name: '內容品質評估',
          description: '評估內容品質和可讀性',
          estimatedTime: 60,
          status: 'pending' as const,
          progress: 0,
        },
        {
          id: 'technical-seo',
          name: '技術 SEO 分析',
          description: '檢查技術 SEO 要素',
          estimatedTime: 40,
          status: 'pending' as const,
          progress: 0,
        },
        {
          id: 'ai-optimization',
          name: 'AI 搜索優化',
          description: '生成 AI 搜索引擎優化建議',
          estimatedTime: 90,
          status: 'pending' as const,
          progress: 0,
        },
        {
          id: 'report-generation',
          name: '報告生成',
          description: '整理並生成完整分析報告',
          estimatedTime: 30,
          status: 'pending' as const,
          progress: 0,
        },
      ];

      // 如果包含競爭對手分析，添加額外階段
      if (request.includeCompetitorAnalysis && request.competitors?.length) {
        initialStages.splice(4, 0, {
          id: 'competitor-analysis',
          name: '競爭對手分析',
          description: '比較分析競爭對手 SEO 策略',
          estimatedTime: 120,
          status: 'pending' as const,
          progress: 0,
        });
      }

      setStages(initialStages);

      toast.success('開始執行 SEO 分析...');

      // 執行分析
      const result = await openaiSEOAnalysisService.executeFullAnalysis(
        request,
        handleStageUpdate
      );

      setIsRunning(false);
      toast.success('SEO 分析完成！');
      onComplete?.(result);

    } catch (error) {
      setIsRunning(false);
      const errorMessage = error instanceof Error ? error.message : '分析執行失敗';
      setError(errorMessage);
      toast.error(errorMessage);
    }
  };

  // 暫停分析
  const pauseAnalysis = () => {
    setIsPaused(true);
    toast.info('分析已暫停');
  };

  // 恢復分析
  const resumeAnalysis = () => {
    setIsPaused(false);
    toast.info('分析已恢復');
  };

  // 停止分析
  const stopAnalysis = () => {
    setIsRunning(false);
    setIsPaused(false);
    toast.info('分析已停止');
    onCancel?.();
  };

  // 獲取階段圖標
  const getStageIcon = (stageId: string, status: string) => {
    const IconComponent = stageIcons[stageId as keyof typeof stageIcons] || Clock;
    
    if (status === 'completed') {
      return <CheckCircle className="w-5 h-5 text-green-600" />;
    } else if (status === 'failed') {
      return <AlertCircle className="w-5 h-5 text-red-600" />;
    } else if (status === 'running') {
      return <Loader2 className="w-5 h-5 text-blue-600 animate-spin" />;
    } else {
      return <IconComponent className="w-5 h-5 text-gray-400" />;
    }
  };

  // 獲取階段狀態顏色
  const getStageStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-50 border-green-200';
      case 'running':
        return 'bg-blue-50 border-blue-200';
      case 'failed':
        return 'bg-red-50 border-red-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  // 格式化時間
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // 計算已用時間
  const getElapsedTime = () => {
    if (!startTime) return 0;
    return Math.floor((Date.now() - startTime.getTime()) / 1000);
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* 分析控制面板 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-blue-100">
                <BarChart3 className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <CardTitle className="text-xl">SEO 分析執行</CardTitle>
                <p className="text-sm text-gray-600 mt-1">
                  正在分析: {request.url}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {!isRunning ? (
                <Button onClick={startAnalysis} className="bg-green-600 hover:bg-green-700">
                  <Play className="w-4 h-4 mr-2" />
                  開始分析
                </Button>
              ) : (
                <>
                  {!isPaused ? (
                    <Button onClick={pauseAnalysis} variant="outline">
                      <Pause className="w-4 h-4 mr-2" />
                      暫停
                    </Button>
                  ) : (
                    <Button onClick={resumeAnalysis} className="bg-blue-600 hover:bg-blue-700">
                      <Play className="w-4 h-4 mr-2" />
                      恢復
                    </Button>
                  )}
                  <Button onClick={stopAnalysis} variant="destructive">
                    <Square className="w-4 h-4 mr-2" />
                    停止
                  </Button>
                </>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 整體進度 */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">整體進度</span>
              <span className="text-sm font-semibold">{Math.round(overallProgress)}%</span>
            </div>
            <Progress value={overallProgress} className="h-2" />
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>已用時間: {formatTime(getElapsedTime())}</span>
              {estimatedEndTime && (
                <span>預估完成: {estimatedEndTime.toLocaleTimeString('zh-TW')}</span>
              )}
            </div>
          </div>

          {/* 分析參數 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <Target className="w-5 h-5 text-blue-600 mx-auto mb-1" />
              <div className="text-xs text-gray-600">目標關鍵字</div>
              <div className="text-sm font-semibold">{request.targetKeywords.length}</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <Users className="w-5 h-5 text-green-600 mx-auto mb-1" />
              <div className="text-xs text-gray-600">競爭對手</div>
              <div className="text-sm font-semibold">{request.competitors?.length || 0}</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <Timer className="w-5 h-5 text-orange-600 mx-auto mb-1" />
              <div className="text-xs text-gray-600">預估時間</div>
              <div className="text-sm font-semibold">{formatTime(totalEstimatedTime)}</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <DollarSign className="w-5 h-5 text-purple-600 mx-auto mb-1" />
              <div className="text-xs text-gray-600">API 成本</div>
              <div className="text-sm font-semibold">${apiUsage.estimatedCost.toFixed(4)}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 錯誤顯示 */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <div>
                <h4 className="font-medium text-red-900">分析執行失敗</h4>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 分析階段 */}
      {stages.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">分析階段</CardTitle>
            <p className="text-sm text-gray-600">
              {stages.filter(s => s.status === 'completed').length} / {stages.length} 階段已完成
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stages.map((stage, index) => (
                <div
                  key={stage.id}
                  className={cn(
                    'p-4 rounded-lg border transition-colors',
                    getStageStatusColor(stage.status),
                    currentStageIndex === index && 'ring-2 ring-blue-500 ring-opacity-50'
                  )}
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      {getStageIcon(stage.id, stage.status)}
                      <div>
                        <h4 className="font-medium text-gray-900">{stage.name}</h4>
                        <p className="text-sm text-gray-600">{stage.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge
                        variant={
                          stage.status === 'completed'
                            ? 'default'
                            : stage.status === 'running'
                            ? 'secondary'
                            : stage.status === 'failed'
                            ? 'destructive'
                            : 'outline'
                        }
                        className={cn(
                          stage.status === 'completed' && 'bg-green-100 text-green-800',
                          stage.status === 'running' && 'bg-blue-100 text-blue-800'
                        )}
                      >
                        {stage.status === 'completed'
                          ? '已完成'
                          : stage.status === 'running'
                          ? '進行中'
                          : stage.status === 'failed'
                          ? '失敗'
                          : '等待中'}
                      </Badge>
                      <span className="text-sm text-gray-500">
                        {formatTime(stage.estimatedTime)}
                      </span>
                    </div>
                  </div>

                  {/* 階段進度條 */}
                  {stage.status === 'running' && (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-600">階段進度</span>
                        <span className="text-xs font-semibold">{stage.progress}%</span>
                      </div>
                      <Progress value={stage.progress} className="h-1" />
                    </div>
                  )}

                  {/* 階段結果預覽 */}
                  {stage.status === 'completed' && stage.result && (
                    <div className="mt-3 p-3 bg-white rounded border">
                      <h5 className="text-sm font-medium text-gray-900 mb-2">階段結果</h5>
                      <div className="text-xs text-gray-600">
                        {stage.id === 'keyword-analysis' && stage.result.seoScore && (
                          <div>SEO 評分: {stage.result.seoScore}/100</div>
                        )}
                        {stage.id === 'content-quality' && stage.result.readabilityScore && (
                          <div>可讀性評分: {stage.result.readabilityScore}/100</div>
                        )}
                        {stage.id === 'technical-seo' && stage.result.pageSpeed && (
                          <div>頁面速度評分: {stage.result.pageSpeed.score}/100</div>
                        )}
                        {stage.id === 'report-generation' && stage.result.overallScore && (
                          <div>整體評分: {stage.result.overallScore}/100</div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* 階段錯誤 */}
                  {stage.status === 'failed' && stage.error && (
                    <div className="mt-3 p-3 bg-red-50 rounded border border-red-200">
                      <h5 className="text-sm font-medium text-red-900 mb-1">錯誤信息</h5>
                      <p className="text-xs text-red-700">{stage.error}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* API 使用統計 */}
      {isRunning && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">API 使用統計</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-lg font-bold text-blue-600">{apiUsage.requestCount}</div>
                <div className="text-xs text-blue-700">API 請求次數</div>
              </div>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-lg font-bold text-green-600">{apiUsage.tokensUsed.toLocaleString()}</div>
                <div className="text-xs text-green-700">使用 Tokens</div>
              </div>
              <div className="text-center p-3 bg-purple-50 rounded-lg">
                <div className="text-lg font-bold text-purple-600">${apiUsage.estimatedCost.toFixed(4)}</div>
                <div className="text-xs text-purple-700">預估成本</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
