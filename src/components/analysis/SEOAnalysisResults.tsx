/**
 * SEO 分析結果顯示組件
 * 展示完整的 SEO 分析結果和建議
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Download,
  Share2,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Target,
  BarChart3,
  FileText,
  Users,
  Zap,
  ExternalLink,
  Copy,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { SEOAnalysisResult } from '@/services/openai-seo-analysis';
import { toast } from 'sonner';

interface SEOAnalysisResultsProps {
  result: SEOAnalysisResult;
  onExport?: (format: 'pdf' | 'excel' | 'csv') => void;
  onShare?: () => void;
  onNewAnalysis?: () => void;
  className?: string;
}

export default function SEOAnalysisResults({
  result,
  onExport,
  onShare,
  onNewAnalysis,
  className,
}: SEOAnalysisResultsProps) {
  const [activeTab, setActiveTab] = useState('overview');

  // 獲取評分顏色
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  // 獲取評分背景色
  const getScoreBgColor = (score: number) => {
    if (score >= 80) return 'bg-green-50 border-green-200';
    if (score >= 60) return 'bg-yellow-50 border-yellow-200';
    return 'bg-red-50 border-red-200';
  };

  // 獲取優先級顏色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 複製分析 ID
  const copyAnalysisId = () => {
    navigator.clipboard.writeText(result.analysisId);
    toast.success('分析 ID 已複製到剪貼板');
  };

  // 導出報告
  const handleExport = (format: 'pdf' | 'excel' | 'csv') => {
    onExport?.(format);
    toast.success(`正在生成 ${format.toUpperCase()} 報告...`);
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* 分析結果標題 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl">SEO 分析結果</CardTitle>
              <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
                <span>分析網站: {result.url}</span>
                <span>完成時間: {new Date(result.completedAt).toLocaleString('zh-TW')}</span>
                <button
                  onClick={copyAnalysisId}
                  className="flex items-center gap-1 hover:text-blue-600 transition-colors"
                >
                  <Copy className="w-3 h-3" />
                  ID: {result.analysisId.slice(-8)}
                </button>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button onClick={() => handleExport('pdf')} variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                導出 PDF
              </Button>
              <Button onClick={onShare} variant="outline" size="sm">
                <Share2 className="w-4 h-4 mr-2" />
                分享
              </Button>
              <Button onClick={onNewAnalysis} className="bg-blue-600 hover:bg-blue-700">
                新分析
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* 整體評分 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">整體評分</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className={cn('text-center p-4 rounded-lg border', getScoreBgColor(result.overallScore))}>
              <div className={cn('text-3xl font-bold', getScoreColor(result.overallScore))}>
                {result.overallScore}
              </div>
              <div className="text-sm text-gray-600 mt-1">整體評分</div>
            </div>
            <div className={cn('text-center p-4 rounded-lg border', getScoreBgColor(result.metrics.seoScore))}>
              <div className={cn('text-2xl font-bold', getScoreColor(result.metrics.seoScore))}>
                {result.metrics.seoScore}
              </div>
              <div className="text-sm text-gray-600 mt-1">SEO 評分</div>
            </div>
            <div className={cn('text-center p-4 rounded-lg border', getScoreBgColor(result.metrics.contentScore))}>
              <div className={cn('text-2xl font-bold', getScoreColor(result.metrics.contentScore))}>
                {result.metrics.contentScore}
              </div>
              <div className="text-sm text-gray-600 mt-1">內容評分</div>
            </div>
            <div className={cn('text-center p-4 rounded-lg border', getScoreBgColor(result.metrics.technicalScore))}>
              <div className={cn('text-2xl font-bold', getScoreColor(result.metrics.technicalScore))}>
                {result.metrics.technicalScore}
              </div>
              <div className="text-sm text-gray-600 mt-1">技術評分</div>
            </div>
            <div className={cn('text-center p-4 rounded-lg border', getScoreBgColor(result.metrics.readabilityScore))}>
              <div className={cn('text-2xl font-bold', getScoreColor(result.metrics.readabilityScore))}>
                {result.metrics.readabilityScore}
              </div>
              <div className="text-sm text-gray-600 mt-1">可讀性評分</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 詳細結果標籤頁 */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">概覽</TabsTrigger>
          <TabsTrigger value="keywords">關鍵字</TabsTrigger>
          <TabsTrigger value="recommendations">建議</TabsTrigger>
          <TabsTrigger value="swot">SWOT 分析</TabsTrigger>
          <TabsTrigger value="stages">階段詳情</TabsTrigger>
        </TabsList>

        {/* 概覽標籤 */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 關鍵指標 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">關鍵指標</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {Object.entries(result.metrics.keywordDensity).map(([keyword, density]) => (
                  <div key={keyword} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{keyword}</span>
                      <span className="text-sm text-gray-600">{density.toFixed(2)}%</span>
                    </div>
                    <Progress value={density * 20} className="h-2" />
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* 快速洞察 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">快速洞察</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <div>
                    <div className="font-medium text-green-900">優勢領域</div>
                    <div className="text-sm text-green-700">
                      {result.summary.strengths.length} 個強項
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-red-50 rounded-lg">
                  <AlertTriangle className="w-5 h-5 text-red-600" />
                  <div>
                    <div className="font-medium text-red-900">改進機會</div>
                    <div className="text-sm text-red-700">
                      {result.summary.weaknesses.length} 個待改進項目
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                  <TrendingUp className="w-5 h-5 text-blue-600" />
                  <div>
                    <div className="font-medium text-blue-900">成長機會</div>
                    <div className="text-sm text-blue-700">
                      {result.summary.opportunities.length} 個機會點
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 關鍵字標籤 */}
        <TabsContent value="keywords" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">關鍵字密度分析</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(result.metrics.keywordDensity).map(([keyword, density]) => (
                  <div key={keyword} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium">{keyword}</h4>
                      <div className="flex items-center gap-2">
                        <Badge variant={density >= 1 && density <= 3 ? 'default' : 'secondary'}>
                          {density.toFixed(2)}%
                        </Badge>
                        {density >= 1 && density <= 3 ? (
                          <CheckCircle className="w-4 h-4 text-green-600" />
                        ) : (
                          <AlertTriangle className="w-4 h-4 text-yellow-600" />
                        )}
                      </div>
                    </div>
                    <Progress value={Math.min(density * 20, 100)} className="h-2" />
                    <p className="text-xs text-gray-600 mt-2">
                      {density < 1
                        ? '密度偏低，建議增加關鍵字使用'
                        : density > 3
                        ? '密度偏高，可能被視為關鍵字堆砌'
                        : '密度適中，符合 SEO 最佳實踐'}
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 建議標籤 */}
        <TabsContent value="recommendations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">優化建議</CardTitle>
              <p className="text-sm text-gray-600">
                根據分析結果，以下是按優先級排序的改進建議
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {result.recommendations.map((rec, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <Badge className={getPriorityColor(rec.priority)}>
                            {rec.priority === 'high' ? '高優先級' : rec.priority === 'medium' ? '中優先級' : '低優先級'}
                          </Badge>
                          <span className="text-sm text-gray-600">{rec.category}</span>
                        </div>
                        <h4 className="font-medium text-gray-900 mb-1">{rec.title}</h4>
                        <p className="text-sm text-gray-600 mb-3">{rec.description}</p>
                        <div className="grid grid-cols-2 gap-4 text-xs">
                          <div>
                            <span className="text-gray-500">預期影響: </span>
                            <span className="font-medium">{rec.impact}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">實施難度: </span>
                            <span className="font-medium">{rec.effort}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* SWOT 分析標籤 */}
        <TabsContent value="swot" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="border-green-200">
              <CardHeader className="bg-green-50">
                <CardTitle className="text-lg text-green-900 flex items-center gap-2">
                  <CheckCircle className="w-5 h-5" />
                  優勢 (Strengths)
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-6">
                <ul className="space-y-2">
                  {result.summary.strengths.map((strength, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <div className="w-1.5 h-1.5 rounded-full bg-green-600 mt-2 flex-shrink-0" />
                      <span className="text-sm">{strength}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            <Card className="border-red-200">
              <CardHeader className="bg-red-50">
                <CardTitle className="text-lg text-red-900 flex items-center gap-2">
                  <AlertTriangle className="w-5 h-5" />
                  劣勢 (Weaknesses)
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-6">
                <ul className="space-y-2">
                  {result.summary.weaknesses.map((weakness, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <div className="w-1.5 h-1.5 rounded-full bg-red-600 mt-2 flex-shrink-0" />
                      <span className="text-sm">{weakness}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            <Card className="border-blue-200">
              <CardHeader className="bg-blue-50">
                <CardTitle className="text-lg text-blue-900 flex items-center gap-2">
                  <TrendingUp className="w-5 h-5" />
                  機會 (Opportunities)
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-6">
                <ul className="space-y-2">
                  {result.summary.opportunities.map((opportunity, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <div className="w-1.5 h-1.5 rounded-full bg-blue-600 mt-2 flex-shrink-0" />
                      <span className="text-sm">{opportunity}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            <Card className="border-yellow-200">
              <CardHeader className="bg-yellow-50">
                <CardTitle className="text-lg text-yellow-900 flex items-center gap-2">
                  <TrendingDown className="w-5 h-5" />
                  威脅 (Threats)
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-6">
                <ul className="space-y-2">
                  {result.summary.threats.map((threat, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <div className="w-1.5 h-1.5 rounded-full bg-yellow-600 mt-2 flex-shrink-0" />
                      <span className="text-sm">{threat}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 階段詳情標籤 */}
        <TabsContent value="stages" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">分析階段詳情</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {result.stages.map((stage, index) => (
                  <div key={stage.id} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{stage.name}</h4>
                      <Badge variant={stage.status === 'completed' ? 'default' : 'secondary'}>
                        {stage.status === 'completed' ? '已完成' : stage.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{stage.description}</p>
                    {stage.result && (
                      <div className="bg-gray-50 p-3 rounded text-xs">
                        <strong>階段結果:</strong> 
                        <pre className="mt-1 whitespace-pre-wrap">
                          {JSON.stringify(stage.result, null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
