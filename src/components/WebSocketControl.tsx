/**
 * WebSocket 控制組件
 * 用於管理 WebSocket 連接狀態和配置
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Wifi,
  WifiOff,
  Settings,
  AlertTriangle,
  CheckCircle,
  Info,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useWebSocket } from '@/hooks/useWebSocket';
import { 
  enableWebSocket, 
  disableWebSocket, 
  WEBSOCKET_CONFIG 
} from '@/config/websocket';

interface WebSocketControlProps {
  className?: string;
  showDetails?: boolean;
}

export default function WebSocketControl({ 
  className, 
  showDetails = true 
}: WebSocketControlProps) {
  const [localEnabled, setLocalEnabled] = useState(WEBSOCKET_CONFIG.enabled);
  const { 
    isConnected, 
    isConnecting, 
    error, 
    connect, 
    disconnect,
    isWebSocketEnabled 
  } = useWebSocket({ autoConnect: false });

  // 切換 WebSocket 功能
  const handleToggleWebSocket = (enabled: boolean) => {
    setLocalEnabled(enabled);
    
    if (enabled) {
      enableWebSocket();
      // 如果啟用，嘗試連接
      setTimeout(() => {
        connect();
      }, 100);
    } else {
      disableWebSocket();
      // 如果禁用，斷開連接
      disconnect();
    }
  };

  // 手動連接/斷開
  const handleConnectionToggle = () => {
    if (isConnected) {
      disconnect();
    } else {
      connect();
    }
  };

  // 獲取連接狀態
  const getConnectionStatus = () => {
    if (!localEnabled) {
      return { status: 'disabled', text: '已禁用', color: 'gray' };
    }
    if (isConnecting) {
      return { status: 'connecting', text: '連接中', color: 'yellow' };
    }
    if (isConnected) {
      return { status: 'connected', text: '已連接', color: 'green' };
    }
    if (error) {
      return { status: 'error', text: '連接錯誤', color: 'red' };
    }
    return { status: 'disconnected', text: '未連接', color: 'gray' };
  };

  const connectionStatus = getConnectionStatus();

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {localEnabled ? (
            <Wifi className="h-5 w-5 text-blue-600" />
          ) : (
            <WifiOff className="h-5 w-5 text-gray-400" />
          )}
          WebSocket 連接控制
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 功能開關 */}
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="websocket-enabled">啟用 WebSocket 功能</Label>
            <p className="text-sm text-gray-600">
              用於實時通知和進度更新
            </p>
          </div>
          <Switch
            id="websocket-enabled"
            checked={localEnabled}
            onCheckedChange={handleToggleWebSocket}
          />
        </div>

        {/* 連接狀態 */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">連接狀態</span>
          <div className="flex items-center gap-2">
            <Badge 
              variant={connectionStatus.color === 'green' ? 'default' : 'secondary'}
              className={cn(
                connectionStatus.color === 'green' && 'bg-green-100 text-green-800',
                connectionStatus.color === 'yellow' && 'bg-yellow-100 text-yellow-800',
                connectionStatus.color === 'red' && 'bg-red-100 text-red-800',
                connectionStatus.color === 'gray' && 'bg-gray-100 text-gray-800'
              )}
            >
              {connectionStatus.text}
            </Badge>
            {localEnabled && (
              <Button
                size="sm"
                variant="outline"
                onClick={handleConnectionToggle}
                disabled={isConnecting}
              >
                {isConnected ? '斷開' : '連接'}
              </Button>
            )}
          </div>
        </div>

        {/* 詳細信息 */}
        {showDetails && (
          <div className="space-y-3">
            {/* WebSocket URL */}
            <div className="text-sm">
              <span className="font-medium">服務器地址: </span>
              <code className="bg-gray-100 px-2 py-1 rounded text-xs">
                {WEBSOCKET_CONFIG.url}
              </code>
            </div>

            {/* 錯誤信息 */}
            {error && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  連接錯誤: {error}
                </AlertDescription>
              </Alert>
            )}

            {/* 狀態說明 */}
            {!localEnabled && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  WebSocket 功能已禁用。啟用後可以接收實時通知和進度更新。
                </AlertDescription>
              </Alert>
            )}

            {localEnabled && !isConnected && !error && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  WebSocket 服務器未運行。請確保服務器在 {WEBSOCKET_CONFIG.url} 上運行。
                  <br />
                  <span className="text-xs mt-1 block">
                    啟動命令: <code>cd websocket-server && node simple-ws.js</code>
                  </span>
                </AlertDescription>
              </Alert>
            )}

            {isConnected && (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  WebSocket 連接正常，可以接收實時更新。
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}

        {/* 配置信息 */}
        {showDetails && WEBSOCKET_CONFIG.debug && (
          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
              <Settings className="h-4 w-4" />
              調試信息
            </h4>
            <div className="text-xs space-y-1 text-gray-600">
              <div>配置啟用: {WEBSOCKET_CONFIG.enabled ? '是' : '否'}</div>
              <div>本地啟用: {localEnabled ? '是' : '否'}</div>
              <div>Hook 啟用: {isWebSocketEnabled ? '是' : '否'}</div>
              <div>自動連接: {WEBSOCKET_CONFIG.options.autoConnect ? '是' : '否'}</div>
              <div>最大重試: {WEBSOCKET_CONFIG.options.maxReconnectAttempts}</div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
