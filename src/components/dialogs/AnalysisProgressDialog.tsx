/**
 * 分析進度對話框組件
 * 顯示產品分析的實時進度和狀態
 */

'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import {
  CheckCircle,
  Clock,
  AlertCircle,
  Loader2,
  BarChart3,
  Eye,
  MessageSquare,
  Link as LinkIcon,
  X,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { AnalysisStatus } from '@/services/product-analysis';

interface AnalysisProgressDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  analysisId?: string;
  onComplete?: (results: any) => void;
  onError?: (error: string) => void;
}

const analysisSteps = [
  {
    id: 'initialization',
    title: '初始化分析',
    description: '準備分析環境和參數',
    icon: Clock,
  },
  {
    id: 'data_collection',
    title: '數據收集',
    description: '從各 AI 引擎收集回應數據',
    icon: BarChart3,
  },
  {
    id: 'visibility_analysis',
    title: '可見度分析',
    description: '分析品牌在搜索結果中的可見度',
    icon: Eye,
  },
  {
    id: 'response_analysis',
    title: '回應分析',
    description: '分析 AI 引擎的回應內容',
    icon: MessageSquare,
  },
  {
    id: 'citation_analysis',
    title: '引用分析',
    description: '檢查品牌被引用的情況',
    icon: LinkIcon,
  },
  {
    id: 'report_generation',
    title: '生成報告',
    description: '整理分析結果並生成報告',
    icon: CheckCircle,
  },
];

export default function AnalysisProgressDialog({
  open,
  onOpenChange,
  analysisId,
  onComplete,
  onError,
}: AnalysisProgressDialogProps) {
  const [status, setStatus] = useState<AnalysisStatus | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [error, setError] = useState<string | null>(null);

  // 模擬分析進度
  useEffect(() => {
    if (!open || !analysisId) return;

    let interval: NodeJS.Timeout;
    let stepIndex = 0;

    const simulateProgress = () => {
      interval = setInterval(() => {
        if (stepIndex < analysisSteps.length) {
          const progress = ((stepIndex + 1) / analysisSteps.length) * 100;
          
          setStatus({
            analysisId: analysisId,
            status: stepIndex === analysisSteps.length - 1 ? 'completed' : 'processing',
            progress: Math.round(progress),
            message: analysisSteps[stepIndex].description,
            startedAt: new Date().toISOString(),
            estimatedCompletion: new Date(Date.now() + (6 - stepIndex) * 60000).toISOString(),
          });

          setCurrentStep(stepIndex);
          stepIndex++;

          // 如果完成，觸發回調
          if (stepIndex === analysisSteps.length) {
            clearInterval(interval);
            setTimeout(() => {
              if (onComplete) {
                onComplete({
                  analysisId,
                  completedAt: new Date().toISOString(),
                  summary: '分析已完成，發現了多個優化機會',
                });
              }
            }, 1000);
          }
        }
      }, 2000); // 每2秒更新一次
    };

    // 開始模擬
    simulateProgress();

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [open, analysisId, onComplete]);

  const getStepStatus = (stepIndex: number) => {
    if (stepIndex < currentStep) return 'completed';
    if (stepIndex === currentStep) return 'processing';
    return 'pending';
  };

  const getStepIcon = (step: typeof analysisSteps[0], stepIndex: number) => {
    const stepStatus = getStepStatus(stepIndex);
    const IconComponent = step.icon;

    if (stepStatus === 'completed') {
      return <CheckCircle className="w-5 h-5 text-green-600" />;
    } else if (stepStatus === 'processing') {
      return <Loader2 className="w-5 h-5 text-blue-600 animate-spin" />;
    } else {
      return <IconComponent className="w-5 h-5 text-gray-400" />;
    }
  };

  const handleClose = () => {
    if (status?.status === 'processing') {
      // 如果正在處理中，確認是否要關閉
      if (window.confirm('分析正在進行中，確定要關閉嗎？分析將在背景繼續執行。')) {
        onOpenChange(false);
      }
    } else {
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-blue-100">
                <BarChart3 className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <DialogTitle className="text-xl">產品分析進度</DialogTitle>
                <DialogDescription>
                  分析 ID: {analysisId}
                </DialogDescription>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* 整體進度 */}
          {status && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">
                  整體進度
                </span>
                <div className="flex items-center gap-2">
                  <Badge
                    variant={
                      status.status === 'completed'
                        ? 'default'
                        : status.status === 'processing'
                        ? 'secondary'
                        : 'outline'
                    }
                    className={cn(
                      status.status === 'completed' && 'bg-green-100 text-green-800',
                      status.status === 'processing' && 'bg-blue-100 text-blue-800'
                    )}
                  >
                    {status.status === 'completed'
                      ? '已完成'
                      : status.status === 'processing'
                      ? '進行中'
                      : '排隊中'}
                  </Badge>
                  <span className="text-sm font-semibold">
                    {status.progress}%
                  </span>
                </div>
              </div>
              <Progress value={status.progress} className="h-2" />
              <p className="text-sm text-gray-600">{status.message}</p>
            </div>
          )}

          {/* 分析步驟 */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold text-gray-900">分析步驟</h4>
            <div className="space-y-3">
              {analysisSteps.map((step, index) => {
                const stepStatus = getStepStatus(index);
                return (
                  <div
                    key={step.id}
                    className={cn(
                      'flex items-center gap-3 p-3 rounded-lg border transition-colors',
                      stepStatus === 'completed' && 'bg-green-50 border-green-200',
                      stepStatus === 'processing' && 'bg-blue-50 border-blue-200',
                      stepStatus === 'pending' && 'bg-gray-50 border-gray-200'
                    )}
                  >
                    <div className="flex-shrink-0">
                      {getStepIcon(step, index)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p
                        className={cn(
                          'text-sm font-medium',
                          stepStatus === 'completed' && 'text-green-900',
                          stepStatus === 'processing' && 'text-blue-900',
                          stepStatus === 'pending' && 'text-gray-500'
                        )}
                      >
                        {step.title}
                      </p>
                      <p
                        className={cn(
                          'text-xs',
                          stepStatus === 'completed' && 'text-green-700',
                          stepStatus === 'processing' && 'text-blue-700',
                          stepStatus === 'pending' && 'text-gray-400'
                        )}
                      >
                        {step.description}
                      </p>
                    </div>
                    {stepStatus === 'completed' && (
                      <div className="text-xs text-green-600 font-medium">
                        完成
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          {/* 預估完成時間 */}
          {status?.estimatedCompletion && status.status === 'processing' && (
            <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-900">
                  預估完成時間
                </span>
              </div>
              <p className="text-sm text-blue-700 mt-1">
                {new Date(status.estimatedCompletion).toLocaleString('zh-TW')}
              </p>
            </div>
          )}

          {/* 錯誤信息 */}
          {error && (
            <div className="p-3 bg-red-50 rounded-lg border border-red-200">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <span className="text-sm font-medium text-red-900">
                  分析失敗
                </span>
              </div>
              <p className="text-sm text-red-700 mt-1">{error}</p>
            </div>
          )}

          {/* 操作按鈕 */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            {status?.status === 'completed' ? (
              <Button onClick={() => onOpenChange(false)}>
                查看結果
              </Button>
            ) : (
              <Button variant="outline" onClick={handleClose}>
                {status?.status === 'processing' ? '在背景執行' : '關閉'}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
