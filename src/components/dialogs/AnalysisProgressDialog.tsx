/**
 * 分析進度對話框組件
 * 顯示產品分析的實時進度和狀態
 */

'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import {
  CheckCircle,
  Clock,
  AlertCircle,
  Loader2,
  BarChart3,
  Eye,
  MessageSquare,
  Link as LinkIcon,
  X,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { AnalysisStatus, productAnalysisService } from '@/services/product-analysis';

interface AnalysisProgressDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  analysisId?: string;
  onComplete?: (results: any) => void;
  onError?: (error: string) => void;
}

const analysisSteps = [
  {
    id: 'initialization',
    title: '初始化分析',
    description: '準備分析環境和參數',
    icon: Clock,
  },
  {
    id: 'data_collection',
    title: '數據收集',
    description: '從各 AI 引擎收集回應數據',
    icon: BarChart3,
  },
  {
    id: 'visibility_analysis',
    title: '可見度分析',
    description: '分析品牌在搜索結果中的可見度',
    icon: Eye,
  },
  {
    id: 'response_analysis',
    title: '回應分析',
    description: '分析 AI 引擎的回應內容',
    icon: MessageSquare,
  },
  {
    id: 'citation_analysis',
    title: '引用分析',
    description: '檢查品牌被引用的情況',
    icon: LinkIcon,
  },
  {
    id: 'report_generation',
    title: '生成報告',
    description: '整理分析結果並生成報告',
    icon: CheckCircle,
  },
];

export default function AnalysisProgressDialog({
  open,
  onOpenChange,
  analysisId,
  onComplete,
  onError,
}: AnalysisProgressDialogProps) {
  const [status, setStatus] = useState<AnalysisStatus | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [error, setError] = useState<string | null>(null);

  // 實際輪詢分析狀態
  useEffect(() => {
    if (!open || !analysisId) return;

    let intervalId: NodeJS.Timeout;
    let isCancelled = false;

    const pollStatus = async () => {
      try {
        if (isCancelled) return;
        
        const currentStatus = await productAnalysisService.getAnalysisStatus(analysisId);
        
        if (isCancelled) return;
        
        setStatus(currentStatus);
        setError(null);

        // 根據進度計算當前步驟
        const stepIndex = Math.floor((currentStatus.progress / 100) * (analysisSteps.length - 1));
        setCurrentStep(Math.min(stepIndex, analysisSteps.length - 1));

        // 如果分析完成，觸發回調
        if (currentStatus.status === 'completed') {
          if (onComplete && currentStatus.results) {
            setTimeout(() => {
              onComplete(currentStatus.results);
            }, 1000);
          }
          return;
        }

        // 如果分析失敗，觸發錯誤回調
        if (currentStatus.status === 'failed') {
          const errorMessage = currentStatus.error || '分析過程中發生未知錯誤';
          setError(errorMessage);
          if (onError) {
            setTimeout(() => {
              onError(errorMessage);
            }, 1000);
          }
          return;
        }

        // 如果仍在處理中，繼續輪詢
        if (currentStatus.status === 'processing' || currentStatus.status === 'queued') {
          intervalId = setTimeout(pollStatus, 2000);
        }

      } catch (error) {
        if (isCancelled) return;
        
        console.error('輪詢分析狀態失敗:', error);
        const errorMessage = error instanceof Error ? error.message : '無法獲取分析狀態';
        setError(errorMessage);
        
        if (onError) {
          setTimeout(() => {
            onError(errorMessage);
          }, 1000);
        }
      }
    };

    // 開始輪詢
    pollStatus();

    return () => {
      isCancelled = true;
      if (intervalId) {
        clearTimeout(intervalId);
      }
    };
  }, [open, analysisId, onComplete, onError]);

  const getStepStatus = (stepIndex: number) => {
    if (stepIndex < currentStep) return 'completed';
    if (stepIndex === currentStep) return 'processing';
    return 'pending';
  };

  const getStepIcon = (step: typeof analysisSteps[0], stepIndex: number) => {
    const stepStatus = getStepStatus(stepIndex);
    const IconComponent = step.icon;

    if (stepStatus === 'completed') {
      return <CheckCircle className="w-5 h-5 text-green-600" />;
    } else if (stepStatus === 'processing') {
      return <Loader2 className="w-5 h-5 text-blue-600 animate-spin" />;
    } else {
      return <IconComponent className="w-5 h-5 text-gray-400" />;
    }
  };

  const handleClose = () => {
    if (status?.status === 'processing') {
      // 如果正在處理中，確認是否要關閉
      if (window.confirm('分析正在進行中，確定要關閉嗎？分析將在背景繼續執行。')) {
        onOpenChange(false);
      }
    } else {
      onOpenChange(false);
    }
  };

  const getStatusBadge = () => {
    if (!status) return null;

    switch (status.status) {
      case 'completed':
        return (
          <Badge className="bg-green-100 text-green-800 border-green-200">
            ✅ 已完成
          </Badge>
        );
      case 'processing':
        return (
          <Badge className="bg-blue-100 text-blue-800 border-blue-200">
            🔄 進行中
          </Badge>
        );
      case 'queued':
        return (
          <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
            ⏳ 排隊中
          </Badge>
        );
      case 'failed':
        return (
          <Badge className="bg-red-100 text-red-800 border-red-200">
            ❌ 已失敗
          </Badge>
        );
      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-blue-100">
                <BarChart3 className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <DialogTitle className="text-xl">產品分析進度</DialogTitle>
                <DialogDescription>
                  分析 ID: {analysisId}
                </DialogDescription>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {getStatusBadge()}
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClose}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* 整體進度 */}
          {status && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">
                  整體進度
                </span>
                <span className="text-sm font-semibold">
                  {status.progress}%
                </span>
              </div>
              <Progress value={status.progress} className="h-2" />
              <p className="text-sm text-gray-600">{status.message}</p>
              
              {/* 預計完成時間 */}
              {status.estimatedCompletion && status.status !== 'completed' && (
                <p className="text-xs text-gray-500">
                  預計完成時間：{new Date(status.estimatedCompletion).toLocaleTimeString()}
                </p>
              )}
              
              {/* 已完成時間 */}
              {status.completedAt && (
                <p className="text-xs text-green-600">
                  完成時間：{new Date(status.completedAt).toLocaleTimeString()}
                </p>
              )}
            </div>
          )}

          {/* 錯誤信息 */}
          {error && (
            <div className="p-4 rounded-lg bg-red-50 border border-red-200">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-red-800">分析失敗</h4>
                  <p className="text-sm text-red-700 mt-1">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* 分析步驟 */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold text-gray-900">分析步驟</h4>
            <div className="space-y-3">
              {analysisSteps.map((step, index) => {
                const stepStatus = getStepStatus(index);
                return (
                  <div
                    key={step.id}
                    className={cn(
                      'flex items-center gap-3 p-3 rounded-lg border transition-colors',
                      stepStatus === 'completed' && 'bg-green-50 border-green-200',
                      stepStatus === 'processing' && 'bg-blue-50 border-blue-200',
                      stepStatus === 'pending' && 'bg-gray-50 border-gray-200'
                    )}
                  >
                    <div className="flex-shrink-0">
                      {getStepIcon(step, index)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p
                        className={cn(
                          'text-sm font-medium',
                          stepStatus === 'completed' && 'text-green-900',
                          stepStatus === 'processing' && 'text-blue-900',
                          stepStatus === 'pending' && 'text-gray-500'
                        )}
                      >
                        {step.title}
                      </p>
                      <p
                        className={cn(
                          'text-xs',
                          stepStatus === 'completed' && 'text-green-700',
                          stepStatus === 'processing' && 'text-blue-700',
                          stepStatus === 'pending' && 'text-gray-400'
                        )}
                      >
                        {step.description}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* 分析結果預覽 */}
          {status?.status === 'completed' && status.results && (
            <div className="space-y-4">
              <h4 className="text-sm font-semibold text-gray-900">分析結果</h4>
              <div className="p-4 rounded-lg bg-green-50 border border-green-200">
                <p className="text-sm text-green-800 mb-3">{status.results.summary}</p>
                {status.results.visibilityScore && (
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-green-700">總體可見度分數：</span>
                    <Badge className="bg-green-200 text-green-800">
                      {status.results.visibilityScore.toFixed(1)}%
                    </Badge>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
