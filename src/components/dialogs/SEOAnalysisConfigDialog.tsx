/**
 * SEO 分析配置對話框
 * 允許用戶配置分析參數並啟動真實的 OpenAI 分析
 */

'use client';

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Globe,
  Target,
  Users,
  Settings,
  AlertTriangle,
  Info,
  Plus,
  X,
  DollarSign,
  Clock,
  BarChart3,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { SEOAnalysisRequest } from '@/services/openai-seo-analysis';
import { toast } from 'sonner';

interface SEOAnalysisConfigDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onStartAnalysis: (request: SEOAnalysisRequest) => void;
  defaultUrl?: string;
}

export default function SEOAnalysisConfigDialog({
  open,
  onOpenChange,
  onStartAnalysis,
  defaultUrl = '',
}: SEOAnalysisConfigDialogProps) {
  const [url, setUrl] = useState(defaultUrl);
  const [targetKeywords, setTargetKeywords] = useState<string[]>(['SEO', 'AI 優化']);
  const [newKeyword, setNewKeyword] = useState('');
  const [competitors, setCompetitors] = useState<string[]>([]);
  const [newCompetitor, setNewCompetitor] = useState('');
  const [analysisDepth, setAnalysisDepth] = useState<'basic' | 'standard' | 'comprehensive'>('standard');
  const [includeCompetitorAnalysis, setIncludeCompetitorAnalysis] = useState(false);
  const [language, setLanguage] = useState<'zh-TW' | 'zh-CN' | 'en'>('zh-TW');
  const [isValidating, setIsValidating] = useState(false);

  // 分析深度配置
  const analysisDepthConfig = {
    basic: {
      name: '基礎分析',
      description: '快速 SEO 檢查，包含基本關鍵字和技術分析',
      estimatedTime: '3-5 分鐘',
      estimatedCost: '$0.02-0.05',
      features: ['關鍵字密度', '基本技術 SEO', '內容品質評估'],
    },
    standard: {
      name: '標準分析',
      description: '全面的 SEO 分析，包含 AI 優化建議',
      estimatedTime: '8-12 分鐘',
      estimatedCost: '$0.08-0.15',
      features: ['完整關鍵字分析', '技術 SEO 檢查', '內容品質評估', 'AI 搜索優化', '詳細報告'],
    },
    comprehensive: {
      name: '深度分析',
      description: '最詳細的分析，包含競爭對手比較和策略建議',
      estimatedTime: '15-20 分鐘',
      estimatedCost: '$0.20-0.35',
      features: ['全方位 SEO 分析', '競爭對手研究', 'AI 搜索優化', '策略建議', '執行計劃', '詳細報告'],
    },
  };

  // 添加關鍵字
  const addKeyword = () => {
    if (newKeyword.trim() && !targetKeywords.includes(newKeyword.trim())) {
      setTargetKeywords([...targetKeywords, newKeyword.trim()]);
      setNewKeyword('');
    }
  };

  // 移除關鍵字
  const removeKeyword = (keyword: string) => {
    setTargetKeywords(targetKeywords.filter(k => k !== keyword));
  };

  // 添加競爭對手
  const addCompetitor = () => {
    if (newCompetitor.trim() && !competitors.includes(newCompetitor.trim())) {
      setCompetitors([...competitors, newCompetitor.trim()]);
      setNewCompetitor('');
    }
  };

  // 移除競爭對手
  const removeCompetitor = (competitor: string) => {
    setCompetitors(competitors.filter(c => c !== competitor));
  };

  // 驗證 URL
  const validateUrl = async (urlToValidate: string) => {
    try {
      new URL(urlToValidate);
      return true;
    } catch {
      return false;
    }
  };

  // 處理開始分析
  const handleStartAnalysis = async () => {
    // 驗證必填欄位
    if (!url.trim()) {
      toast.error('請輸入要分析的網站 URL');
      return;
    }

    if (targetKeywords.length === 0) {
      toast.error('請至少添加一個目標關鍵字');
      return;
    }

    // 驗證 URL 格式
    const isValidUrl = await validateUrl(url);
    if (!isValidUrl) {
      toast.error('請輸入有效的 URL 格式');
      return;
    }

    // 構建分析請求
    const request: SEOAnalysisRequest = {
      url: url.trim(),
      targetKeywords,
      competitors: includeCompetitorAnalysis ? competitors : [],
      analysisDepth,
      includeCompetitorAnalysis,
      language,
    };

    // 啟動分析
    onStartAnalysis(request);
    onOpenChange(false);
  };

  // 重置表單
  const resetForm = () => {
    setUrl(defaultUrl);
    setTargetKeywords(['SEO', 'AI 優化']);
    setNewKeyword('');
    setCompetitors([]);
    setNewCompetitor('');
    setAnalysisDepth('standard');
    setIncludeCompetitorAnalysis(false);
    setLanguage('zh-TW');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl flex items-center gap-2">
            <Settings className="h-6 w-6 text-blue-600" />
            配置 SEO 分析
          </DialogTitle>
          <DialogDescription>
            設定分析參數以獲得最準確的 SEO 優化建議
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* 網站 URL */}
          <div className="space-y-2">
            <Label htmlFor="url" className="flex items-center gap-2">
              <Globe className="h-4 w-4" />
              網站 URL *
            </Label>
            <Input
              id="url"
              placeholder="https://example.com"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              className="w-full"
            />
            <p className="text-xs text-gray-600">
              請輸入要分析的完整網站 URL
            </p>
          </div>

          {/* 目標關鍵字 */}
          <div className="space-y-3">
            <Label className="flex items-center gap-2">
              <Target className="h-4 w-4" />
              目標關鍵字 *
            </Label>
            <div className="flex gap-2">
              <Input
                placeholder="輸入關鍵字"
                value={newKeyword}
                onChange={(e) => setNewKeyword(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && addKeyword()}
                className="flex-1"
              />
              <Button onClick={addKeyword} size="sm">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {targetKeywords.map((keyword) => (
                <Badge key={keyword} variant="secondary" className="flex items-center gap-1">
                  {keyword}
                  <button
                    onClick={() => removeKeyword(keyword)}
                    className="ml-1 hover:text-red-600"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
          </div>

          {/* 分析深度 */}
          <div className="space-y-3">
            <Label className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              分析深度
            </Label>
            <div className="grid grid-cols-1 gap-3">
              {Object.entries(analysisDepthConfig).map(([key, config]) => (
                <div
                  key={key}
                  className={cn(
                    'p-4 border rounded-lg cursor-pointer transition-colors',
                    analysisDepth === key
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  )}
                  onClick={() => setAnalysisDepth(key as any)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{config.name}</h4>
                    <div className="flex items-center gap-2 text-xs text-gray-600">
                      <Clock className="h-3 w-3" />
                      {config.estimatedTime}
                      <DollarSign className="h-3 w-3" />
                      {config.estimatedCost}
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{config.description}</p>
                  <div className="flex flex-wrap gap-1">
                    {config.features.map((feature) => (
                      <Badge key={feature} variant="outline" className="text-xs">
                        {feature}
                      </Badge>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 競爭對手分析 */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                競爭對手分析
              </Label>
              <Switch
                checked={includeCompetitorAnalysis}
                onCheckedChange={setIncludeCompetitorAnalysis}
              />
            </div>
            
            {includeCompetitorAnalysis && (
              <>
                <div className="flex gap-2">
                  <Input
                    placeholder="競爭對手網站 URL"
                    value={newCompetitor}
                    onChange={(e) => setNewCompetitor(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && addCompetitor()}
                    className="flex-1"
                  />
                  <Button onClick={addCompetitor} size="sm">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {competitors.map((competitor) => (
                    <Badge key={competitor} variant="secondary" className="flex items-center gap-1">
                      {competitor}
                      <button
                        onClick={() => removeCompetitor(competitor)}
                        className="ml-1 hover:text-red-600"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <div className="flex items-start gap-2">
                    <Info className="h-4 w-4 text-yellow-600 mt-0.5" />
                    <div className="text-sm text-yellow-800">
                      <strong>注意：</strong>競爭對手分析會增加分析時間和成本，但能提供更深入的市場洞察。
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>

          {/* 語言設定 */}
          <div className="space-y-2">
            <Label>分析語言</Label>
            <Select value={language} onValueChange={(value: any) => setLanguage(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="zh-TW">繁體中文 (台灣)</SelectItem>
                <SelectItem value="zh-CN">简体中文 (中国)</SelectItem>
                <SelectItem value="en">English</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Separator />

          {/* 成本預估 */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              成本預估
            </h4>
            <div className="text-sm text-blue-800 space-y-1">
              <div>分析深度: {analysisDepthConfig[analysisDepth].name}</div>
              <div>預估時間: {analysisDepthConfig[analysisDepth].estimatedTime}</div>
              <div>預估成本: {analysisDepthConfig[analysisDepth].estimatedCost}</div>
              {includeCompetitorAnalysis && competitors.length > 0 && (
                <div className="text-xs text-blue-600 mt-2">
                  * 競爭對手分析可能增加 20-50% 的時間和成本
                </div>
              )}
            </div>
          </div>
        </div>

        <DialogFooter className="flex items-center justify-between">
          <Button variant="outline" onClick={resetForm}>
            重置
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button 
              onClick={handleStartAnalysis}
              disabled={!url.trim() || targetKeywords.length === 0}
              className="bg-blue-600 hover:bg-blue-700"
            >
              開始分析
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
