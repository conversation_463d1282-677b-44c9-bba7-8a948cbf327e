'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Activity,
  Users,
  TrendingUp,
  AlertCircle,
  Play,
  Pause,
  RefreshCw,
  BarChart3,
  Eye,
  Clock
} from 'lucide-react';

interface QueryData {
  id: string;
  query: string;
  source: string;
  intent: string;
  confidence: number;
  timestamp: string;
}

interface RealTimeMetrics {
  queries_per_minute: number;
  active_users: number;
  avg_response_time: string;
  success_rate: string;
  top_intent_current: string;
  alert_count: number;
}

interface WebSocketData {
  type: 'data' | 'initial';
  topic: string;
  data: any;
  timestamp: string;
}

export default function RealTimeQueryAnalytics() {
  const [isConnected, setIsConnected] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [recentQueries, setRecentQueries] = useState<QueryData[]>([]);
  const [metrics, setMetrics] = useState<RealTimeMetrics>({
    queries_per_minute: 0,
    active_users: 0,
    avg_response_time: '0ms',
    success_rate: '0.000',
    top_intent_current: '',
    alert_count: 0
  });

  const wsRef = useRef<WebSocket | null>(null);

  // WebSocket 連接管理
  const connectWebSocket = () => {
    try {
      // 注意：這裡使用模擬的 WebSocket 連接
      // 在實際部署中，需要配置真實的 WebSocket 服務器
      const wsUrl = process.env.NODE_ENV === 'development' 
        ? 'ws://localhost:3001/ws' 
        : 'wss://your-domain.com/ws';
      
      // 由於我們還沒有真實的 WebSocket 服務器，我們將模擬連接
      simulateWebSocketConnection();
      
    } catch (error) {
      console.error('WebSocket 連接失敗:', error);
    }
  };

  // 模擬 WebSocket 連接（用於演示）
  const simulateWebSocketConnection = () => {
    setIsConnected(true);
    
    // 模擬定期接收數據
    const interval = setInterval(() => {
      if (isStreaming) {
        // 模擬新查詢數據
        const newQuery: QueryData = {
          id: Date.now().toString(),
          query: getRandomQuery(),
          source: Math.random() > 0.5 ? 'web' : 'api',
          intent: getRandomIntent(),
          confidence: Math.random() * 0.3 + 0.7,
          timestamp: new Date().toISOString()
        };

        setRecentQueries(prev => [newQuery, ...prev.slice(0, 9)]);

        // 更新指標
        setMetrics(prev => ({
          ...prev,
          queries_per_minute: Math.floor(Math.random() * 20) + 5,
          active_users: Math.floor(Math.random() * 100) + 50,
          avg_response_time: (Math.random() * 200 + 100).toFixed(0) + 'ms',
          success_rate: (Math.random() * 0.05 + 0.95).toFixed(3),
          alert_count: Math.floor(Math.random() * 3)
        }));
      }
    }, 2000);

    // 保存 interval ID 用於清理
    wsRef.current = { close: () => clearInterval(interval) } as any;
  };

  const getRandomQuery = (): string => {
    const queries = [
      'AI SEO 最佳實踐指南',
      '競爭對手關鍵字分析',
      '網站速度優化技巧',
      '內容營銷策略規劃',
      '社交媒體 SEO 整合',
      '本地搜索排名提升',
      '移動端 SEO 優化',
      '語音搜索趨勢分析'
    ];
    return queries[Math.floor(Math.random() * queries.length)];
  };

  const getRandomIntent = (): string => {
    const intents = [
      'product_research',
      'seo_optimization',
      'competitor_analysis',
      'technical_support',
      'content_strategy'
    ];
    return intents[Math.floor(Math.random() * intents.length)];
  };

  const getIntentDisplay = (intent: string): string => {
    const intentMap: Record<string, string> = {
      'product_research': '產品研究',
      'seo_optimization': 'SEO優化',
      'competitor_analysis': '競爭分析',
      'technical_support': '技術支援',
      'content_strategy': '內容策略'
    };
    return intentMap[intent] || intent;
  };

  const getIntentColor = (intent: string): string => {
    const colorMap: Record<string, string> = {
      'product_research': 'bg-blue-100 text-blue-800',
      'seo_optimization': 'bg-green-100 text-green-800',
      'competitor_analysis': 'bg-purple-100 text-purple-800',
      'technical_support': 'bg-orange-100 text-orange-800',
      'content_strategy': 'bg-pink-100 text-pink-800'
    };
    return colorMap[intent] || 'bg-gray-100 text-gray-800';
  };

  const toggleStreaming = () => {
    setIsStreaming(!isStreaming);
  };

  const refreshConnection = () => {
    if (wsRef.current) {
      wsRef.current.close();
    }
    connectWebSocket();
  };

  useEffect(() => {
    connectWebSocket();
    
    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  return (
    <div className="space-y-6">
      {/* 控制面板 */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-blue-900 flex items-center gap-2">
                <Activity className="h-5 w-5" />
                實時查詢分析
              </CardTitle>
              <CardDescription className="text-blue-700">
                監控用戶查詢和系統性能指標
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge 
                variant={isConnected ? "default" : "secondary"}
                className={isConnected ? "bg-green-100 text-green-800" : ""}
              >
                {isConnected ? '已連接' : '未連接'}
              </Badge>
              <Button
                onClick={toggleStreaming}
                variant={isStreaming ? "secondary" : "default"}
                size="sm"
                className="gap-2"
              >
                {isStreaming ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                {isStreaming ? '暫停' : '開始'}
              </Button>
              <Button
                onClick={refreshConnection}
                variant="outline"
                size="sm"
                className="gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                重新連接
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* 實時指標 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-white border-border-light">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">每分鐘查詢</p>
                <p className="text-2xl font-bold text-primary">{metrics.queries_per_minute}</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-lg">
                <BarChart3 className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white border-border-light">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">活躍用戶</p>
                <p className="text-2xl font-bold text-primary">{metrics.active_users}</p>
              </div>
              <div className="p-3 bg-green-100 rounded-lg">
                <Users className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white border-border-light">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">響應時間</p>
                <p className="text-2xl font-bold text-primary">{metrics.avg_response_time}</p>
              </div>
              <div className="p-3 bg-orange-100 rounded-lg">
                <Clock className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white border-border-light">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">成功率</p>
                <p className="text-2xl font-bold text-primary">{(parseFloat(metrics.success_rate) * 100).toFixed(1)}%</p>
              </div>
              <div className="p-3 bg-purple-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 實時查詢流 */}
      <Card className="bg-white border-border-light">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            實時查詢流
          </CardTitle>
          <CardDescription>
            最新的用戶查詢和分析結果
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentQueries.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                {isStreaming ? '等待查詢數據...' : '點擊開始按鈕開始監控'}
              </div>
            ) : (
              recentQueries.map((query) => (
                <div 
                  key={query.id}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h4 className="font-medium text-gray-900">{query.query}</h4>
                      <Badge className={getIntentColor(query.intent)}>
                        {getIntentDisplay(query.intent)}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {query.source}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span>置信度: {(query.confidence * 100).toFixed(1)}%</span>
                      <span>{new Date(query.timestamp).toLocaleTimeString()}</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`w-3 h-3 rounded-full ${
                      query.confidence > 0.8 ? 'bg-green-500' : 
                      query.confidence > 0.6 ? 'bg-yellow-500' : 'bg-red-500'
                    }`} />
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* 系統警告 */}
      {metrics.alert_count > 0 && (
        <Card className="bg-yellow-50 border-yellow-200">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <AlertCircle className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="font-medium text-yellow-800">
                  系統警告
                </p>
                <p className="text-sm text-yellow-700">
                  檢測到 {metrics.alert_count} 個需要關注的問題
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 