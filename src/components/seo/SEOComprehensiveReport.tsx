'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  FileText, 
  Download, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Target,
  BarChart3,
  Users,
  Zap
} from 'lucide-react';
import { toast } from 'sonner';

interface SEOReportFormData {
  url: string;
  title: string;
  content: string;
  targetKeywords: string[];
  competitorUrls: string[];
  businessType: 'ecommerce' | 'blog' | 'corporate' | 'local' | 'saas';
  targetMarket: 'taiwan' | 'hongkong' | 'macau' | 'all';
  format: 'json' | 'markdown' | 'pdf';
}

interface SEOReportResult {
  executiveSummary: {
    overallScore: number;
    technicalScore: number;
    contentScore: number;
    userExperienceScore: number;
    competitivenessScore: number;
  };
  technicalAnalysis: any;
  contentOptimization: any;
  competitorAnalysis: any;
  actionPlan: any;
  roiProjection: any;
  metadata: {
    reportId: string;
    generatedAt: string;
    version: string;
    aiModel: string;
    targetMarket: string;
  };
}

export default function SEOComprehensiveReport() {
  const [formData, setFormData] = useState<SEOReportFormData>({
    url: '',
    title: '',
    content: '',
    targetKeywords: [],
    competitorUrls: [],
    businessType: 'corporate',
    targetMarket: 'taiwan',
    format: 'json'
  });

  const [keywordInput, setKeywordInput] = useState('');
  const [competitorInput, setCompetitorInput] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [report, setReport] = useState<SEOReportResult | null>(null);
  const [activeTab, setActiveTab] = useState('form');

  const handleAddKeyword = () => {
    if (keywordInput.trim() && !formData.targetKeywords.includes(keywordInput.trim())) {
      setFormData(prev => ({
        ...prev,
        targetKeywords: [...prev.targetKeywords, keywordInput.trim()]
      }));
      setKeywordInput('');
    }
  };

  const handleRemoveKeyword = (keyword: string) => {
    setFormData(prev => ({
      ...prev,
      targetKeywords: prev.targetKeywords.filter(k => k !== keyword)
    }));
  };

  const handleAddCompetitor = () => {
    if (competitorInput.trim() && !formData.competitorUrls.includes(competitorInput.trim())) {
      setFormData(prev => ({
        ...prev,
        competitorUrls: [...prev.competitorUrls, competitorInput.trim()]
      }));
      setCompetitorInput('');
    }
  };

  const handleRemoveCompetitor = (url: string) => {
    setFormData(prev => ({
      ...prev,
      competitorUrls: prev.competitorUrls.filter(u => u !== url)
    }));
  };

  const generateReport = async () => {
    if (!formData.url || !formData.content || formData.targetKeywords.length === 0) {
      toast.error('請填寫必要欄位：網站 URL、內容和至少一個目標關鍵字');
      return;
    }

    setIsGenerating(true);
    try {
      const response = await fetch('/api/seo/comprehensive-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (result.success) {
        setReport(result.data.report);
        setActiveTab('report');
        toast.success('SEO 綜合報告生成成功！');
      } else {
        toast.error(result.error || 'SEO 報告生成失敗');
      }
    } catch (error) {
      console.error('生成報告失敗:', error);
      toast.error('生成報告時發生錯誤');
    } finally {
      setIsGenerating(false);
    }
  };

  const downloadReport = async (format: 'markdown' | 'pdf') => {
    if (!report) return;

    try {
      const response = await fetch('/api/seo/comprehensive-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          format
        }),
      });

      const result = await response.json();

      if (result.success) {
        if (format === 'markdown') {
          // 下載 Markdown 文件
          const blob = new Blob([result.data.content], { type: 'text/markdown' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `seo-report-${new Date().toISOString().split('T')[0]}.md`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        } else if (format === 'pdf') {
          // 開啟 PDF 下載連結
          window.open(result.data.downloadUrl, '_blank');
        }
        toast.success(`${format.toUpperCase()} 報告下載成功！`);
      } else {
        toast.error('下載報告失敗');
      }
    } catch (error) {
      console.error('下載報告失敗:', error);
      toast.error('下載報告時發生錯誤');
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return 'default';
    if (score >= 60) return 'secondary';
    return 'destructive';
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">AI SEO 優化王 - 綜合分析報告</h1>
        <p className="text-gray-600">
          生成詳細的 SEO 分析結果和優化建議報告，適合台灣、香港、澳門市場
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="form" className="flex items-center gap-2">
            <FileText className="w-4 h-4" />
            報告設定
          </TabsTrigger>
          <TabsTrigger value="report" className="flex items-center gap-2" disabled={!report}>
            <BarChart3 className="w-4 h-4" />
            分析報告
          </TabsTrigger>
        </TabsList>

        <TabsContent value="form" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5" />
                基本資訊
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">網站 URL *</label>
                  <Input
                    placeholder="https://example.com"
                    value={formData.url}
                    onChange={(e) => setFormData(prev => ({ ...prev, url: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">網站標題</label>
                  <Input
                    placeholder="網站標題"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">網站內容 *</label>
                <Textarea
                  placeholder="請輸入要分析的網站內容..."
                  rows={6}
                  value={formData.content}
                  onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                />
                <p className="text-xs text-gray-500">
                  內容長度: {formData.content.length} 字元 (最少需要 100 字元)
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">業務類型</label>
                  <select
                    className="w-full p-2 border border-gray-300 rounded-md"
                    value={formData.businessType}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      businessType: e.target.value as any 
                    }))}
                  >
                    <option value="corporate">企業網站</option>
                    <option value="ecommerce">電商網站</option>
                    <option value="blog">部落格</option>
                    <option value="local">在地商家</option>
                    <option value="saas">SaaS 服務</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">目標市場</label>
                  <select
                    className="w-full p-2 border border-gray-300 rounded-md"
                    value={formData.targetMarket}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      targetMarket: e.target.value as any 
                    }))}
                  >
                    <option value="taiwan">台灣</option>
                    <option value="hongkong">香港</option>
                    <option value="macau">澳門</option>
                    <option value="all">台灣、香港、澳門</option>
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5" />
                目標關鍵字 *
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  placeholder="輸入關鍵字"
                  value={keywordInput}
                  onChange={(e) => setKeywordInput(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleAddKeyword()}
                />
                <Button onClick={handleAddKeyword} variant="outline">
                  新增
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {formData.targetKeywords.map((keyword, index) => (
                  <Badge key={index} variant="secondary" className="cursor-pointer">
                    {keyword}
                    <button
                      onClick={() => handleRemoveKeyword(keyword)}
                      className="ml-2 text-red-500 hover:text-red-700"
                    >
                      ×
                    </button>
                  </Badge>
                ))}
              </div>
              {formData.targetKeywords.length === 0 && (
                <p className="text-sm text-red-500">請至少新增一個目標關鍵字</p>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                競爭對手網站 (選填)
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  placeholder="https://competitor.com"
                  value={competitorInput}
                  onChange={(e) => setCompetitorInput(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleAddCompetitor()}
                />
                <Button onClick={handleAddCompetitor} variant="outline">
                  新增
                </Button>
              </div>
              <div className="space-y-2">
                {formData.competitorUrls.map((url, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span className="text-sm">{url}</span>
                    <button
                      onClick={() => handleRemoveCompetitor(url)}
                      className="text-red-500 hover:text-red-700"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-center">
            <Button
              onClick={generateReport}
              disabled={isGenerating || !formData.url || !formData.content || formData.targetKeywords.length === 0}
              className="px-8 py-3 text-lg"
            >
              {isGenerating ? (
                <>
                  <Clock className="w-5 h-5 mr-2 animate-spin" />
                  生成報告中...
                </>
              ) : (
                <>
                  <Zap className="w-5 h-5 mr-2" />
                  生成 SEO 綜合報告
                </>
              )}
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="report" className="space-y-6">
          {report && (
            <>
              {/* 執行摘要 */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <BarChart3 className="w-5 h-5" />
                      執行摘要
                    </CardTitle>
                    <div className="flex gap-2">
                      <Button
                        onClick={() => downloadReport('markdown')}
                        variant="outline"
                        size="sm"
                      >
                        <Download className="w-4 h-4 mr-2" />
                        下載 Markdown
                      </Button>
                      <Button
                        onClick={() => downloadReport('pdf')}
                        variant="outline"
                        size="sm"
                      >
                        <Download className="w-4 h-4 mr-2" />
                        下載 PDF
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div className="text-center space-y-2">
                      <div className="text-2xl font-bold text-blue-600">
                        {report.executiveSummary.overallScore}
                      </div>
                      <div className="text-sm text-gray-600">綜合評分</div>
                      <Progress value={report.executiveSummary.overallScore} className="h-2" />
                    </div>
                    <div className="text-center space-y-2">
                      <div className={`text-2xl font-bold ${getScoreColor(report.executiveSummary.technicalScore)}`}>
                        {report.executiveSummary.technicalScore}
                      </div>
                      <div className="text-sm text-gray-600">技術 SEO</div>
                      <Progress value={report.executiveSummary.technicalScore} className="h-2" />
                    </div>
                    <div className="text-center space-y-2">
                      <div className={`text-2xl font-bold ${getScoreColor(report.executiveSummary.contentScore)}`}>
                        {report.executiveSummary.contentScore}
                      </div>
                      <div className="text-sm text-gray-600">內容品質</div>
                      <Progress value={report.executiveSummary.contentScore} className="h-2" />
                    </div>
                    <div className="text-center space-y-2">
                      <div className={`text-2xl font-bold ${getScoreColor(report.executiveSummary.userExperienceScore)}`}>
                        {report.executiveSummary.userExperienceScore}
                      </div>
                      <div className="text-sm text-gray-600">用戶體驗</div>
                      <Progress value={report.executiveSummary.userExperienceScore} className="h-2" />
                    </div>
                    <div className="text-center space-y-2">
                      <div className={`text-2xl font-bold ${getScoreColor(report.executiveSummary.competitivenessScore)}`}>
                        {report.executiveSummary.competitivenessScore}
                      </div>
                      <div className="text-sm text-gray-600">競爭力</div>
                      <Progress value={report.executiveSummary.competitivenessScore} className="h-2" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 技術分析 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="w-5 h-5" />
                    技術分析
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold mb-2">Core Web Vitals</h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="p-3 border rounded">
                          <div className="text-sm text-gray-600">LCP</div>
                          <div className="text-lg font-semibold">
                            {report.technicalAnalysis.coreWebVitals.lcp}ms
                          </div>
                          <Badge variant={report.technicalAnalysis.coreWebVitals.lcp > 2500 ? 'destructive' : 'default'}>
                            {report.technicalAnalysis.coreWebVitals.lcp > 2500 ? '需要改善' : '良好'}
                          </Badge>
                        </div>
                        <div className="p-3 border rounded">
                          <div className="text-sm text-gray-600">FID</div>
                          <div className="text-lg font-semibold">
                            {report.technicalAnalysis.coreWebVitals.fid}ms
                          </div>
                          <Badge variant={report.technicalAnalysis.coreWebVitals.fid > 100 ? 'destructive' : 'default'}>
                            {report.technicalAnalysis.coreWebVitals.fid > 100 ? '需要改善' : '良好'}
                          </Badge>
                        </div>
                        <div className="p-3 border rounded">
                          <div className="text-sm text-gray-600">CLS</div>
                          <div className="text-lg font-semibold">
                            {report.technicalAnalysis.coreWebVitals.cls}
                          </div>
                          <Badge variant={report.technicalAnalysis.coreWebVitals.cls > 0.1 ? 'destructive' : 'default'}>
                            {report.technicalAnalysis.coreWebVitals.cls > 0.1 ? '需要改善' : '良好'}
                          </Badge>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-semibold mb-2">優化建議</h4>
                      <ul className="space-y-1">
                        {report.technicalAnalysis.coreWebVitals.recommendations.map((rec: string, index: number) => (
                          <li key={index} className="flex items-start gap-2">
                            <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                            <span className="text-sm">{rec}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* ROI 預測 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5" />
                    ROI 預測
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold mb-3">3 個月預期成果</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>整體 SEO 評分</span>
                          <span className="font-semibold text-green-600">
                            +{report.roiProjection.threeMonthProjection.overallScoreImprovement} 分
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>有機流量增長</span>
                          <span className="font-semibold text-green-600">
                            +{report.roiProjection.threeMonthProjection.organicTrafficGrowth}%
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>關鍵字排名</span>
                          <span className="font-semibold text-green-600">
                            +{report.roiProjection.threeMonthProjection.keywordRankingIncrease}%
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>轉換率提升</span>
                          <span className="font-semibold text-green-600">
                            +{report.roiProjection.threeMonthProjection.conversionRateImprovement}%
                          </span>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-3">投資回報分析</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>投資成本</span>
                          <span className="font-semibold">
                            NT$ {report.roiProjection.investment.total.toLocaleString()}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>預期收益</span>
                          <span className="font-semibold text-green-600">
                            NT$ {report.roiProjection.expectedReturns.total.toLocaleString()}
                          </span>
                        </div>
                        <div className="flex justify-between text-lg">
                          <span className="font-semibold">ROI</span>
                          <span className="font-bold text-green-600">
                            {report.roiProjection.roi}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 報告資訊 */}
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center text-sm text-gray-500 space-y-1">
                    <p>報告 ID: {report.metadata.reportId}</p>
                    <p>生成時間: {new Date(report.metadata.generatedAt).toLocaleString('zh-TW')}</p>
                    <p>AI 模型: {report.metadata.aiModel}</p>
                    <p>目標市場: {report.metadata.targetMarket}</p>
                    <p className="italic">
                      本報告由 AI SEO 優化王系統自動生成，結合 AI 分析和專業 SEO 知識，為{report.metadata.targetMarket}市場量身定制。
                    </p>
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
