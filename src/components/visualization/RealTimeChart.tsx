'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import * as d3 from 'd3';

interface DataPoint {
  timestamp: Date;
  value: number;
  category?: string;
  label?: string;
}

interface RealTimeChartProps {
  title: string;
  wsUrl?: string;
  topic: string;
  width?: number;
  height?: number;
  maxDataPoints?: number;
  refreshInterval?: number;
  chartType?: 'line' | 'bar' | 'area' | 'scatter';
  color?: string;
  showGrid?: boolean;
  showLegend?: boolean;
  enableZoom?: boolean;
  enableBrush?: boolean;
}

// 全局 WebSocket 管理器
class WebSocketManager {
  private static instance: WebSocketManager;
  private ws: WebSocket | null = null;
  private subscribers: Map<string, Set<(data: any) => void>> = new Map();
  private isConnecting = false;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private url = '';

  static getInstance(): WebSocketManager {
    if (!WebSocketManager.instance) {
      WebSocketManager.instance = new WebSocketManager();
    }
    return WebSocketManager.instance;
  }

  connect(url: string) {
    if (this.ws?.readyState === WebSocket.OPEN || this.isConnecting) {
      return;
    }

    this.url = url;
    this.isConnecting = true;

    try {
      this.ws = new WebSocket(url);
      
      this.ws.onopen = () => {
        console.log('共享 WebSocket 連接成功');
        this.isConnecting = false;
        // 清除重連定時器
        if (this.reconnectTimeout) {
          clearTimeout(this.reconnectTimeout);
          this.reconnectTimeout = null;
        }
      };

      this.ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          if (message.type === 'data' && message.topic) {
            const topicSubscribers = this.subscribers.get(message.topic);
            if (topicSubscribers) {
              topicSubscribers.forEach(callback => callback(message));
            }
          }
        } catch (error) {
          console.error('解析 WebSocket 消息失敗:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('共享 WebSocket 連接關閉');
        this.isConnecting = false;
        this.ws = null;
        
        // 如果有訂閱者，則嘗試重連（限制重連次數）
        if (this.subscribers.size > 0 && !this.reconnectTimeout) {
          this.reconnectTimeout = setTimeout(() => {
            this.reconnectTimeout = null;
            this.connect(this.url);
          }, 3000);
        }
      };

      this.ws.onerror = (error) => {
        console.error('共享 WebSocket 錯誤:', error);
        this.isConnecting = false;
      };

    } catch (error) {
      console.error('創建 WebSocket 連接失敗:', error);
      this.isConnecting = false;
    }
  }

  subscribe(topic: string, callback: (data: any) => void) {
    if (!this.subscribers.has(topic)) {
      this.subscribers.set(topic, new Set());
    }
    this.subscribers.get(topic)!.add(callback);

    // 如果這是第一個訂閱者，發送訂閱消息
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'subscribe',
        topic: topic
      }));
    }
  }

  unsubscribe(topic: string, callback: (data: any) => void) {
    const topicSubscribers = this.subscribers.get(topic);
    if (topicSubscribers) {
      topicSubscribers.delete(callback);
      if (topicSubscribers.size === 0) {
        this.subscribers.delete(topic);
        
        // 如果沒有訂閱者了，發送取消訂閱消息
        if (this.ws?.readyState === WebSocket.OPEN) {
          this.ws.send(JSON.stringify({
            type: 'unsubscribe',
            topic: topic
          }));
        }
      }
    }

    // 如果沒有任何訂閱者，關閉連接
    if (this.subscribers.size === 0) {
      this.disconnect();
    }
  }

  disconnect() {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    
    this.isConnecting = false;
    this.subscribers.clear();
  }

  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }
}

export const RealTimeChart: React.FC<RealTimeChartProps> = ({
  title,
  wsUrl = process.env.NEXT_PUBLIC_WEBSOCKET_URL || 'ws://localhost:8003/ws',
  topic,
  width = 800,
  height = 400,
  maxDataPoints = 100,
  refreshInterval = 1000,
  chartType = 'line',
  color = '#3b82f6',
  showGrid = true,
  showLegend = true,
  enableZoom = true,
  enableBrush = false
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [data, setData] = useState<DataPoint[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const wsManager = useRef<WebSocketManager>(WebSocketManager.getInstance());

  // WebSocket 連接和消息處理
  useEffect(() => {
    const manager = wsManager.current;
    
    const handleMessage = (message: any) => {
      const newDataPoint: DataPoint = {
        timestamp: new Date(message.timestamp || Date.now()),
        value: message.data.value || Math.random() * 100,
        category: message.data.category,
        label: message.data.label
      };

      setData(prevData => {
        const newData = [...prevData, newDataPoint];
        // 限制數據點數量
        if (newData.length > maxDataPoints) {
          return newData.slice(-maxDataPoints);
        }
        return newData;
      });
    };

    // 訂閱主題
    manager.subscribe(topic, handleMessage);
    
    // 嘗試連接（如果尚未連接）
    manager.connect(wsUrl);
    
    // 檢查連接狀態
    const checkConnection = () => {
      setIsConnected(manager.isConnected());
    };
    
    const connectionCheckInterval = setInterval(checkConnection, 1000);

    return () => {
      clearInterval(connectionCheckInterval);
      manager.unsubscribe(topic, handleMessage);
    };
  }, [wsUrl, topic, maxDataPoints]);

  // D3.js 圖表繪製
  const drawChart = useCallback(() => {
    if (!svgRef.current || data.length === 0) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove(); // 清除之前的內容

    const margin = { top: 20, right: 30, bottom: 40, left: 50 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    // 創建主要的 g 元素
    const g = svg.append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // 設置比例尺
    const xScale = d3.scaleTime()
      .domain(d3.extent(data, d => d.timestamp) as [Date, Date])
      .range([0, innerWidth]);

    const yScale = d3.scaleLinear()
      .domain(d3.extent(data, d => d.value) as [number, number])
      .nice()
      .range([innerHeight, 0]);

    // 添加網格線
    if (showGrid) {
      // X 軸網格線
      g.append('g')
        .attr('class', 'grid')
        .attr('transform', `translate(0,${innerHeight})`)
        .call(d3.axisBottom(xScale)
          .tickSize(-innerHeight)
          .tickFormat(() => '')
        )
        .style('stroke-dasharray', '3,3')
        .style('opacity', 0.3);

      // Y 軸網格線
      g.append('g')
        .attr('class', 'grid')
        .call(d3.axisLeft(yScale)
          .tickSize(-innerWidth)
          .tickFormat(() => '')
        )
        .style('stroke-dasharray', '3,3')
        .style('opacity', 0.3);
    }

    // 根據圖表類型繪製
    switch (chartType) {
      case 'line':
        drawLineChart(g, data, xScale, yScale, color);
        break;
      case 'area':
        drawAreaChart(g, data, xScale, yScale, color, innerHeight);
        break;
      case 'bar':
        drawBarChart(g, data, xScale, yScale, color, innerWidth);
        break;
      case 'scatter':
        drawScatterPlot(g, data, xScale, yScale, color);
        break;
    }

    // 添加 X 軸
    g.append('g')
      .attr('transform', `translate(0,${innerHeight})`)
      .call(d3.axisBottom(xScale)
        .tickFormat(d3.timeFormat('%H:%M:%S'))
      );

    // 添加 Y 軸
    g.append('g')
      .call(d3.axisLeft(yScale));

    // 添加標題
    svg.append('text')
      .attr('x', width / 2)
      .attr('y', margin.top / 2)
      .attr('text-anchor', 'middle')
      .style('font-size', '14px')
      .style('font-weight', 'bold')
      .text(title);

    // 添加工具提示（如果啟用）
    if (showLegend) {
      addTooltip(svg, g, data, xScale, yScale);
    }

    // 添加縮放功能（如果啟用）
    if (enableZoom) {
      addZoom(svg, g, xScale, yScale, innerWidth, innerHeight);
    }

  }, [data, width, height, chartType, color, showGrid, showLegend, enableZoom, title]);

  // 圖表繪製函數們...
  const drawLineChart = (g: d3.Selection<SVGGElement, unknown, null, undefined>, 
                        data: DataPoint[], 
                        xScale: d3.ScaleTime<number, number>, 
                        yScale: d3.ScaleLinear<number, number>, 
                        color: string) => {
    const line = d3.line<DataPoint>()
      .x(d => xScale(d.timestamp))
      .y(d => yScale(d.value))
      .curve(d3.curveMonotoneX);

    g.append('path')
      .datum(data)
      .attr('fill', 'none')
      .attr('stroke', color)
      .attr('stroke-width', 2)
      .attr('d', line);

    // 添加數據點
    g.selectAll('.dot')
      .data(data)
      .enter().append('circle')
      .attr('class', 'dot')
      .attr('cx', d => xScale(d.timestamp))
      .attr('cy', d => yScale(d.value))
      .attr('r', 3)
      .attr('fill', color);
  };

  const drawAreaChart = (g: d3.Selection<SVGGElement, unknown, null, undefined>, 
                        data: DataPoint[], 
                        xScale: d3.ScaleTime<number, number>, 
                        yScale: d3.ScaleLinear<number, number>, 
                        color: string,
                        height: number) => {
    const area = d3.area<DataPoint>()
      .x(d => xScale(d.timestamp))
      .y0(height)
      .y1(d => yScale(d.value))
      .curve(d3.curveMonotoneX);

    const line = d3.line<DataPoint>()
      .x(d => xScale(d.timestamp))
      .y(d => yScale(d.value))
      .curve(d3.curveMonotoneX);

    // 添加填充區域
    g.append('path')
      .datum(data)
      .attr('fill', color)
      .attr('opacity', 0.3)
      .attr('d', area);

    // 添加邊界線
    g.append('path')
      .datum(data)
      .attr('fill', 'none')
      .attr('stroke', color)
      .attr('stroke-width', 2)
      .attr('d', line);
  };

  const drawBarChart = (g: d3.Selection<SVGGElement, unknown, null, undefined>, 
                       data: DataPoint[], 
                       xScale: d3.ScaleTime<number, number>, 
                       yScale: d3.ScaleLinear<number, number>, 
                       color: string,
                       width: number) => {
    const barWidth = width / data.length * 0.8;

    g.selectAll('.bar')
      .data(data)
      .enter().append('rect')
      .attr('class', 'bar')
      .attr('x', d => xScale(d.timestamp) - barWidth / 2)
      .attr('width', barWidth)
      .attr('y', d => yScale(d.value))
      .attr('height', d => yScale(0) - yScale(d.value))
      .attr('fill', color);
  };

  const drawScatterPlot = (g: d3.Selection<SVGGElement, unknown, null, undefined>, 
                          data: DataPoint[], 
                          xScale: d3.ScaleTime<number, number>, 
                          yScale: d3.ScaleLinear<number, number>, 
                          color: string) => {
    g.selectAll('.dot')
      .data(data)
      .enter().append('circle')
      .attr('class', 'dot')
      .attr('cx', d => xScale(d.timestamp))
      .attr('cy', d => yScale(d.value))
      .attr('r', 4)
      .attr('fill', color)
      .attr('opacity', 0.7);
  };

  const addTooltip = (svg: d3.Selection<SVGSVGElement, unknown, null, undefined>,
                     g: d3.Selection<SVGGElement, unknown, null, undefined>,
                     data: DataPoint[],
                     xScale: d3.ScaleTime<number, number>,
                     yScale: d3.ScaleLinear<number, number>) => {
    // 工具提示實現
    const tooltip = d3.select('body').append('div')
      .attr('class', 'tooltip')
      .style('opacity', 0)
      .style('position', 'absolute')
      .style('background', 'rgba(0, 0, 0, 0.8)')
      .style('color', 'white')
      .style('padding', '8px')
      .style('border-radius', '4px')
      .style('pointer-events', 'none')
      .style('font-size', '12px');

    g.selectAll('.dot')
      .on('mouseover', (event, d) => {
        tooltip.transition()
          .duration(200)
          .style('opacity', .9);
        tooltip.html(`時間: ${d.timestamp.toLocaleTimeString()}<br/>值: ${d.value.toFixed(2)}`)
          .style('left', (event.pageX + 10) + 'px')
          .style('top', (event.pageY - 28) + 'px');
      })
      .on('mouseout', () => {
        tooltip.transition()
          .duration(500)
          .style('opacity', 0);
      });
  };

  const addZoom = (svg: d3.Selection<SVGSVGElement, unknown, null, undefined>,
                  g: d3.Selection<SVGGElement, unknown, null, undefined>,
                  xScale: d3.ScaleTime<number, number>,
                  yScale: d3.ScaleLinear<number, number>,
                  width: number,
                  height: number) => {
    // 縮放功能實現
    const zoom = d3.zoom()
      .scaleExtent([0.5, 10])
      .on('zoom', (event) => {
        const newXScale = event.transform.rescaleX(xScale);
        const newYScale = event.transform.rescaleY(yScale);
        
        // 重新繪製內容...
        // 這裡可以添加更複雜的縮放邏輯
      });

    svg.call(zoom as any);
  };

  // 當數據更新時重新繪製圖表
  useEffect(() => {
    drawChart();
  }, [drawChart]);

  return (
    <div className="w-full bg-white rounded-lg shadow-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">{title}</h3>
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
          <span className="text-sm text-gray-600">
            {isConnected ? '已連接' : '連接中...'}
          </span>
          <span className="text-sm text-gray-500">
            數據點: {data.length}
          </span>
        </div>
      </div>
      
      <div className="relative">
        <svg 
          ref={svgRef} 
          width={width} 
          height={height} 
          className="border border-gray-200 rounded"
        />
      </div>
      
      <div className="mt-4 text-sm text-gray-600">
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <div 
              className="w-4 h-4 rounded mr-2" 
              style={{ backgroundColor: color }}
            />
            <span>{topic}</span>
          </div>
          <span>更新頻率: {refreshInterval}ms</span>
          <span>圖表類型: {chartType}</span>
        </div>
      </div>
    </div>
  );
};

export default RealTimeChart; 