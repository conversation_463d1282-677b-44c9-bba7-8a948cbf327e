"use client"

import * as React from "react"
import * as SliderPrimitive from "@radix-ui/react-slider"

import { cn } from "@/lib/utils"

// 更強大的數值驗證函數
const validateSliderValue = (value: number | undefined, defaultValue: number = 0): number => {
  if (value === undefined || value === null) {
    console.warn('🔧 Slider: undefined/null value, using default:', defaultValue);
    return defaultValue;
  }
  
  if (typeof value !== 'number') {
    console.warn('🔧 Slider: non-number value, converting:', value);
    const converted = Number(value);
    if (isNaN(converted)) {
      console.warn('🔧 Slider: conversion failed, using default:', defaultValue);
      return defaultValue;
    }
    return converted;
  }
  
  if (isNaN(value) || !isFinite(value)) {
    console.warn('🔧 Slider: invalid number value, using default:', defaultValue, '(was:', value, ')');
    return defaultValue;
  }
  
  return value;
};

// 驗證數值陣列
const validateSliderArray = (values: number[] | undefined, defaultValues: number[] = [0]): number[] => {
  if (!Array.isArray(values) || values.length === 0) {
    console.warn('🔧 Slider: invalid array, using defaults:', defaultValues);
    return defaultValues;
  }
  
  return values.map((value, index) => 
    validateSliderValue(value, defaultValues[index] || defaultValues[0] || 0)
  );
};

const Slider = React.forwardRef<
  React.ElementRef<typeof SliderPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>
>(({ className, value, defaultValue, onValueChange, ...props }, ref) => {
  // 使用 useMemo 來確保值在渲染時是安全的
  const safeValue = React.useMemo(() => {
    if (value !== undefined) {
      return validateSliderArray(value, [0]);
    }
    return value;
  }, [value]);

  const safeDefaultValue = React.useMemo(() => {
    if (defaultValue !== undefined) {
      return validateSliderArray(defaultValue, [0]);
    }
    return defaultValue;
  }, [defaultValue]);

  // 包裝 onValueChange 以確保傳遞的值是安全的
  const safeOnValueChange = React.useCallback((newValues: number[]) => {
    if (onValueChange) {
      const safeValues = validateSliderArray(newValues, [0]);
      console.log('🔧 Slider: Safe value change:', newValues, '->', safeValues);
      onValueChange(safeValues);
    }
  }, [onValueChange]);

  // 開發環境中的額外驗證
  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      if (value && value.some(v => isNaN(v))) {
        console.error('⚠️ Slider: NaN values detected in value prop:', value);
      }
      if (defaultValue && defaultValue.some(v => isNaN(v))) {
        console.error('⚠️ Slider: NaN values detected in defaultValue prop:', defaultValue);
      }
    }
  }, [value, defaultValue]);

  return (
    <SliderPrimitive.Root
      ref={ref}
      className={cn(
        "relative flex w-full touch-none select-none items-center",
        className
      )}
      value={safeValue}
      defaultValue={safeDefaultValue}
      onValueChange={safeOnValueChange}
      {...props}
    >
      <SliderPrimitive.Track className="relative h-1.5 w-full grow overflow-hidden rounded-full bg-primary/20">
        <SliderPrimitive.Range className="absolute h-full bg-primary" />
      </SliderPrimitive.Track>
      <SliderPrimitive.Thumb className="block h-4 w-4 rounded-full border border-primary/50 bg-background shadow transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50" />
    </SliderPrimitive.Root>
  )
})
Slider.displayName = SliderPrimitive.Root.displayName

export { Slider }
