import * as React from "react"

import { cn } from "@/lib/utils"

function Input({ className, type, value, ...props }: React.ComponentProps<"input">) {
  // 最激進的 NaN 處理 - 在任何情況下都不允許 NaN 通過
  const ultimateSafeValue = React.useMemo(() => {
    // 1. 首先檢查是否為 undefined 或 null
    if (value === undefined || value === null) {
      return '';
    }
    
    // 2. 如果是數字類型的輸入，進行特殊處理
    if (type === 'number') {
      // 2a. 處理數字類型
      if (typeof value === 'number') {
        if (isNaN(value) || !isFinite(value)) {
          console.warn('🚨 Input: Blocking NaN/Infinite number value:', value);
          return '';
        }
        return String(value);
      }
      
      // 2b. 處理字串類型
      if (typeof value === 'string') {
        if (value === '' || value === 'NaN' || value === 'Infinity' || value === '-Infinity') {
          return '';
        }
        
        // 嘗試轉換為數字驗證
        const numValue = Number(value);
        if (isNaN(numValue) || !isFinite(numValue)) {
          console.warn('🚨 Input: Blocking invalid number string:', value);
          return '';
        }
        return value;
      }
      
      // 2c. 其他類型強制轉換為字串並檢查
      const stringValue = String(value);
      if (stringValue === 'NaN' || stringValue === 'Infinity' || stringValue === '-Infinity') {
        console.warn('🚨 Input: Blocking converted invalid value:', stringValue);
        return '';
      }
      
      return stringValue;
    }
    
    // 3. 非數字類型，但仍要檢查是否為 NaN
    if (typeof value === 'number' && isNaN(value)) {
      console.warn('🚨 Input: Blocking NaN in non-number input:', value);
      return '';
    }
    
    // 4. 最終檢查 - 轉換為字串後檢查是否包含 NaN
    const finalString = String(value);
    if (finalString === 'NaN') {
      console.warn('🚨 Input: Final NaN check blocked:', value);
      return '';
    }
    
    return value;
  }, [type, value]);

  // 額外的運行時檢查
  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      if (typeof ultimateSafeValue === 'number' && isNaN(ultimateSafeValue)) {
        console.error('🚨 CRITICAL: NaN value still exists after all processing!', {
          originalValue: value,
          processedValue: ultimateSafeValue,
          type,
          props
        });
      }
      
      if (String(ultimateSafeValue) === 'NaN') {
        console.error('🚨 CRITICAL: String "NaN" detected!', {
          originalValue: value,
          processedValue: ultimateSafeValue,
          type,
          props
        });
      }
    }
  }, [ultimateSafeValue, value, type, props]);

  return (
    <input
      type={type}
      value={ultimateSafeValue}
      data-slot="input"
      className={cn(
        "file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
        "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
        "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
        className
      )}
      {...props}
    />
  )
}

export { Input }
