import * as React from "react"

import { cn } from "@/lib/utils"

function Input({ className, type, value, ...props }: React.ComponentProps<"input">) {
  // 簡化的值處理邏輯
  const safeValue = React.useMemo(() => {
    if (value === undefined || value === null) {
      return '';
    }

    // 對於數字類型輸入，確保值是有效的
    if (type === 'number' && typeof value === 'number') {
      if (isNaN(value) || !isFinite(value)) {
        return '';
      }
      return String(value);
    }

    // 對於字符串值，檢查是否為 'NaN'
    const stringValue = String(value);
    if (stringValue === 'NaN' || stringValue === 'Infinity' || stringValue === '-Infinity') {
      return '';
    }

    return value;
  }, [type, value]);

  return (
    <input
      type={type}
      value={safeValue}
      data-slot="input"
      className={cn(
        "input-base",
        "file:text-text-primary placeholder:text-text-tertiary selection:bg-primary/20 selection:text-primary",
        "hover:border-border-strong transition-colors duration-200",
        "aria-invalid:ring-destructive/20 aria-invalid:border-destructive",
        className
      )}
      {...props}
    />
  )
}

export { Input }
