'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Search, BarChart3, Target, Zap, Users, BookOpen } from 'lucide-react';

const FeaturesSection = () => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // 使用 setTimeout 避免 hydration 警告
    const timer = setTimeout(() => {
      setIsClient(true);
    }, 0);

    return () => clearTimeout(timer);
  }, []);

  const features = [
    {
      icon: Search,
      title: '研究問題',
      subtitle: '了解用戶的提問',
      description: '研究搜索量、提問關鍵字和主題。了解 AI 搜索引擎針對每個產品、地區、行業和細分市場看到的問題。',
      capabilities: [
        '按意圖級別識別熱門問題',
        '了解哪些主題最重要',
        '按地區、角色和產品細分數據'
      ],
      link: '/product/research',
      image: '/images/features/query-analytics.png'
    },
    {
      icon: BarChart3,
      title: '測量可見度',
      subtitle: '跨所有主要 AI 搜索引擎追蹤可見度',
      description: '跨主要 AI 搜索引擎（包括 ChatGPT、AI Oracle、Perplexity、Gemini 和 Claude）追蹤您品牌的表現。',
      capabilities: [
        '查看您與競爭對手的排名',
        '測量聲音份額和品牌可見度',
        '了解您品牌的情感',
        '通過角色級分析了解不同細分市場的可見度'
      ],
      link: '/product/measure',
      image: '/images/features/visibility-tracking.png'
    },
    {
      icon: Target,
      title: '分析回應和來源',
      subtitle: '了解您的出現方式和原因',
      description: '分析回應、主題和引用，以了解推動可見度的因素，以及您需要採取行動的地方。',
      capabilities: [
        '分析回應以找到差距和機會',
        '深入的引用分析以找到高影響力的內容和品牌',
        '分析主題以了解重點關注的地方',
        '查看競爭對手在做什麼'
      ],
      link: '/product/analyze',
      image: '/images/features/response-analysis.png'
    },
    {
      icon: Zap,
      title: '採取行動',
      subtitle: '運行實驗並優化 AI 搜索可見度',
      description: '使用我們的 AI 優化策略手冊來操作程序，包括多變量，改進變異性為 20% 到 40%+。',
      capabilities: [
        '經過驗證的 AI 搜索優化策略手冊',
        '專家級建議幫助您優化',
        '測量實驗對可見度的影響'
      ],
      link: '/product/optimize',
      image: '/images/features/optimization.png'
    }
  ];

  const valueProps = [
    {
      icon: Users,
      title: '專屬分析師支持',
      description: '專屬分析師直接與您的團隊合作'
    },
    {
      icon: Zap,
      title: '實驗平台',
      description: '為實驗和迭代而構建的平台'
    },
    {
      icon: BookOpen,
      title: '可執行的內容簡報',
      description: '您的寫手可以立即執行的內容簡報'
    },
    {
      icon: Target,
      title: '聯盟激活套件',
      description: '即用型聯盟列表和外展策略'
    },
    {
      icon: Search,
      title: '量身定制的策略手冊',
      description: '針對您目標量身定制的行動驅動策略手冊'
    }
  ];

  return (
    <section className="py-24 bg-gradient-to-b from-background to-surface-1/30">
      <div className="container-section space-y-section">
        {/* 增強的標題區域 */}
        <motion.div
          initial={isClient ? { opacity: 0, y: 40 } : { opacity: 1, y: 0 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="text-center max-w-4xl mx-auto"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-surface-1/60 border border-border-light/60 backdrop-blur-sm mb-6">
            <div className="w-2 h-2 rounded-full bg-gradient-to-r from-primary to-accent-green animate-pulse" />
            <span className="text-sm font-medium text-gradient">
              不只是另一個儀表板
            </span>
          </div>

          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-text-primary mb-6 text-balance">
            量身定制的
            <span className="text-gradient"> AI 策略手冊</span>
          </h2>
          <p className="text-xl text-text-secondary mb-12 leading-relaxed text-balance">
            行動驅動的解決方案，構建讓 AI 聊天機器人主動推薦您的品牌策略
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {valueProps.map((prop, index) => (
              <motion.div
                key={prop.title}
                initial={isClient ? { opacity: 0, y: 30 } : { opacity: 1, y: 0 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full text-center bg-gradient-to-br from-dark-800 to-dark-900">
                  <div className="w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <prop.icon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-white mb-2">
                    {prop.title}
                  </h3>
                  <p className="text-gray-300 text-sm">
                    {prop.description}
                  </p>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Main Features */}
        <div className="space-y-20">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={isClient ? { opacity: 0, y: 50 } : { opacity: 1, y: 0 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className={`flex flex-col ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} items-center gap-12`}
            >
              {/* Content */}
              <div className="flex-1 space-y-6">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
                    <feature.icon className="h-5 w-5 text-white" />
                  </div>
                  <span className="text-primary-400 text-sm font-medium uppercase tracking-wider">
                    {feature.subtitle}
                  </span>
                </div>

                <h3 className="text-3xl sm:text-4xl font-bold text-white">
                  {feature.title}
                </h3>

                <p className="text-xl text-gray-300 leading-relaxed">
                  {feature.description}
                </p>

                <div className="space-y-3">
                  <h4 className="text-lg font-semibold text-white">主要功能：</h4>
                  <ul className="space-y-2">
                    {feature.capabilities.map((capability, capIndex) => (
                      <li key={capIndex} className="flex items-start space-x-3">
                        <div className="w-1.5 h-1.5 bg-primary-400 rounded-full mt-2 flex-shrink-0" />
                        <span className="text-gray-300">{capability}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <Button variant="outline" className="mt-6">
                  了解更多
                </Button>
              </div>

              {/* Image Placeholder */}
              <div className="flex-1">
                <div className="bg-gradient-to-br from-dark-700 to-dark-800 rounded-2xl p-8 border border-dark-600">
                  <div className="bg-dark-600 rounded-lg h-64 flex items-center justify-center">
                    <feature.icon className="h-16 w-16 text-gray-400" />
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
