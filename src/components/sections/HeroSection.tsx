'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ArrowRight, Play, Search, Sparkles, BarChart3, TrendingUp } from 'lucide-react';

const HeroSection = () => {
  // 移除所有動畫以避免 Hydration 問題
  // 使用純 CSS 動畫替代 Framer Motion

  const trustedBrands = [
    { name: 'Fiverr', logo: '/images/clients/fiverr.png' },
    { name: 'Cato Networks', logo: '/images/clients/cato.png' },
    { name: 'HiBob', logo: '/images/clients/bob.png' },
    { name: 'MyHeritage', logo: '/images/clients/myheritage.png' },
    { name: 'Monday.com', logo: '/images/clients/monday.png' },
    { name: 'Next', logo: '/images/clients/next.png' },
    { name: 'Wix', logo: '/images/clients/wix.png' },
    { name: 'LHG', logo: '/images/clients/lhg.png' },
  ];

  const features = [
    { icon: Search, title: '發現查詢', description: '找出使用者在AI搜索引擎中提問的關鍵詞和話題' },
    { icon: Sparkles, title: '提升可見度', description: '確保您的內容在AI搜索結果中獲得最大曝光' },
    { icon: BarChart3, title: '數據分析', description: '追踪並分析AI搜索表現的關鍵指標' },
    { icon: TrendingUp, title: '優化轉化', description: '提高從AI搜索引擎到您網站的轉換率' },
  ];

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-hero">
      {/* 增強的背景效果 */}
      <div className="absolute inset-0">
        {/* 精細網格圖案 */}
        <div className="absolute inset-0 opacity-[0.02]"
          style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, hsl(var(--primary)) 1px, transparent 0)`,
            backgroundSize: '32px 32px'
          }}
        />

        {/* 動態漸變模糊形狀 - 更精緻的配色 */}
        <div className="absolute top-20 -left-20 w-[45%] h-[45%] rounded-full bg-gradient-to-br from-primary/8 to-primary-light/12 blur-[120px] animate-pulse-slow" />
        <div className="absolute -bottom-20 -right-20 w-[40%] h-[50%] rounded-full bg-gradient-to-tl from-accent/10 to-accent/15 blur-[140px] animate-pulse-slow"
          style={{ animationDelay: '3s', animationDuration: '10s' }}
        />
        <div className="absolute top-1/3 left-1/4 w-[30%] h-[30%] rounded-full bg-gradient-to-r from-primary-light/6 to-accent/8 blur-[100px] animate-float"
          style={{ animationDelay: '1.5s' }}
        />

        {/* 新增的光暈效果 */}
        <div className="absolute top-1/2 right-1/4 w-[20%] h-[25%] rounded-full bg-gradient-to-bl from-primary/5 to-transparent blur-[80px] animate-bounce-gentle"
          style={{ animationDelay: '2.5s' }}
        />
      </div>

      {/* 主要內容 */}
      <div className="relative z-10 container-section">
        <div className="grid lg:grid-cols-2 gap-16 items-center pt-24 pb-12">
          {/* 左側文字內容 */}
          <div className="text-left space-y-8 animate-fade-in-up">
            {/* 增強的標籤設計 */}
            <div className="inline-flex items-center gap-2 px-5 py-2.5 rounded-full bg-surface-1/60 border border-border-light/60 backdrop-blur-md shadow-sm hover:shadow-md transition-all duration-300">
              <div className="w-2 h-2 rounded-full bg-gradient-to-r from-primary to-accent animate-pulse" />
              <span className="text-sm font-medium text-gradient">
                全新 AI SEO 管理平台
              </span>
            </div>

            {/* 增強的標題設計 */}
            <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold text-text-primary leading-[1.1] tracking-tight text-balance">
              為您的品牌在
              <br />
              <span className="relative inline-block">
                <span className="bg-gradient-to-r from-primary via-primary-light to-accent bg-clip-text text-transparent">
                  AI 搜索引擎中
                </span>
                {/* 動態下劃線效果 */}
                <div}}
                  className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-primary/60 to-accent/60 rounded-full origin-left"
                />
              </span>
              <br className="md:hidden" />
              <span className="relative inline-block">
                獲得
                <span className="text-gradient ml-2">優勢</span>
                {/* 裝飾性光暈 */}
                <div}}
                  className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-primary to-accent rounded-full blur-sm opacity-60 animate-pulse"
                />
              </span>
            </h1>

            {/* 增強的描述 */}
            <p}}
              className="text-lg sm:text-xl text-text-secondary leading-relaxed max-w-2xl text-balance"
            >
              發現客戶的提問，測量可見度，研究引用並優化所有主要 AI 搜索引擎的轉換率，
              <span className="text-text-primary font-medium">全方位提升品牌在新一代搜索中的表現</span>。
            </p>

            {/* 增強的按鈕組 */}
            <div}}
              className="flex flex-col sm:flex-row gap-4"
            >
              <Link href="/auth/login">
                <Button
                  size="lg"
                  className="group btn-primary px-8 py-4 text-base font-semibold bg-gradient-primary hover:shadow-glow transition-all duration-300 transform hover:scale-[1.02]"
                >
                  立即開始免費試用
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-200" />
                </Button>
              </Link>

              <Button
                variant="outline"
                size="lg"
                className="group px-8 py-4 text-base font-medium border-border-medium hover:border-primary/50 bg-surface-1/40 hover:bg-surface-1/60 backdrop-blur-md transition-all duration-300"
              >
                <Play className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform duration-200" />
                觀看產品演示
              </Button>
            </div>

            {/* 增強的品牌信任區域 */}
            <div}}
              className="pt-8 border-t border-border-light/50"
            >
              <p className="text-text-tertiary text-sm mb-6 uppercase tracking-wider font-medium flex items-center gap-2">
                <span className="w-8 h-px bg-gradient-to-r from-primary/50 to-transparent"></span>
                受到領先品牌信賴
              </p>

              <div className="flex flex-wrap items-center gap-8">
                {trustedBrands.map((brand, index) => (
                  <div
                    key={brand.name}}}
                    className="group flex items-center justify-center h-12 min-w-[80px] opacity-60 hover:opacity-100 transition-all duration-300 transform hover:scale-105"
                  >
                    <div className="bg-gradient-to-r from-surface-2 to-surface-3 border border-border-light rounded-lg px-4 py-2 text-xs font-medium text-text-secondary group-hover:text-text-primary group-hover:border-primary/30 transition-all duration-300">
                      {brand.name}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 增強的右側內容區塊 */}
          <div}}
            className="relative z-20 hidden lg:block"
          >
            {/* 主要內容卡片 */}
            <div className="relative card-hover bg-card/60 backdrop-blur-xl rounded-3xl border border-border-light/60 overflow-hidden shadow-2xl">
              {/* 漸變背景 */}
              <div className="absolute inset-0 bg-gradient-to-br from-primary/3 via-transparent to-accent/4" />

              {/* 頂部光暈效果 */}
              <div className="absolute top-0 left-1/2 -translate-x-1/2 w-1/2 h-px bg-gradient-to-r from-transparent via-primary/50 to-transparent" />

              <div className="relative z-10 p-8">
                {/* 增強的標題欄 */}
                <div className="flex items-center justify-between mb-8">
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <div className="h-3 w-3 rounded-full bg-gradient-to-r from-accent to-accent-light animate-pulse" />
                      <div className="absolute inset-0 h-3 w-3 rounded-full bg-accent/30 animate-ping" />
                    </div>
                    <span className="text-sm font-semibold text-text-primary">AI 搜索分析</span>
                  </div>
                  <div className="flex space-x-1.5">
                    <div className="h-2 w-2 rounded-full bg-text-tertiary/40" />
                    <div className="h-2 w-2 rounded-full bg-text-tertiary/40" />
                    <div className="h-2 w-2 rounded-full bg-text-tertiary/40" />
                  </div>
                </div>

                {/* 增強的功能列表 */}
                <div className="space-y-5">
                  {features.map((feature, index) => (
                    <div
                      key={index}}}
                      className="group flex items-start space-x-4 p-4 rounded-xl hover:bg-surface-1/30 transition-all duration-300 cursor-pointer border border-transparent hover:border-border-light/50"
                    >
                      <div className="relative">
                        <div className="bg-gradient-to-br from-surface-1/80 to-surface-2/60 p-3 rounded-xl border border-border-light/40 group-hover:border-primary/30 transition-all duration-300 group-hover:shadow-sm">
                          <feature.icon className="h-5 w-5 text-primary group-hover:text-primary-light transition-colors duration-300" />
                        </div>
                        {/* 裝飾性光點 */}
                        <div className="absolute -top-1 -right-1 w-2 h-2 bg-gradient-to-r from-primary to-accent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-semibold text-text-primary mb-2 group-hover:text-primary transition-colors duration-300">
                          {feature.title}
                        </h4>
                        <p className="text-xs text-text-secondary leading-relaxed">
                          {feature.description}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* 增強的浮動統計卡片 */}
            <div}}}
              className="absolute top-0 left-0 bg-card/90 backdrop-blur-xl rounded-xl border border-border-light/60 p-4 shadow-xl hover:shadow-2xl transition-all duration-300 group"
            >
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gradient-to-br from-primary/10 to-primary/20 rounded-lg">
                  <Search className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <div className="text-xs font-semibold text-text-primary">搜索量增加</div>
                  <div className="text-lg font-bold text-gradient">+45%</div>
                </div>
              </div>
              {/* 裝飾性進度條 */}
              <div className="mt-2 h-1 bg-surface-2 rounded-full overflow-hidden">
                <div}}}
                  className="h-full bg-gradient-to-r from-primary to-primary-light rounded-full"
                />
              </div>
            </div>

            <div}}
              className="absolute bottom-0 right-0 bg-card/90 backdrop-blur-xl rounded-xl border border-border-light/60 p-4 shadow-xl hover:shadow-2xl transition-all duration-300 group"
            >
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gradient-to-br from-accent/10 to-accent/20 rounded-lg">
                  <TrendingUp className="h-4 w-4 text-accent" />
                </div>
                <div>
                  <div className="text-xs font-semibold text-text-primary">轉化率提升</div>
                  <div className="text-lg font-bold text-gradient">+38%</div>
                </div>
              </div>
              {/* 裝飾性進度條 */}
              <div className="mt-2 h-1 bg-surface-2 rounded-full overflow-hidden">
                <div}}}
                  className="h-full bg-gradient-to-r from-accent to-accent-light rounded-full"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 增強的滾動指示器 */}
      <div}}
        className="absolute bottom-12 left-1/2 transform -translate-x-1/2 flex flex-col items-center space-y-2"
      >
        <div className="w-6 h-10 border-2 border-border-medium rounded-full flex justify-center relative overflow-hidden bg-surface-1/20 backdrop-blur-sm">
          <div
            className="w-1.5 h-3 bg-gradient-to-b from-primary to-accent rounded-full mt-2"}}
          />
          {/* 光暈效果 */}
          <div className="absolute inset-0 bg-gradient-to-b from-primary/10 to-accent/10 rounded-full" />
        </div>
        <span className="text-xs text-text-tertiary font-medium">向下滾動探索更多</span>
      </div>
    </section>
  );
};

export default HeroSection;
