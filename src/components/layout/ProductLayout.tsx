'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { ArrowRight, CheckCircle, Brain, TrendingUp, FileText, Search, Star, BarChart } from 'lucide-react';

interface ProductLayoutProps {
  title: string;
  subtitle: string;
  description: string;
  heroImage?: string;
  features: {
    title: string;
    description: string;
    capabilities: string[];
    image?: string;
    icon?: React.ComponentType<any>;
    gradient?: string;
  }[];
  benefits: {
    icon: string;
    title: string;
    description: string;
  }[];
  faqs: {
    question: string;
    answer: string;
  }[];
  ctaTitle: string;
  ctaDescription: string;
}

// 渲染圖標的輔助函數
const renderIcon = (iconName: string, className: string) => {
  switch (iconName) {
    case 'Brain':
      return <Brain className={className} />;
    case 'TrendingUp':
      return <TrendingUp className={className} />;
    case 'FileText':
      return <FileText className={className} />;
    case 'Search':
      return <Search className={className} />;
    case 'Star':
      return <Star className={className} />;
    case 'BarChart':
      return <BarChart className={className} />;
    default:
      return <CheckCircle className={className} />;
  }
};

const ProductLayout: React.FC<ProductLayoutProps> = ({
  title,
  subtitle,
  description,
  heroImage,
  features,
  benefits,
  faqs,
  ctaTitle,
  ctaDescription,
}) => {
  return (
    <div className="min-h-screen pt-16">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900 relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary-600/10 rounded-full blur-3xl animate-float" />
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-accent-600/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }} />
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-12"
          >
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6">
              {title}
            </h1>
            <p className="text-xl sm:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto">
              {subtitle}
            </p>
            <p className="text-lg text-gray-400 mb-8 max-w-3xl mx-auto">
              {description}
            </p>
            <Button variant="default" size="lg" className="group">
              免費試用
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Button>
          </motion.div>

          {/* Hero Image/Dashboard Preview */}
          {heroImage && (
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="max-w-5xl mx-auto"
            >
              <div className="bg-gradient-to-br from-dark-700 to-dark-800 rounded-2xl p-8 border border-dark-600">
                <div className="bg-dark-600 rounded-lg h-96 flex items-center justify-center">
                  <div className="text-gray-400 text-center">
                    <div className="w-24 h-24 bg-primary-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
                      <CheckCircle className="h-12 w-12 text-primary-400" />
                    </div>
                    <p className="text-lg">儀表板預覽</p>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-dark-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              我們不只是一家儀表板公司。
            </h2>
            <p className="text-xl text-gray-300">
              我們幫助分析可見度並將其轉化為行動
            </p>
          </motion.div>

          <div className="space-y-20">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
                className={`flex flex-col ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} items-center gap-12`}
              >
                {/* Content */}
                <div className="flex-1 space-y-6">
                  <h3 className="text-3xl sm:text-4xl font-bold text-white">
                    {feature.title}
                  </h3>
                  <p className="text-xl text-gray-300 leading-relaxed">
                    {feature.description}
                  </p>
                  <div className="space-y-3">
                    <h4 className="text-lg font-semibold text-white">主要功能：</h4>
                    <ul className="space-y-2">
                      {feature.capabilities.map((capability, capIndex) => (
                        <li key={capIndex} className="flex items-start space-x-3">
                          <div className="w-1.5 h-1.5 bg-primary-400 rounded-full mt-2 flex-shrink-0" />
                          <span className="text-gray-300">{capability}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <Button variant="outline">
                    了解更多
                  </Button>
                </div>

                {/* Image */}
                <div className="flex-1">
                  <div className="bg-gradient-to-br from-dark-700 to-dark-800 rounded-2xl p-8 border border-dark-600">
                    <div className="bg-dark-600 rounded-lg h-64 flex items-center justify-center">
                      <CheckCircle className="h-16 w-16 text-gray-400" />
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-dark-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              易於使用的分析
            </h2>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <motion.div
                key={benefit.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full text-center bg-gradient-to-br from-primary-700 to-primary-900">
                  <div className="w-16 h-16 bg-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    {renderIcon(benefit.icon, "h-8 w-8 text-white")}
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-2">
                    {benefit.title}
                  </h3>
                  <p className="text-gray-300">
                    {benefit.description}
                  </p>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-dark-900">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              常見問題
            </h2>
          </motion.div>

          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card>
                  <h3 className="text-lg font-semibold text-white mb-3">
                    {faq.question}
                  </h3>
                  <p className="text-gray-300 leading-relaxed">
                    {faq.answer}
                  </p>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary-600 to-accent-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              {ctaTitle}
            </h2>
            <p className="text-xl text-white/90 mb-8">
              {ctaDescription}
            </p>
            <Button variant="secondary" size="lg" className="bg-white text-primary-600 hover:bg-gray-100">
              開始使用
            </Button>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default ProductLayout;
