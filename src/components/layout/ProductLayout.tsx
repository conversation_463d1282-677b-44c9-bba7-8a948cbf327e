'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { ArrowRight, CheckCircle, Brain, TrendingUp, FileText, Search, Star, BarChart } from 'lucide-react';

interface ProductLayoutProps {
  title: string;
  subtitle: string;
  description: string;
  heroImage?: string;
  features: {
    title: string;
    description: string;
    capabilities: string[];
    image?: string;
    icon?: React.ComponentType<any>;
    gradient?: string;
  }[];
  benefits: {
    icon: string;
    title: string;
    description: string;
  }[];
  faqs: {
    question: string;
    answer: string;
  }[];
  ctaTitle: string;
  ctaDescription: string;
}

// 渲染圖標的輔助函數
const renderIcon = (iconName: string, className: string) => {
  switch (iconName) {
    case 'Brain':
      return <Brain className={className} />;
    case 'TrendingUp':
      return <TrendingUp className={className} />;
    case 'FileText':
      return <FileText className={className} />;
    case 'Search':
      return <Search className={className} />;
    case 'Star':
      return <Star className={className} />;
    case 'BarChart':
      return <BarChart className={className} />;
    default:
      return <CheckCircle className={className} />;
  }
};

const ProductLayout: React.FC<ProductLayoutProps> = ({
  title,
  subtitle,
  description,
  heroImage,
  features,
  benefits,
  faqs,
  ctaTitle,
  ctaDescription,
}) => {
  return (
    <div className="min-h-screen pt-20">
      {/* 增強的 Hero Section */}
      <section className="py-24 bg-gradient-hero relative overflow-hidden">
        <div className="absolute inset-0">
          {/* 精緻的背景效果 */}
          <div className="absolute top-1/4 left-1/4 w-80 h-80 bg-gradient-to-br from-primary/8 to-primary-light/12 rounded-full blur-3xl animate-float" />
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-tl from-accent-green/10 to-accent-green-light/15 rounded-full blur-3xl animate-float" style={{ animationDelay: '3s' }} />
          <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-primary/5 to-accent-green/8 rounded-full blur-2xl animate-pulse-slow" />
        </div>

        <div className="relative z-10 container-section">
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="text-center space-y-8 max-w-5xl mx-auto"
          >
            {/* 產品標籤 */}
            <div className="inline-flex items-center gap-2 px-5 py-2.5 rounded-full bg-surface-1/60 border border-border-light/60 backdrop-blur-md shadow-sm">
              <div className="w-2 h-2 rounded-full bg-gradient-to-r from-primary to-accent-green animate-pulse" />
              <span className="text-sm font-medium text-gradient">
                AI SEO 產品功能
              </span>
            </div>

            <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold text-text-primary leading-tight text-balance">
              {title}
            </h1>

            <p className="text-xl sm:text-2xl text-text-secondary leading-relaxed max-w-4xl mx-auto text-balance">
              {subtitle}
            </p>

            <p className="text-lg text-text-tertiary leading-relaxed max-w-3xl mx-auto text-balance">
              {description}
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-4">
              <Button variant="gradient" size="xl" className="group">
                立即免費試用
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-200" />
              </Button>
              <Button variant="outline" size="xl" className="group">
                查看演示
              </Button>
            </div>
          </motion.div>

          {/* 增強的儀表板預覽 */}
          {heroImage && (
            <motion.div
              initial={{ opacity: 0, y: 60 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.4, ease: "easeOut" }}
              className="max-w-6xl mx-auto mt-16"
            >
              <div className="relative card-hover bg-card/60 backdrop-blur-xl rounded-3xl p-8 border border-border-light/60 shadow-2xl">
                {/* 頂部光暈 */}
                <div className="absolute top-0 left-1/2 -translate-x-1/2 w-1/2 h-px bg-gradient-to-r from-transparent via-primary/50 to-transparent" />

                <div className="bg-surface-1/40 rounded-2xl h-96 flex items-center justify-center relative overflow-hidden">
                  {/* 背景網格 */}
                  <div className="absolute inset-0 opacity-5"
                    style={{
                      backgroundImage: `radial-gradient(circle at 1px 1px, hsl(var(--primary)) 1px, transparent 0)`,
                      backgroundSize: '24px 24px'
                    }}
                  />

                  <div className="text-center z-10">
                    <div className="w-32 h-32 bg-gradient-primary rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-glow">
                      <CheckCircle className="h-16 w-16 text-white drop-shadow-sm" />
                    </div>
                    <p className="text-xl font-semibold text-text-primary mb-2">產品儀表板預覽</p>
                    <p className="text-text-secondary">即將推出完整的產品演示</p>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </section>

      {/* 增強的功能展示區域 */}
      <section className="py-24 bg-gradient-to-b from-background to-surface-1/30">
        <div className="container-section space-y-section">
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            viewport={{ once: true }}
            className="text-center max-w-4xl mx-auto"
          >
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-surface-1/60 border border-border-light/60 backdrop-blur-sm mb-6">
              <div className="w-2 h-2 rounded-full bg-gradient-to-r from-primary to-accent-green animate-pulse" />
              <span className="text-sm font-medium text-gradient">
                不只是儀表板
              </span>
            </div>

            <h2 className="text-4xl sm:text-5xl font-bold text-text-primary mb-6 text-balance">
              我們幫助分析可見度並
              <span className="text-gradient">轉化為行動</span>
            </h2>
            <p className="text-xl text-text-secondary leading-relaxed text-balance">
              深度分析與智能建議相結合，讓每個數據點都成為可執行的策略
            </p>
          </motion.div>

          <div className="space-y-32">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 60 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
                viewport={{ once: true }}
                className={`flex flex-col ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} items-center gap-16`}
              >
                {/* 增強的內容區域 */}
                <div className="flex-1 space-y-8">
                  {/* 功能圖標和標題 */}
                  <div className="flex items-center gap-4">
                    {feature.icon && (
                      <div className={`p-4 rounded-2xl bg-gradient-to-br ${feature.gradient || 'from-primary to-primary-light'} shadow-glow`}>
                        <feature.icon className="h-8 w-8 text-white" />
                      </div>
                    )}
                    <div>
                      <h3 className="text-3xl sm:text-4xl font-bold text-text-primary leading-tight">
                        {feature.title}
                      </h3>
                    </div>
                  </div>

                  <p className="text-xl text-text-secondary leading-relaxed">
                    {feature.description}
                  </p>

                  {/* 增強的功能列表 */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-text-primary flex items-center gap-2">
                      <div className="w-1 h-6 bg-gradient-to-b from-primary to-accent-green rounded-full" />
                      主要功能
                    </h4>
                    <ul className="space-y-3">
                      {feature.capabilities.map((capability, capIndex) => (
                        <motion.li
                          key={capIndex}
                          initial={{ opacity: 0, x: -20 }}
                          whileInView={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.5, delay: 0.4 + capIndex * 0.1 }}
                          viewport={{ once: true }}
                          className="flex items-start space-x-4 group"
                        >
                          <div className="w-6 h-6 bg-gradient-to-br from-primary/20 to-accent-green/20 rounded-lg flex items-center justify-center mt-0.5 group-hover:from-primary/30 group-hover:to-accent-green/30 transition-colors duration-200">
                            <CheckCircle className="h-4 w-4 text-primary" />
                          </div>
                          <span className="text-text-secondary group-hover:text-text-primary transition-colors duration-200 leading-relaxed">
                            {capability}
                          </span>
                        </motion.li>
                      ))}
                    </ul>
                  </div>

                  <Button variant="outline" size="lg" className="group">
                    深入了解功能
                    <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-200" />
                  </Button>
                </div>

                {/* 增強的圖片/演示區域 */}
                <div className="flex-1">
                  <div className="relative card-hover bg-card/60 backdrop-blur-xl rounded-3xl p-8 border border-border-light/60 shadow-2xl">
                    {/* 裝飾性頂部線條 */}
                    <div className="absolute top-0 left-1/2 -translate-x-1/2 w-1/3 h-px bg-gradient-to-r from-transparent via-primary/50 to-transparent" />

                    <div className="bg-surface-1/40 rounded-2xl h-80 flex items-center justify-center relative overflow-hidden">
                      {/* 背景圖案 */}
                      <div className="absolute inset-0 opacity-5"
                        style={{
                          backgroundImage: `radial-gradient(circle at 1px 1px, hsl(var(--primary)) 1px, transparent 0)`,
                          backgroundSize: '20px 20px'
                        }}
                      />

                      {/* 功能預覽內容 */}
                      <div className="text-center z-10">
                        {feature.icon && (
                          <div className={`w-20 h-20 bg-gradient-to-br ${feature.gradient || 'from-primary to-primary-light'} rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-glow`}>
                            <feature.icon className="h-10 w-10 text-white" />
                          </div>
                        )}
                        <p className="text-lg font-semibold text-text-primary mb-2">功能演示</p>
                        <p className="text-text-secondary">即將推出互動式演示</p>
                      </div>

                      {/* 裝飾性光暈 */}
                      <div className="absolute bottom-4 right-4 w-16 h-16 bg-gradient-to-br from-primary/20 to-accent-green/20 rounded-full blur-xl" />
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-dark-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              易於使用的分析
            </h2>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <motion.div
                key={benefit.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full text-center bg-gradient-to-br from-primary-700 to-primary-900">
                  <div className="w-16 h-16 bg-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    {renderIcon(benefit.icon, "h-8 w-8 text-white")}
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-2">
                    {benefit.title}
                  </h3>
                  <p className="text-gray-300">
                    {benefit.description}
                  </p>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-dark-900">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              常見問題
            </h2>
          </motion.div>

          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card>
                  <h3 className="text-lg font-semibold text-white mb-3">
                    {faq.question}
                  </h3>
                  <p className="text-gray-300 leading-relaxed">
                    {faq.answer}
                  </p>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary-600 to-accent-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              {ctaTitle}
            </h2>
            <p className="text-xl text-white/90 mb-8">
              {ctaDescription}
            </p>
            <Button variant="secondary" size="lg" className="bg-white text-primary-600 hover:bg-gray-100">
              開始使用
            </Button>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default ProductLayout;
