'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Menu, X, ChevronDown, Search, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);

    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    // 初始檢查滾動位置
    handleScroll();

    // 確保在客戶端才添加事件監聽器
    if (typeof window !== 'undefined') {
      window.addEventListener('scroll', handleScroll);
      return () => window.removeEventListener('scroll', handleScroll);
    }

    // 如果不在客戶端，返回空的清理函數
    return () => {};
  }, []);

  const toggleSubmenu = (name: string) => {
    setActiveSubmenu(activeSubmenu === name ? null : name);
  };

  const navItems = [
    {
      name: '產品',
      href: '/product',
      submenu: [
        { name: '研究功能', href: '/product/research', icon: Search },
        { name: '測量功能', href: '/product/measure', icon: Sparkles },
        { name: '分析功能', href: '/product/analyze', icon: Sparkles },
        { name: '優化功能', href: '/product/optimize', icon: Sparkles },
        { name: 'AI內容生成器', href: '/ai-content-generator', icon: Sparkles },
      ]
    },
    {
      name: '解決方案',
      href: '/solutions',
      submenu: [
        { name: '內部團隊', href: '/solutions/internal-teams', icon: Sparkles },
        { name: '代理商', href: '/solutions/agencies', icon: Sparkles },
        { name: '企業客戶', href: '/solutions/enterprise', icon: Sparkles },
      ]
    },
    {
      name: '公司',
      href: '/company',
      submenu: [
        { name: '關於我們', href: '/about', icon: Sparkles },
        { name: '職業機會', href: '/careers', icon: Sparkles },
        { name: '聯絡我們', href: '/contact', icon: Sparkles },
      ]
    },
    { name: '定價', href: '/pricing' },
    {
      name: '資源',
      href: '/resources',
      submenu: [
        { name: '部落格', href: '/blog', icon: Sparkles },
        { name: '客戶案例', href: '/resources/customers', icon: Sparkles },
        { name: '頂級合作夥伴', href: '/top-affiliates', icon: Sparkles },
      ]
    },
  ];

  return (
    <nav className={cn(
      'fixed top-0 left-0 right-0 z-50 transition-all duration-300',
      // 修復 Hydration 問題：確保服務器端和客戶端初始狀態一致
      isScrolled
        ? 'bg-card/95 backdrop-blur-xl border-b border-border-light shadow-card'
        : 'bg-card/80 backdrop-blur-md'
    )}>
      <div className="container-section">
        <div className="flex justify-between items-center h-20">
          {/* 增強的 Logo 設計 */}
          <Link href="/" className="flex items-center space-x-3 group">
            <div className="relative w-11 h-11 bg-gradient-primary rounded-xl flex items-center justify-center overflow-hidden shadow-button group-hover:shadow-glow transition-all duration-300 transform group-hover:scale-105">
              <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"></div>
              <span className="text-white font-bold text-lg relative z-10 drop-shadow-sm">AI</span>
              {/* 裝飾性光暈 */}
              <div className="absolute -inset-1 bg-gradient-primary rounded-xl blur opacity-30 group-hover:opacity-50 transition-opacity duration-300 -z-10"></div>
            </div>
            <div className="flex flex-col">
              <span className="text-xl font-bold text-gradient group-hover:scale-105 transition-transform duration-300 hidden sm:block">
                AI SEO 優化王
              </span>
              <span className="text-lg font-bold text-gradient group-hover:scale-105 transition-transform duration-300 sm:hidden">
                AI SEO 優化
              </span>
              <span className="text-xs text-text-tertiary hidden sm:block">
                新一代 AI 搜索優化平台
              </span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center">
            <ul className="flex items-center space-x-1">
              {navItems.map((item) => (
                <li key={item.name} className="relative">
                  {item.submenu ? (
                    <button
                      onClick={() => toggleSubmenu(item.name)}
                      className={cn(
                        "flex items-center gap-2 px-4 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 focus:ring-offset-background",
                        activeSubmenu === item.name
                          ? "bg-surface-hover text-text-primary shadow-sm"
                          : "text-text-secondary hover:text-text-primary hover:bg-surface-hover/60"
                      )}
                      aria-expanded={activeSubmenu === item.name}
                      aria-haspopup="true"
                    >
                      {item.name}
                      <ChevronDown className={cn(
                        "h-4 w-4 transition-all duration-200",
                        activeSubmenu === item.name ? "rotate-180" : ""
                      )} />
                    </button>
                  ) : (
                    <Link 
                      href={item.href}
                      className="flex items-center px-4 py-2 rounded-lg text-sm font-medium text-foreground/80 hover:text-foreground hover:bg-surface-2/80 transition-colors duration-200"
                    >
                      {item.name}
                    </Link>
                  )}
                  
                  {item.submenu && activeSubmenu === item.name && (
                    <div className="absolute left-0 mt-2 w-64 bg-card border border-border rounded-xl shadow-lg shadow-primary/10 backdrop-blur-sm py-2 z-50 overflow-hidden">
                      {item.submenu.map((subitem) => (
                        <Link
                          key={subitem.name}
                          href={subitem.href}
                          className="flex items-center gap-2 px-4 py-2.5 text-sm text-foreground/80 hover:bg-surface-3 hover:text-foreground transition-colors"
                          onClick={() => setActiveSubmenu(null)}
                        >
                          {subitem.icon && (
                            <subitem.icon className="h-4 w-4 text-primary-light" />
                          )}
                          {subitem.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </li>
              ))}
            </ul>
            
            <div className="flex items-center ml-4 pl-4 border-l border-border/30">
              <Link href="/auth/login">
                <Button variant="ghost" size="sm" className="text-foreground/70 hover:text-foreground">
                  登入
                </Button>
              </Link>
            </div>
          </div>

          {/* Mobile Navigation Toggle */}
          <div className="md:hidden flex items-center">
            <button
              className="text-foreground/80 hover:text-foreground p-2 rounded-lg bg-surface-2/50 hover:bg-surface-2 transition-colors focus:outline-none focus:ring-2 focus:ring-primary/50"
              onClick={() => setIsOpen(!isOpen)}
              aria-label={isOpen ? "關閉選單" : "開啟選單"}
              aria-expanded={isOpen}
              aria-controls="mobile-menu"
            >
              {isOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div
            id="mobile-menu"
            className="md:hidden py-4 border-t border-border/30 bg-surface-1/95 backdrop-blur-lg"
            role="navigation"
            aria-label="移動端導航選單"
          >
            <ul className="space-y-0.5">
              {navItems.map((item) => (
                <li key={item.name}>
                  {item.submenu ? (
                    <div className="py-1">
                      <button
                        onClick={() => toggleSubmenu(item.name)}
                        className="flex items-center justify-between w-full px-4 py-3 text-foreground/90 hover:text-foreground bg-surface-2/20 hover:bg-surface-2/50 rounded-lg"
                      >
                        <span className="font-medium">{item.name}</span>
                        <ChevronDown className={cn(
                          "h-4 w-4 transition-transform duration-200",
                          activeSubmenu === item.name ? "rotate-180" : ""
                        )} />
                      </button>
                      
                      {activeSubmenu === item.name && (
                        <ul className="mt-1 space-y-0.5 px-2">
                          {item.submenu.map((subitem) => (
                            <li key={subitem.name}>
                              <Link
                                href={subitem.href}
                                onClick={() => {
                                  setActiveSubmenu(null);
                                  setIsOpen(false);
                                }}
                                className="flex items-center gap-2 px-4 py-2.5 text-sm text-foreground/80 hover:text-foreground bg-surface-2/20 hover:bg-surface-2/50 rounded-lg"
                              >
                                {subitem.icon && (
                                  <subitem.icon className="h-4 w-4 text-primary-light" />
                                )}
                                {subitem.name}
                              </Link>
                            </li>
                          ))}
                        </ul>
                      )}
                    </div>
                  ) : (
                    <Link
                      href={item.href}
                      onClick={() => setIsOpen(false)}
                      className="block px-4 py-3 text-foreground/90 hover:text-foreground bg-surface-2/20 hover:bg-surface-2/50 rounded-lg font-medium"
                    >
                      {item.name}
                    </Link>
                  )}
                </li>
              ))}
            </ul>
            
            <div className="mt-4 px-2">
              <Link href="/auth/login" className="block">
                <Button variant="outline" size="sm" className="w-full">
                  登入
                </Button>
              </Link>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;
