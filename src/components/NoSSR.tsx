/**
 * NoSSR 組件 - 避免 Hydration 錯誤
 * 
 * 這個組件確保包裝的內容只在客戶端渲染，
 * 避免 SSR/CSR 不匹配導致的 Hydration 錯誤
 */

'use client';

import { useEffect, useState } from 'react';

interface NoSSRProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export default function NoSSR({ children, fallback = null }: NoSSRProps) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}
