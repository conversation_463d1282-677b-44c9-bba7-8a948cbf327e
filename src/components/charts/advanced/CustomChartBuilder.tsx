/**
 * 自定義圖表構建器
 * 允許用戶自定義圖表配置和樣式
 */

import React, { useState, useCallback, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Settings, 
  Palette, 
  Bar<PERSON>hart3, 
  <PERSON><PERSON>hart as LineChartIcon,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Save,
  Download,
  Eye,
  Wand2
} from 'lucide-react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@/components/charts';
import { CHART_COLORS } from '../ChartConfig';

// 圖表配置接口
export interface ChartConfig {
  type: 'line' | 'bar' | 'pie' | 'area';
  title: string;
  description: string;
  xAxisKey: string;
  yAxisKey: string;
  colors: string[];
  showGrid: boolean;
  showTooltip: boolean;
  showLegend: boolean;
  animationDuration: number;
  height: number;
  responsive: boolean;
  theme: 'light' | 'dark' | 'auto';
  customStyles: {
    backgroundColor: string;
    borderColor: string;
    borderWidth: number;
    borderRadius: number;
    fontSize: number;
    fontFamily: string;
  };
}

// 預設配置
const defaultConfig: ChartConfig = {
  type: 'line',
  title: '自定義圖表',
  description: '使用圖表構建器創建的圖表',
  xAxisKey: 'date',
  yAxisKey: 'value',
  colors: [CHART_COLORS.primary, CHART_COLORS.secondary],
  showGrid: true,
  showTooltip: true,
  showLegend: true,
  animationDuration: 750,
  height: 400,
  responsive: true,
  theme: 'light',
  customStyles: {
    backgroundColor: '#ffffff',
    borderColor: '#e5e7eb',
    borderWidth: 1,
    borderRadius: 8,
    fontSize: 12,
    fontFamily: 'Inter, sans-serif',
  },
};

// 預設主題
const themes = {
  light: {
    backgroundColor: '#ffffff',
    borderColor: '#e5e7eb',
    textColor: '#374151',
    gridColor: '#f3f4f6',
  },
  dark: {
    backgroundColor: '#1f2937',
    borderColor: '#374151',
    textColor: '#f9fafb',
    gridColor: '#374151',
  },
  blue: {
    backgroundColor: '#eff6ff',
    borderColor: '#3b82f6',
    textColor: '#1e40af',
    gridColor: '#dbeafe',
  },
  green: {
    backgroundColor: '#f0fdf4',
    borderColor: '#22c55e',
    textColor: '#166534',
    gridColor: '#dcfce7',
  },
};

// 顏色預設
const colorPresets = [
  { name: '藍色系', colors: ['#3b82f6', '#60a5fa', '#93c5fd'] },
  { name: '綠色系', colors: ['#22c55e', '#4ade80', '#86efac'] },
  { name: '紫色系', colors: ['#8b5cf6', '#a78bfa', '#c4b5fd'] },
  { name: '橙色系', colors: ['#f97316', '#fb923c', '#fdba74'] },
  { name: '紅色系', colors: ['#ef4444', '#f87171', '#fca5a5'] },
  { name: '彩虹', colors: ['#ef4444', '#f97316', '#eab308', '#22c55e', '#3b82f6', '#8b5cf6'] },
];

// 添加安全的數字轉換函數
const safeParseInt = (value: string, defaultValue: number = 0): number => {
  if (value === '' || value === undefined || value === null) {
    return defaultValue;
  }
  
  const parsed = parseInt(String(value));
  if (isNaN(parsed)) {
    return defaultValue;
  }
  
  return parsed;
};

// 自定義圖表構建器組件
export function CustomChartBuilder({
  data = [],
  onConfigChange,
  onSave,
  className,
}: {
  data?: any[];
  onConfigChange?: (config: ChartConfig) => void;
  onSave?: (config: ChartConfig) => void;
  className?: string;
}) {
  const [config, setConfig] = useState<ChartConfig>(defaultConfig);
  const [activeTab, setActiveTab] = useState('basic');
  const [previewMode, setPreviewMode] = useState(true);

  // 更新配置
  const updateConfig = useCallback((updates: Partial<ChartConfig>) => {
    const newConfig = { ...config, ...updates };
    setConfig(newConfig);
    onConfigChange?.(newConfig);
  }, [config, onConfigChange]);

  // 更新自定義樣式
  const updateCustomStyles = useCallback((updates: Partial<ChartConfig['customStyles']>) => {
    updateConfig({
      customStyles: { ...config.customStyles, ...updates }
    });
  }, [config.customStyles, updateConfig]);

  // 應用主題
  const applyTheme = useCallback((themeName: keyof typeof themes) => {
    const theme = themes[themeName];
    updateConfig({
      theme: themeName as any,
      customStyles: {
        ...config.customStyles,
        backgroundColor: theme.backgroundColor,
        borderColor: theme.borderColor,
      }
    });
  }, [config.customStyles, updateConfig]);

  // 應用顏色預設
  const applyColorPreset = useCallback((colors: string[]) => {
    updateConfig({ colors });
  }, [updateConfig]);

  // 獲取可用的數據鍵
  const availableKeys = useMemo(() => {
    if (data.length === 0) return [];
    return Object.keys(data[0]);
  }, [data]);

  // 渲染圖表預覽
  const renderChartPreview = () => {
    if (!previewMode || data.length === 0) {
      return (
        <div className="h-96 flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg">
          <div className="text-center">
            <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">
              {data.length === 0 ? '請提供數據以預覽圖表' : '預覽已關閉'}
            </p>
          </div>
        </div>
      );
    }

    const chartProps = {
      title: config.title,
      description: config.description,
      data,
      xAxisKey: config.xAxisKey,
      yAxisKey: config.yAxisKey,
      height: config.height,
      colors: config.colors,
      showGrid: config.showGrid,
      showTooltip: config.showTooltip,
      showLegend: config.showLegend,
      animationDuration: config.animationDuration,
      responsive: config.responsive,
    };

    switch (config.type) {
      case 'bar':
        return <BarChart {...chartProps} />;
      case 'pie':
        return <PieChart {...chartProps} />;
      case 'area':
        return <AreaChart {...chartProps} />;
      default:
        return <LineChart {...chartProps} />;
    }
  };

  return (
    <div className={`grid grid-cols-1 lg:grid-cols-2 gap-6 ${className}`}>
      {/* 配置面板 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Settings className="h-5 w-5 mr-2" />
                圖表配置
              </CardTitle>
              <CardDescription>自定義圖表的外觀和行為</CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={() => setPreviewMode(!previewMode)}>
                <Eye className="h-4 w-4 mr-1" />
                {previewMode ? '隱藏' : '顯示'}預覽
              </Button>
              {onSave && (
                <Button size="sm" onClick={() => onSave(config)}>
                  <Save className="h-4 w-4 mr-1" />
                  保存
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">基本</TabsTrigger>
              <TabsTrigger value="style">樣式</TabsTrigger>
              <TabsTrigger value="colors">顏色</TabsTrigger>
              <TabsTrigger value="advanced">高級</TabsTrigger>
            </TabsList>

            {/* 基本配置 */}
            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="chart-type">圖表類型</Label>
                  <Select value={config.type} onValueChange={(value: any) => updateConfig({ type: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="line">折線圖</SelectItem>
                      <SelectItem value="bar">柱狀圖</SelectItem>
                      <SelectItem value="pie">圓餅圖</SelectItem>
                      <SelectItem value="area">面積圖</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="height">高度</Label>
                  <Input
                    id="height"
                    type="number"
                    value={config.height}
                    onChange={(e) => updateConfig({ height: Math.max(200, safeParseInt(e.target.value) || 400) })}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="title">標題</Label>
                <Input
                  id="title"
                  value={config.title}
                  onChange={(e) => updateConfig({ title: e.target.value })}
                />
              </div>

              <div>
                <Label htmlFor="description">描述</Label>
                <Input
                  id="description"
                  value={config.description}
                  onChange={(e) => updateConfig({ description: e.target.value })}
                />
              </div>

              {availableKeys.length > 0 && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>X 軸數據</Label>
                    <Select value={config.xAxisKey} onValueChange={(value) => updateConfig({ xAxisKey: value })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {availableKeys.map(key => (
                          <SelectItem key={key} value={key}>{key}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>Y 軸數據</Label>
                    <Select value={config.yAxisKey} onValueChange={(value) => updateConfig({ yAxisKey: value })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {availableKeys.map(key => (
                          <SelectItem key={key} value={key}>{key}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}
            </TabsContent>

            {/* 樣式配置 */}
            <TabsContent value="style" className="space-y-4">
              <div className="flex items-center justify-between">
                <Label>顯示網格</Label>
                <Switch
                  checked={config.showGrid}
                  onCheckedChange={(checked) => updateConfig({ showGrid: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label>顯示工具提示</Label>
                <Switch
                  checked={config.showTooltip}
                  onCheckedChange={(checked) => updateConfig({ showTooltip: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label>顯示圖例</Label>
                <Switch
                  checked={config.showLegend}
                  onCheckedChange={(checked) => updateConfig({ showLegend: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label>響應式</Label>
                <Switch
                  checked={config.responsive}
                  onCheckedChange={(checked) => updateConfig({ responsive: checked })}
                />
              </div>

              <div>
                <Label>動畫時長: {config.animationDuration || 1000}ms</Label>
                <Slider
                  value={[config.animationDuration || 1000]}
                  onValueChange={(value) => updateConfig({ animationDuration: value[0] || 1000 })}
                  max={2000}
                  step={100}
                  className="mt-2"
                />
              </div>

              <Separator />

              <div>
                <Label className="text-sm font-medium">主題預設</Label>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {Object.entries(themes).map(([name, theme]) => (
                    <Button
                      key={name}
                      variant="outline"
                      size="sm"
                      onClick={() => applyTheme(name as keyof typeof themes)}
                      className="justify-start"
                    >
                      <div 
                        className="w-4 h-4 rounded mr-2"
                        style={{ backgroundColor: theme.backgroundColor, border: `1px solid ${theme.borderColor}` }}
                      />
                      {name}
                    </Button>
                  ))}
                </div>
              </div>
            </TabsContent>

            {/* 顏色配置 */}
            <TabsContent value="colors" className="space-y-4">
              <div>
                <Label className="text-sm font-medium">顏色預設</Label>
                <div className="grid grid-cols-1 gap-2 mt-2">
                  {colorPresets.map((preset, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      size="sm"
                      onClick={() => applyColorPreset(preset.colors)}
                      className="justify-start h-auto p-3"
                    >
                      <div className="flex items-center space-x-2">
                        <div className="flex space-x-1">
                          {preset.colors.slice(0, 3).map((color, colorIndex) => (
                            <div
                              key={colorIndex}
                              className="w-4 h-4 rounded"
                              style={{ backgroundColor: color }}
                            />
                          ))}
                        </div>
                        <span>{preset.name}</span>
                      </div>
                    </Button>
                  ))}
                </div>
              </div>

              <Separator />

              <div>
                <Label className="text-sm font-medium">當前顏色</Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {config.colors.map((color, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <div
                        className="w-8 h-8 rounded border"
                        style={{ backgroundColor: color }}
                      />
                      <Input
                        type="color"
                        value={color}
                        onChange={(e) => {
                          const newColors = [...config.colors];
                          newColors[index] = e.target.value;
                          updateConfig({ colors: newColors });
                        }}
                        className="w-16 h-8 p-1"
                      />
                    </div>
                  ))}
                </div>
              </div>
            </TabsContent>

            {/* 高級配置 */}
            <TabsContent value="advanced" className="space-y-4">
              <div>
                <Label htmlFor="bg-color">背景顏色</Label>
                <Input
                  id="bg-color"
                  type="color"
                  value={config.customStyles.backgroundColor}
                  onChange={(e) => updateCustomStyles({ backgroundColor: e.target.value })}
                />
              </div>

              <div>
                <Label htmlFor="border-color">邊框顏色</Label>
                <Input
                  id="border-color"
                  type="color"
                  value={config.customStyles.borderColor}
                  onChange={(e) => updateCustomStyles({ borderColor: e.target.value })}
                />
              </div>

              <div>
                <Label>邊框寬度: {config.customStyles.borderWidth || 1}px</Label>
                <Slider
                  value={[config.customStyles.borderWidth || 1]}
                  onValueChange={(value) => updateCustomStyles({ borderWidth: value[0] || 1 })}
                  max={5}
                  step={1}
                  className="mt-2"
                />
              </div>

              <div>
                <Label>圓角: {config.customStyles.borderRadius || 0}px</Label>
                <Slider
                  value={[config.customStyles.borderRadius || 0]}
                  onValueChange={(value) => updateCustomStyles({ borderRadius: value[0] || 0 })}
                  max={20}
                  step={1}
                  className="mt-2"
                />
              </div>

              <div>
                <Label>字體大小: {config.customStyles.fontSize || 12}px</Label>
                <Slider
                  value={[config.customStyles.fontSize || 12]}
                  onValueChange={(value) => updateCustomStyles({ fontSize: value[0] || 12 })}
                  min={8}
                  max={24}
                  step={1}
                  className="mt-2"
                />
              </div>

              <div>
                <Label htmlFor="font-family">字體</Label>
                <Select 
                  value={config.customStyles.fontFamily} 
                  onValueChange={(value) => updateCustomStyles({ fontFamily: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Inter, sans-serif">Inter</SelectItem>
                    <SelectItem value="Roboto, sans-serif">Roboto</SelectItem>
                    <SelectItem value="Arial, sans-serif">Arial</SelectItem>
                    <SelectItem value="Georgia, serif">Georgia</SelectItem>
                    <SelectItem value="monospace">Monospace</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* 預覽面板 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Eye className="h-5 w-5 mr-2" />
            圖表預覽
          </CardTitle>
          <CardDescription>實時預覽您的圖表配置</CardDescription>
        </CardHeader>
        <CardContent>
          {renderChartPreview()}
        </CardContent>
      </Card>
    </div>
  );
}

export default CustomChartBuilder;
