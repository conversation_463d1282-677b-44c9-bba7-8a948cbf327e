/**
 * 圖表配置和主題系統
 * 統一管理所有圖表的樣式、顏色和配置
 */

import { ChartOptions } from 'chart.js';

// 主題顏色配置 - 明亮清新的色調
export const CHART_COLORS = {
  // 主要品牌色 - 藍色系
  primary: '#4F46E5',
  primaryLight: '#818CF8',
  primaryDark: '#3730A3',
  
  // 次要顏色 - 紫色系
  secondary: '#7C3AED',
  secondaryLight: '#A78BFA',
  secondaryDark: '#5B21B6',
  
  // 成功/警告/錯誤色
  success: '#059669',
  warning: '#D97706',
  error: '#DC2626',
  
  // 灰階色 - 淺色主題優化
  gray: {
    50: '#F9FAFB',
    100: '#F3F4F6',
    200: '#E5E7EB',
    300: '#D1D5DB',
    400: '#9CA3AF',
    500: '#6B7280',
    600: '#4B5563',
    700: '#374151',
    800: '#1F2937',
    900: '#111827',
  },
  
  // 圖表專用色彩調色板 - 明亮清新的色調
  palette: [
    '#4F46E5', // 靛藍色
    '#059669', // 翠綠色
    '#DC2626', // 紅色
    '#D97706', // 橙色
    '#7C3AED', // 紫色
    '#0891B2', // 青色
    '#65A30D', // 萊姆色
    '#EA580C', // 橙紅色
    '#BE185D', // 粉紅色
    '#4338CA', // 深藍色
  ],
  
  // 漸變色 - 明亮主題
  gradients: {
    primary: ['#4F46E5', '#818CF8'],
    secondary: ['#7C3AED', '#A78BFA'],
    success: ['#059669', '#10B981'],
    warning: ['#D97706', '#F59E0B'],
    error: ['#DC2626', '#F87171'],
  },
};

// 字體配置
export const CHART_FONTS = {
  family: '"Inter", "Noto Sans TC", "Source Han Sans TC", sans-serif',
  size: {
    xs: 10,
    sm: 12,
    base: 14,
    lg: 16,
    xl: 18,
    '2xl': 20,
  },
  weight: {
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
  },
};

// 響應式斷點
export const CHART_BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
};

// Chart.js 默認配置 - 淺色主題優化
export const DEFAULT_CHART_OPTIONS: ChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  interaction: {
    intersect: false,
    mode: 'index',
  },
  plugins: {
    legend: {
      display: true,
      position: 'top',
      labels: {
        font: {
          family: CHART_FONTS.family,
          size: CHART_FONTS.size.sm,
          weight: CHART_FONTS.weight.medium,
        },
        color: CHART_COLORS.gray[700], // 深灰色文字，適合淺色背景
        usePointStyle: true,
        padding: 20,
      },
    },
    tooltip: {
      backgroundColor: '#FFFFFF', // 白色背景
      titleColor: CHART_COLORS.gray[900], // 深色標題
      bodyColor: CHART_COLORS.gray[700], // 深灰色內容
      borderColor: CHART_COLORS.gray[300], // 淺灰色邊框
      borderWidth: 1,
      cornerRadius: 8,
      titleFont: {
        family: CHART_FONTS.family,
        size: CHART_FONTS.size.sm,
        weight: CHART_FONTS.weight.semibold,
      },
      bodyFont: {
        family: CHART_FONTS.family,
        size: CHART_FONTS.size.sm,
        weight: CHART_FONTS.weight.normal,
      },
      padding: 12,
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    },
  },
  scales: {
    x: {
      grid: {
        color: CHART_COLORS.gray[200],
        drawBorder: false,
      },
      ticks: {
        font: {
          family: CHART_FONTS.family,
          size: CHART_FONTS.size.xs,
          weight: CHART_FONTS.weight.normal,
        },
        color: CHART_COLORS.gray[600],
      },
    },
    y: {
      grid: {
        color: CHART_COLORS.gray[200],
        drawBorder: false,
      },
      ticks: {
        font: {
          family: CHART_FONTS.family,
          size: CHART_FONTS.size.xs,
          weight: CHART_FONTS.weight.normal,
        },
        color: CHART_COLORS.gray[600],
      },
    },
  },
  elements: {
    point: {
      radius: 4,
      hoverRadius: 6,
      borderWidth: 2,
    },
    line: {
      borderWidth: 2,
      tension: 0.4,
    },
    bar: {
      borderRadius: 4,
      borderSkipped: false,
    },
  },
  animation: {
    duration: 750,
    easing: 'easeInOutQuart',
  },
};

// Recharts 默認配置 - 淺色主題優化
export const DEFAULT_RECHARTS_CONFIG = {
  margin: { top: 20, right: 30, left: 20, bottom: 20 },
  colors: CHART_COLORS.palette,
  fontSize: CHART_FONTS.size.sm,
  fontFamily: CHART_FONTS.family,
  gridColor: CHART_COLORS.gray[200],
  textColor: CHART_COLORS.gray[700],
  tooltipStyle: {
    backgroundColor: '#FFFFFF',
    border: `1px solid ${CHART_COLORS.gray[300]}`,
    borderRadius: '8px',
    color: CHART_COLORS.gray[900],
    fontSize: CHART_FONTS.size.sm,
    fontFamily: CHART_FONTS.family,
    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  },
};

// 圖表類型配置
export const CHART_TYPES = {
  LINE: 'line',
  BAR: 'bar',
  PIE: 'pie',
  DOUGHNUT: 'doughnut',
  RADAR: 'radar',
  AREA: 'area',
  SCATTER: 'scatter',
} as const;

// 動畫配置
export const CHART_ANIMATIONS = {
  fadeIn: {
    duration: 500,
    easing: 'easeInOutQuart',
  },
  slideUp: {
    duration: 750,
    easing: 'easeOutQuart',
  },
  bounce: {
    duration: 1000,
    easing: 'easeOutBounce',
  },
  smooth: {
    duration: 800,
    easing: 'easeInOutCubic',
  },
  fast: {
    duration: 300,
    easing: 'easeOutQuad',
  },
  realTime: {
    duration: 200,
    easing: 'linear',
  },
};

// 實時更新動畫配置
export const REAL_TIME_ANIMATIONS = {
  dataUpdate: {
    duration: 400,
    easing: 'easeInOutQuart',
    delay: 0,
  },
  pointHighlight: {
    duration: 200,
    easing: 'easeOutBack',
    scale: 1.2,
  },
  lineGrow: {
    duration: 600,
    easing: 'easeOutCubic',
  },
  barGrow: {
    duration: 500,
    easing: 'easeOutBack',
  },
  pieRotate: {
    duration: 800,
    easing: 'easeInOutQuart',
  },
};

// 工具函數：獲取響應式圖表高度
export function getResponsiveHeight(breakpoint: keyof typeof CHART_BREAKPOINTS): number {
  const heights = {
    sm: 200,
    md: 250,
    lg: 300,
    xl: 350,
    '2xl': 400,
  };
  return heights[breakpoint] || heights.md;
}

// 工具函數：生成漸變色
export function createGradient(
  ctx: CanvasRenderingContext2D,
  colors: string[],
  direction: 'vertical' | 'horizontal' = 'vertical'
): CanvasGradient {
  const gradient = direction === 'vertical' 
    ? ctx.createLinearGradient(0, 0, 0, ctx.canvas.height)
    : ctx.createLinearGradient(0, 0, ctx.canvas.width, 0);
  
  colors.forEach((color, index) => {
    gradient.addColorStop(index / (colors.length - 1), color);
  });
  
  return gradient;
}

// 工具函數：格式化數值
export function formatChartValue(value: number, type: 'number' | 'percentage' | 'currency' = 'number'): string {
  switch (type) {
    case 'percentage':
      return `${value.toFixed(1)}%`;
    case 'currency':
      return `$${value.toLocaleString()}`;
    default:
      return value.toLocaleString();
  }
}

// 工具函數：獲取顏色透明度版本
export function getColorWithOpacity(color: string, opacity: number): string {
  // 如果是 hex 顏色，轉換為 rgba
  if (color.startsWith('#')) {
    const hex = color.slice(1);
    const r = parseInt(hex.slice(0, 2), 16);
    const g = parseInt(hex.slice(2, 4), 16);
    const b = parseInt(hex.slice(4, 6), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }
  
  // 如果已經是 rgba，替換透明度
  if (color.startsWith('rgba')) {
    return color.replace(/[\d.]+\)$/g, `${opacity})`);
  }
  
  // 如果是 rgb，轉換為 rgba
  if (color.startsWith('rgb')) {
    return color.replace('rgb', 'rgba').replace(')', `, ${opacity})`);
  }
  
  return color;
}

export default {
  CHART_COLORS,
  CHART_FONTS,
  CHART_BREAKPOINTS,
  DEFAULT_CHART_OPTIONS,
  DEFAULT_RECHARTS_CONFIG,
  CHART_TYPES,
  CHART_ANIMATIONS,
  getResponsiveHeight,
  createGradient,
  formatChartValue,
  getColorWithOpacity,
};
