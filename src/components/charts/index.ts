/**
 * 圖表組件統一導出
 * 提供所有圖表組件的統一入口
 */

// 基礎圖表組件系統
export { default as BaseChart, ChartSkeleton } from './BaseChart';
export type { BaseChartProps } from './BaseChart';

// 圖表配置和工具
export * from './ChartConfig';

// Chart.js 組件
export {
  default as ChartJSWrapper,
  LineChart as ChartJSLineChart,
  BarChart as ChartJSBarChart,
  PieChart as ChartJSPieChart,
  <PERSON><PERSON><PERSON><PERSON><PERSON> as ChartJSDoughnut<PERSON>hart,
  RadarChart as ChartJSRadarChart,
  AreaChart as ChartJ<PERSON>rea<PERSON><PERSON>,
  ScatterChart as ChartJSScatterChart,
} from './ChartJSWrapper';
export type { ChartJSWrapperProps } from './ChartJSWrapper';

// Recharts 組件
export {
  LineChart as RechartsLineChart,
  BarChart as RechartsBarChart,
  AreaChart as RechartsAreaChart,
  PieChart as RechartsPieChart,
  RadarChart as RechartsRadarChart,
} from './RechartsWrapper';
export type { RechartsWrapperProps } from './RechartsWrapper';

// 默認導出 (推薦使用 Recharts)
export {
  LineChart,
  BarChart,
  AreaChart,
  PieChart,
  RadarChart,
} from './RechartsWrapper';

// === 現有的專用圖表組件 ===

// 連結健康度趨勢圖表
export {
  LinkHealthTrendChart,
  default as LinkHealthTrend
} from './LinkHealthTrendChart';

// 性能監控圖表
export {
  PerformanceChart,
  PerformanceSummary,
  default as Performance
} from './PerformanceChart';

// SEO 評分趨勢圖表
export {
  SEOScoreTrendChart,
  SEOScoreSummary,
  default as SEOScoreTrend
} from './SEOScoreTrendChart';

// 品牌可見度圖表
export {
  BrandVisibilityChart,
  BrandVisibilitySummary,
  default as BrandVisibility
} from './BrandVisibilityChart';

// 圖表類型定義
export interface ChartProps {
  height?: number;
  showGrid?: boolean;
  showLegend?: boolean;
}

export interface TrendChartProps extends ChartProps {
  timeRange?: '7d' | '30d' | '90d' | 'all';
}

// 圖表主題配置 - 淺色主題優化
export const chartTheme = {
  colors: {
    primary: 'rgb(79, 70, 229)',     // 靛藍色
    success: 'rgb(5, 150, 105)',     // 翠綠色
    warning: 'rgb(217, 119, 6)',     // 橙色
    danger: 'rgb(220, 38, 38)',      // 紅色
    info: 'rgb(14, 165, 233)',       // 天藍色
    gray: 'rgb(107, 114, 128)',      // 中性灰
  },
  fonts: {
    family: 'Inter, "Noto Sans TC", sans-serif',
    size: {
      small: 11,
      medium: 12,
      large: 14,
    }
  },
  spacing: {
    padding: 20,
    margin: 15,
  }
};

// 通用圖表配置 - 淺色主題優化
export const defaultChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      labels: {
        usePointStyle: true,
        padding: chartTheme.spacing.padding,
        font: {
          size: chartTheme.fonts.size.medium,
          family: chartTheme.fonts.family
        },
        color: '#374151' // 深灰色，適合淺色背景
      }
    },
    tooltip: {
      backgroundColor: '#FFFFFF', // 白色背景
      titleColor: '#111827',       // 深色標題
      bodyColor: '#374151',        // 深灰色內容
      borderColor: '#D1D5DB',      // 淺灰色邊框
      borderWidth: 1,
      cornerRadius: 8,
      displayColors: true,
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    }
  },
  scales: {
    x: {
      grid: {
        color: 'rgba(229, 231, 235, 0.8)', // 淺灰色網格線
      },
      ticks: {
        font: {
          size: chartTheme.fonts.size.small,
          family: chartTheme.fonts.family
        },
        color: '#6b7280' // 中性灰色
      }
    },
    y: {
      grid: {
        color: 'rgba(229, 231, 235, 0.8)', // 淺灰色網格線
      },
      ticks: {
        font: {
          size: chartTheme.fonts.size.small,
          family: chartTheme.fonts.family
        },
        color: '#6b7280' // 中性灰色
      }
    }
  }
};
