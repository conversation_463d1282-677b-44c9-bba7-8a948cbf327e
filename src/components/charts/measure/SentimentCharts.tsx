/**
 * 情感分析圖表組件
 * 專用於品牌情感和安全分析的圖表展示
 */

import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, PieC<PERSON>, AreaChart } from '@/components/charts';
import { CHART_COLORS } from '@/components/charts/ChartConfig';
import type { SentimentData } from '@/services/measureApi';

// 情感分佈圓餅圖
export function SentimentDistributionChart({ 
  data, 
  loading = false,
  error = null 
}: {
  data?: SentimentData;
  loading?: boolean;
  error?: string | null;
}) {
  const sentimentData = React.useMemo(() => {
    if (!data?.overall) return [];

    return [
      { name: '正面', value: data.overall.positive, color: CHART_COLORS.success },
      { name: '中性', value: data.overall.neutral, color: CHART_COLORS.gray[400] },
      { name: '負面', value: data.overall.negative, color: CHART_COLORS.error },
    ];
  }, [data]);

  return (
    <PieChart
      title="整體情感分佈"
      description="品牌相關內容的情感傾向分佈"
      data={sentimentData}
      loading={loading}
      error={error}
      height={300}
      colors={[CHART_COLORS.success, CHART_COLORS.gray[400], CHART_COLORS.error]}
      showTooltip={true}
      showLegend={true}
    />
  );
}

// 情感趨勢圖
export function SentimentTrendChart({ 
  data, 
  timeRange = '30d',
  loading = false,
  error = null 
}: {
  data?: SentimentData;
  timeRange?: string;
  loading?: boolean;
  error?: string | null;
}) {
  const trendData = React.useMemo(() => {
    if (!data?.overall) return [];

    const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
    const { positive, neutral, negative } = data.overall;

    // 使用基於索引的偽隨機數生成器，確保 SSR/CSR 一致性
    const seededRandom = (seed: number) => {
      const x = Math.sin(seed) * 10000;
      return x - Math.floor(x);
    };

    return Array.from({ length: days }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (days - 1 - i));

      // 使用基於索引的種子生成一致的變化
      const posVariation = (seededRandom(i * 3 + 1) - 0.5) * 10;
      const negVariation = (seededRandom(i * 3 + 2) - 0.5) * 5;
      const neuVariation = (seededRandom(i * 3 + 3) - 0.5) * 5;

      return {
        date: date.toLocaleDateString('zh-TW', { month: 'short', day: 'numeric' }),
        positive: Math.max(0, Math.min(100, positive + posVariation)),
        neutral: Math.max(0, Math.min(100, neutral + neuVariation)),
        negative: Math.max(0, Math.min(100, negative + negVariation)),
      };
    });
  }, [data, timeRange]);

  return (
    <AreaChart
      title="情感趨勢變化"
      description="品牌情感指標的時間變化趨勢"
      data={trendData}
      xAxisKey="date"
      yAxisKey="positive"
      xAxisLabel="日期"
      yAxisLabel="情感分數 (%)"
      loading={loading}
      error={error}
      height={300}
      colors={[CHART_COLORS.success, CHART_COLORS.gray[400], CHART_COLORS.error]}
      showGrid={true}
      showTooltip={true}
      showLegend={true}
    />
  );
}

// 各引擎情感表現
export function EngineSentimentChart({ 
  data, 
  loading = false,
  error = null 
}: {
  data?: SentimentData;
  loading?: boolean;
  error?: string | null;
}) {
  const engineData = React.useMemo(() => {
    if (!data?.by_engine) return [];

    return data.by_engine.map(engine => ({
      name: engine.engine,
      positive: engine.positive,
      neutral: engine.neutral,
      negative: engine.negative,
      safety: engine.safety_score,
    }));
  }, [data]);

  return (
    <BarChart
      title="各引擎情感表現"
      description="不同 AI 引擎中的品牌情感分佈"
      data={engineData}
      xAxisKey="name"
      yAxisKey="positive"
      xAxisLabel="AI 搜尋引擎"
      yAxisLabel="正面情感比例 (%)"
      loading={loading}
      error={error}
      height={300}
      colors={[CHART_COLORS.success]}
      showGrid={true}
      showTooltip={true}
      showLegend={false}
    />
  );
}

// 安全分數圖表
export function SafetyScoreChart({ 
  data, 
  loading = false,
  error = null 
}: {
  data?: SentimentData;
  loading?: boolean;
  error?: string | null;
}) {
  const safetyData = React.useMemo(() => {
    if (!data?.by_engine) return [];

    return data.by_engine.map(engine => ({
      name: engine.engine,
      safety: engine.safety_score,
      risk: 100 - engine.safety_score, // 風險分數
    }));
  }, [data]);

  return (
    <BarChart
      title="安全分數評估"
      description="各引擎中品牌內容的安全性評分"
      data={safetyData}
      xAxisKey="name"
      yAxisKey="safety"
      xAxisLabel="AI 搜尋引擎"
      yAxisLabel="安全分數"
      loading={loading}
      error={error}
      height={300}
      colors={[CHART_COLORS.primary]}
      showGrid={true}
      showTooltip={true}
      showLegend={false}
    />
  );
}

// 關鍵詞情感分析
export function KeywordSentimentChart({ 
  data, 
  loading = false,
  error = null 
}: {
  data?: SentimentData;
  loading?: boolean;
  error?: string | null;
}) {
  const keywordData = React.useMemo(() => {
    if (!data?.top_keywords) return [];

    return data.top_keywords.slice(0, 8).map(keyword => ({
      name: keyword.keyword,
      sentiment: keyword.sentiment_score,
      mentions: keyword.mentions,
    }));
  }, [data]);

  return (
    <BarChart
      title="關鍵詞情感分析"
      description="熱門關鍵詞的情感傾向分析"
      data={keywordData}
      xAxisKey="name"
      yAxisKey="sentiment"
      xAxisLabel="關鍵詞"
      yAxisLabel="情感分數"
      loading={loading}
      error={error}
      height={300}
      colors={CHART_COLORS.palette}
      showGrid={true}
      showTooltip={true}
      showLegend={false}
    />
  );
}

// 風險議題分析
export function RiskIssuesChart({ 
  data, 
  loading = false,
  error = null 
}: {
  data?: SentimentData;
  loading?: boolean;
  error?: string | null;
}) {
  const riskData = React.useMemo(() => {
    if (!data?.risk_issues) return [];

    return data.risk_issues.map(issue => ({
      name: issue.issue,
      severity: issue.severity === 'high' ? 80 : issue.severity === 'medium' ? 50 : 20,
      mentions: issue.mentions,
      impact: issue.impact_score,
    }));
  }, [data]);

  return (
    <BarChart
      title="風險議題分析"
      description="品牌相關的潛在風險議題"
      data={riskData}
      xAxisKey="name"
      yAxisKey="severity"
      xAxisLabel="風險議題"
      yAxisLabel="嚴重程度"
      loading={loading}
      error={error}
      height={300}
      colors={[CHART_COLORS.warning]}
      showGrid={true}
      showTooltip={true}
      showLegend={false}
    />
  );
}

// 情感分析儀表板組合
export function SentimentDashboard({ 
  data, 
  timeRange = '30d',
  loading = false,
  error = null 
}: {
  data?: SentimentData;
  timeRange?: string;
  loading?: boolean;
  error?: string | null;
}) {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* 情感分佈 */}
      <SentimentDistributionChart 
        data={data}
        loading={loading}
        error={error}
      />
      
      {/* 安全分數 */}
      <SafetyScoreChart 
        data={data}
        loading={loading}
        error={error}
      />
      
      {/* 情感趨勢 */}
      <div className="lg:col-span-2">
        <SentimentTrendChart 
          data={data}
          timeRange={timeRange}
          loading={loading}
          error={error}
        />
      </div>
      
      {/* 各引擎表現 */}
      <EngineSentimentChart 
        data={data}
        loading={loading}
        error={error}
      />
      
      {/* 關鍵詞情感 */}
      <KeywordSentimentChart 
        data={data}
        loading={loading}
        error={error}
      />
      
      {/* 風險議題 */}
      <div className="lg:col-span-2">
        <RiskIssuesChart 
          data={data}
          loading={loading}
          error={error}
        />
      </div>
    </div>
  );
}

export default SentimentDashboard;
