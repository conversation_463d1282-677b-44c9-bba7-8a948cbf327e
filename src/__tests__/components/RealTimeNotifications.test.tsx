/**
 * RealTimeNotifications 組件單元測試
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { RealTimeNotifications } from '@/components/notifications/RealTimeNotifications';
import { useWebSocket } from '@/hooks/useWebSocket';

// Mock useWebSocket Hook
jest.mock('@/hooks/useWebSocket');

// Mock UI components
jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, className, ...props }: any) => (
    <button onClick={onClick} className={className} {...props}>
      {children}
    </button>
  )
}));

jest.mock('@/components/ui/badge', () => ({
  Badge: ({ children, className }: any) => (
    <span className={className}>{children}</span>
  )
}));

jest.mock('@/components/ui/card', () => ({
  Card: ({ children, className }: any) => (
    <div className={className}>{children}</div>
  ),
  CardHeader: ({ children, className }: any) => (
    <div className={className}>{children}</div>
  ),
  CardTitle: ({ children, className }: any) => (
    <h3 className={className}>{children}</h3>
  ),
  CardContent: ({ children, className }: any) => (
    <div className={className}>{children}</div>
  ),
  CardDescription: ({ children, className }: any) => (
    <p className={className}>{children}</p>
  )
}));

jest.mock('@/components/ui/switch', () => ({
  Switch: ({ checked, onCheckedChange, id, ...props }: any) => (
    <input
      type="checkbox"
      checked={checked}
      onChange={(e) => onCheckedChange?.(e.target.checked)}
      id={id}
      role="switch"
      {...props}
    />
  )
}));

jest.mock('@/components/ui/label', () => ({
  Label: ({ children, htmlFor, className }: any) => (
    <label htmlFor={htmlFor} className={className}>
      {children}
    </label>
  )
}));

// Mock Lucide React icons
jest.mock('lucide-react', () => ({
  Bell: () => <span data-testid="bell-icon">Bell</span>,
  Wifi: () => <span data-testid="wifi-icon">Wifi</span>,
  WifiOff: () => <span data-testid="wifi-off-icon">WifiOff</span>,
  Loader2: () => <span data-testid="loader2-icon">Loader2</span>,
  CheckCircle: () => <span data-testid="check-circle-icon">CheckCircle</span>,
  AlertCircle: () => <span data-testid="alert-circle-icon">AlertCircle</span>,
  Info: () => <span data-testid="info-icon">Info</span>,
  X: () => <span data-testid="x-icon">X</span>,
  Settings: () => <span data-testid="settings-icon">Settings</span>
}));

const mockUseWebSocket = useWebSocket as jest.MockedFunction<typeof useWebSocket>;

// Mock 返回值
const mockWebSocketReturn = {
  socket: null,
  isConnected: false,
  isConnecting: false,
  error: null,
  lastMessage: null,
  connect: jest.fn(),
  disconnect: jest.fn(),
  sendMessage: jest.fn(),
  subscribeToBrand: jest.fn(),
  unsubscribeFromBrand: jest.fn(),
  subscribeToAnalysis: jest.fn(),
  onAnalysisProgress: jest.fn(),
  onAnalysisComplete: jest.fn(),
  onAnalysisError: jest.fn(),
  onBrandCreated: jest.fn()
};

describe('RealTimeNotifications Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseWebSocket.mockReturnValue(mockWebSocketReturn);
  });

  describe('Rendering', () => {
    it('should render notification button', () => {
      render(<RealTimeNotifications />);

      const notificationButton = screen.getByRole('button', { name: /實時通知/i });
      expect(notificationButton).toBeInTheDocument();
    });

    it('should show unread count badge when there are unread notifications', async () => {
      // 創建一個可以觸發回調的 mock
      let progressCallback: any = null;

      mockUseWebSocket.mockImplementation((options) => {
        return {
          ...mockWebSocketReturn,
          onAnalysisProgress: (callback: any) => {
            progressCallback = callback;
          }
        };
      });

      const { rerender } = render(<RealTimeNotifications />);

      // 打開通知面板
      const notificationButton = screen.getByRole('button', { name: /實時通知/i });
      fireEvent.click(notificationButton);

      // 觸發進度回調
      if (progressCallback) {
        progressCallback({
          type: 'analysis_progress',
          brandId: 'brand-123',
          analysisType: 'visibility',
          progress: 50,
          timestamp: new Date().toISOString(),
          data: { currentStep: '分析中' }
        });
      }

      // 重新渲染以反映狀態變化
      rerender(<RealTimeNotifications />);

      // 應該顯示未讀計數
      await waitFor(() => {
        const badge = screen.getByText('1');
        expect(badge).toBeInTheDocument();
      });
    });

    it('should not show badge when no unread notifications', () => {
      render(<RealTimeNotifications />);

      const badge = screen.queryByText(/\d+/);
      expect(badge).not.toBeInTheDocument();
    });
  });

  describe('Notification Panel', () => {
    it('should toggle notification panel when button is clicked', () => {
      render(<RealTimeNotifications />);

      const notificationButton = screen.getByRole('button', { name: /實時通知/i });

      // 面板應該初始隱藏 - 檢查面板特有的元素
      expect(screen.queryByText('已連接')).not.toBeInTheDocument();
      expect(screen.queryByText('連接中')).not.toBeInTheDocument();
      expect(screen.queryByText('未連接')).not.toBeInTheDocument();

      // 點擊按鈕打開面板
      fireEvent.click(notificationButton);
      expect(screen.getByText('未連接')).toBeInTheDocument(); // 連接狀態應該顯示

      // 再次點擊關閉面板 - 找到 X 圖標
      const closeButton = screen.getByTestId('x-icon').closest('button');
      fireEvent.click(closeButton!);
      expect(screen.queryByText('未連接')).not.toBeInTheDocument();
    });

    it('should show connection status', () => {
      mockUseWebSocket.mockReturnValue({
        ...mockWebSocketReturn,
        isConnected: true
      });

      render(<RealTimeNotifications />);

      const notificationButton = screen.getByRole('button', { name: /實時通知/i });
      fireEvent.click(notificationButton);

      expect(screen.getByText('已連接')).toBeInTheDocument();
    });

    it('should show connecting status', () => {
      mockUseWebSocket.mockReturnValue({
        ...mockWebSocketReturn,
        isConnecting: true
      });

      render(<RealTimeNotifications />);

      const notificationButton = screen.getByRole('button', { name: /實時通知/i });
      fireEvent.click(notificationButton);

      expect(screen.getByText('連接中')).toBeInTheDocument();
    });

    it('should show error status', () => {
      mockUseWebSocket.mockReturnValue({
        ...mockWebSocketReturn,
        error: 'Connection failed'
      });

      render(<RealTimeNotifications />);

      const notificationButton = screen.getByRole('button', { name: /實時通知/i });
      fireEvent.click(notificationButton);

      expect(screen.getByText('連接錯誤')).toBeInTheDocument();
      expect(screen.getByText('Connection failed')).toBeInTheDocument();
    });
  });

  describe('Notifications Display', () => {
    it('should show empty state when no notifications', () => {
      render(<RealTimeNotifications />);

      const notificationButton = screen.getByRole('button', { name: /實時通知/i });
      fireEvent.click(notificationButton);

      expect(screen.getByText('暫無通知')).toBeInTheDocument();
    });

    it('should display progress notifications', async () => {
      // 創建一個可以觸發回調的 mock
      let progressCallback: any = null;

      mockUseWebSocket.mockImplementation((options) => {
        return {
          ...mockWebSocketReturn,
          onAnalysisProgress: (callback: any) => {
            progressCallback = callback;
          }
        };
      });

      const { rerender } = render(<RealTimeNotifications />);

      // 打開面板
      const notificationButton = screen.getByRole('button', { name: /實時通知/i });
      fireEvent.click(notificationButton);

      // 模擬進度通知
      const progressMessage = {
        type: 'analysis_progress' as const,
        brandId: 'brand-123',
        analysisType: 'visibility',
        progress: 75,
        timestamp: new Date().toISOString(),
        data: { currentStep: '分析品牌可見度' }
      };

      // 觸發進度回調
      if (progressCallback) {
        progressCallback(progressMessage);
      }

      rerender(<RealTimeNotifications />);

      await waitFor(() => {
        expect(screen.getByText('visibility 分析進度')).toBeInTheDocument();
        expect(screen.getByText('品牌 brand-123 - 75% 完成')).toBeInTheDocument();
        expect(screen.getByText('75%')).toBeInTheDocument();
      });
    });

    it('should display complete notifications', async () => {
      // 創建一個可以觸發回調的 mock
      let completeCallback: any = null;

      mockUseWebSocket.mockImplementation((options) => {
        return {
          ...mockWebSocketReturn,
          onAnalysisComplete: (callback: any) => {
            completeCallback = callback;
          }
        };
      });

      const { rerender } = render(<RealTimeNotifications />);

      // 打開面板
      const notificationButton = screen.getByRole('button', { name: /實時通知/i });
      fireEvent.click(notificationButton);

      // 模擬完成通知
      const completeMessage = {
        type: 'analysis_complete' as const,
        brandId: 'brand-123',
        analysisType: 'visibility',
        result: { score: 85 },
        timestamp: new Date().toISOString()
      };

      // 觸發完成回調
      if (completeCallback) {
        completeCallback(completeMessage);
      }

      rerender(<RealTimeNotifications />);

      await waitFor(() => {
        expect(screen.getByText('visibility 分析完成')).toBeInTheDocument();
        expect(screen.getByText('品牌 brand-123 的分析已成功完成')).toBeInTheDocument();
      });
    });

    it('should display error notifications', async () => {
      // 創建一個可以觸發回調的 mock
      let errorCallback: any = null;

      mockUseWebSocket.mockImplementation((options) => {
        return {
          ...mockWebSocketReturn,
          onAnalysisError: (callback: any) => {
            errorCallback = callback;
          }
        };
      });

      const { rerender } = render(<RealTimeNotifications />);

      // 打開面板
      const notificationButton = screen.getByRole('button', { name: /實時通知/i });
      fireEvent.click(notificationButton);

      // 模擬錯誤通知
      const errorMessage = {
        type: 'analysis_error' as const,
        brandId: 'brand-123',
        analysisType: 'visibility',
        error: 'Analysis failed due to network error',
        timestamp: new Date().toISOString()
      };

      // 觸發錯誤回調
      if (errorCallback) {
        errorCallback(errorMessage);
      }

      rerender(<RealTimeNotifications />);

      await waitFor(() => {
        expect(screen.getByText('visibility 分析失敗')).toBeInTheDocument();
        expect(screen.getByText('品牌 brand-123: Analysis failed due to network error')).toBeInTheDocument();
      });
    });
  });

  describe('Notification Management', () => {
    it('should mark notification as read when clicked', async () => {
      // 創建一個可以觸發回調的 mock
      let progressCallback: any = null;

      mockUseWebSocket.mockImplementation((options) => {
        return {
          ...mockWebSocketReturn,
          onAnalysisProgress: (callback: any) => {
            progressCallback = callback;
          }
        };
      });

      const { rerender } = render(<RealTimeNotifications />);

      // 打開面板
      const notificationButton = screen.getByRole('button', { name: /實時通知/i });
      fireEvent.click(notificationButton);

      // 添加通知
      const progressMessage = {
        type: 'analysis_progress' as const,
        brandId: 'brand-123',
        analysisType: 'visibility',
        progress: 50,
        timestamp: new Date().toISOString(),
        data: { currentStep: '分析中' }
      };

      if (progressCallback) {
        progressCallback(progressMessage);
      }

      rerender(<RealTimeNotifications />);

      await waitFor(() => {
        const notification = screen.getByText('visibility 分析進度');
        expect(notification).toBeInTheDocument();

        // 點擊通知標記為已讀
        fireEvent.click(notification.closest('div')!);
      });

      // 檢查通知是否仍然存在但已標記為已讀（背景色變化）
      await waitFor(() => {
        const notification = screen.getByText('visibility 分析進度');
        expect(notification).toBeInTheDocument();
      });
    });

    it('should clear all notifications', async () => {
      // 創建一個可以觸發回調的 mock
      let progressCallback: any = null;

      mockUseWebSocket.mockImplementation((options) => {
        return {
          ...mockWebSocketReturn,
          onAnalysisProgress: (callback: any) => {
            progressCallback = callback;
          }
        };
      });

      const { rerender } = render(<RealTimeNotifications />);

      // 打開面板
      const notificationButton = screen.getByRole('button', { name: /實時通知/i });
      fireEvent.click(notificationButton);

      // 添加通知
      const progressMessage = {
        type: 'analysis_progress' as const,
        brandId: 'brand-123',
        analysisType: 'visibility',
        progress: 50,
        timestamp: new Date().toISOString(),
        data: { currentStep: '分析中' }
      };

      if (progressCallback) {
        progressCallback(progressMessage);
      }

      rerender(<RealTimeNotifications />);

      await waitFor(() => {
        expect(screen.getByText('visibility 分析進度')).toBeInTheDocument();
      });

      // 點擊全部清除
      const clearAllButton = screen.getByText('全部清除');
      fireEvent.click(clearAllButton);

      await waitFor(() => {
        expect(screen.getByText('暫無通知')).toBeInTheDocument();
      });
    });

    it('should clear only read notifications', async () => {
      // 創建一個可以觸發回調的 mock
      let progressCallback: any = null;

      mockUseWebSocket.mockImplementation((options) => {
        return {
          ...mockWebSocketReturn,
          onAnalysisProgress: (callback: any) => {
            progressCallback = callback;
          }
        };
      });

      const { rerender } = render(<RealTimeNotifications />);

      // 打開面板
      const notificationButton = screen.getByRole('button', { name: /實時通知/i });
      fireEvent.click(notificationButton);

      // 添加兩個通知
      const progressMessage1 = {
        type: 'analysis_progress' as const,
        brandId: 'brand-123',
        analysisType: 'visibility',
        progress: 50,
        timestamp: new Date().toISOString(),
        data: { currentStep: '分析中' }
      };

      const progressMessage2 = {
        type: 'analysis_progress' as const,
        brandId: 'brand-456',
        analysisType: 'query',
        progress: 75,
        timestamp: new Date().toISOString(),
        data: { currentStep: '查詢分析中' }
      };

      if (progressCallback) {
        progressCallback(progressMessage1);
        progressCallback(progressMessage2);
      }

      rerender(<RealTimeNotifications />);

      await waitFor(() => {
        expect(screen.getByText('visibility 分析進度')).toBeInTheDocument();
        expect(screen.getByText('query 分析進度')).toBeInTheDocument();
      });

      // 標記第一個通知為已讀
      const firstNotification = screen.getByText('visibility 分析進度');
      fireEvent.click(firstNotification.closest('div')!);

      // 點擊清除已讀
      const clearReadButton = screen.getByText('清除已讀');
      fireEvent.click(clearReadButton);

      await waitFor(() => {
        expect(screen.queryByText('visibility 分析進度')).not.toBeInTheDocument();
        expect(screen.getByText('query 分析進度')).toBeInTheDocument();
      });
    });
  });

  describe('Auto Subscription', () => {
    it('should auto subscribe to brand when brandId is provided and connected', () => {
      mockUseWebSocket.mockReturnValue({
        ...mockWebSocketReturn,
        isConnected: true
      });

      render(<RealTimeNotifications brandId="brand-123" />);

      expect(mockWebSocketReturn.subscribeToBrand).toHaveBeenCalledWith('brand-123');
    });

    it('should not auto subscribe when not connected', () => {
      mockUseWebSocket.mockReturnValue({
        ...mockWebSocketReturn,
        isConnected: false
      });

      render(<RealTimeNotifications brandId="brand-123" />);

      expect(mockWebSocketReturn.subscribeToBrand).not.toHaveBeenCalled();
    });

    it('should toggle auto subscription setting', () => {
      render(<RealTimeNotifications />);

      // 打開面板
      const notificationButton = screen.getByRole('button', { name: /實時通知/i });
      fireEvent.click(notificationButton);

      // 找到自動訂閱開關
      const autoSubscribeSwitch = screen.getByRole('switch', { name: /自動訂閱/i });
      expect(autoSubscribeSwitch).toBeChecked();

      // 關閉自動訂閱
      fireEvent.click(autoSubscribeSwitch);
      expect(autoSubscribeSwitch).not.toBeChecked();
    });
  });

  describe('Reconnection', () => {
    it('should show reconnect button when not connected', () => {
      mockUseWebSocket.mockReturnValue({
        ...mockWebSocketReturn,
        isConnected: false
      });

      render(<RealTimeNotifications />);

      // 打開面板
      const notificationButton = screen.getByRole('button', { name: /實時通知/i });
      fireEvent.click(notificationButton);

      const reconnectButton = screen.getByText('重新連接');
      expect(reconnectButton).toBeInTheDocument();

      // 點擊重新連接
      fireEvent.click(reconnectButton);
      expect(mockWebSocketReturn.connect).toHaveBeenCalled();
    });

    it('should not show reconnect button when connected', () => {
      mockUseWebSocket.mockReturnValue({
        ...mockWebSocketReturn,
        isConnected: true
      });

      render(<RealTimeNotifications />);

      // 打開面板
      const notificationButton = screen.getByRole('button', { name: /實時通知/i });
      fireEvent.click(notificationButton);

      const reconnectButton = screen.queryByText('重新連接');
      expect(reconnectButton).not.toBeInTheDocument();
    });
  });
});
