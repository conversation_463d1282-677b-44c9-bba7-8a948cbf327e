/**
 * 產品研究數據服務單元測試
 */

import { productResearchDataService } from '@/services/product-research-data';
import { Brand } from '@/types/product-research';

// Mock axios
jest.mock('axios');

// Mock API 響應數據
const mockBrand: Brand = {
  id: 'brand-test-123',
  name: '測試品牌',
  domain: 'test-brand.com',
  industry: '軟體科技',
  description: '這是一個測試品牌',
  status: 'active',
  targetRegions: ['北美', '歐洲'],
  targetPersonas: ['IT 管理者', '決策者'],
  competitors: ['競爭對手A', '競爭對手B'],
  keywords: ['測試', '品牌', '軟體'],
  settings: {
    autoAnalysis: true,
    notificationEnabled: true,
    reportFrequency: 'weekly'
  },
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
};

const mockBrandList = [mockBrand];

const mockApiResponse = {
  success: true,
  message: '操作成功',
  data: mockBrand
};

const mockListResponse = {
  success: true,
  message: '品牌列表獲取成功',
  data: mockBrandList,
  pagination: {
    total: 1,
    page: 1,
    limit: 20,
    totalPages: 1
  }
};

describe('ProductResearchDataService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Brand Operations', () => {
    it('should fetch brands list successfully', async () => {
      const result = await productResearchDataService.getBrands();
      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(Array.isArray(result.data)).toBe(true);
    });

    it('should fetch single brand successfully', async () => {
      const result = await productResearchDataService.getBrand('brand-test-123');
      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
    });

    it('should create brand successfully', async () => {
      const brandData = {
        name: '測試品牌',
        domain: 'test-brand.com',
        industry: '軟體科技'
      };

      const result = await productResearchDataService.createBrand(brandData);
      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
    });

    it('should update brand successfully', async () => {
      const updateData = {
        name: '更新後的品牌名稱'
      };

      const result = await productResearchDataService.updateBrand('brand-test-123', updateData);
      expect(result).toBeDefined();
      expect(result.success).toBe(true);
    });

    it('should delete brand successfully', async () => {
      const result = await productResearchDataService.deleteBrand('brand-test-123');
      expect(result).toBeDefined();
      expect(result.success).toBe(true);
    });
  });

  describe('Brand Statistics', () => {
    it('should fetch brand statistics successfully', async () => {
      const result = await productResearchDataService.getBrandStats('brand-test-123');
      expect(result).toBeDefined();
      expect(typeof result.totalAnalyses).toBe('number');
      expect(typeof result.avgVisibilityScore).toBe('number');
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      // 模擬網絡錯誤的情況
      try {
        await productResearchDataService.getBrand('non-existent-brand');
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });
});
