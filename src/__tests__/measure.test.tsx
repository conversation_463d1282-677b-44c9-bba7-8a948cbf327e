/**
 * 測量功能模組測試
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { jest } from '@jest/globals';
import '@testing-library/jest-dom';

// Mock Next.js router
const mockPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    pathname: '/admin/measure',
  }),
}));

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
  },
}));

// Mock API calls
global.fetch = jest.fn() as jest.MockedFunction<typeof fetch>;

const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

describe('測量功能模組', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockFetch.mockClear();
  });

  describe('測量功能主頁面', () => {
    it('應該正確渲染頁面標題和統計概覽', async () => {
      // Mock API response
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            total_brands: 12,
            active_campaigns: 8,
            total_queries: 2847,
            avg_visibility: 73.5,
            competitor_tracked: 24,
            sentiment_score: 8.2
          }
        })
      } as Response);

      const MeasurePage = (await import('../app/admin/measure/page')).default;
      render(<MeasurePage />);

      // 檢查頁面標題
      expect(screen.getByText('測量功能')).toBeInTheDocument();
      expect(screen.getByText('全面測量您的品牌在 AI 搜尋引擎中的表現，包括可見度、競爭定位、購買旅程和情感分析')).toBeInTheDocument();

      // 等待數據載入
      await waitFor(() => {
        expect(screen.getByText('12')).toBeInTheDocument(); // 追蹤品牌
        expect(screen.getByText('8')).toBeInTheDocument(); // 活躍行銷活動
        expect(screen.getByText('2,847')).toBeInTheDocument(); // 總查詢數
      });
    });

    it('應該顯示功能模組卡片', async () => {
      const MeasurePage = (await import('../app/admin/measure/page')).default;
      render(<MeasurePage />);

      await waitFor(() => {
        expect(screen.getByText('品牌可見度分析')).toBeInTheDocument();
        expect(screen.getByText('競爭定位分析')).toBeInTheDocument();
        expect(screen.getByText('購買旅程分析')).toBeInTheDocument();
        expect(screen.getByText('準確性與情感分析')).toBeInTheDocument();
      });
    });

    it('應該處理開始新分析按鈕點擊', async () => {
      const MeasurePage = (await import('../app/admin/measure/page')).default;
      render(<MeasurePage />);

      await waitFor(() => {
        const analyzeButton = screen.getByText('開始新分析');
        expect(analyzeButton).toBeInTheDocument();
        
        fireEvent.click(analyzeButton);
        // 這裡可以添加更多的互動測試
      });
    });
  });

  describe('品牌可見度分析頁面', () => {
    it('應該正確渲染可見度分析頁面', async () => {
      const VisibilityPage = (await import('../app/admin/measure/visibility/page')).default;
      render(<VisibilityPage />);

      expect(screen.getByText('品牌可見度分析')).toBeInTheDocument();
      expect(screen.getByText('測量您的品牌在主要 AI 搜尋引擎中的可見度和聲量份額')).toBeInTheDocument();
    });

    it('應該顯示分析設定表單', async () => {
      const VisibilityPage = (await import('../app/admin/measure/visibility/page')).default;
      render(<VisibilityPage />);

      expect(screen.getByText('分析設定')).toBeInTheDocument();
      expect(screen.getByText('選擇品牌')).toBeInTheDocument();
      expect(screen.getByText('時間範圍')).toBeInTheDocument();
      expect(screen.getByText('AI 搜尋引擎')).toBeInTheDocument();
    });

    it('應該處理分析按鈕點擊', async () => {
      const VisibilityPage = (await import('../app/admin/measure/visibility/page')).default;
      render(<VisibilityPage />);

      const analyzeButton = screen.getByText('開始分析');
      expect(analyzeButton).toBeInTheDocument();
      
      fireEvent.click(analyzeButton);
      
      // 檢查按鈕狀態變化
      await waitFor(() => {
        expect(screen.getByText('分析中...')).toBeInTheDocument();
      });
    });

    it('應該顯示 AI 搜尋引擎覆蓋範圍', async () => {
      const VisibilityPage = (await import('../app/admin/measure/visibility/page')).default;
      render(<VisibilityPage />);

      expect(screen.getByText('AI 搜尋引擎覆蓋範圍')).toBeInTheDocument();
      expect(screen.getByText('ChatGPT')).toBeInTheDocument();
      expect(screen.getByText('Google Gemini')).toBeInTheDocument();
      expect(screen.getByText('Perplexity')).toBeInTheDocument();
    });
  });

  describe('競爭分析頁面', () => {
    it('應該正確渲染競爭分析頁面', async () => {
      const CompetitivePage = (await import('../app/admin/measure/competitive/page')).default;
      render(<CompetitivePage />);

      expect(screen.getByText('競爭定位分析')).toBeInTheDocument();
      expect(screen.getByText('與競爭對手進行基準比較，識別市場機會和定位策略')).toBeInTheDocument();
    });

    it('應該顯示競爭排名', async () => {
      const CompetitivePage = (await import('../app/admin/measure/competitive/page')).default;
      render(<CompetitivePage />);

      expect(screen.getByText('競爭排名')).toBeInTheDocument();
      expect(screen.getByText('SEO 大師')).toBeInTheDocument();
      expect(screen.getByText('AI SEO 優化王')).toBeInTheDocument();
    });

    it('應該處理添加競爭對手按鈕', async () => {
      const CompetitivePage = (await import('../app/admin/measure/competitive/page')).default;
      render(<CompetitivePage />);

      const addButton = screen.getByText('添加競爭對手');
      expect(addButton).toBeInTheDocument();
      
      fireEvent.click(addButton);
      // 可以添加更多的互動測試
    });
  });

  describe('購買旅程分析頁面', () => {
    it('應該正確渲染購買旅程分析頁面', async () => {
      const JourneyPage = (await import('../app/admin/measure/journey/page')).default;
      render(<JourneyPage />);

      expect(screen.getByText('購買旅程分析')).toBeInTheDocument();
      expect(screen.getByText('分析不同人群在購買旅程中的行為模式，識別轉換機會和優化點')).toBeInTheDocument();
    });

    it('應該顯示旅程階段', async () => {
      const JourneyPage = (await import('../app/admin/measure/journey/page')).default;
      render(<JourneyPage />);

      expect(screen.getByText('購買旅程階段')).toBeInTheDocument();
      expect(screen.getByText('認知階段')).toBeInTheDocument();
      expect(screen.getByText('考慮階段')).toBeInTheDocument();
      expect(screen.getByText('決策階段')).toBeInTheDocument();
    });

    it('應該顯示人群分析標籤', async () => {
      const JourneyPage = (await import('../app/admin/measure/journey/page')).default;
      render(<JourneyPage />);

      expect(screen.getByText('人群分析')).toBeInTheDocument();
      expect(screen.getByText('轉換漏斗')).toBeInTheDocument();
      expect(screen.getByText('缺口識別')).toBeInTheDocument();
    });
  });

  describe('情感分析頁面', () => {
    it('應該正確渲染情感分析頁面', async () => {
      const SentimentPage = (await import('../app/admin/measure/sentiment/page')).default;
      render(<SentimentPage />);

      expect(screen.getByText('準確性與情感分析')).toBeInTheDocument();
      expect(screen.getByText('監控品牌情感並識別 AI 搜尋回應中的不準確資訊和品牌安全問題')).toBeInTheDocument();
    });

    it('應該顯示情感概覽統計', async () => {
      const SentimentPage = (await import('../app/admin/measure/sentiment/page')).default;
      render(<SentimentPage />);

      expect(screen.getByText('整體情感評分')).toBeInTheDocument();
      expect(screen.getByText('正面情感')).toBeInTheDocument();
      expect(screen.getByText('中性情感')).toBeInTheDocument();
      expect(screen.getByText('負面情感')).toBeInTheDocument();
    });

    it('應該顯示分析標籤', async () => {
      const SentimentPage = (await import('../app/admin/measure/sentiment/page')).default;
      render(<SentimentPage />);

      expect(screen.getByText('情感分析')).toBeInTheDocument();
      expect(screen.getByText('幻覺檢測')).toBeInTheDocument();
      expect(screen.getByText('品牌安全')).toBeInTheDocument();
    });
  });

  describe('設定頁面', () => {
    it('應該正確渲染設定頁面', async () => {
      const SettingsPage = (await import('../app/admin/measure/settings/page')).default;
      render(<SettingsPage />);

      expect(screen.getByText('測量功能設定')).toBeInTheDocument();
      expect(screen.getByText('配置測量功能的各項參數和偏好設定')).toBeInTheDocument();
    });

    it('應該顯示設定標籤', async () => {
      const SettingsPage = (await import('../app/admin/measure/settings/page')).default;
      render(<SettingsPage />);

      expect(screen.getByText('一般設定')).toBeInTheDocument();
      expect(screen.getByText('通知設定')).toBeInTheDocument();
      expect(screen.getByText('AI 引擎')).toBeInTheDocument();
      expect(screen.getByText('品牌安全')).toBeInTheDocument();
      expect(screen.getByText('API 設定')).toBeInTheDocument();
    });

    it('應該處理儲存設定按鈕', async () => {
      const SettingsPage = (await import('../app/admin/measure/settings/page')).default;
      render(<SettingsPage />);

      const saveButton = screen.getByText('儲存設定');
      expect(saveButton).toBeInTheDocument();
      
      fireEvent.click(saveButton);
      
      // 檢查按鈕狀態變化
      await waitFor(() => {
        expect(screen.getByText('儲存中...')).toBeInTheDocument();
      });
    });
  });

  describe('API 整合測試', () => {
    it('應該正確調用測量統計 API', async () => {
      const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            total_brands: 12,
            active_campaigns: 8
          }
        })
      } as Response);

      const MeasurePage = (await import('../app/admin/measure/page')).default;
      render(<MeasurePage />);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/api/v1/admin/measure/stats'),
          expect.objectContaining({
            method: 'GET',
            headers: expect.objectContaining({
              'Content-Type': 'application/json'
            })
          })
        );
      });
    });

    it('應該處理 API 錯誤', async () => {
      const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;
      mockFetch.mockRejectedValueOnce(new Error('API Error'));

      const MeasurePage = (await import('../app/admin/measure/page')).default;
      render(<MeasurePage />);

      // 檢查錯誤處理
      await waitFor(() => {
        // 這裡可以檢查錯誤狀態的顯示
      });
    });
  });

  describe('響應式設計測試', () => {
    it('應該在不同螢幕尺寸下正確顯示', async () => {
      // 模擬手機螢幕
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      const MeasurePage = (await import('../app/admin/measure/page')).default;
      render(<MeasurePage />);

      // 檢查響應式布局
      await waitFor(() => {
        const container = screen.getByText('測量功能').closest('div');
        expect(container).toBeInTheDocument();
      });
    });
  });

  describe('無障礙性測試', () => {
    it('應該具有適當的 ARIA 標籤', async () => {
      const MeasurePage = (await import('../app/admin/measure/page')).default;
      render(<MeasurePage />);

      // 檢查按鈕的可訪問性
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toBeInTheDocument();
      });
    });

    it('應該支援鍵盤導航', async () => {
      const MeasurePage = (await import('../app/admin/measure/page')).default;
      render(<MeasurePage />);

      // 檢查可聚焦元素
      const focusableElements = screen.getAllByRole('button');
      expect(focusableElements.length).toBeGreaterThan(0);
    });
  });
});
