/**
 * OpenAI 設置管理 Hook
 */

import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { ApiService } from '@/services/api';
import type {
  OpenAISettingsDisplay,
  OpenAISettingsUpdate,
  OpenAITestRequest,
  OpenAITestResponse,
  OpenAIUsageStats,
  OpenAIFormErrors
} from '@/types/openai';

interface UseOpenAISettingsReturn {
  // 狀態
  settings: OpenAISettingsDisplay | null;
  usageStats: OpenAIUsageStats | null;
  loading: boolean;
  saving: boolean;
  testing: boolean;
  errors: OpenAIFormErrors;
  
  // 操作
  loadSettings: () => Promise<void>;
  updateSettings: (updates: OpenAISettingsUpdate) => Promise<boolean>;
  testConnection: (testRequest: OpenAITestRequest) => Promise<OpenAITestResponse | null>;
  resetSettings: () => Promise<boolean>;
  loadUsageStats: () => Promise<void>;
  clearErrors: () => void;
  setError: (field: keyof OpenAIFormErrors, message: string) => void;
}

export function useOpenAISettings(): UseOpenAISettingsReturn {
  const [settings, setSettings] = useState<OpenAISettingsDisplay | null>(null);
  const [usageStats, setUsageStats] = useState<OpenAIUsageStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [errors, setErrors] = useState<OpenAIFormErrors>({});

  /**
   * 載入設置
   */
  const loadSettings = useCallback(async () => {
    setLoading(true);
    try {
      const response = await ApiService.get('/api/v1/admin/openai/settings');
      setSettings(response.data);
    } catch (error) {
      console.error('載入 OpenAI 設置失敗:', error);
      toast.error(error instanceof Error ? error.message : '載入設置失敗');
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 更新設置
   */
  const updateSettings = useCallback(async (updates: OpenAISettingsUpdate): Promise<boolean> => {
    setSaving(true);
    setErrors({});
    
    try {
      // 客戶端驗證
      const validationErrors = validateSettings(updates);
      if (Object.keys(validationErrors).length > 0) {
        setErrors(validationErrors);
        return false;
      }

      const response = await ApiService.put('/api/v1/admin/openai/settings', updates);
      
      if (response.success) {
        toast.success('OpenAI 設置更新成功');
        await loadSettings(); // 重新載入設置
        return true;
      } else {
        toast.error(response.message || '更新設置失敗');
        return false;
      }
    } catch (error) {
      console.error('更新 OpenAI 設置失敗:', error);
      const errorMessage = error instanceof Error ? error.message : '更新設置失敗';
      toast.error(errorMessage);
      return false;
    } finally {
      setSaving(false);
    }
  }, [loadSettings]);

  /**
   * 測試連接
   */
  const testConnection = useCallback(async (testRequest: OpenAITestRequest): Promise<OpenAITestResponse | null> => {
    setTesting(true);
    
    try {
      const response = await ApiService.post('/api/v1/admin/openai/settings/test', testRequest);
      
      if (response.data.success) {
        toast.success('OpenAI 連接測試成功');
      } else {
        toast.error(`連接測試失敗: ${response.data.error_message}`);
      }
      
      return response.data;
    } catch (error) {
      console.error('測試 OpenAI 連接失敗:', error);
      const errorMessage = error instanceof Error ? error.message : '連接測試失敗';
      toast.error(errorMessage);
      return null;
    } finally {
      setTesting(false);
    }
  }, []);

  /**
   * 重置設置
   */
  const resetSettings = useCallback(async (): Promise<boolean> => {
    setSaving(true);
    
    try {
      const response = await ApiService.delete('/api/v1/admin/openai/settings');
      
      if (response.success) {
        toast.success('OpenAI 設置已重置');
        await loadSettings(); // 重新載入設置
        return true;
      } else {
        toast.error(response.message || '重置設置失敗');
        return false;
      }
    } catch (error) {
      console.error('重置 OpenAI 設置失敗:', error);
      const errorMessage = error instanceof Error ? error.message : '重置設置失敗';
      toast.error(errorMessage);
      return false;
    } finally {
      setSaving(false);
    }
  }, [loadSettings]);

  /**
   * 載入使用統計
   */
  const loadUsageStats = useCallback(async () => {
    try {
      const response = await ApiService.get('/api/v1/admin/openai/usage-stats');
      setUsageStats(response.data);
    } catch (error) {
      console.error('載入使用統計失敗:', error);
      // 不顯示錯誤提示，因為這不是關鍵功能
    }
  }, []);

  /**
   * 清除錯誤
   */
  const clearErrors = useCallback(() => {
    setErrors({});
  }, []);

  /**
   * 設置錯誤
   */
  const setError = useCallback((field: keyof OpenAIFormErrors, message: string) => {
    setErrors(prev => ({ ...prev, [field]: message }));
  }, []);

  // 初始載入
  useEffect(() => {
    loadSettings();
    loadUsageStats();
  }, [loadSettings, loadUsageStats]);

  return {
    settings,
    usageStats,
    loading,
    saving,
    testing,
    errors,
    loadSettings,
    updateSettings,
    testConnection,
    resetSettings,
    loadUsageStats,
    clearErrors,
    setError
  };
}

/**
 * 驗證設置
 */
function validateSettings(settings: OpenAISettingsUpdate): OpenAIFormErrors {
  const errors: OpenAIFormErrors = {};

  // API 密鑰驗證
  if (!settings.api_key || settings.api_key.trim() === '') {
    errors.api_key = 'API 密鑰不能為空';
  } else if (!settings.api_key.startsWith('sk-')) {
    errors.api_key = 'API 密鑰必須以 "sk-" 開頭';
  } else if (settings.api_key.length < 20) {
    errors.api_key = 'API 密鑰長度不足';
  }

  // 模型驗證
  if (!settings.model || settings.model.trim() === '') {
    errors.model = '請選擇模型';
  }

  // Max tokens 驗證
  if (settings.max_tokens !== undefined) {
    if (settings.max_tokens <= 0) {
      errors.max_tokens = 'Max tokens 必須大於 0';
    } else if (settings.max_tokens > 16000) {
      errors.max_tokens = 'Max tokens 不能超過 16000';
    }
  }

  // Temperature 驗證
  if (settings.temperature !== undefined) {
    if (settings.temperature < 0 || settings.temperature > 2) {
      errors.temperature = 'Temperature 必須在 0-2 之間';
    }
  }

  // Timeout 驗證
  if (settings.timeout !== undefined && settings.timeout <= 0) {
    errors.timeout = 'Timeout 必須大於 0';
  }

  return errors;
}
