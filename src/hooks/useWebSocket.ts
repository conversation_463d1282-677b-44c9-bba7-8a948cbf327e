import { useState, useRef, useCallback, useEffect } from 'react';
import { toast } from 'sonner';

// WebSocket 消息接口
export interface WebSocketMessage {
  type: string;
  timestamp: string;
}

export interface AnalysisProgressMessage extends WebSocketMessage {
  type: 'analysis_progress';
  brandId: string;
  analysisType: string;
  progress: number;
  data?: Record<string, any>;
}

export interface AnalysisCompleteMessage extends WebSocketMessage {
  type: 'analysis_complete';
  brandId: string;
  analysisType: string;
  result: Record<string, any>;
}

export interface AnalysisErrorMessage extends WebSocketMessage {
  type: 'analysis_error';
  brandId: string;
  analysisType: string;
  error: string;
}

export interface BrandCreatedMessage extends WebSocketMessage {
  type: 'brand_created';
  brandId: string;
  brandName: string;
}

export interface UseWebSocketOptions {
  autoConnect?: boolean;
  autoReconnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  showToastNotifications?: boolean;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: string) => void;
  onMessage?: (message: WebSocketMessage) => void;
}

export interface UseWebSocketReturn {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  lastMessage: WebSocketMessage | null;
  
  // 連接控制
  connect: () => void;
  disconnect: () => void;
  sendMessage: (message: any) => void;
  
  // 品牌管理
  subscribeToBrand: (brandId: string) => void;
  unsubscribeFromBrand: (brandId: string) => void;
  
  // 事件監聽器
  onAnalysisProgress: (callback: (message: AnalysisProgressMessage) => void) => void;
  onAnalysisComplete: (callback: (message: AnalysisCompleteMessage) => void) => void;
  onAnalysisError: (callback: (message: AnalysisErrorMessage) => void) => void;
  onBrandCreated: (callback: (message: BrandCreatedMessage) => void) => void;
}

// WebSocket URL 配置
const WEBSOCKET_URL = process.env.NEXT_PUBLIC_WEBSOCKET_URL;

// 檢查 WebSocket 是否已配置
const isWebSocketEnabled = !!WEBSOCKET_URL;

export function useWebSocket(options: UseWebSocketOptions = {}): UseWebSocketReturn {
  // 默認選項
  const opts = {
    autoConnect: true,
    autoReconnect: true,
    reconnectInterval: 3000,
    maxReconnectAttempts: 5,
    showToastNotifications: false,
    ...options
  };

  // 狀態管理
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);

  // 引用管理
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const subscribedBrandsRef = useRef<Set<string>>(new Set());
  const eventCallbacksRef = useRef({
    analysisProgress: [] as ((message: AnalysisProgressMessage) => void)[],
    analysisComplete: [] as ((message: AnalysisCompleteMessage) => void)[],
    analysisError: [] as ((message: AnalysisErrorMessage) => void)[],
    brandCreated: [] as ((message: BrandCreatedMessage) => void)[]
  });

  // 清理連接
  const cleanup = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    
    setIsConnected(false);
    setIsConnecting(false);
  }, []);

  // 處理 WebSocket 消息
  const handleMessage = useCallback((event: MessageEvent) => {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      setLastMessage(message);
      
      // 觸發通用消息回調
      if (opts.onMessage) {
        opts.onMessage(message);
      }
      
      // 根據消息類型觸發特定回調
      switch (message.type) {
        case 'analysis_progress':
          eventCallbacksRef.current.analysisProgress.forEach(callback => 
            callback(message as AnalysisProgressMessage)
          );
          break;
          
        case 'analysis_complete':
          eventCallbacksRef.current.analysisComplete.forEach(callback => 
            callback(message as AnalysisCompleteMessage)
          );
          if (opts.showToastNotifications) {
            const msg = message as AnalysisCompleteMessage;
            toast.success(`${msg.analysisType} 分析完成`, {
              description: `品牌 ${msg.brandId} 的分析已成功完成`
            });
          }
          break;
          
        case 'analysis_error':
          eventCallbacksRef.current.analysisError.forEach(callback => 
            callback(message as AnalysisErrorMessage)
          );
          if (opts.showToastNotifications) {
            const msg = message as AnalysisErrorMessage;
            toast.error(`${msg.analysisType} 分析失敗`, {
              description: `品牌 ${msg.brandId}: ${msg.error}`
            });
          }
          break;
          
        case 'brand_created':
          eventCallbacksRef.current.brandCreated.forEach(callback => 
            callback(message as BrandCreatedMessage)
          );
          if (opts.showToastNotifications) {
            const msg = message as BrandCreatedMessage;
            toast.success('品牌創建成功', {
              description: `新品牌 "${msg.brandName}" 已創建`
            });
          }
          break;
      }
      
    } catch (err) {
      console.error('Error parsing WebSocket message:', err);
      setError('消息解析錯誤');
    }
  }, [opts.onMessage, opts.showToastNotifications]);

  // 連接 WebSocket
  const connect = useCallback(() => {
    if (isConnecting || isConnected) return;

    // 檢查 WebSocket 是否已啟用
    if (!isWebSocketEnabled) {
      console.log('WebSocket 未配置，跳過連接');
      return;
    }

    setIsConnecting(true);
    setError(null);

    try {
      const ws = new WebSocket(WEBSOCKET_URL!);
      
      ws.onopen = () => {
        console.log('WebSocket connected');
        setIsConnected(true);
        setIsConnecting(false);
        setError(null);
        reconnectAttemptsRef.current = 0;
        
        // 重新訂閱之前的品牌
        subscribedBrandsRef.current.forEach(brandId => {
          ws.send(JSON.stringify({
            type: 'subscribe_brand',
            brandId
          }));
        });
        
        if (opts.onConnect) {
          opts.onConnect();
        }
        
        if (opts.showToastNotifications) {
          toast.success('WebSocket 已連接');
        }
      };
      
      ws.onmessage = handleMessage;
      
      ws.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        setIsConnected(false);
        setIsConnecting(false);
        
        if (opts.onDisconnect) {
          opts.onDisconnect();
        }
        
        // 自動重連
        if (opts.autoReconnect && reconnectAttemptsRef.current < opts.maxReconnectAttempts!) {
          const delay = opts.reconnectInterval! * Math.pow(2, reconnectAttemptsRef.current);
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttemptsRef.current++;
            console.log(`Attempting to reconnect (${reconnectAttemptsRef.current}/${opts.maxReconnectAttempts})...`);
            // 直接調用內部連接邏輯，避免無限循環
            if (reconnectAttemptsRef.current <= opts.maxReconnectAttempts!) {
              setIsConnecting(true);
              setError(null);
              
              try {
                const retryWs = new WebSocket(WEBSOCKET_URL);
                retryWs.onopen = ws.onopen;
                retryWs.onmessage = ws.onmessage;
                retryWs.onclose = ws.onclose;
                retryWs.onerror = ws.onerror;
                wsRef.current = retryWs;
              } catch (retryErr) {
                console.error('Error creating retry WebSocket:', retryErr);
                setError('重連失敗');
                setIsConnecting(false);
              }
            }
          }, delay);
          
          if (opts.showToastNotifications) {
            toast.info(`連接斷開，${delay / 1000}秒後重試...`);
          }
        }
      };
      
      ws.onerror = (event) => {
        console.error('WebSocket error:', event);
        const errorMsg = 'WebSocket 連接錯誤';
        setError(errorMsg);
        setIsConnecting(false);
        
        if (opts.onError) {
          opts.onError(errorMsg);
        }
        
        if (opts.showToastNotifications) {
          toast.error(errorMsg);
        }
      };
      
      wsRef.current = ws;
      
    } catch (err) {
      console.error('Error creating WebSocket:', err);
      const errorMsg = '無法創建 WebSocket 連接';
      setError(errorMsg);
      setIsConnecting(false);
      
      if (opts.onError) {
        opts.onError(errorMsg);
      }
    }
  }, [isConnecting, isConnected, handleMessage, opts.onConnect, opts.onDisconnect, opts.onError, opts.autoReconnect, opts.maxReconnectAttempts, opts.reconnectInterval, opts.showToastNotifications]);

  // 斷開連接
  const disconnect = useCallback(() => {
    cleanup();
    
    if (opts.onDisconnect) {
      opts.onDisconnect();
    }
    
    if (opts.showToastNotifications) {
      toast.info('WebSocket 已斷開');
    }
  }, [cleanup, opts.onDisconnect, opts.showToastNotifications]);

  // 發送消息
  const sendMessage = useCallback((message: any) => {
    if (wsRef.current && isConnected) {
      try {
        wsRef.current.send(JSON.stringify(message));
      } catch (err) {
        console.error('Error sending message:', err);
        setError('發送消息失敗');
      }
    } else {
      console.warn('WebSocket not connected, cannot send message');
      setError('WebSocket 未連接');
    }
  }, [isConnected]);

  // 訂閱品牌
  const subscribeToBrand = useCallback((brandId: string) => {
    subscribedBrandsRef.current.add(brandId);
    sendMessage({
      type: 'subscribe_brand',
      brandId
    });
  }, [sendMessage]);

  // 取消訂閱品牌
  const unsubscribeFromBrand = useCallback((brandId: string) => {
    subscribedBrandsRef.current.delete(brandId);
    sendMessage({
      type: 'unsubscribe_brand',
      brandId
    });
  }, [sendMessage]);

  // 事件監聽器註冊函數
  const onAnalysisProgress = useCallback((callback: (message: AnalysisProgressMessage) => void) => {
    eventCallbacksRef.current.analysisProgress.push(callback);
    
    // 返回清理函數
    return () => {
      const index = eventCallbacksRef.current.analysisProgress.indexOf(callback);
      if (index > -1) {
        eventCallbacksRef.current.analysisProgress.splice(index, 1);
      }
    };
  }, []);

  const onAnalysisComplete = useCallback((callback: (message: AnalysisCompleteMessage) => void) => {
    eventCallbacksRef.current.analysisComplete.push(callback);
    
    return () => {
      const index = eventCallbacksRef.current.analysisComplete.indexOf(callback);
      if (index > -1) {
        eventCallbacksRef.current.analysisComplete.splice(index, 1);
      }
    };
  }, []);

  const onAnalysisError = useCallback((callback: (message: AnalysisErrorMessage) => void) => {
    eventCallbacksRef.current.analysisError.push(callback);
    
    return () => {
      const index = eventCallbacksRef.current.analysisError.indexOf(callback);
      if (index > -1) {
        eventCallbacksRef.current.analysisError.splice(index, 1);
      }
    };
  }, []);

  const onBrandCreated = useCallback((callback: (message: BrandCreatedMessage) => void) => {
    eventCallbacksRef.current.brandCreated.push(callback);
    
    return () => {
      const index = eventCallbacksRef.current.brandCreated.indexOf(callback);
      if (index > -1) {
        eventCallbacksRef.current.brandCreated.splice(index, 1);
      }
    };
  }, []);

  // 修復的初始化連接 - 移除有問題的依賴項
  useEffect(() => {
    let mounted = true;

    if (opts.autoConnect && mounted && isWebSocketEnabled) {
      // 延遲連接以避免競態條件
      const timeoutId = setTimeout(() => {
        if (mounted && !isConnected && !isConnecting) {
          connect();
        }
      }, 100);

      return () => {
        mounted = false;
        clearTimeout(timeoutId);
        cleanup();
      };
    }

    return () => {
      mounted = false;
      cleanup();
    };
  }, []); // 移除所有可能導致循環的依賴項

  return {
    isConnected,
    isConnecting,
    error,
    lastMessage,
    connect,
    disconnect,
    sendMessage,
    subscribeToBrand,
    unsubscribeFromBrand,
    onAnalysisProgress,
    onAnalysisComplete,
    onAnalysisError,
    onBrandCreated,
    isWebSocketEnabled
  };
} 