services:
  # === 數據存儲層 ===
  postgres:
    image: postgres:16
    container_name: aiseo-postgres-essential
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=ai_seo_king
      - POSTGRES_USER=ai_seo_user
      - POSTGRES_PASSWORD=ai_seo_password
    volumes:
      - postgres-data-essential:/var/lib/postgresql/data
    networks:
      - aiseo-essential-network
    restart: unless-stopped

  redis:
    image: redis:7.2-alpine
    container_name: aiseo-redis-essential
    ports:
      - "6379:6379"
    volumes:
      - redis-data-essential:/data
    networks:
      - aiseo-essential-network
    restart: unless-stopped

  elasticsearch:
    image: elasticsearch:8.11.0
    container_name: aiseo-elasticsearch-essential
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    volumes:
      - elasticsearch-data-essential:/usr/share/elasticsearch/data
    networks:
      - aiseo-essential-network
    restart: unless-stopped

  # === Kafka 消息隊列 ===
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: aiseo-zookeeper-essential
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper-data-essential:/var/lib/zookeeper/data
      - zookeeper-logs-essential:/var/lib/zookeeper/log
    networks:
      - aiseo-essential-network
    restart: unless-stopped

  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: aiseo-kafka-essential
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
    volumes:
      - kafka-data-essential:/var/lib/kafka/data
    networks:
      - aiseo-essential-network
    restart: unless-stopped

  # === Apache Spark ===
  spark-master:
    image: bitnami/spark:3.5
    container_name: aiseo-spark-master-essential
    ports:
      - "8080:8080"  # Spark Master Web UI
      - "7077:7077"  # Spark Master Port
    environment:
      - SPARK_MODE=master
      - SPARK_RPC_AUTHENTICATION_ENABLED=no
      - SPARK_RPC_ENCRYPTION_ENABLED=no
      - SPARK_LOCAL_STORAGE_ENCRYPTION_ENABLED=no
      - SPARK_SSL_ENABLED=no
    volumes:
      - spark-data-essential:/opt/bitnami/spark/work
    networks:
      - aiseo-essential-network
    restart: unless-stopped

  spark-worker:
    image: bitnami/spark:3.5
    container_name: aiseo-spark-worker-essential
    depends_on:
      - spark-master
    environment:
      - SPARK_MODE=worker
      - SPARK_MASTER_URL=spark://spark-master:7077
      - SPARK_WORKER_MEMORY=1G
      - SPARK_WORKER_CORES=1
      - SPARK_RPC_AUTHENTICATION_ENABLED=no
      - SPARK_RPC_ENCRYPTION_ENABLED=no
      - SPARK_LOCAL_STORAGE_ENCRYPTION_ENABLED=no
      - SPARK_SSL_ENABLED=no
    volumes:
      - spark-data-essential:/opt/bitnami/spark/work
    networks:
      - aiseo-essential-network
    restart: unless-stopped

  # === Apache Flink ===
  flink-jobmanager:
    image: flink:1.18
    container_name: aiseo-flink-jobmanager-essential
    ports:
      - "8081:8081"
    command: jobmanager
    environment:
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: flink-jobmanager
        taskmanager.numberOfTaskSlots: 2
        parallelism.default: 1
    volumes:
      - flink-data-essential:/opt/flink/data
    networks:
      - aiseo-essential-network
    restart: unless-stopped

  flink-taskmanager:
    image: flink:1.18
    depends_on:
      - flink-jobmanager
    command: taskmanager
    environment:
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: flink-jobmanager
        taskmanager.numberOfTaskSlots: 2
        parallelism.default: 1
    volumes:
      - flink-data-essential:/opt/flink/data
    networks:
      - aiseo-essential-network
    restart: unless-stopped

  # === MLflow ===
  mlflow:
    image: python:3.11-slim
    container_name: aiseo-mlflow-essential
    ports:
      - "5000:5000"
    command: >
      bash -c "pip install mlflow==2.15.1 psycopg2-binary boto3 && 
      mlflow server 
      --backend-store-uri ******************************************************/ai_seo_king
      --default-artifact-root s3://mlflow-artifacts/
      --host 0.0.0.0
      --port 5000"
    depends_on:
      - postgres
    environment:
      - AWS_ACCESS_KEY_ID=minioadmin
      - AWS_SECRET_ACCESS_KEY=minioadmin
      - MLFLOW_S3_ENDPOINT_URL=http://minio:9000
    networks:
      - aiseo-essential-network
    restart: unless-stopped

  # === MinIO Object Storage ===
  minio:
    image: minio/minio:latest
    container_name: aiseo-minio-essential
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    command: server /data --console-address ":9001"
    volumes:
      - minio-data-essential:/data
    networks:
      - aiseo-essential-network
    restart: unless-stopped

  # === Kibana ===
  kibana:
    image: kibana:8.11.0
    container_name: aiseo-kibana-essential
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - ELASTICSEARCH_USERNAME=""
      - ELASTICSEARCH_PASSWORD=""
    depends_on:
      - elasticsearch
    networks:
      - aiseo-essential-network
    restart: unless-stopped

  # === Kafka UI ===
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: aiseo-kafka-ui-essential
    ports:
      - "8090:8080"
    environment:
      - KAFKA_CLUSTERS_0_NAME=aiseo-kafka
      - KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS=kafka:9092
    depends_on:
      - kafka
    networks:
      - aiseo-essential-network
    restart: unless-stopped

volumes:
  postgres-data-essential:
  redis-data-essential:
  elasticsearch-data-essential:
  zookeeper-data-essential:
  zookeeper-logs-essential:
  kafka-data-essential:
  spark-data-essential:
  flink-data-essential:
  minio-data-essential:

networks:
  aiseo-essential-network:
    driver: bridge 