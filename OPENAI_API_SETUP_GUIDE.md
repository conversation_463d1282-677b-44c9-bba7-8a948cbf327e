# OpenAI API 配置與測試指南

## 🔑 如何獲取 OpenAI API 密鑰

### 步驟 1：註冊 OpenAI 帳戶
1. 訪問 [OpenAI 官網](https://openai.com/)
2. 點擊「Sign up」註冊新帳戶或「Log in」登入現有帳戶

### 步驟 2：獲取 API 密鑰
1. 登入後，訪問 [OpenAI API Keys 頁面](https://platform.openai.com/api-keys)
2. 點擊「Create new secret key」
3. 為密鑰命名（例如：AI-SEO-King-Development）
4. 複製生成的密鑰（以 `sk-` 開頭，類似：`sk-proj-xxxxxxxxxxxxx`）

⚠️ **重要提醒：**
- 密鑰只會顯示一次，請立即保存
- 不要在代碼庫中直接寫入密鑰
- 定期輪換密鑰以保證安全

## 🔧 在系統中設置 API 密鑰

### 方法 1：通過系統設置頁面
1. 訪問 `http://localhost:3000/admin/settings`
2. 在「OpenAI API 配置」區域找到「API Key」欄位
3. 貼上您的密鑰（替換示例密鑰）
4. 點擊「保存設置」

### 方法 2：通過環境變量
1. 編輯項目根目錄的 `.env` 文件
2. 修改以下行：
   ```bash
   OPENAI_API_KEY=your-actual-api-key-here
   ```
3. 重啟後端服務器

## 🧪 測試 API 連接

### 演示測試（無需真實密鑰）
- 點擊「演示測試」按鈕
- 查看模擬的成功回應

### 真實 API 測試
1. 確保已設置真實的 API 密鑰
2. 點擊「測試連接」按鈕
3. 查看真實的 OpenAI API 回應

## 📊 API 使用監控

成功連接後，系統會顯示：
- ✅ 使用的模型（如 gpt-4o-mini）
- ⏱️ 回應時間
- 🔢 Token 使用量
- 💬 測試回應內容

## 🔍 故障排除

### 常見錯誤與解決方案

#### 1. "請設置您的真實 OpenAI API 密鑰"
- **原因：** 當前使用的是示例密鑰
- **解決：** 按照上述步驟設置真實密鑰

#### 2. "無效的 OpenAI API Key 格式"
- **原因：** 密鑰格式不正確
- **解決：** 確保密鑰以 `sk-` 開頭

#### 3. "API 錯誤 401"
- **原因：** 密鑰無效或已過期
- **解決：** 檢查密鑰是否正確，或生成新密鑰

#### 4. "請求超時"
- **原因：** 網路連接問題
- **解決：** 檢查網路連接，或稍後重試

## 💰 API 費用管理

### 設置使用限制
1. 訪問 [OpenAI 使用頁面](https://platform.openai.com/usage)
2. 設置月度消費限制
3. 監控每日使用量

### 優化成本
- 使用 `gpt-4o-mini` 進行測試（更便宜）
- 限制 `max_tokens` 參數
- 實施適當的快取策略

## 🔐 安全最佳實踐

1. **環境隔離**
   - 開發、測試、生產使用不同密鑰
   - 定期輪換密鑰

2. **權限控制**
   - 只給予必要的 API 權限
   - 監控 API 使用情況

3. **密鑰保護**
   - 使用環境變量存儲密鑰
   - 不要將密鑰提交到版本控制

## 📞 技術支援

如遇到問題，請檢查：
1. [OpenAI API 文檔](https://platform.openai.com/docs)
2. [API 狀態頁面](https://status.openai.com/)
3. 項目的系統日誌

---

**最後更新：** 2025年1月27日  
**版本：** 2.0.0 