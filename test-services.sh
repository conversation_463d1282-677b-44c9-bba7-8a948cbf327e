#!/bin/bash

echo "🔍 === AI SEO 優化王 - 服務健康檢查 ==="
echo ""

# 測試前端服務
echo "🌐 測試前端服務..."
curl -s http://localhost:3000 > /dev/null && echo "  ✅ Next.js (3000)" || echo "  ❌ Next.js (3000)"
curl -s http://localhost:8002 > /dev/null && echo "  ✅ WebSocket (8002)" || echo "  ❌ WebSocket (8002)"

echo ""
echo "📊 測試數據服務..."
# 測試數據庫服務
curl -s http://localhost:9200/_cluster/health > /dev/null && echo "  ✅ Elasticsearch (9200)" || echo "  ❌ Elasticsearch (9200)"
pg_isready -h localhost -p 5432 > /dev/null 2>&1 && echo "  ✅ PostgreSQL (5432)" || echo "  ❌ PostgreSQL (5432)"
redis-cli -p 6379 ping > /dev/null 2>&1 && echo "  ✅ Redis (6379)" || echo "  ❌ Redis (6379)"

echo ""
echo "⚡ 測試大數據處理..."
# 測試大數據服務
curl -s http://localhost:8080 > /dev/null && echo "  ✅ Spark Master (8080)" || echo "  ❌ Spark Master (8080)"
curl -s http://localhost:8081 > /dev/null && echo "  ✅ Flink JobManager (8081)" || echo "  ❌ Flink JobManager (8081)"

echo ""
echo "🤖 測試機器學習平台..."
curl -s http://localhost:5000 > /dev/null && echo "  ✅ MLflow (5000)" || echo "  ❌ MLflow (5000)"
curl -s http://localhost:9000/minio/health/live > /dev/null && echo "  ✅ MinIO (9000)" || echo "  ❌ MinIO (9000)"

echo ""
echo "📈 測試可視化工具..."
curl -s http://localhost:5601/app/home > /dev/null && echo "  ✅ Kibana (5601)" || echo "  ❌ Kibana (5601)"
curl -s http://localhost:8090 > /dev/null && echo "  ✅ Kafka UI (8090)" || echo "  ❌ Kafka UI (8090)"

echo ""
echo "🚀 === 服務檢查完成 ==="
echo ""
echo "📱 立即訪問:"
echo "  • 前端應用: http://localhost:3000"
echo "  • 實時圖表: http://localhost:3000/demo/realtime-charts"
echo "  • Spark UI: http://localhost:8080"
echo "  • Flink UI: http://localhost:8081"
echo "  • Kibana: http://localhost:5601"
echo "  • MinIO Console: http://localhost:9001"
echo "" 