# AI SEO 數據收集管道系統

> 企業級數據收集、處理和分析管道，為 AI SEO 優化提供實時數據支持

## 🚀 系統概覽

AI SEO 數據收集管道是一個高性能、可擴展的數據處理系統，專為 SEO 分析和優化需求設計。系統采用微服務架構，支持多種數據來源的實時收集、處理和存儲。

### 核心特性

- ✅ **多來源數據收集**: 支持網站搜索、API 請求、第三方平台數據
- ✅ **實時數據處理**: 異步管道處理，支持批次和流式處理
- ✅ **智能數據富化**: 自動添加地理位置、用戶畫像、意圖分類等信息
- ✅ **性能自動優化**: 基於機器學習的自動調優和瓶頸檢測
- ✅ **企業級監控**: 實時指標監控、告警和可視化儀表板
- ✅ **高可用設計**: 容錯機制、自動重試和故障恢復

## 🏗️ 系統架構

### 核心組件

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   數據收集器     │───▶│   消息隊列      │───▶│   數據處理器     │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • WebCollector  │    │ • Kafka Topics  │    │ • DataCleaning  │
│ • APICollector  │    │ • Queue Manager │    │ • DataEnrich    │
│ • ThirdParty    │    │ • Dead Letter   │    │ • IntentClass   │
└─────────────────┘    └─────────────────┘    │ • TopicExtract  │
                                               └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐            │
│   存儲層        │◀───│   性能優化器     │◀───────────┘
├─────────────────┤    ├─────────────────┤
│ • Elasticsearch │    │ • 瓶頸檢測      │
│ • PostgreSQL    │    │ • 自動調優      │
│ • Redis Cache   │    │ • 資源管理      │
└─────────────────┘    └─────────────────┘
```

### 數據流程

1. **數據收集階段**
   - 多種收集器並行工作
   - 統一數據格式標準化
   - 初步質量檢查

2. **消息隊列緩衝**
   - Kafka 分布式消息處理
   - 負載均衡和容錯機制
   - 重試和死信隊列

3. **數據處理管道**
   - 數據清洗和標準化
   - 地理位置和用戶信息富化
   - 意圖分類和主題提取

4. **存儲和索引**
   - Elasticsearch 全文搜索
   - PostgreSQL 結構化存儲
   - Redis 緩存和統計

5. **監控和優化**
   - 實時性能監控
   - 自動瓶頸檢測
   - 動態資源調整

## 📦 組件詳解

### 數據收集器 (Collectors)

#### BaseCollector
所有收集器的基礎類，提供：
- 統一的數據收集接口
- 緩衝區管理
- 統計指標追蹤
- 錯誤處理機制

#### WebSearchCollector
收集網站內部搜索數據：
```python
# 支持的數據類型
- 搜索查詢詞
- 用戶會話信息
- 搜索結果點擊
- 搜索時間和頻率
```

#### APICollector
收集 API 請求數據：
```python
# 監控的 API 端點
- SEO 分析請求
- 關鍵詞查詢
- 競爭對手分析
- 內容優化請求
```

#### ThirdPartyCollector
整合第三方平台數據：
```python
# 支持的平台
- Google Search Console
- Google Analytics
- 社交媒體 API
- SEO 工具 API
```

### 數據處理器 (Processors)

#### DataCleaningProcessor
數據清洗和標準化：
- 查詢詞標準化
- 停用詞過濾
- 特殊字符處理
- 數據格式統一

#### DataEnrichmentProcessor
數據富化和增強：
- IP 地理位置識別
- 設備信息分析
- 用戶畫像匹配
- 時間維度分析

#### IntentClassificationProcessor
用戶意圖分類：
```python
# 意圖類型
- 信息搜索 (informational)
- 導航搜索 (navigational)  
- 交易搜索 (transactional)
- 本地搜索 (local)
```

#### TopicExtractionProcessor
主題和關鍵詞提取：
- 行業特定主題識別
- 關鍵概念提取
- 語義關聯分析
- 趨勢主題發現

## 🛠️ 安裝和部署

### 系統要求

- **操作系統**: Linux/macOS/Windows
- **Python**: 3.11+
- **Docker**: 20.0+
- **內存**: 最低 4GB，推薦 8GB+
- **存儲**: 最低 10GB 可用空間

### 快速開始

#### 1. 自動化部署（推薦）

```bash
# 克隆項目
git clone <repository-url>
cd AISEOking

# 一鍵啟動所有服務
./scripts/quick_start.sh
```

#### 2. 手動部署

```bash
# 1. 設置環境變量
cp .env.example .env
# 編輯 .env 文件，設置必要的配置

# 2. 啟動基礎設施
docker-compose -f docker-compose.minimal.yml up -d

# 3. 等待服務就緒
./scripts/deploy_pipeline.sh health

# 4. 啟動 FastAPI 後端
cd backend-fastapi
uvicorn app.main:app --host 0.0.0.0 --port 8000

# 5. 啟動數據收集管道
python ../scripts/start_pipeline.py start
```

### 環境配置

```bash
# .env 文件配置示例
DATABASE_URL=postgresql://ai_seo_user:ai_seo_password@localhost:5432/ai_seo_king
REDIS_URL=redis://:aiseo123@localhost:6379/0
ELASTICSEARCH_URL=http://localhost:9200
OPENAI_API_KEY=your_openai_api_key_here

# 管道性能配置
PIPELINE_BATCH_SIZE=100
PIPELINE_FLUSH_INTERVAL=5000
PIPELINE_MAX_RETRIES=3
PIPELINE_PARALLELISM=4
```

## 📊 監控和管理

### Web 儀表板

訪問 `http://localhost:8000/docs` 查看完整 API 文檔

#### 主要監控端點

- **健康檢查**: `GET /api/v1/pipeline/health`
- **管道狀態**: `GET /api/v1/pipeline/status`  
- **性能指標**: `GET /api/v1/pipeline/metrics`
- **收集器狀態**: `GET /api/v1/pipeline/collectors`
- **處理器狀態**: `GET /api/v1/pipeline/processors`

#### 儀表板功能

```bash
# 實時監控
GET /api/v1/pipeline/dashboard/overview

# 時間序列數據
GET /api/v1/pipeline/dashboard/timeseries/throughput
GET /api/v1/pipeline/dashboard/timeseries/latency
GET /api/v1/pipeline/dashboard/timeseries/errors

# 告警配置
POST /api/v1/pipeline/dashboard/alerts/config
GET /api/v1/pipeline/dashboard/alerts
```

### 命令行工具

#### 部署腳本

```bash
# 部署管道
./scripts/deploy_pipeline.sh deploy

# 檢查狀態
./scripts/deploy_pipeline.sh status

# 查看日誌
./scripts/deploy_pipeline.sh logs

# 健康檢查
./scripts/deploy_pipeline.sh health

# 重啟服務
./scripts/deploy_pipeline.sh restart
```

#### 管道管理

```bash
# 啟動管道
python scripts/start_pipeline.py start

# 查看狀態
python scripts/start_pipeline.py status

# 查看詳細指標
python scripts/start_pipeline.py metrics

# 測試管道
python scripts/start_pipeline.py test --test-samples 50

# 停止管道
python scripts/start_pipeline.py stop
```

## 🔧 API 使用指南

### 數據收集 API

#### 單個查詢收集

```python
import requests

# 收集單個查詢數據
data = {
    "query": "SEO 優化技巧",
    "source": "web_search",
    "user_id": "user123",
    "session_id": "session456",
    "url": "https://example.com/search",
    "user_agent": "Mozilla/5.0...",
    "ip_address": "***********"
}

response = requests.post(
    "http://localhost:8000/api/v1/pipeline/collect",
    json=data,
    headers={"Authorization": "Bearer YOUR_TOKEN"}
)
```

#### 批次數據收集

```python
# 批次收集查詢數據
queries = [
    {"query": "關鍵詞研究", "source": "api_request"},
    {"query": "內容優化", "source": "web_search"},
    {"query": "競爭分析", "source": "third_party"}
]

response = requests.post(
    "http://localhost:8000/api/v1/pipeline/collect/batch",
    json=queries,
    headers={"Authorization": "Bearer YOUR_TOKEN"}
)
```

### 管道控制 API

```python
# 啟動管道
requests.post("http://localhost:8000/api/v1/pipeline/start")

# 停止管道  
requests.post("http://localhost:8000/api/v1/pipeline/stop")

# 重啟管道
requests.post("http://localhost:8000/api/v1/pipeline/restart")

# 獲取狀態
response = requests.get("http://localhost:8000/api/v1/pipeline/status")
print(response.json())
```

## ⚡ 性能優化

### 自動優化功能

系統內建智能優化器，可自動：

1. **批次大小調整**: 根據吞吐量動態調整
2. **並行度優化**: 基於 CPU 使用率調整
3. **內存管理**: 自動垃圾回收和緩存清理
4. **重試策略**: 根據錯誤率調整重試參數

### 手動調優參數

```python
# 在 .env 文件中調整
PIPELINE_BATCH_SIZE=200          # 批次大小
PIPELINE_PARALLELISM=8           # 並行線程數
PIPELINE_FLUSH_INTERVAL=3000     # 刷新間隔(ms)
PIPELINE_MAX_RETRIES=5           # 最大重試次數
```

### 性能監控指標

- **吞吐量**: 每分鐘處理的查詢數
- **延遲**: 平均處理時間
- **錯誤率**: 失敗請求百分比
- **資源使用**: CPU 和內存使用率

## 🔒 安全考慮

### 數據安全

- 敏感數據加密存儲
- 用戶隱私信息脫敏
- 數據傳輸 HTTPS 加密
- 定期數據清理策略

### 訪問控制

```python
# JWT 認證配置
JWT_SECRET_KEY="your-secret-key"
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=1440

# API 限流
RATE_LIMIT_PER_MINUTE=1000
RATE_LIMIT_BURST=100
```

### 系統安全

- Docker 容器隔離
- 最小權限原則
- 定期安全更新
- 日誌審計功能

## 🚨 故障排除

### 常見問題

#### 1. 管道無法啟動

```bash
# 檢查服務狀態
docker-compose -f docker-compose.minimal.yml ps

# 查看服務日誌
docker-compose -f docker-compose.minimal.yml logs elasticsearch
docker-compose -f docker-compose.minimal.yml logs postgres
```

#### 2. 連接錯誤

```bash
# 檢查網絡連接
curl http://localhost:9200/_cluster/health
redis-cli -h localhost -p 6379 ping
```

#### 3. 性能問題

```bash
# 查看詳細指標
python scripts/start_pipeline.py metrics

# 檢查資源使用
docker stats
```

### 日誌分析

```bash
# FastAPI 日誌
tail -f backend-fastapi/logs/app.log

# 管道處理日誌  
grep "ERROR\|WARNING" backend-fastapi/logs/pipeline.log

# 性能日誌
grep "optimization" backend-fastapi/logs/optimizer.log
```

## 📈 擴展和定制

### 添加新的收集器

```python
# 創建自定義收集器
from app.services.collectors.base_collector import BaseCollector

class CustomCollector(BaseCollector):
    async def collect_data(self, source_config):
        # 實現自定義數據收集邏輯
        pass
```

### 添加新的處理器

```python
# 創建自定義處理器
class CustomProcessor:
    async def process(self, data):
        # 實現自定義數據處理邏輯
        return processed_data
```

### 配置新的數據來源

```yaml
# 在配置文件中添加
collectors:
  custom_source:
    enabled: true
    endpoint: "https://api.example.com"
    rate_limit: 100
    timeout: 30
```

## 📚 開發指南

### 開發環境設置

```bash
# 1. 創建開發環境
python -m venv .venv
source .venv/bin/activate

# 2. 安裝開發依賴
pip install -r backend-fastapi/requirements-dev.txt

# 3. 設置 pre-commit hooks
pre-commit install

# 4. 運行測試
pytest backend-fastapi/tests/
```

### 代碼結構

```
backend-fastapi/app/
├── services/
│   ├── data_collection_pipeline.py    # 主管道服務
│   ├── pipeline_optimizer.py          # 性能優化器
│   ├── collectors/                     # 數據收集器
│   │   ├── base_collector.py
│   │   ├── web_search_collector.py
│   │   ├── api_collector.py
│   │   └── third_party_collector.py
│   └── processors/                     # 數據處理器
│       ├── data_cleaning_processor.py
│       ├── data_enrichment_processor.py
│       ├── intent_classification_processor.py
│       └── topic_extraction_processor.py
├── api/v1/endpoints/
│   ├── data_pipeline.py               # 管道控制 API
│   └── pipeline_dashboard.py          # 儀表板 API
└── core/
    ├── config.py                       # 配置管理
    ├── database.py                     # 數據庫連接
    └── redis.py                        # Redis 連接
```

### 測試策略

```bash
# 單元測試
pytest backend-fastapi/tests/unit/

# 集成測試
pytest backend-fastapi/tests/integration/

# 性能測試
pytest backend-fastapi/tests/performance/

# 端到端測試
pytest backend-fastapi/tests/e2e/
```

## 🤝 貢獻指南

### 貢獻流程

1. Fork 項目
2. 創建特性分支
3. 提交更改
4. 創建 Pull Request
5. 代碼審查
6. 合併到主分支

### 代碼規範

- 遵循 PEP 8 Python 代碼規範
- 使用 Black 代碼格式化
- 編寫完整的文檔字符串
- 添加適當的類型注解
- 編寫相應的測試用例

## 📄 許可證

本項目采用 MIT 許可證，詳見 [LICENSE](LICENSE) 文件。

## 🆘 支持和反饋

- **問題報告**: 在 GitHub Issues 中報告 bug
- **功能請求**: 提交 feature request
- **文檔**: 查看完整的 API 文檔
- **社區**: 加入開發者討論群

---

**最後更新**: 2024年12月26日  
**版本**: 1.0.0  
**維護者**: AI SEO King 開發團隊 