[2025-06-24T17:25:21.532+0000][37032][gc,init] CardTable entry size: 512
[2025-06-24T17:25:21.533+0000][37032][gc     ] Using G1
[2025-06-24T17:25:21.651+0000][37032][gc,init] Version: 21.0.1+12-29 (release)
[2025-06-24T17:25:21.651+0000][37032][gc,init] CPUs: 20 total, 20 available
[2025-06-24T17:25:21.651+0000][37032][gc,init] Memory: 65536M
[2025-06-24T17:25:21.651+0000][37032][gc,init] Large Page Support: Disabled
[2025-06-24T17:25:21.651+0000][37032][gc,init] NUMA Support: Disabled
[2025-06-24T17:25:21.651+0000][37032][gc,init] Compressed Oops: Enabled (Zero based)
[2025-06-24T17:25:21.651+0000][37032][gc,init] Heap Region Size: 4M
[2025-06-24T17:25:21.651+0000][37032][gc,init] Heap Min Capacity: 2G
[2025-06-24T17:25:21.651+0000][37032][gc,init] Heap Initial Capacity: 2G
[2025-06-24T17:25:21.651+0000][37032][gc,init] Heap Max Capacity: 2G
[2025-06-24T17:25:21.651+0000][37032][gc,init] Pre-touch: Enabled
[2025-06-24T17:25:21.651+0000][37032][gc,init] Parallel Workers: 15
[2025-06-24T17:25:21.651+0000][37032][gc,init] Concurrent Workers: 4
[2025-06-24T17:25:21.651+0000][37032][gc,init] Concurrent Refinement Workers: 15
[2025-06-24T17:25:21.651+0000][37032][gc,init] Periodic GC: Disabled
[2025-06-24T17:25:21.655+0000][37032][gc,metaspace] CDS archive(s) mapped at: [0x0000007000000000-0x0000007000ca4000-0x0000007000ca4000), size 13254656, SharedBaseAddress: 0x0000007000000000, ArchiveRelocationMode: 1.
[2025-06-24T17:25:21.655+0000][37032][gc,metaspace] Compressed class space mapped at: 0x0000007001000000-0x0000007041000000, reserved size: 1073741824
[2025-06-24T17:25:21.655+0000][37032][gc,metaspace] Narrow klass base: 0x0000007000000000, Narrow klass shift: 0, Narrow klass range: 0x100000000
[2025-06-24T17:25:21.773+0000][37032][safepoint   ] Safepoint "ICBufferFull", Time since last: 115003042 ns, Reaching safepoint: 23792 ns, Cleanup: 103125 ns, At safepoint: 29666 ns, Total: 156583 ns
[2025-06-24T17:25:21.867+0000][37032][safepoint   ] Safepoint "ICBufferFull", Time since last: 93918167 ns, Reaching safepoint: 1875 ns, Cleanup: 95125 ns, At safepoint: 458 ns, Total: 97458 ns
[2025-06-24T17:25:21.976+0000][37032][safepoint   ] Safepoint "ICBufferFull", Time since last: 109497500 ns, Reaching safepoint: 20792 ns, Cleanup: 105792 ns, At safepoint: 31125 ns, Total: 157709 ns
[2025-06-24T17:25:22.092+0000][37032][safepoint   ] Safepoint "ICBufferFull", Time since last: 115925083 ns, Reaching safepoint: 2375 ns, Cleanup: 103250 ns, At safepoint: 44083 ns, Total: 149708 ns
[2025-06-24T17:25:22.150+0000][37032][safepoint   ] Safepoint "ICBufferFull", Time since last: 57884000 ns, Reaching safepoint: 2459 ns, Cleanup: 87583 ns, At safepoint: 667 ns, Total: 90709 ns
[2025-06-24T17:25:22.393+0000][37032][safepoint   ] Safepoint "ICBufferFull", Time since last: 242746375 ns, Reaching safepoint: 25750 ns, Cleanup: 127041 ns, At safepoint: 27042 ns, Total: 179833 ns
[2025-06-24T17:25:22.519+0000][37032][safepoint   ] Safepoint "ICBufferFull", Time since last: 125176208 ns, Reaching safepoint: 2667 ns, Cleanup: 137208 ns, At safepoint: 792 ns, Total: 140667 ns
[2025-06-24T17:25:22.600+0000][37032][gc,start    ] GC(0) Pause Young (Normal) (G1 Evacuation Pause)
[2025-06-24T17:25:22.600+0000][37032][gc,task     ] GC(0) Using 15 workers of 15 for evacuation
[2025-06-24T17:25:22.600+0000][37032][gc,age      ] GC(0) Desired survivor size 8388608 bytes, new threshold 15 (max threshold 15)
[2025-06-24T17:25:22.604+0000][37032][gc,phases   ] GC(0)   Pre Evacuate Collection Set: 0.2ms
[2025-06-24T17:25:22.604+0000][37032][gc,phases   ] GC(0)   Merge Heap Roots: 0.1ms
[2025-06-24T17:25:22.604+0000][37032][gc,phases   ] GC(0)   Evacuate Collection Set: 2.4ms
[2025-06-24T17:25:22.604+0000][37032][gc,phases   ] GC(0)   Post Evacuate Collection Set: 0.7ms
[2025-06-24T17:25:22.604+0000][37032][gc,phases   ] GC(0)   Other: 0.4ms
[2025-06-24T17:25:22.604+0000][37032][gc,age      ] GC(0) Age table with threshold 15 (max threshold 15)
[2025-06-24T17:25:22.604+0000][37032][gc,age      ] GC(0) - age   1:    8134552 bytes,    8134552 total
[2025-06-24T17:25:22.604+0000][37032][gc,heap     ] GC(0) Eden regions: 25->0(22)
[2025-06-24T17:25:22.604+0000][37032][gc,heap     ] GC(0) Survivor regions: 0->3(4)
[2025-06-24T17:25:22.604+0000][37032][gc,heap     ] GC(0) Old regions: 1->1
[2025-06-24T17:25:22.604+0000][37032][gc,heap     ] GC(0) Humongous regions: 0->0
[2025-06-24T17:25:22.604+0000][37032][gc,metaspace] GC(0) Metaspace: 16072K(16384K)->16072K(16384K) NonClass: 14216K(14400K)->14216K(14400K) Class: 1855K(1984K)->1855K(1984K)
[2025-06-24T17:25:22.604+0000][37032][gc          ] GC(0) Pause Young (Normal) (G1 Evacuation Pause) 101M->9M(2048M) 3.998ms
[2025-06-24T17:25:22.604+0000][37032][gc,cpu      ] GC(0) User=0.02s Sys=0.01s Real=0.00s
[2025-06-24T17:25:22.604+0000][37032][safepoint   ] Safepoint "G1CollectForAllocation", Time since last: 80907042 ns, Reaching safepoint: 2083 ns, Cleanup: 56417 ns, At safepoint: 4090125 ns, Total: 4148625 ns
[2025-06-24T17:25:22.684+0000][37032][gc,start    ] GC(1) Pause Young (Normal) (G1 Evacuation Pause)
[2025-06-24T17:25:22.685+0000][37032][gc,task     ] GC(1) Using 15 workers of 15 for evacuation
[2025-06-24T17:25:22.685+0000][37032][gc,age      ] GC(1) Desired survivor size 8388608 bytes, new threshold 15 (max threshold 15)
[2025-06-24T17:25:22.689+0000][37032][gc,phases   ] GC(1)   Pre Evacuate Collection Set: 0.2ms
[2025-06-24T17:25:22.689+0000][37032][gc,phases   ] GC(1)   Merge Heap Roots: 0.1ms
[2025-06-24T17:25:22.689+0000][37032][gc,phases   ] GC(1)   Evacuate Collection Set: 3.1ms
[2025-06-24T17:25:22.689+0000][37032][gc,phases   ] GC(1)   Post Evacuate Collection Set: 0.6ms
[2025-06-24T17:25:22.689+0000][37032][gc,phases   ] GC(1)   Other: 0.0ms
[2025-06-24T17:25:22.689+0000][37032][gc,age      ] GC(1) Age table with threshold 15 (max threshold 15)
[2025-06-24T17:25:22.689+0000][37032][gc,age      ] GC(1) - age   1:    2529896 bytes,    2529896 total
[2025-06-24T17:25:22.689+0000][37032][gc,age      ] GC(1) - age   2:    8017720 bytes,   10547616 total
[2025-06-24T17:25:22.689+0000][37032][gc,heap     ] GC(1) Eden regions: 22->0(22)
[2025-06-24T17:25:22.689+0000][37032][gc,heap     ] GC(1) Survivor regions: 3->3(4)
[2025-06-24T17:25:22.689+0000][37032][gc,heap     ] GC(1) Old regions: 1->1
[2025-06-24T17:25:22.689+0000][37032][gc,heap     ] GC(1) Humongous regions: 0->0
[2025-06-24T17:25:22.689+0000][37032][gc,metaspace] GC(1) Metaspace: 16375K(16640K)->16375K(16640K) NonClass: 14495K(14656K)->14495K(14656K) Class: 1880K(1984K)->1880K(1984K)
[2025-06-24T17:25:22.689+0000][37032][gc          ] GC(1) Pause Young (Normal) (G1 Evacuation Pause) 97M->12M(2048M) 4.200ms
[2025-06-24T17:25:22.689+0000][37032][gc,cpu      ] GC(1) User=0.03s Sys=0.01s Real=0.00s
[2025-06-24T17:25:22.689+0000][37032][safepoint   ] Safepoint "G1CollectForAllocation", Time since last: 80621166 ns, Reaching safepoint: 21500 ns, Cleanup: 82459 ns, At safepoint: 4284625 ns, Total: 4388584 ns
[2025-06-24T17:25:22.794+0000][37032][safepoint   ] Safepoint "ICBufferFull", Time since last: 105347083 ns, Reaching safepoint: 2375 ns, Cleanup: 122750 ns, At safepoint: 750 ns, Total: 125875 ns
[2025-06-24T17:25:22.825+0000][37032][gc,start    ] GC(2) Pause Young (Normal) (G1 Evacuation Pause)
[2025-06-24T17:25:22.825+0000][37032][gc,task     ] GC(2) Using 15 workers of 15 for evacuation
[2025-06-24T17:25:22.825+0000][37032][gc,age      ] GC(2) Desired survivor size 8388608 bytes, new threshold 2 (max threshold 15)
[2025-06-24T17:25:22.830+0000][37032][gc,phases   ] GC(2)   Pre Evacuate Collection Set: 0.2ms
[2025-06-24T17:25:22.830+0000][37032][gc,phases   ] GC(2)   Merge Heap Roots: 0.1ms
[2025-06-24T17:25:22.830+0000][37032][gc,phases   ] GC(2)   Evacuate Collection Set: 3.5ms
[2025-06-24T17:25:22.830+0000][37032][gc,phases   ] GC(2)   Post Evacuate Collection Set: 0.5ms
[2025-06-24T17:25:22.830+0000][37032][gc,phases   ] GC(2)   Other: 0.0ms
[2025-06-24T17:25:22.830+0000][37032][gc,age      ] GC(2) Age table with threshold 2 (max threshold 15)
[2025-06-24T17:25:22.830+0000][37032][gc,age      ] GC(2) - age   1:    2371784 bytes,    2371784 total
[2025-06-24T17:25:22.830+0000][37032][gc,age      ] GC(2) - age   2:     326152 bytes,    2697936 total
[2025-06-24T17:25:22.830+0000][37032][gc,heap     ] GC(2) Eden regions: 22->0(29)
[2025-06-24T17:25:22.830+0000][37032][gc,heap     ] GC(2) Survivor regions: 3->1(4)
[2025-06-24T17:25:22.830+0000][37032][gc,heap     ] GC(2) Old regions: 1->3
[2025-06-24T17:25:22.830+0000][37032][gc,heap     ] GC(2) Humongous regions: 0->0
[2025-06-24T17:25:22.830+0000][37032][gc,metaspace] GC(2) Metaspace: 18418K(18816K)->18418K(18816K) NonClass: 16284K(16512K)->16284K(16512K) Class: 2134K(2304K)->2134K(2304K)
[2025-06-24T17:25:22.830+0000][37032][gc          ] GC(2) Pause Young (Normal) (G1 Evacuation Pause) 100M->12M(2048M) 4.419ms
[2025-06-24T17:25:22.830+0000][37032][gc,cpu      ] GC(2) User=0.03s Sys=0.01s Real=0.01s
[2025-06-24T17:25:22.830+0000][37032][safepoint   ] Safepoint "G1CollectForAllocation", Time since last: 30973000 ns, Reaching safepoint: 2500 ns, Cleanup: 30542 ns, At safepoint: 4477625 ns, Total: 4510667 ns
[2025-06-24T17:25:22.948+0000][37032][gc,start    ] GC(3) Pause Young (Normal) (G1 Evacuation Pause)
[2025-06-24T17:25:22.948+0000][37032][gc,task     ] GC(3) Using 15 workers of 15 for evacuation
[2025-06-24T17:25:22.948+0000][37032][gc,age      ] GC(3) Desired survivor size 8388608 bytes, new threshold 15 (max threshold 15)
[2025-06-24T17:25:22.950+0000][37032][gc,phases   ] GC(3)   Pre Evacuate Collection Set: 0.2ms
[2025-06-24T17:25:22.950+0000][37032][gc,phases   ] GC(3)   Merge Heap Roots: 0.1ms
[2025-06-24T17:25:22.950+0000][37032][gc,phases   ] GC(3)   Evacuate Collection Set: 0.8ms
[2025-06-24T17:25:22.950+0000][37032][gc,phases   ] GC(3)   Post Evacuate Collection Set: 0.3ms
[2025-06-24T17:25:22.950+0000][37032][gc,phases   ] GC(3)   Other: 0.0ms
[2025-06-24T17:25:22.950+0000][37032][gc,age      ] GC(3) Age table with threshold 15 (max threshold 15)
[2025-06-24T17:25:22.950+0000][37032][gc,age      ] GC(3) - age   1:    2717824 bytes,    2717824 total
[2025-06-24T17:25:22.950+0000][37032][gc,age      ] GC(3) - age   2:    1669432 bytes,    4387256 total
[2025-06-24T17:25:22.950+0000][37032][gc,age      ] GC(3) - age   3:     325304 bytes,    4712560 total
[2025-06-24T17:25:22.950+0000][37032][gc,heap     ] GC(3) Eden regions: 29->0(305)
[2025-06-24T17:25:22.950+0000][37032][gc,heap     ] GC(3) Survivor regions: 1->2(4)
[2025-06-24T17:25:22.950+0000][37032][gc,heap     ] GC(3) Old regions: 3->3
[2025-06-24T17:25:22.950+0000][37032][gc,heap     ] GC(3) Humongous regions: 0->0
[2025-06-24T17:25:22.950+0000][37032][gc,metaspace] GC(3) Metaspace: 19060K(19456K)->19060K(19456K) NonClass: 16813K(17024K)->16813K(17024K) Class: 2246K(2432K)->2246K(2432K)
[2025-06-24T17:25:22.950+0000][37032][gc          ] GC(3) Pause Young (Normal) (G1 Evacuation Pause) 128M->13M(2048M) 1.624ms
[2025-06-24T17:25:22.950+0000][37032][gc,cpu      ] GC(3) User=0.02s Sys=0.01s Real=0.01s
[2025-06-24T17:25:22.950+0000][37032][safepoint   ] Safepoint "G1CollectForAllocation", Time since last: 118493083 ns, Reaching safepoint: 2333 ns, Cleanup: 111875 ns, At safepoint: 1703417 ns, Total: 1817625 ns
[2025-06-24T17:25:23.331+0000][37032][safepoint   ] Safepoint "ICBufferFull", Time since last: 381155417 ns, Reaching safepoint: 2583 ns, Cleanup: 122333 ns, At safepoint: 542 ns, Total: 125458 ns
[2025-06-24T17:25:23.543+0000][37032][gc,start    ] GC(4) Pause Young (Concurrent Start) (Metadata GC Threshold)
[2025-06-24T17:25:23.543+0000][37032][gc,task     ] GC(4) Using 15 workers of 15 for evacuation
[2025-06-24T17:25:23.543+0000][37032][gc,age      ] GC(4) Desired survivor size 81788928 bytes, new threshold 15 (max threshold 15)
[2025-06-24T17:25:23.545+0000][37032][gc,phases   ] GC(4)   Pre Evacuate Collection Set: 0.2ms
[2025-06-24T17:25:23.545+0000][37032][gc,phases   ] GC(4)   Merge Heap Roots: 0.1ms
[2025-06-24T17:25:23.545+0000][37032][gc,phases   ] GC(4)   Evacuate Collection Set: 1.1ms
[2025-06-24T17:25:23.545+0000][37032][gc,phases   ] GC(4)   Post Evacuate Collection Set: 0.5ms
[2025-06-24T17:25:23.545+0000][37032][gc,phases   ] GC(4)   Other: 0.1ms
[2025-06-24T17:25:23.545+0000][37032][gc,age      ] GC(4) Age table with threshold 15 (max threshold 15)
[2025-06-24T17:25:23.545+0000][37032][gc,age      ] GC(4) - age   1:    5933688 bytes,    5933688 total
[2025-06-24T17:25:23.545+0000][37032][gc,age      ] GC(4) - age   2:    1885896 bytes,    7819584 total
[2025-06-24T17:25:23.545+0000][37032][gc,age      ] GC(4) - age   3:    1665296 bytes,    9484880 total
[2025-06-24T17:25:23.545+0000][37032][gc,age      ] GC(4) - age   4:     325112 bytes,    9809992 total
[2025-06-24T17:25:23.545+0000][37032][gc,heap     ] GC(4) Eden regions: 198->0(304)
[2025-06-24T17:25:23.545+0000][37032][gc,heap     ] GC(4) Survivor regions: 2->3(39)
[2025-06-24T17:25:23.545+0000][37032][gc,heap     ] GC(4) Old regions: 3->3
[2025-06-24T17:25:23.545+0000][37032][gc,heap     ] GC(4) Humongous regions: 0->0
[2025-06-24T17:25:23.545+0000][37032][gc,metaspace] GC(4) Metaspace: 20953K(21504K)->20953K(21504K) NonClass: 18435K(18752K)->18435K(18752K) Class: 2517K(2752K)->2517K(2752K)
[2025-06-24T17:25:23.545+0000][37032][gc          ] GC(4) Pause Young (Concurrent Start) (Metadata GC Threshold) 803M->18M(2048M) 2.150ms
[2025-06-24T17:25:23.545+0000][37032][gc,cpu      ] GC(4) User=0.01s Sys=0.00s Real=0.00s
[2025-06-24T17:25:23.545+0000][37032][gc          ] GC(5) Concurrent Mark Cycle
[2025-06-24T17:25:23.545+0000][37032][safepoint   ] Safepoint "CollectForMetadataAllocation", Time since last: 211496875 ns, Reaching safepoint: 2958 ns, Cleanup: 61042 ns, At safepoint: 2214292 ns, Total: 2278292 ns
[2025-06-24T17:25:23.545+0000][37032][gc,marking  ] GC(5) Concurrent Scan Root Regions
[2025-06-24T17:25:23.546+0000][37032][gc,marking  ] GC(5) Concurrent Scan Root Regions 0.724ms
[2025-06-24T17:25:23.546+0000][37032][gc,marking  ] GC(5) Concurrent Mark
[2025-06-24T17:25:23.546+0000][37032][gc,marking  ] GC(5) Concurrent Mark From Roots
[2025-06-24T17:25:23.546+0000][37032][gc,task     ] GC(5) Using 4 workers of 4 for marking
[2025-06-24T17:25:23.549+0000][37032][gc,marking  ] GC(5) Concurrent Mark From Roots 3.100ms
[2025-06-24T17:25:23.549+0000][37032][gc,marking  ] GC(5) Concurrent Preclean
[2025-06-24T17:25:23.549+0000][37032][gc,marking  ] GC(5) Concurrent Preclean 0.031ms
[2025-06-24T17:25:23.549+0000][37032][gc,start    ] GC(5) Pause Remark
[2025-06-24T17:25:23.550+0000][37032][gc          ] GC(5) Pause Remark 22M->22M(2048M) 1.341ms
[2025-06-24T17:25:23.550+0000][37032][gc,cpu      ] GC(5) User=0.01s Sys=0.00s Real=0.01s
[2025-06-24T17:25:23.551+0000][37032][safepoint   ] Safepoint "G1PauseRemark", Time since last: 3951333 ns, Reaching safepoint: 48875 ns, Cleanup: 4917 ns, At safepoint: 1450416 ns, Total: 1504208 ns
[2025-06-24T17:25:23.551+0000][37032][gc,marking  ] GC(5) Concurrent Mark 4.710ms
[2025-06-24T17:25:23.551+0000][37032][gc,marking  ] GC(5) Concurrent Rebuild Remembered Sets and Scrub Regions
[2025-06-24T17:25:23.553+0000][37032][gc,marking  ] GC(5) Concurrent Rebuild Remembered Sets and Scrub Regions 2.597ms
[2025-06-24T17:25:23.553+0000][37032][gc,start    ] GC(5) Pause Cleanup
[2025-06-24T17:25:23.553+0000][37032][gc          ] GC(5) Pause Cleanup 22M->22M(2048M) 0.113ms
[2025-06-24T17:25:23.553+0000][37032][gc,cpu      ] GC(5) User=0.00s Sys=0.00s Real=0.00s
[2025-06-24T17:25:23.553+0000][37032][safepoint   ] Safepoint "G1PauseCleanup", Time since last: 2658834 ns, Reaching safepoint: 23458 ns, Cleanup: 750 ns, At safepoint: 154292 ns, Total: 178500 ns
[2025-06-24T17:25:23.553+0000][37032][gc,marking  ] GC(5) Concurrent Clear Claimed Marks
[2025-06-24T17:25:23.553+0000][37032][gc,marking  ] GC(5) Concurrent Clear Claimed Marks 0.031ms
[2025-06-24T17:25:23.553+0000][37032][gc,marking  ] GC(5) Concurrent Cleanup for Next Mark
[2025-06-24T17:25:23.554+0000][37032][gc,marking  ] GC(5) Concurrent Cleanup for Next Mark 0.668ms
[2025-06-24T17:25:23.554+0000][37032][gc          ] GC(5) Concurrent Mark Cycle 9.038ms
[2025-06-24T17:25:23.950+0000][37032][safepoint   ] Safepoint "ICBufferFull", Time since last: 396745416 ns, Reaching safepoint: 3750 ns, Cleanup: 100625 ns, At safepoint: 459 ns, Total: 104834 ns
[2025-06-24T17:25:24.025+0000][37032][safepoint   ] Safepoint "ICBufferFull", Time since last: 74614916 ns, Reaching safepoint: 2750 ns, Cleanup: 133709 ns, At safepoint: 375 ns, Total: 136834 ns
[2025-06-24T17:25:24.236+0000][37032][safepoint   ] Safepoint "ICBufferFull", Time since last: 211078208 ns, Reaching safepoint: 2583 ns, Cleanup: 142334 ns, At safepoint: 29958 ns, Total: 174875 ns
[2025-06-24T17:25:24.312+0000][37032][safepoint   ] Safepoint "ICBufferFull", Time since last: 76171292 ns, Reaching safepoint: 2666 ns, Cleanup: 90000 ns, At safepoint: 625 ns, Total: 93291 ns
