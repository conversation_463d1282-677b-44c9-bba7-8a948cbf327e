[2025-06-24T16:07:12.789+0000][86945][gc,init] CardTable entry size: 512
[2025-06-24T16:07:12.789+0000][86945][gc     ] Using G1
[2025-06-24T16:07:18.928+0000][86945][gc,init] Version: 21.0.1+12-29 (release)
[2025-06-24T16:07:18.928+0000][86945][gc,init] CPUs: 20 total, 20 available
[2025-06-24T16:07:18.928+0000][86945][gc,init] Memory: 65536M
[2025-06-24T16:07:18.928+0000][86945][gc,init] Large Page Support: Disabled
[2025-06-24T16:07:18.928+0000][86945][gc,init] NUMA Support: Disabled
[2025-06-24T16:07:18.928+0000][86945][gc,init] Compressed Oops: Enabled (Non-zero based)
[2025-06-24T16:07:18.928+0000][86945][gc,init] Heap Region Size: 16M
[2025-06-24T16:07:18.928+0000][86945][gc,init] Heap Min Capacity: 31G
[2025-06-24T16:07:18.928+0000][86945][gc,init] Heap Initial Capacity: 31G
[2025-06-24T16:07:18.928+0000][86945][gc,init] Heap Max Capacity: 31G
[2025-06-24T16:07:18.928+0000][86945][gc,init] Pre-touch: Enabled
[2025-06-24T16:07:18.928+0000][86945][gc,init] Parallel Workers: 15
[2025-06-24T16:07:18.928+0000][86945][gc,init] Concurrent Workers: 4
[2025-06-24T16:07:18.928+0000][86945][gc,init] Concurrent Refinement Workers: 15
[2025-06-24T16:07:18.928+0000][86945][gc,init] Periodic GC: Disabled
[2025-06-24T16:07:18.933+0000][86945][gc,metaspace] CDS archive(s) mapped at: [0x000000a000000000-0x000000a000ca4000-0x000000a000ca4000), size 13254656, SharedBaseAddress: 0x000000a000000000, ArchiveRelocationMode: 1.
[2025-06-24T16:07:18.933+0000][86945][gc,metaspace] Compressed class space mapped at: 0x000000a001000000-0x000000a041000000, reserved size: 1073741824
[2025-06-24T16:07:18.933+0000][86945][gc,metaspace] Narrow klass base: 0x000000a000000000, Narrow klass shift: 0, Narrow klass range: 0x100000000
[2025-06-24T16:07:19.085+0000][86945][safepoint   ] Safepoint "ICBufferFull", Time since last: 149226625 ns, Reaching safepoint: 32500 ns, Cleanup: 115000 ns, At safepoint: 29833 ns, Total: 177333 ns
[2025-06-24T16:07:19.148+0000][86945][safepoint   ] Safepoint "ICBufferFull", Time since last: 62741375 ns, Reaching safepoint: 2125 ns, Cleanup: 101542 ns, At safepoint: 28791 ns, Total: 132458 ns
[2025-06-24T16:07:19.306+0000][86945][safepoint   ] Safepoint "ICBufferFull", Time since last: 157214375 ns, Reaching safepoint: 1542 ns, Cleanup: 138958 ns, At safepoint: 667 ns, Total: 141167 ns
[2025-06-24T16:07:19.444+0000][86945][safepoint   ] Safepoint "ICBufferFull", Time since last: 138016792 ns, Reaching safepoint: 2083 ns, Cleanup: 103750 ns, At safepoint: 33500 ns, Total: 139333 ns
[2025-06-24T16:07:19.868+0000][86945][safepoint   ] Safepoint "ICBufferFull", Time since last: 423787958 ns, Reaching safepoint: 24375 ns, Cleanup: 100042 ns, At safepoint: 3958 ns, Total: 128375 ns
[2025-06-24T16:07:19.996+0000][86945][safepoint   ] Safepoint "ICBufferFull", Time since last: 128598542 ns, Reaching safepoint: 5292 ns, Cleanup: 121625 ns, At safepoint: 791 ns, Total: 127708 ns
[2025-06-24T16:07:20.272+0000][86945][safepoint   ] Safepoint "ICBufferFull", Time since last: 276005375 ns, Reaching safepoint: 2209 ns, Cleanup: 140708 ns, At safepoint: 5083 ns, Total: 148000 ns
[2025-06-24T16:07:20.402+0000][86945][safepoint   ] Safepoint "ICBufferFull", Time since last: 128961500 ns, Reaching safepoint: 2417 ns, Cleanup: 113625 ns, At safepoint: 625 ns, Total: 116667 ns
[2025-06-24T16:07:20.972+0000][86945][safepoint   ] Safepoint "ICBufferFull", Time since last: 570798167 ns, Reaching safepoint: 2041 ns, Cleanup: 140167 ns, At safepoint: 625 ns, Total: 142833 ns
[2025-06-24T16:07:21.249+0000][86945][gc,start    ] GC(0) Pause Young (Concurrent Start) (Metadata GC Threshold)
[2025-06-24T16:07:21.249+0000][86945][gc,task     ] GC(0) Using 15 workers of 15 for evacuation
[2025-06-24T16:07:21.249+0000][86945][gc,age      ] GC(0) Desired survivor size 109051904 bytes, new threshold 15 (max threshold 15)
[2025-06-24T16:07:21.254+0000][86945][gc,phases   ] GC(0)   Pre Evacuate Collection Set: 0.4ms
[2025-06-24T16:07:21.254+0000][86945][gc,phases   ] GC(0)   Merge Heap Roots: 0.2ms
[2025-06-24T16:07:21.254+0000][86945][gc,phases   ] GC(0)   Evacuate Collection Set: 3.4ms
[2025-06-24T16:07:21.254+0000][86945][gc,phases   ] GC(0)   Post Evacuate Collection Set: 0.8ms
[2025-06-24T16:07:21.254+0000][86945][gc,phases   ] GC(0)   Other: 0.0ms
[2025-06-24T16:07:21.254+0000][86945][gc,age      ] GC(0) Age table with threshold 15 (max threshold 15)
[2025-06-24T16:07:21.254+0000][86945][gc,age      ] GC(0) - age   1:   17977240 bytes,   17977240 total
[2025-06-24T16:07:21.254+0000][86945][gc,heap     ] GC(0) Eden regions: 75->0(97)
[2025-06-24T16:07:21.254+0000][86945][gc,heap     ] GC(0) Survivor regions: 0->2(13)
[2025-06-24T16:07:21.254+0000][86945][gc,heap     ] GC(0) Old regions: 1->1
[2025-06-24T16:07:21.254+0000][86945][gc,heap     ] GC(0) Humongous regions: 0->0
[2025-06-24T16:07:21.254+0000][86945][gc,metaspace] GC(0) Metaspace: 20982K(21504K)->20982K(21504K) NonClass: 18433K(18752K)->18433K(18752K) Class: 2549K(2752K)->2549K(2752K)
[2025-06-24T16:07:21.254+0000][86945][gc          ] GC(0) Pause Young (Concurrent Start) (Metadata GC Threshold) 1193M->18M(31744M) 4.925ms
[2025-06-24T16:07:21.254+0000][86945][gc          ] GC(1) Concurrent Mark Cycle
[2025-06-24T16:07:21.254+0000][86945][gc,cpu      ] GC(0) User=0.04s Sys=0.02s Real=0.01s
[2025-06-24T16:07:21.254+0000][86945][gc,marking  ] GC(1) Concurrent Scan Root Regions
[2025-06-24T16:07:21.254+0000][86945][safepoint   ] Safepoint "CollectForMetadataAllocation", Time since last: 276456667 ns, Reaching safepoint: 4541 ns, Cleanup: 95292 ns, At safepoint: 5004375 ns, Total: 5104208 ns
[2025-06-24T16:07:21.257+0000][86945][gc,marking  ] GC(1) Concurrent Scan Root Regions 3.474ms
[2025-06-24T16:07:21.258+0000][86945][gc,marking  ] GC(1) Concurrent Mark
[2025-06-24T16:07:21.258+0000][86945][gc,marking  ] GC(1) Concurrent Mark From Roots
[2025-06-24T16:07:21.258+0000][86945][gc,task     ] GC(1) Using 4 workers of 4 for marking
[2025-06-24T16:07:21.259+0000][86945][gc,marking  ] GC(1) Concurrent Mark From Roots 1.525ms
[2025-06-24T16:07:21.259+0000][86945][gc,marking  ] GC(1) Concurrent Preclean
[2025-06-24T16:07:21.259+0000][86945][gc,marking  ] GC(1) Concurrent Preclean 0.012ms
[2025-06-24T16:07:21.259+0000][86945][gc,start    ] GC(1) Pause Remark
[2025-06-24T16:07:21.261+0000][86945][gc          ] GC(1) Pause Remark 34M->34M(31744M) 1.303ms
[2025-06-24T16:07:21.261+0000][86945][gc,cpu      ] GC(1) User=0.01s Sys=0.00s Real=0.01s
[2025-06-24T16:07:21.261+0000][86945][safepoint   ] Safepoint "G1PauseRemark", Time since last: 5099292 ns, Reaching safepoint: 37500 ns, Cleanup: 23125 ns, At safepoint: 1345833 ns, Total: 1406458 ns
[2025-06-24T16:07:21.261+0000][86945][gc,marking  ] GC(1) Concurrent Mark 3.036ms
[2025-06-24T16:07:21.261+0000][86945][gc,marking  ] GC(1) Concurrent Rebuild Remembered Sets and Scrub Regions
[2025-06-24T16:07:21.261+0000][86945][gc,marking  ] GC(1) Concurrent Rebuild Remembered Sets and Scrub Regions 0.320ms
[2025-06-24T16:07:21.261+0000][86945][gc,start    ] GC(1) Pause Cleanup
[2025-06-24T16:07:21.261+0000][86945][gc          ] GC(1) Pause Cleanup 34M->34M(31744M) 0.142ms
[2025-06-24T16:07:21.261+0000][86945][gc,cpu      ] GC(1) User=0.00s Sys=0.00s Real=0.00s
[2025-06-24T16:07:21.261+0000][86945][safepoint   ] Safepoint "G1PauseCleanup", Time since last: 351292 ns, Reaching safepoint: 16083 ns, Cleanup: 583 ns, At safepoint: 212792 ns, Total: 229458 ns
[2025-06-24T16:07:21.261+0000][86945][gc,marking  ] GC(1) Concurrent Clear Claimed Marks
[2025-06-24T16:07:21.261+0000][86945][gc,marking  ] GC(1) Concurrent Clear Claimed Marks 0.019ms
[2025-06-24T16:07:21.261+0000][86945][gc,marking  ] GC(1) Concurrent Cleanup for Next Mark
[2025-06-24T16:07:21.281+0000][86945][gc,marking  ] GC(1) Concurrent Cleanup for Next Mark 19.359ms
[2025-06-24T16:07:21.281+0000][86945][gc          ] GC(1) Concurrent Mark Cycle 26.581ms
[2025-06-24T16:07:21.665+0000][86945][safepoint   ] Safepoint "ICBufferFull", Time since last: 403628917 ns, Reaching safepoint: 2458 ns, Cleanup: 105667 ns, At safepoint: 25291 ns, Total: 133416 ns
[2025-06-24T16:07:21.826+0000][86945][safepoint   ] Safepoint "ICBufferFull", Time since last: 160590834 ns, Reaching safepoint: 2083 ns, Cleanup: 142083 ns, At safepoint: 834 ns, Total: 145000 ns
[2025-06-24T16:07:21.952+0000][86945][safepoint   ] Safepoint "ICBufferFull", Time since last: 125985416 ns, Reaching safepoint: 19375 ns, Cleanup: 114750 ns, At safepoint: 3667 ns, Total: 137792 ns
[2025-06-24T16:07:22.185+0000][86945][safepoint   ] Safepoint "ICBufferFull", Time since last: 232948542 ns, Reaching safepoint: 20416 ns, Cleanup: 119875 ns, At safepoint: 25250 ns, Total: 165541 ns
[2025-06-24T16:07:22.693+0000][86945][safepoint   ] Safepoint "ICBufferFull", Time since last: 508278917 ns, Reaching safepoint: 2583 ns, Cleanup: 125167 ns, At safepoint: 1375 ns, Total: 129125 ns
[2025-06-24T16:07:22.826+0000][86945][safepoint   ] Safepoint "ICBufferFull", Time since last: 132583583 ns, Reaching safepoint: 3042 ns, Cleanup: 107833 ns, At safepoint: 26542 ns, Total: 137417 ns
[2025-06-24T16:07:22.846+0000][86945][gc,start    ] GC(2) Pause Young (Prepare Mixed) (Metadata GC Threshold)
[2025-06-24T16:07:22.846+0000][86945][gc,task     ] GC(2) Using 15 workers of 15 for evacuation
[2025-06-24T16:07:22.846+0000][86945][gc,age      ] GC(2) Desired survivor size 109051904 bytes, new threshold 15 (max threshold 15)
[2025-06-24T16:07:22.853+0000][86945][gc,phases   ] GC(2)   Pre Evacuate Collection Set: 0.2ms
[2025-06-24T16:07:22.853+0000][86945][gc,phases   ] GC(2)   Merge Heap Roots: 0.1ms
[2025-06-24T16:07:22.853+0000][86945][gc,phases   ] GC(2)   Evacuate Collection Set: 5.8ms
[2025-06-24T16:07:22.853+0000][86945][gc,phases   ] GC(2)   Post Evacuate Collection Set: 0.9ms
[2025-06-24T16:07:22.853+0000][86945][gc,phases   ] GC(2)   Other: 0.0ms
[2025-06-24T16:07:22.853+0000][86945][gc,age      ] GC(2) Age table with threshold 15 (max threshold 15)
[2025-06-24T16:07:22.853+0000][86945][gc,age      ] GC(2) - age   1:   28982208 bytes,   28982208 total
[2025-06-24T16:07:22.853+0000][86945][gc,age      ] GC(2) - age   2:   17606464 bytes,   46588672 total
[2025-06-24T16:07:22.853+0000][86945][gc,heap     ] GC(2) Eden regions: 52->0(96)
[2025-06-24T16:07:22.853+0000][86945][gc,heap     ] GC(2) Survivor regions: 2->3(13)
[2025-06-24T16:07:22.853+0000][86945][gc,heap     ] GC(2) Old regions: 1->1
[2025-06-24T16:07:22.853+0000][86945][gc,heap     ] GC(2) Humongous regions: 0->0
[2025-06-24T16:07:22.853+0000][86945][gc,metaspace] GC(2) Metaspace: 35080K(36096K)->35080K(36096K) NonClass: 30479K(31104K)->30479K(31104K) Class: 4600K(4992K)->4600K(4992K)
[2025-06-24T16:07:22.853+0000][86945][gc          ] GC(2) Pause Young (Prepare Mixed) (Metadata GC Threshold) 842M->47M(31744M) 7.237ms
[2025-06-24T16:07:22.853+0000][86945][gc,cpu      ] GC(2) User=0.09s Sys=0.01s Real=0.01s
[2025-06-24T16:07:22.853+0000][86945][safepoint   ] Safepoint "CollectForMetadataAllocation", Time since last: 19520333 ns, Reaching safepoint: 1875 ns, Cleanup: 10000 ns, At safepoint: 7302334 ns, Total: 7314209 ns
[2025-06-24T16:07:22.863+0000][86945][gc,start    ] GC(3) Pause Young (Mixed) (Metadata GC Threshold)
[2025-06-24T16:07:22.863+0000][86945][gc,task     ] GC(3) Using 15 workers of 15 for evacuation
[2025-06-24T16:07:22.863+0000][86945][gc,age      ] GC(3) Desired survivor size 109051904 bytes, new threshold 15 (max threshold 15)
[2025-06-24T16:07:22.870+0000][86945][gc,phases   ] GC(3)   Pre Evacuate Collection Set: 0.2ms
[2025-06-24T16:07:22.870+0000][86945][gc,phases   ] GC(3)   Merge Heap Roots: 0.1ms
[2025-06-24T16:07:22.870+0000][86945][gc,phases   ] GC(3)   Evacuate Collection Set: 5.8ms
[2025-06-24T16:07:22.870+0000][86945][gc,phases   ] GC(3)   Post Evacuate Collection Set: 1.1ms
[2025-06-24T16:07:22.870+0000][86945][gc,phases   ] GC(3)   Other: 0.0ms
[2025-06-24T16:07:22.870+0000][86945][gc,age      ] GC(3) Age table with threshold 15 (max threshold 15)
[2025-06-24T16:07:22.870+0000][86945][gc,age      ] GC(3) - age   1:      44240 bytes,      44240 total
[2025-06-24T16:07:22.870+0000][86945][gc,age      ] GC(3) - age   2:   28823184 bytes,   28867424 total
[2025-06-24T16:07:22.870+0000][86945][gc,age      ] GC(3) - age   3:   17606464 bytes,   46473888 total
[2025-06-24T16:07:22.870+0000][86945][gc,heap     ] GC(3) Eden regions: 1->0(96)
[2025-06-24T16:07:22.870+0000][86945][gc,heap     ] GC(3) Survivor regions: 3->3(13)
[2025-06-24T16:07:22.870+0000][86945][gc,heap     ] GC(3) Old regions: 1->1
[2025-06-24T16:07:22.870+0000][86945][gc,heap     ] GC(3) Humongous regions: 0->0
[2025-06-24T16:07:22.870+0000][86945][gc,metaspace] GC(3) Metaspace: 35359K(36416K)->35359K(36416K) NonClass: 30733K(31360K)->30733K(31360K) Class: 4625K(5056K)->4625K(5056K)
[2025-06-24T16:07:22.870+0000][86945][gc          ] GC(3) Pause Young (Mixed) (Metadata GC Threshold) 60M->48M(31744M) 7.469ms
[2025-06-24T16:07:22.870+0000][86945][gc,cpu      ] GC(3) User=0.09s Sys=0.00s Real=0.01s
[2025-06-24T16:07:22.870+0000][86945][safepoint   ] Safepoint "CollectForMetadataAllocation", Time since last: 9929750 ns, Reaching safepoint: 2000 ns, Cleanup: 18916 ns, At safepoint: 7540834 ns, Total: 7561750 ns
[2025-06-24T16:07:22.882+0000][86945][gc,start    ] GC(4) Pause Young (Concurrent Start) (Metadata GC Threshold)
[2025-06-24T16:07:22.882+0000][86945][gc,task     ] GC(4) Using 15 workers of 15 for evacuation
[2025-06-24T16:07:22.882+0000][86945][gc,age      ] GC(4) Desired survivor size 109051904 bytes, new threshold 15 (max threshold 15)
[2025-06-24T16:07:22.889+0000][86945][gc,phases   ] GC(4)   Pre Evacuate Collection Set: 0.2ms
[2025-06-24T16:07:22.889+0000][86945][gc,phases   ] GC(4)   Merge Heap Roots: 0.1ms
[2025-06-24T16:07:22.889+0000][86945][gc,phases   ] GC(4)   Evacuate Collection Set: 5.4ms
[2025-06-24T16:07:22.889+0000][86945][gc,phases   ] GC(4)   Post Evacuate Collection Set: 0.9ms
[2025-06-24T16:07:22.889+0000][86945][gc,phases   ] GC(4)   Other: 0.0ms
[2025-06-24T16:07:22.889+0000][86945][gc,age      ] GC(4) Age table with threshold 15 (max threshold 15)
[2025-06-24T16:07:22.889+0000][86945][gc,age      ] GC(4) - age   1:      25784 bytes,      25784 total
[2025-06-24T16:07:22.889+0000][86945][gc,age      ] GC(4) - age   2:      28184 bytes,      53968 total
[2025-06-24T16:07:22.889+0000][86945][gc,age      ] GC(4) - age   3:   28823008 bytes,   28876976 total
[2025-06-24T16:07:22.889+0000][86945][gc,age      ] GC(4) - age   4:   17606464 bytes,   46483440 total
[2025-06-24T16:07:22.889+0000][86945][gc,heap     ] GC(4) Eden regions: 1->0(96)
[2025-06-24T16:07:22.889+0000][86945][gc,heap     ] GC(4) Survivor regions: 3->3(13)
[2025-06-24T16:07:22.889+0000][86945][gc,heap     ] GC(4) Old regions: 1->1
[2025-06-24T16:07:22.889+0000][86945][gc,heap     ] GC(4) Humongous regions: 0->0
[2025-06-24T16:07:22.889+0000][86945][gc,metaspace] GC(4) Metaspace: 35705K(36736K)->35705K(36736K) NonClass: 31053K(31680K)->31053K(31680K) Class: 4651K(5056K)->4651K(5056K)
[2025-06-24T16:07:22.889+0000][86945][gc          ] GC(4) Pause Young (Concurrent Start) (Metadata GC Threshold) 59M->48M(31744M) 6.808ms
[2025-06-24T16:07:22.889+0000][86945][gc          ] GC(5) Concurrent Mark Cycle
[2025-06-24T16:07:22.889+0000][86945][gc,cpu      ] GC(4) User=0.08s Sys=0.00s Real=0.00s
[2025-06-24T16:07:22.889+0000][86945][gc,marking  ] GC(5) Concurrent Scan Root Regions
[2025-06-24T16:07:22.889+0000][86945][safepoint   ] Safepoint "CollectForMetadataAllocation", Time since last: 11518625 ns, Reaching safepoint: 2125 ns, Cleanup: 2875 ns, At safepoint: 6893416 ns, Total: 6898416 ns
[2025-06-24T16:07:22.895+0000][86945][gc,marking  ] GC(5) Concurrent Scan Root Regions 6.664ms
[2025-06-24T16:07:22.895+0000][86945][gc,marking  ] GC(5) Concurrent Mark
[2025-06-24T16:07:22.896+0000][86945][gc,marking  ] GC(5) Concurrent Mark From Roots
[2025-06-24T16:07:22.896+0000][86945][gc,task     ] GC(5) Using 4 workers of 4 for marking
[2025-06-24T16:07:22.897+0000][86945][gc,marking  ] GC(5) Concurrent Mark From Roots 1.151ms
[2025-06-24T16:07:22.897+0000][86945][gc,marking  ] GC(5) Concurrent Preclean
[2025-06-24T16:07:22.897+0000][86945][gc,marking  ] GC(5) Concurrent Preclean 0.006ms
[2025-06-24T16:07:22.897+0000][86945][gc,start    ] GC(5) Pause Remark
[2025-06-24T16:07:22.900+0000][86945][gc          ] GC(5) Pause Remark 59M->59M(31744M) 2.516ms
[2025-06-24T16:07:22.900+0000][86945][gc,cpu      ] GC(5) User=0.01s Sys=0.01s Real=0.01s
[2025-06-24T16:07:22.900+0000][86945][safepoint   ] Safepoint "G1PauseRemark", Time since last: 8034375 ns, Reaching safepoint: 209500 ns, Cleanup: 7917 ns, At safepoint: 2611292 ns, Total: 2828709 ns
[2025-06-24T16:07:22.900+0000][86945][gc,marking  ] GC(5) Concurrent Mark 4.157ms
[2025-06-24T16:07:22.900+0000][86945][gc,marking  ] GC(5) Concurrent Rebuild Remembered Sets and Scrub Regions
[2025-06-24T16:07:22.900+0000][86945][gc,marking  ] GC(5) Concurrent Rebuild Remembered Sets and Scrub Regions 0.345ms
[2025-06-24T16:07:22.900+0000][86945][gc,start    ] GC(5) Pause Cleanup
[2025-06-24T16:07:22.900+0000][86945][gc          ] GC(5) Pause Cleanup 59M->59M(31744M) 0.161ms
[2025-06-24T16:07:22.900+0000][86945][gc,cpu      ] GC(5) User=0.01s Sys=0.00s Real=0.00s
[2025-06-24T16:07:22.900+0000][86945][safepoint   ] Safepoint "G1PauseCleanup", Time since last: 427625 ns, Reaching safepoint: 2541 ns, Cleanup: 9334 ns, At safepoint: 233666 ns, Total: 245541 ns
[2025-06-24T16:07:22.900+0000][86945][gc,marking  ] GC(5) Concurrent Clear Claimed Marks
[2025-06-24T16:07:22.900+0000][86945][gc,marking  ] GC(5) Concurrent Clear Claimed Marks 0.060ms
[2025-06-24T16:07:22.900+0000][86945][gc,marking  ] GC(5) Concurrent Cleanup for Next Mark
[2025-06-24T16:07:22.906+0000][86945][gc,marking  ] GC(5) Concurrent Cleanup for Next Mark 5.890ms
[2025-06-24T16:07:22.906+0000][86945][gc          ] GC(5) Concurrent Mark Cycle 17.590ms
[2025-06-24T16:07:22.973+0000][86945][gc,heap,exit] Heap
[2025-06-24T16:07:22.973+0000][86945][gc,heap,exit]  garbage-first heap   total 32505856K, used 136040K [0x0000000301000000, 0x0000000ac1000000)
[2025-06-24T16:07:22.973+0000][86945][gc,heap,exit]   region size 16384K, 9 young (147456K), 3 survivors (49152K)
[2025-06-24T16:07:22.973+0000][86945][gc,heap,exit]  Metaspace       used 37089K, committed 38208K, reserved 1114112K
[2025-06-24T16:07:22.973+0000][86945][gc,heap,exit]   class space    used 4796K, committed 5248K, reserved 1048576K
