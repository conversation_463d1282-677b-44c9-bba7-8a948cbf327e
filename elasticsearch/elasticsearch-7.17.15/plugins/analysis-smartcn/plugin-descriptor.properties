# Elasticsearch plugin descriptor file
# This file must exist as 'plugin-descriptor.properties' inside a plugin.
#
### example plugin for "foo"
#
# foo.zip <-- zip file for the plugin, with this structure:
# |____   <arbitrary name1>.jar <-- classes, resources, dependencies
# |____   <arbitrary nameN>.jar <-- any number of jars
# |____   plugin-descriptor.properties <-- example contents below:
#
# classname=foo.bar.BazPlugin
# description=My cool plugin
# version=6.0
# elasticsearch.version=6.0
# java.version=1.8
#
### mandatory elements for all plugins:
#
# 'type': the type of this plugin. 'isolated' indicated a typical sandboxed plugin,
# whereas 'bootstrap' indicates a plugin whose jars are added to the JVM's boot
# classpath.
type=isolated
#
# 'description': simple summary of the plugin
description=Smart Chinese Analysis plugin integrates Lucene Smart Chinese analysis module into elasticsearch.
#
# 'version': plugin's version
version=7.17.15
#
# 'name': the plugin name
name=analysis-smartcn

#
# 'classname': the name of the class to load, fully-qualified. Only applies to
# "isolated" plugins
classname=org.elasticsearch.plugin.analysis.smartcn.AnalysisSmartChinesePlugin

#
# 'java.version': version of java the code is built against
# use the system property java.specification.version
# version string must be a sequence of nonnegative decimal integers
# separated by "."'s and may have leading zeros
java.version=1.8
#
# 'elasticsearch.version': version of elasticsearch compiled against
elasticsearch.version=7.17.15
### optional elements for plugins:
#
#  'extended.plugins': other plugins this plugin extends through SPI
extended.plugins=
#
# 'has.native.controller': whether or not the plugin has a native controller
has.native.controller=false


