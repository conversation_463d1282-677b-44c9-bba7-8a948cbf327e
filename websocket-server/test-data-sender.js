const WebSocket = require('ws');
const Redis = require('ioredis');

// Redis 連接
const redis = new Redis({
  host: 'localhost',
  port: 6379,
  db: 2
});

// 生成隨機測試數據
function generateTestData(topic) {
  const baseValues = {
    'seo-analysis': 85,
    'keyword-ranking': 50,
    'website-traffic': 1000,
    'competitor-analysis': 75
  };

  const base = baseValues[topic] || 50;
  const variation = base * 0.2; // 20% 變化範圍
  const value = base + (Math.random() - 0.5) * variation;

  return {
    type: 'data',
    topic: topic,
    timestamp: new Date().toISOString(),
    data: {
      value: Math.max(0, value),
      category: topic,
      label: `${topic} 數據`
    }
  };
}

// 發送測試數據
async function sendTestData() {
  const topics = [
    'seo-analysis',
    'keyword-ranking', 
    'website-traffic',
    'competitor-analysis'
  ];

  setInterval(async () => {
    for (const topic of topics) {
      const data = generateTestData(topic);
      
      try {
        // 發布到 Redis
        await redis.publish('realtime-data', JSON.stringify(data));
        console.log(`📤 發送數據到 ${topic}:`, data.data.value.toFixed(2));
      } catch (error) {
        console.error('發送數據失敗:', error);
      }
    }
  }, 2000); // 每2秒發送一次
}

// 測試 WebSocket 連接
function testWebSocketConnection() {
  const ws = new WebSocket('ws://localhost:8002/ws');
  
  ws.on('open', () => {
    console.log('✅ WebSocket 連接成功');
    
    // 訂閱所有測試主題
    const topics = ['seo-analysis', 'keyword-ranking', 'website-traffic', 'competitor-analysis'];
    topics.forEach(topic => {
      ws.send(JSON.stringify({
        type: 'subscribe',
        topic: topic
      }));
      console.log(`📡 訂閱主題: ${topic}`);
    });
  });

  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data);
      console.log('📨 收到消息:', message);
    } catch (error) {
      console.error('解析消息失敗:', error);
    }
  });

  ws.on('error', (error) => {
    console.error('❌ WebSocket 錯誤:', error);
  });

  ws.on('close', () => {
    console.log('🔌 WebSocket 連接關閉');
  });
}

// 主函數
async function main() {
  console.log('🚀 啟動測試數據發送器');
  
  try {
    // 測試 Redis 連接
    await redis.ping();
    console.log('✅ Redis 連接成功');
    
    // 測試 WebSocket 連接
    testWebSocketConnection();
    
    // 開始發送測試數據
    setTimeout(() => {
      console.log('📡 開始發送測試數據...');
      sendTestData();
    }, 2000);
    
  } catch (error) {
    console.error('❌ 初始化失敗:', error);
    process.exit(1);
  }
}

// 優雅退出
process.on('SIGINT', async () => {
  console.log('\n🛑 正在停止測試數據發送器...');
  await redis.disconnect();
  process.exit(0);
});

main(); 