const WebSocket = require('ws');
const http = require('http');

// 配置
const PORT = 8003;

// 創建 HTTP 服務器
const server = http.createServer((req, res) => {
  if (req.url === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ 
      status: 'ok', 
      timestamp: new Date().toISOString(),
      connections: wss.clients.size
    }));
  } else {
    res.writeHead(404);
    res.end('Not Found');
  }
});

// 創建 WebSocket 服務器
const wss = new WebSocket.Server({ 
  server,
  path: '/ws'
});

let clientCounter = 0;

// WebSocket 連接處理
wss.on('connection', (ws, req) => {
  const clientId = `client_${++clientCounter}_${Date.now()}`;
  
  console.log(`客戶端 ${clientId} 已連接，當前連接數: ${wss.clients.size}`);

  // 發送歡迎消息
  ws.send(JSON.stringify({
    type: 'connection',
    clientId,
    message: '連接成功',
    timestamp: new Date().toISOString()
  }));

  // 處理客戶端消息
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message.toString());
      console.log(`📨 收到消息:`, data);
      
      if (data.type === 'subscribe') {
        ws.send(JSON.stringify({
          type: 'subscribed',
          topic: data.topic,
          message: `已訂閱主題: ${data.topic}`
        }));
      }
    } catch (error) {
      console.error('處理消息錯誤:', error);
      ws.send(JSON.stringify({
        type: 'error',
        message: '無效的消息格式'
      }));
    }
  });

  // 處理連接關閉
  ws.on('close', () => {
    console.log(`客戶端 ${clientId} 已斷開，當前連接數: ${wss.clients.size}`);
  });

  // 錯誤處理
  ws.on('error', (error) => {
    console.error(`客戶端 ${clientId} 錯誤:`, error);
  });
});

// 模擬數據發送
function sendTestData() {
  const topics = ['seo-analysis', 'keyword-ranking', 'website-traffic', 'competitor-analysis'];
  
  topics.forEach(topic => {
    const testData = {
      type: 'data',
      topic,
      data: {
        value: Math.random() * 100,
        timestamp: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    };

    // 發送到所有連接的客戶端
    wss.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify(testData));
      }
    });

    console.log(`📤 發送數據到 ${topic}: ${testData.data.value.toFixed(2)}`);
  });
}

// 啟動服務器
server.listen(PORT, () => {
  console.log(`WebSocket 服務器啟動在端口 ${PORT}`);
  console.log(`WebSocket 端點: ws://localhost:${PORT}/ws`);
  console.log(`健康檢查: http://localhost:${PORT}/health`);
  
  // 2秒後開始發送測試數據
  setTimeout(() => {
    console.log('📡 開始發送測試數據...');
    setInterval(sendTestData, 2000);
  }, 2000);
});

// 優雅關閉
process.on('SIGTERM', () => {
  console.log('收到 SIGTERM，開始關閉...');
  server.close(() => {
    console.log('服務器已關閉');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('收到 SIGINT，開始關閉...');
  server.close(() => {
    console.log('服務器已關閉');
    process.exit(0);
  });
}); 