const WebSocket = require('ws');
const http = require('http');
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const redis = require('redis');

// 環境配置
const PORT = process.env.PORT || 8002;
const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379/2';

// 創建 Express 應用
const app = express();
app.use(helmet());
app.use(compression());
app.use(cors());
app.use(express.json());

// 健康檢查端點
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    connections: clients.size
  });
});

// 創建 HTTP 服務器
const server = http.createServer(app);

// 創建 WebSocket 服務器
const wss = new WebSocket.Server({ 
  server,
  path: '/ws',
  clientTracking: true
});

// Redis 客戶端
let redisClient;

// 連接管理
const clients = new Map();
const subscriptions = new Map(); // 主題訂閱管理

// 初始化 Redis 連接
async function initRedis() {
  try {
    redisClient = redis.createClient({ url: REDIS_URL });
    
    redisClient.on('error', (err) => {
      console.log('Redis 連接錯誤 (繼續運行):', err.message);
    });

    redisClient.on('connect', () => {
      console.log('Redis 連接成功');
    });

    await redisClient.connect();
  } catch (error) {
    console.log('Redis 初始化失敗 (繼續運行):', error.message);
  }
}

// WebSocket 連接處理
wss.on('connection', (ws, req) => {
  const clientId = generateClientId();
  clients.set(clientId, {
    ws,
    subscriptions: new Set(),
    lastPing: Date.now()
  });

  console.log(`客戶端 ${clientId} 已連接，當前連接數: ${clients.size}`);

  // 發送歡迎消息
  ws.send(JSON.stringify({
    type: 'connection',
    clientId,
    message: '連接成功',
    timestamp: new Date().toISOString()
  }));

  // 處理客戶端消息
  ws.on('message', async (message) => {
    try {
      const data = JSON.parse(message.toString());
      await handleClientMessage(clientId, data);
    } catch (error) {
      console.error('處理客戶端消息錯誤:', error);
      ws.send(JSON.stringify({
        type: 'error',
        message: '無效的消息格式'
      }));
    }
  });

  // 處理連接關閉
  ws.on('close', () => {
    const client = clients.get(clientId);
    if (client) {
      // 清理訂閱
      client.subscriptions.forEach(topic => {
        if (subscriptions.has(topic)) {
          subscriptions.get(topic).delete(clientId);
          if (subscriptions.get(topic).size === 0) {
            subscriptions.delete(topic);
          }
        }
      });
    }
    
    clients.delete(clientId);
    console.log(`客戶端 ${clientId} 已斷開，當前連接數: ${clients.size}`);
  });

  // 心跳檢測
  ws.on('pong', () => {
    const client = clients.get(clientId);
    if (client) {
      client.lastPing = Date.now();
    }
  });
});

// 處理客戶端消息
async function handleClientMessage(clientId, data) {
  const client = clients.get(clientId);
  if (!client) return;

  switch (data.type) {
    case 'subscribe':
      // 訂閱主題
      if (data.topic) {
        client.subscriptions.add(data.topic);
        
        if (!subscriptions.has(data.topic)) {
          subscriptions.set(data.topic, new Set());
        }
        subscriptions.get(data.topic).add(clientId);

        client.ws.send(JSON.stringify({
          type: 'subscribed',
          topic: data.topic,
          message: `已訂閱主題: ${data.topic}`
        }));

        console.log(`客戶端 ${clientId} 訂閱主題: ${data.topic}`);
      }
      break;

    case 'unsubscribe':
      // 取消訂閱主題
      if (data.topic && client.subscriptions.has(data.topic)) {
        client.subscriptions.delete(data.topic);
        
        if (subscriptions.has(data.topic)) {
          subscriptions.get(data.topic).delete(clientId);
          if (subscriptions.get(data.topic).size === 0) {
            subscriptions.delete(data.topic);
          }
        }

        client.ws.send(JSON.stringify({
          type: 'unsubscribed',
          topic: data.topic,
          message: `已取消訂閱主題: ${data.topic}`
        }));

        console.log(`客戶端 ${clientId} 取消訂閱主題: ${data.topic}`);
      }
      break;

    case 'ping':
      // 心跳回應
      client.ws.send(JSON.stringify({
        type: 'pong',
        timestamp: new Date().toISOString()
      }));
      break;

    default:
      client.ws.send(JSON.stringify({
        type: 'error',
        message: `未知的消息類型: ${data.type}`
      }));
  }
}

// 廣播消息到訂閱者
function broadcastToSubscribers(topic, data) {
  if (!subscriptions.has(topic)) return;

  const message = JSON.stringify({
    type: 'data',
    topic,
    data,
    timestamp: new Date().toISOString()
  });

  subscriptions.get(topic).forEach(clientId => {
    const client = clients.get(clientId);
    if (client && client.ws.readyState === WebSocket.OPEN) {
      try {
        client.ws.send(message);
      } catch (error) {
        console.error(`發送消息到客戶端 ${clientId} 失敗:`, error);
      }
    }
  });

  console.log(`廣播消息到主題 ${topic}，接收者: ${subscriptions.get(topic).size} 個客戶端`);
}

// 生成客戶端 ID
function generateClientId() {
  return 'client_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
}

// 心跳檢測
function heartbeat() {
  const now = Date.now();
  const timeout = 60000; // 60秒超時

  clients.forEach((client, clientId) => {
    if (now - client.lastPing > timeout) {
      console.log(`客戶端 ${clientId} 心跳超時，斷開連接`);
      client.ws.terminate();
      clients.delete(clientId);
    } else if (client.ws.readyState === WebSocket.OPEN) {
      client.ws.ping();
    }
  });
}

// 模擬數據發送器
function startDataSimulator() {
  console.log('📡 開始發送測試數據...');
  
  const topics = [
    'seo-analysis',
    'keyword-ranking', 
    'website-traffic',
    'competitor-analysis'
  ];

  setInterval(() => {
    topics.forEach(topic => {
      const mockData = generateMockData(topic);
      broadcastToSubscribers(topic, mockData);
      console.log(`📤 發送數據到 ${topic}: ${JSON.stringify(mockData).substring(0, 50)}...`);
    });
  }, 2000); // 每2秒發送一次
}

// 生成模擬數據
function generateMockData(topic) {
  const baseData = {
    timestamp: new Date().toISOString(),
    topic
  };

  switch (topic) {
    case 'seo-analysis':
      return {
        ...baseData,
        score: 75 + Math.random() * 20,
        improvements: Math.floor(Math.random() * 10),
        issues: Math.floor(Math.random() * 5)
      };
    
    case 'keyword-ranking':
      return {
        ...baseData,
        position: Math.floor(Math.random() * 100) + 1,
        change: Math.floor((Math.random() - 0.5) * 10),
        keyword: `測試關鍵詞 ${Math.floor(Math.random() * 100)}`
      };
    
    case 'website-traffic':
      return {
        ...baseData,
        visitors: Math.floor(900 + Math.random() * 200),
        pageviews: Math.floor(1500 + Math.random() * 500),
        bounceRate: Math.random() * 0.5 + 0.2
      };
    
    case 'competitor-analysis':
      return {
        ...baseData,
        competitorScore: 65 + Math.random() * 25,
        ourScore: 70 + Math.random() * 20,
        gap: Math.floor((Math.random() - 0.5) * 20)
      };
    
    default:
      return {
        ...baseData,
        value: Math.random() * 100
      };
  }
}

// 啟動服務器
async function startServer() {
  try {
    await initRedis();
    
    server.listen(PORT, () => {
      console.log(`WebSocket 服務器啟動在端口 ${PORT}`);
      console.log(`WebSocket 端點: ws://localhost:${PORT}/ws`);
      console.log(`健康檢查: http://localhost:${PORT}/health`);
      
      // 啟動心跳檢測
      setInterval(heartbeat, 30000); // 每30秒檢查一次
      
      // 啟動數據模擬器
      setTimeout(startDataSimulator, 2000); // 2秒後開始發送數據
    });

    // 優雅關閉
    process.on('SIGTERM', () => {
      console.log('收到 SIGTERM，開始優雅關閉...');
      server.close(() => {
        if (redisClient) {
          redisClient.quit();
        }
        console.log('服務器已關閉');
        process.exit(0);
      });
    });

    process.on('SIGINT', () => {
      console.log('收到 SIGINT，開始優雅關閉...');
      server.close(() => {
        if (redisClient) {
          redisClient.quit();
        }
        console.log('服務器已關閉');
        process.exit(0);
      });
    });

  } catch (error) {
    console.error('啟動服務器失敗:', error);
    process.exit(1);
  }
}

startServer(); 