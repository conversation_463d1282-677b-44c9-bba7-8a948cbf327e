import { RedisCommandArgument, RedisCommandArguments } from '.';
import { GeoCoordinates, GeoUnits, GeoRadiusStoreOptions } from './generic-transformers';
export { FIRST_KEY_INDEX, IS_READ_ONLY } from './GEORADIUS';
export declare function transformArguments(key: RedisCommandArgument, coordinates: GeoCoordinates, radius: number, unit: GeoUnits, destination: RedisCommandArgument, options?: GeoRadiusStoreOptions): RedisCommandArguments;
export declare function transformReply(): number;
