import { RedisCommandArgument, RedisCommandArguments } from '.';
export declare const FIRST_KEY_INDEX = 1;
export declare const IS_READ_ONLY = true;
export interface LPosOptions {
    RANK?: number;
    MAXLEN?: number;
}
export declare function transformArguments(key: RedisCommandArgument, element: RedisCommandArgument, options?: LPosOptions): RedisCommandArguments;
export declare function transformReply(): number | null;
