/** @type {import('next').NextConfig} */
const nextConfig = {
  // 基本配置
  reactStrictMode: true,
  swcMinify: true,
  output: 'standalone',

  // 實驗性功能
  experimental: {
    optimizePackageImports: ['lucide-react'],
  },

  // 開發指示器配置
  devIndicators: {
    buildActivity: true,
    buildActivityPosition: 'bottom-right',
  },

  // Webpack 配置
  webpack: (config, { dev, isServer }) => {
    if (dev && !isServer) {
      // 優化 HMR 和 WebSocket 連接
      config.watchOptions = {
        poll: false, // 禁用輪詢以減少 CPU 使用
        aggregateTimeout: 300, // 增加聚合時間以減少重建頻率
        ignored: [
          '**/node_modules/**',
          '**/.git/**',
          '**/.next/**',
          '**/test-*.html',
          '**/test-*.js',
          '**/.DS_Store',
          '**/logs/**',
          '**/data/**'
        ],
      };

      // 優化 HMR 配置
      if (config.mode === 'development') {
        config.optimization = {
          ...config.optimization,
          splitChunks: {
            ...config.optimization.splitChunks,
            cacheGroups: {
              default: false,
              vendors: false,
              framework: {
                chunks: 'all',
                name: 'framework',
                test: /(?<!node_modules.*)[\\/]node_modules[\\/](react|react-dom|scheduler|prop-types|use-subscription)[\\/]/,
                priority: 40,
                enforce: true,
              },
            },
          },
        };
      }
    }
    return config;
  },

  // 自定義服務器配置 - 更全面的 CORS 處理
  async headers() {
    const corsHeaders = [
      {
        key: 'Access-Control-Allow-Origin',
        value: '*',
      },
      {
        key: 'Access-Control-Allow-Methods',
        value: 'GET, POST, PUT, DELETE, OPTIONS, HEAD',
      },
      {
        key: 'Access-Control-Allow-Headers',
        value: 'Content-Type, Authorization, X-Requested-With, Accept, Origin, Cache-Control, X-File-Name',
      },
      {
        key: 'Access-Control-Allow-Credentials',
        value: 'true',
      },
      {
        key: 'Cross-Origin-Resource-Policy',
        value: 'cross-origin',
      },
      {
        key: 'Cross-Origin-Opener-Policy',
        value: 'same-origin-allow-popups',
      },
      {
        key: 'Cross-Origin-Embedder-Policy',
        value: 'unsafe-none',
      },
    ];

    return [
      {
        // 匹配所有 _next 相關路徑（最廣泛的配置）
        source: '/_next/:path*',
        headers: corsHeaders,
      },
      {
        // 專門針對 webpack-hmr WebSocket
        source: '/_next/webpack-hmr',
        headers: [
          ...corsHeaders,
          {
            key: 'Connection',
            value: 'upgrade',
          },
          {
            key: 'Upgrade',
            value: 'websocket',
          },
        ],
      },
      {
        // 匹配所有靜態資源
        source: '/_next/static/:path*',
        headers: [
          ...corsHeaders,
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      {
        // 專門針對 webpack 熱更新 JSON 文件
        source: '/_next/static/webpack/:path*.json',
        headers: [
          ...corsHeaders,
          {
            key: 'Content-Type',
            value: 'application/json',
          },
        ],
      },
      {
        // 匹配所有 webpack 相關文件
        source: '/_next/static/webpack/:path*',
        headers: corsHeaders,
      },
      {
        // 專門針對 chunks 目錄
        source: '/_next/static/chunks/:path*',
        headers: [
          ...corsHeaders,
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      {
        // 通用 API 路由
        source: '/api/:path*',
        headers: corsHeaders,
      },
      {
        // 根路徑
        source: '/:path*',
        headers: [
          {
            key: 'Cross-Origin-Opener-Policy',
            value: 'same-origin-allow-popups',
          },
          {
            key: 'Cross-Origin-Embedder-Policy',
            value: 'unsafe-none',
          },
        ],
      },
    ];
  },

  // 重寫配置
  async rewrites() {
    return [
      {
        source: '/_next/webpack-hmr',
        destination: '/_next/webpack-hmr',
      },
    ];
  },

  // 圖片配置
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'www.seoking.ai',
      },
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // 編譯器配置
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // 開發服務器配置
  ...(process.env.NODE_ENV === 'development' && {
    webpackDevMiddleware: config => {
      config.watchOptions = {
        poll: false,
        aggregateTimeout: 300,
        ignored: [
          '**/node_modules/**',
          '**/.git/**',
          '**/.next/**',
          '**/logs/**',
          '**/data/**'
        ],
      };
      return config;
    },
    // 添加開發服務器專用配置
    devServer: {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS, HEAD',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept, Origin, Cache-Control',
        'Cross-Origin-Resource-Policy': 'cross-origin',
      },
    },
  }),
};

module.exports = nextConfig;
