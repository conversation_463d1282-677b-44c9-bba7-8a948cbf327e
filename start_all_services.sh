#!/bin/bash

# AI SEO 優化王系統 - 完整服務啟動腳本
# 啟動 Elasticsearch、後端 FastAPI 和前端 Next.js

echo "🚀 啟動 AI SEO 優化王系統..."

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 項目根目錄
PROJECT_ROOT=$(pwd)
ES_DIR="$PROJECT_ROOT/elasticsearch/elasticsearch-7.17.15"
BACKEND_DIR="$PROJECT_ROOT/backend-fastapi"

# 檢查依賴
echo -e "${BLUE}📋 檢查系統依賴...${NC}"

# 檢查 Java
if ! command -v java &> /dev/null; then
    echo -e "${RED}❌ Java 未安裝，請先安裝 OpenJDK 17${NC}"
    exit 1
fi

# 檢查 Python
if [ ! -d "$BACKEND_DIR/.venv" ]; then
    echo -e "${RED}❌ Python 虛擬環境未找到，請先設置後端環境${NC}"
    exit 1
fi

# 檢查 Node.js
if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ Node.js/npm 未安裝${NC}"
    exit 1
fi

# 函數：終止現有進程
cleanup_processes() {
    echo -e "${YELLOW}🧹 清理現有進程...${NC}"
    pkill -f elasticsearch 2>/dev/null || true
    pkill -f uvicorn 2>/dev/null || true
    pkill -f next 2>/dev/null || true
    sleep 3
}

# 函數：檢查端口是否被佔用
check_port() {
    local port=$1
    if lsof -i :$port &> /dev/null; then
        echo -e "${YELLOW}⚠️ 端口 $port 被佔用，正在清理...${NC}"
        lsof -ti :$port | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
}

# 清理現有進程
cleanup_processes

# 檢查並清理端口
check_port 9200
check_port 8000
check_port 3000

# 1. 啟動 Elasticsearch
echo -e "${BLUE}🔍 啟動 Elasticsearch...${NC}"
if [ ! -d "$ES_DIR" ]; then
    echo -e "${RED}❌ Elasticsearch 未找到，請先安裝${NC}"
    exit 1
fi

cd "$ES_DIR"
export PATH="/opt/homebrew/opt/openjdk@17/bin:$PATH"
nohup ./bin/elasticsearch > "$PROJECT_ROOT/elasticsearch.log" 2>&1 &
ES_PID=$!

# 等待 Elasticsearch 啟動
echo -e "${YELLOW}⏳ 等待 Elasticsearch 啟動...${NC}"
for i in {1..30}; do
    if curl -s localhost:9200/_cluster/health &> /dev/null; then
        echo -e "${GREEN}✅ Elasticsearch 啟動成功！${NC}"
        break
    fi
    if [ $i -eq 30 ]; then
        echo -e "${RED}❌ Elasticsearch 啟動失敗${NC}"
        exit 1
    fi
    sleep 2
done

# 檢查 Elasticsearch 健康狀態
ES_STATUS=$(curl -s localhost:9200/_cluster/health | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
echo -e "${GREEN}📊 Elasticsearch 狀態: $ES_STATUS${NC}"

# 2. 啟動後端 FastAPI
echo -e "${BLUE}🔧 啟動後端 FastAPI...${NC}"
cd "$BACKEND_DIR"
source .venv/bin/activate
nohup python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload > "$PROJECT_ROOT/backend.log" 2>&1 &
BACKEND_PID=$!

# 等待後端啟動
echo -e "${YELLOW}⏳ 等待後端 API 啟動...${NC}"
for i in {1..20}; do
    if curl -s localhost:8000/health &> /dev/null; then
        echo -e "${GREEN}✅ 後端 API 啟動成功！${NC}"
        break
    fi
    if [ $i -eq 20 ]; then
        echo -e "${RED}❌ 後端 API 啟動失敗${NC}"
        exit 1
    fi
    sleep 3
done

# 3. 啟動前端 Next.js
echo -e "${BLUE}🌐 啟動前端 Next.js...${NC}"
cd "$PROJECT_ROOT"
nohup npm run dev > "$PROJECT_ROOT/frontend.log" 2>&1 &
FRONTEND_PID=$!

# 等待前端啟動
echo -e "${YELLOW}⏳ 等待前端應用啟動...${NC}"
for i in {1..30}; do
    if curl -s localhost:3000 &> /dev/null; then
        echo -e "${GREEN}✅ 前端應用啟動成功！${NC}"
        break
    fi
    if [ $i -eq 30 ]; then
        echo -e "${RED}❌ 前端應用啟動失敗${NC}"
        exit 1
    fi
    sleep 2
done

# 4. 系統狀態檢查
echo -e "${BLUE}🔍 系統狀態檢查...${NC}"

# 檢查 Elasticsearch
ES_HEALTH=$(curl -s localhost:9200/_cluster/health | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
echo -e "${GREEN}📊 Elasticsearch: $ES_HEALTH${NC}"

# 檢查後端
BACKEND_STATUS=$(curl -s localhost:8000/health | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
echo -e "${GREEN}🔧 後端 API: $BACKEND_STATUS${NC}"

# 檢查前端
FRONTEND_STATUS=$(curl -s -I localhost:3000 | head -1 | grep -o "200 OK" || echo "運行中")
echo -e "${GREEN}🌐 前端應用: $FRONTEND_STATUS${NC}"

# 測試搜索功能
echo -e "${YELLOW}🔍 測試搜索功能...${NC}"
SEARCH_TEST=$(curl -s -X POST "localhost:8000/api/v1/search/content" -H "Content-Type: application/json" -d '{"query": "test", "size": 1}' | python3 -c "import sys, json; data = json.load(sys.stdin); print('搜索正常' if 'total' in data else '搜索異常')" 2>/dev/null || echo "搜索異常")
echo -e "${GREEN}🔍 搜索功能: $SEARCH_TEST${NC}"

# 顯示訪問資訊
echo -e "\n${GREEN}🎉 系統啟動完成！${NC}"
echo -e "${BLUE}📱 訪問地址：${NC}"
echo -e "   前端應用: ${YELLOW}http://localhost:3000${NC}"
echo -e "   後端 API: ${YELLOW}http://localhost:8000${NC}"
echo -e "   API 文檔: ${YELLOW}http://localhost:8000/api/v1/docs${NC}"
echo -e "   Elasticsearch: ${YELLOW}http://localhost:9200${NC}"

echo -e "\n${BLUE}📋 進程 ID：${NC}"
echo -e "   Elasticsearch PID: $ES_PID"
echo -e "   後端 API PID: $BACKEND_PID"
echo -e "   前端應用 PID: $FRONTEND_PID"

echo -e "\n${BLUE}📄 日誌文件：${NC}"
echo -e "   Elasticsearch: $PROJECT_ROOT/elasticsearch.log"
echo -e "   後端 API: $PROJECT_ROOT/backend.log"
echo -e "   前端應用: $PROJECT_ROOT/frontend.log"

echo -e "\n${YELLOW}💡 提示：使用 ./stop_all_services.sh 停止所有服務${NC}"

# 保存 PID 到文件
echo "$ES_PID" > "$PROJECT_ROOT/.elasticsearch.pid"
echo "$BACKEND_PID" > "$PROJECT_ROOT/.backend.pid"
echo "$FRONTEND_PID" > "$PROJECT_ROOT/.frontend.pid"

echo -e "\n${GREEN}✨ AI SEO 優化王系統已就緒！${NC}" 