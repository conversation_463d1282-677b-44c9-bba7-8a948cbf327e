{"timestamp":"2025-06-23 03:41:04.714","level":"error","message":"Redis 服務斷開連接失敗: The client is closed","service":"ai-seo-express","version":"1.0.0","stack":"Error: The client is closed\n    at RedisSocket.disconnect (/Users/<USER>/projects/AISEOking/backend-express/node_modules/@redis/client/dist/lib/client/socket.js:63:19)\n    at Commander.disconnect (/Users/<USER>/projects/AISEOking/backend-express/node_modules/@redis/client/dist/lib/client/index.js:353:64)\n    at RedisService.disconnect (/Users/<USER>/projects/AISEOking/backend-express/src/services/redis.ts:70:25)\n    at ExpressServer.gracefulShutdown (/Users/<USER>/projects/AISEOking/backend-express/src/server.ts:237:33)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"}
{"timestamp":"2025-06-23 08:17:30.740","level":"error","message":"Redis 服務斷開連接失敗: The client is closed","service":"ai-seo-express","version":"1.0.0","stack":"Error: The client is closed\n    at RedisSocket.disconnect (/Users/<USER>/projects/AISEOking/backend-express/node_modules/@redis/client/dist/lib/client/socket.js:63:19)\n    at Commander.disconnect (/Users/<USER>/projects/AISEOking/backend-express/node_modules/@redis/client/dist/lib/client/index.js:353:64)\n    at RedisService.disconnect (/Users/<USER>/projects/AISEOking/backend-express/src/services/redis.ts:70:25)\n    at ExpressServer.gracefulShutdown (/Users/<USER>/projects/AISEOking/backend-express/src/server.ts:237:33)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"}
{"timestamp":"2025-06-23 13:03:20.265","level":"error","message":"Redis 服務斷開連接失敗: The client is closed","service":"ai-seo-express","version":"1.0.0","stack":"Error: The client is closed\n    at RedisSocket.disconnect (/Users/<USER>/projects/AISEOking/backend-express/node_modules/@redis/client/dist/lib/client/socket.js:63:19)\n    at Commander.disconnect (/Users/<USER>/projects/AISEOking/backend-express/node_modules/@redis/client/dist/lib/client/index.js:353:64)\n    at RedisService.disconnect (/Users/<USER>/projects/AISEOking/backend-express/src/services/redis.ts:70:25)\n    at ExpressServer.gracefulShutdown (/Users/<USER>/projects/AISEOking/backend-express/src/server.ts:237:33)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"}
{"timestamp":"2025-06-25 09:40:21.434","level":"error","message":"Redis 服務斷開連接失敗: The client is closed","service":"ai-seo-express","version":"1.0.0","stack":"Error: The client is closed\n    at RedisSocket.disconnect (/Users/<USER>/projects/AISEOking/backend-express/node_modules/@redis/client/dist/lib/client/socket.js:63:19)\n    at Commander.disconnect (/Users/<USER>/projects/AISEOking/backend-express/node_modules/@redis/client/dist/lib/client/index.js:353:64)\n    at RedisService.disconnect (/Users/<USER>/projects/AISEOking/backend-express/src/services/redis.ts:70:25)\n    at ExpressServer.gracefulShutdown (/Users/<USER>/projects/AISEOking/backend-express/src/server.ts:237:33)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"}
