global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'nextjs'
    static_configs:
      - targets: ['nextjs:3000']

  - job_name: 'fastapi'
    static_configs:
      - targets: ['fastapi:8000']

  - job_name: 'express'
    static_configs:
      - targets: ['express:3001']

  - job_name: 'websocket-server'
    static_configs:
      - targets: ['websocket-server:8002']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']

  - job_name: 'elasticsearch'
    static_configs:
      - targets: ['elasticsearch:9200']

  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka1:9092', 'kafka2:9093', 'kafka3:9094']

  - job_name: 'spark-master'
    static_configs:
      - targets: ['spark-master:8080']

  - job_name: 'flink-jobmanager'
    static_configs:
      - targets: ['flink-jobmanager:8081']

  - job_name: 'mlflow'
    static_configs:
      - targets: ['mlflow:5000']
