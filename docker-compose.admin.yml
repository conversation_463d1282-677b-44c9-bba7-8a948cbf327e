version: '3.8'

services:
  # Next.js 前端應用
  admin-frontend:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - NEXTAUTH_URL=${NEXTAUTH_URL}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - WEBSOCKET_URL=ws://websocket-server:3001
      - AI_SERVICE_URL=http://ai-service:5000
    depends_on:
      - postgres
      - redis
      - ai-service
      - websocket-server
    networks:
      - admin-network
    restart: unless-stopped

  # FastAPI 後端 API
  admin-backend:
    build:
      context: ./backend-fastapi
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=redis://redis:6379
      - AI_SERVICE_URL=http://ai-service:5000
      - SECRET_KEY=${SECRET_KEY}
    depends_on:
      - postgres
      - redis
    networks:
      - admin-network
    restart: unless-stopped

  # AI 意圖分類服務
  ai-service:
    build:
      context: ./backend-fastapi/ai-service
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - PORT=5000
      - DEBUG=false
    volumes:
      - ./models:/app/models
    networks:
      - admin-network
    restart: unless-stopped

  # WebSocket 實時數據服務
  websocket-server:
    build:
      context: ./websocket-server
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - PORT=3001
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
    networks:
      - admin-network
    restart: unless-stopped

  # PostgreSQL 數據庫
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-admin_analytics}
      - POSTGRES_USER=${POSTGRES_USER:-admin}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/database-migration.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - admin-network
    restart: unless-stopped

  # Redis 緩存和會話存儲
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - admin-network
    restart: unless-stopped

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.admin.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - admin-frontend
      - admin-backend
    networks:
      - admin-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  admin-network:
    driver: bridge 