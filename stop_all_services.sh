#!/bin/bash

# AI SEO 優化王系統 - 停止所有服務腳本

echo "🛑 停止 AI SEO 優化王系統..."

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

PROJECT_ROOT=$(pwd)

# 函數：停止進程
stop_process() {
    local process_name=$1
    local pid_file=$2
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 $pid 2>/dev/null; then
            echo -e "${YELLOW}🛑 停止 $process_name (PID: $pid)...${NC}"
            kill $pid
            sleep 3
            if kill -0 $pid 2>/dev/null; then
                echo -e "${RED}⚠️ 強制停止 $process_name...${NC}"
                kill -9 $pid
            fi
            echo -e "${GREEN}✅ $process_name 已停止${NC}"
        else
            echo -e "${YELLOW}⚠️ $process_name 進程不存在${NC}"
        fi
        rm -f "$pid_file"
    else
        echo -e "${YELLOW}⚠️ 未找到 $process_name PID 文件${NC}"
    fi
}

# 停止前端
stop_process "前端 Next.js" "$PROJECT_ROOT/.frontend.pid"

# 停止後端
stop_process "後端 FastAPI" "$PROJECT_ROOT/.backend.pid"

# 停止 Elasticsearch
stop_process "Elasticsearch" "$PROJECT_ROOT/.elasticsearch.pid"

# 強制清理進程
echo -e "${YELLOW}🧹 清理殘留進程...${NC}"
pkill -f "next-server" 2>/dev/null || true
pkill -f "uvicorn" 2>/dev/null || true
pkill -f "elasticsearch" 2>/dev/null || true

# 檢查端口是否已釋放
echo -e "${BLUE}🔍 檢查端口狀態...${NC}"

check_port() {
    local port=$1
    local service=$2
    if lsof -i :$port &> /dev/null; then
        echo -e "${RED}⚠️ 端口 $port ($service) 仍被佔用${NC}"
        # 強制釋放端口
        lsof -ti :$port | xargs kill -9 2>/dev/null || true
    else
        echo -e "${GREEN}✅ 端口 $port ($service) 已釋放${NC}"
    fi
}

check_port 3000 "前端"
check_port 8000 "後端"
check_port 9200 "Elasticsearch"

# 清理日誌文件（可選）
read -p "是否清理日誌文件? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}🧹 清理日誌文件...${NC}"
    rm -f "$PROJECT_ROOT/elasticsearch.log"
    rm -f "$PROJECT_ROOT/backend.log"
    rm -f "$PROJECT_ROOT/frontend.log"
    echo -e "${GREEN}✅ 日誌文件已清理${NC}"
fi

echo -e "\n${GREEN}🎉 所有服務已停止！${NC}"
echo -e "${BLUE}💡 使用 ./start_all_services.sh 重新啟動系統${NC}" 