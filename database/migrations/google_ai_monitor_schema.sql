-- 台灣 Google AI 摘要監測器 - 資料庫 Schema
-- 創建時間: 2025-06-27
-- 版本: 1.0.0

-- 啟用 UUID 擴展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. 監測關鍵字表
CREATE TABLE monitoring_keywords (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    keyword VARCHAR(255) NOT NULL,
    category VARCHAR(100),
    target_domain VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    monitoring_frequency VARCHAR(20) DEFAULT 'daily', -- daily, weekly, monthly
    last_monitored_at TIMESTAMP WITH TIME ZONE,
    next_monitoring_at TIMESTAMP WITH TIME ZONE,
    monitoring_config JSONB DEFAULT '{}', -- 監測配置參數
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 約束
    CONSTRAINT valid_frequency CHECK (monitoring_frequency IN ('daily', 'weekly', 'monthly')),
    CONSTRAINT keyword_not_empty CHECK (LENGTH(TRIM(keyword)) > 0)
);

-- 2. 搜尋結果表
CREATE TABLE search_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    keyword_id UUID NOT NULL REFERENCES monitoring_keywords(id) ON DELETE CASCADE,
    search_query VARCHAR(500) NOT NULL,
    search_date TIMESTAMP WITH TIME ZONE NOT NULL,
    search_location VARCHAR(100) DEFAULT 'Taiwan', -- 搜尋地區
    search_language VARCHAR(10) DEFAULT 'zh-TW', -- 搜尋語言
    total_results BIGINT,
    has_ai_summary BOOLEAN DEFAULT false,
    ai_summary_position INTEGER, -- AI 摘要在結果中的位置
    organic_results_count INTEGER DEFAULT 0,
    ads_count INTEGER DEFAULT 0,
    search_metadata JSONB DEFAULT '{}', -- 搜尋元數據
    raw_html TEXT, -- 原始 HTML (可選，用於調試)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 約束
    CONSTRAINT positive_position CHECK (ai_summary_position > 0),
    CONSTRAINT positive_results CHECK (total_results >= 0)
);

-- 3. AI 摘要記錄表
CREATE TABLE ai_summaries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    search_result_id UUID NOT NULL REFERENCES search_results(id) ON DELETE CASCADE,
    summary_content TEXT NOT NULL,
    summary_html TEXT, -- 原始 HTML 內容
    summary_sources JSONB DEFAULT '[]', -- 引用來源列表
    user_domain_mentioned BOOLEAN DEFAULT false,
    user_content_quoted TEXT, -- 被引用的用戶內容
    user_content_similarity DECIMAL(5,4), -- 內容相似度 (0-1)
    summary_type VARCHAR(50) DEFAULT 'ai_overview', -- ai_overview, featured_snippet, etc.
    confidence_score DECIMAL(3,2) DEFAULT 0.8, -- 檢測信心度
    language VARCHAR(10) DEFAULT 'zh-TW',
    word_count INTEGER,
    character_count INTEGER,
    parsed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 約束
    CONSTRAINT valid_confidence CHECK (confidence_score >= 0 AND confidence_score <= 1),
    CONSTRAINT valid_similarity CHECK (user_content_similarity >= 0 AND user_content_similarity <= 1),
    CONSTRAINT content_not_empty CHECK (LENGTH(TRIM(summary_content)) > 0)
);

-- 4. 競爭對手表
CREATE TABLE competitors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    domain VARCHAR(255) NOT NULL,
    company_name VARCHAR(255),
    industry VARCHAR(100),
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    monitoring_priority INTEGER DEFAULT 1, -- 1=高, 2=中, 3=低
    last_analyzed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 約束
    CONSTRAINT domain_format CHECK (domain ~ '^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\.[a-zA-Z]{2,}$'),
    CONSTRAINT valid_priority CHECK (monitoring_priority IN (1, 2, 3)),
    UNIQUE(user_id, domain)
);

-- 5. 監測任務表
CREATE TABLE monitoring_tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    keyword_id UUID REFERENCES monitoring_keywords(id) ON DELETE CASCADE,
    task_type VARCHAR(50) NOT NULL, -- search, analysis, report, cleanup
    status VARCHAR(20) DEFAULT 'pending', -- pending, running, completed, failed, cancelled
    priority INTEGER DEFAULT 2, -- 1=高, 2=中, 3=低
    scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    error_message TEXT,
    error_code VARCHAR(50),
    result_data JSONB DEFAULT '{}',
    execution_time_ms INTEGER, -- 執行時間（毫秒）
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 約束
    CONSTRAINT valid_status CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    CONSTRAINT valid_task_type CHECK (task_type IN ('search', 'analysis', 'report', 'cleanup')),
    CONSTRAINT valid_priority CHECK (priority IN (1, 2, 3)),
    CONSTRAINT positive_retry CHECK (retry_count >= 0 AND retry_count <= max_retries)
);

-- 6. AI 摘要來源表 (正規化引用來源)
CREATE TABLE ai_summary_sources (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ai_summary_id UUID NOT NULL REFERENCES ai_summaries(id) ON DELETE CASCADE,
    source_url TEXT NOT NULL,
    source_domain VARCHAR(255),
    source_title VARCHAR(500),
    source_description TEXT,
    citation_text TEXT, -- 被引用的具體文字
    position_in_summary INTEGER, -- 在摘要中的位置
    is_user_domain BOOLEAN DEFAULT false,
    relevance_score DECIMAL(3,2), -- 相關性評分
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 約束
    CONSTRAINT valid_position CHECK (position_in_summary > 0),
    CONSTRAINT valid_relevance CHECK (relevance_score >= 0 AND relevance_score <= 1)
);

-- 7. 監測統計表 (用於快速查詢統計數據)
CREATE TABLE monitoring_statistics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    keyword_id UUID NOT NULL REFERENCES monitoring_keywords(id) ON DELETE CASCADE,
    date_period DATE NOT NULL, -- 統計日期
    period_type VARCHAR(20) NOT NULL, -- daily, weekly, monthly
    total_searches INTEGER DEFAULT 0,
    ai_summary_appearances INTEGER DEFAULT 0,
    user_domain_mentions INTEGER DEFAULT 0,
    average_ai_position DECIMAL(5,2),
    top_competitors JSONB DEFAULT '[]', -- 主要競爭對手列表
    summary_topics JSONB DEFAULT '[]', -- 摘要主題分析
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 約束
    CONSTRAINT valid_period_type CHECK (period_type IN ('daily', 'weekly', 'monthly')),
    CONSTRAINT positive_searches CHECK (total_searches >= 0),
    CONSTRAINT positive_appearances CHECK (ai_summary_appearances >= 0),
    CONSTRAINT appearances_not_exceed_searches CHECK (ai_summary_appearances <= total_searches),
    UNIQUE(keyword_id, date_period, period_type)
);

-- 8. 系統配置表
CREATE TABLE system_config (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value JSONB NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 創建索引以提升查詢性能

-- 監測關鍵字索引
CREATE INDEX idx_monitoring_keywords_user_active ON monitoring_keywords(user_id, is_active);
CREATE INDEX idx_monitoring_keywords_frequency ON monitoring_keywords(monitoring_frequency, next_monitoring_at) WHERE is_active = true;
CREATE INDEX idx_monitoring_keywords_category ON monitoring_keywords(category) WHERE is_active = true;

-- 搜尋結果索引
CREATE INDEX idx_search_results_keyword_date ON search_results(keyword_id, search_date DESC);
CREATE INDEX idx_search_results_ai_summary ON search_results(has_ai_summary, search_date DESC) WHERE has_ai_summary = true;
CREATE INDEX idx_search_results_date ON search_results(search_date DESC);

-- AI 摘要索引
CREATE INDEX idx_ai_summaries_search_result ON ai_summaries(search_result_id);
CREATE INDEX idx_ai_summaries_user_mentioned ON ai_summaries(user_domain_mentioned, created_at DESC) WHERE user_domain_mentioned = true;
CREATE INDEX idx_ai_summaries_type ON ai_summaries(summary_type, created_at DESC);

-- 競爭對手索引
CREATE INDEX idx_competitors_user_active ON competitors(user_id, is_active);
CREATE INDEX idx_competitors_domain ON competitors(domain);

-- 監測任務索引
CREATE INDEX idx_monitoring_tasks_status_scheduled ON monitoring_tasks(status, scheduled_at);
CREATE INDEX idx_monitoring_tasks_keyword ON monitoring_tasks(keyword_id, created_at DESC);
CREATE INDEX idx_monitoring_tasks_type_status ON monitoring_tasks(task_type, status);

-- AI 摘要來源索引
CREATE INDEX idx_ai_summary_sources_summary ON ai_summary_sources(ai_summary_id);
CREATE INDEX idx_ai_summary_sources_domain ON ai_summary_sources(source_domain);
CREATE INDEX idx_ai_summary_sources_user_domain ON ai_summary_sources(is_user_domain, created_at DESC) WHERE is_user_domain = true;

-- 監測統計索引
CREATE INDEX idx_monitoring_statistics_keyword_period ON monitoring_statistics(keyword_id, date_period DESC, period_type);
CREATE INDEX idx_monitoring_statistics_date_type ON monitoring_statistics(date_period DESC, period_type);

-- 系統配置索引
CREATE INDEX idx_system_config_key_active ON system_config(config_key, is_active);

-- 創建更新時間觸發器函數
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 為需要的表添加更新時間觸發器
CREATE TRIGGER update_monitoring_keywords_updated_at BEFORE UPDATE ON monitoring_keywords FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_competitors_updated_at BEFORE UPDATE ON competitors FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_monitoring_tasks_updated_at BEFORE UPDATE ON monitoring_tasks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_monitoring_statistics_updated_at BEFORE UPDATE ON monitoring_statistics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_system_config_updated_at BEFORE UPDATE ON system_config FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入初始系統配置
INSERT INTO system_config (config_key, config_value, description) VALUES
('google_search_rate_limit', '{"requests_per_second": 1, "daily_quota": 100}', 'Google 搜尋 API 頻率限制配置'),
('default_monitoring_settings', '{"frequency": "daily", "max_retries": 3, "timeout_seconds": 30}', '預設監測設定'),
('ai_summary_detection', '{"confidence_threshold": 0.8, "min_content_length": 50}', 'AI 摘要檢測參數'),
('data_retention', '{"search_results_days": 365, "raw_html_days": 30, "logs_days": 90}', '數據保留政策');

-- 創建視圖以簡化常用查詢

-- 監測概覽視圖
CREATE VIEW monitoring_overview AS
SELECT 
    mk.id,
    mk.keyword,
    mk.category,
    mk.target_domain,
    mk.monitoring_frequency,
    mk.last_monitored_at,
    COUNT(sr.id) as total_searches,
    COUNT(CASE WHEN sr.has_ai_summary THEN 1 END) as ai_summary_count,
    COUNT(CASE WHEN ai.user_domain_mentioned THEN 1 END) as user_mentions,
    ROUND(
        COUNT(CASE WHEN sr.has_ai_summary THEN 1 END)::DECIMAL / 
        NULLIF(COUNT(sr.id), 0) * 100, 2
    ) as ai_summary_rate
FROM monitoring_keywords mk
LEFT JOIN search_results sr ON mk.id = sr.keyword_id
LEFT JOIN ai_summaries ai ON sr.id = ai.search_result_id
WHERE mk.is_active = true
GROUP BY mk.id, mk.keyword, mk.category, mk.target_domain, mk.monitoring_frequency, mk.last_monitored_at;

-- 最近 AI 摘要視圖
CREATE VIEW recent_ai_summaries AS
SELECT 
    ai.id,
    mk.keyword,
    ai.summary_content,
    ai.user_domain_mentioned,
    ai.confidence_score,
    sr.search_date,
    ai.created_at
FROM ai_summaries ai
JOIN search_results sr ON ai.search_result_id = sr.id
JOIN monitoring_keywords mk ON sr.keyword_id = mk.id
WHERE mk.is_active = true
ORDER BY ai.created_at DESC;

-- 添加註釋
COMMENT ON TABLE monitoring_keywords IS '監測關鍵字配置表';
COMMENT ON TABLE search_results IS 'Google 搜尋結果記錄表';
COMMENT ON TABLE ai_summaries IS 'AI 摘要內容分析表';
COMMENT ON TABLE competitors IS '競爭對手管理表';
COMMENT ON TABLE monitoring_tasks IS '監測任務排程表';
COMMENT ON TABLE ai_summary_sources IS 'AI 摘要引用來源表';
COMMENT ON TABLE monitoring_statistics IS '監測統計數據表';
COMMENT ON TABLE system_config IS '系統配置參數表';
