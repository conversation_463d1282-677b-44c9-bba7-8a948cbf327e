INFO:     Will watch for changes in these directories: ['/Users/<USER>/projects/AISEOking/backend-fastapi']
INFO:     Uvicorn running on http://0.0.0.0:8001 (Press CTRL+C to quit)
INFO:     Started reloader process [94942] using WatchFiles
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-25 09:45:53,106 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
INFO:     Started server process [94949]
INFO:     Waiting for application startup.
{"error": "Invalid argument(s) 'pool_size','max_overflow','pool_timeout' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/StaticPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.", "event": "\u6578\u64da\u5eab\u9023\u63a5\u521d\u59cb\u5316\u5931\u6557", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T13:45:53.785544Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T13:45:53.786698Z"}
2025-06-25 09:45:53,786 - app.core.init_data - ERROR - 示範數據初始化失敗 | error=數據庫連接未初始化
{"error": "\u6578\u64da\u5eab\u9023\u63a5\u672a\u521d\u59cb\u5316", "event": "\u793a\u7bc4\u6578\u64da\u521d\u59cb\u5316\u5931\u6557", "logger": "app.main", "level": "warning", "timestamp": "2025-06-25T13:45:53.786787Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T13:45:53.798517Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T13:45:53.798733Z"}
INFO:     Application startup complete.
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T13:50:53.796371Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T13:50:53.797408Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T13:55:53.795872Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T13:55:53.796555Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T14:00:53.792775Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T14:00:53.793665Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T14:05:53.791539Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T14:05:53.791999Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T14:10:53.788916Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T14:10:53.789062Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T14:15:53.786398Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T14:15:53.786523Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T14:20:53.784067Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T14:20:53.784891Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T14:25:53.809099Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T14:25:53.809749Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T14:30:53.810593Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T14:30:53.811576Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T14:35:53.810737Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T14:35:53.811033Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T14:40:53.809937Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T14:40:53.811872Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T14:45:53.864852Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T14:45:53.865887Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T14:50:53.869783Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T14:50:53.869908Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T14:55:53.874244Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T14:55:53.875236Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T15:00:53.879563Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T15:00:53.879961Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T15:05:53.883709Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T15:05:53.884559Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T15:10:53.888747Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T15:10:53.889723Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T15:15:53.894091Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T15:15:53.894938Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T15:20:53.898639Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T15:20:53.899298Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T15:25:53.918677Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T15:25:53.919361Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T15:30:53.924271Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T15:30:53.926225Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T15:35:53.931434Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T15:35:53.932846Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T15:40:53.937966Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T15:40:53.938353Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T15:45:53.902343Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T15:45:53.903411Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T15:50:53.905690Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T15:50:53.906465Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T15:55:53.908262Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T15:55:53.908692Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T16:00:53.910063Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T16:00:53.910514Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T16:05:53.912065Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T16:05:53.912542Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T16:10:53.919517Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T16:10:53.920391Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T16:15:53.923196Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T16:15:53.923349Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T16:20:53.925103Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T16:20:53.925536Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T16:25:53.879715Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T16:25:53.880336Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T16:30:53.877931Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T16:30:53.879085Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T16:35:53.877035Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T16:35:53.877796Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T16:40:53.877030Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T16:40:53.877787Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T16:45:53.877194Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T16:45:53.878585Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T16:50:53.923861Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T16:50:53.924794Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T16:55:53.927108Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T16:55:53.927698Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T17:00:53.929525Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T17:00:53.929793Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T17:05:53.932635Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T17:05:53.932947Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T17:10:53.934624Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T17:10:53.935630Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T17:15:53.940268Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T17:15:53.943127Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T17:20:53.944873Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T17:20:53.951879Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T17:25:53.954756Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T17:25:53.955641Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T17:30:53.957590Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T17:30:53.959164Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T17:35:53.978438Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T17:35:53.978577Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T17:40:53.981653Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T17:40:53.982118Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T17:45:53.985564Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T17:45:53.986038Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T17:50:53.989500Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T17:50:53.989628Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T17:55:53.940900Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T17:55:53.942916Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T18:00:53.942252Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T18:00:53.943158Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T18:05:53.942998Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T18:05:53.943323Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T18:10:53.942537Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T18:10:53.943815Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T18:15:53.942142Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T18:15:53.943973Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T18:20:53.921207Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T18:20:53.923497Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T18:25:53.920916Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T18:25:53.921319Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T18:30:53.917502Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T18:30:53.918273Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T18:35:53.916160Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T18:35:53.916856Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T18:40:53.913749Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T18:40:53.914559Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T18:45:53.971630Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T18:45:53.973862Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T18:50:53.976891Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T18:50:53.977674Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T18:55:53.980658Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T18:55:53.982809Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T19:00:53.985478Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T19:00:53.988693Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T19:05:53.962549Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T19:05:53.964783Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T19:10:53.964387Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T19:10:53.967237Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T19:15:53.967013Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T19:15:53.971311Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T19:20:53.973023Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T19:20:53.976166Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T19:25:53.977379Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T19:25:53.978182Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T19:30:53.962422Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T19:30:53.966065Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T19:35:53.965722Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T19:35:53.968125Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T19:40:53.967260Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T19:40:53.970518Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T19:45:53.969392Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T19:45:53.971129Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T19:50:53.990953Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T19:50:53.991735Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T19:55:53.991525Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T19:55:53.994202Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T20:00:53.994245Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T20:00:53.997563Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T20:05:53.997035Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T20:05:54.000613Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T20:10:54.000101Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T20:10:54.004079Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T20:15:54.004885Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T20:15:54.007151Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T20:20:54.008282Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T20:20:54.011341Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T20:25:54.012368Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T20:25:54.015903Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T20:30:54.083341Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T20:30:54.084601Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T20:35:54.090096Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T20:35:54.091198Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T20:40:54.097123Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T20:40:54.100301Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T20:45:54.106199Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T20:45:54.108440Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T20:50:54.121523Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T20:50:54.124433Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T20:55:54.135618Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T20:55:54.136588Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T21:00:54.143372Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T21:00:54.144305Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T21:05:54.150425Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T21:05:54.151385Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T21:10:54.158166Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T21:10:54.159092Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T21:15:54.160198Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T21:15:54.161079Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T21:20:54.167051Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T21:20:54.169490Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T21:25:54.175354Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T21:25:54.176262Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T21:30:54.182835Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T21:30:54.185918Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T21:35:54.190186Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T21:35:54.191096Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T21:40:54.183259Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T21:40:54.184135Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T21:45:54.190029Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T21:45:54.192962Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T21:50:54.198643Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T21:50:54.201212Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T21:55:54.206194Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T21:55:54.208834Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T22:00:54.204280Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T22:00:54.207598Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T22:05:54.208305Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T22:05:54.211460Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T22:10:54.215631Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T22:10:54.218946Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T22:15:54.223549Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T22:15:54.228370Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T22:20:54.233425Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T22:20:54.236764Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T22:25:54.230386Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T22:25:54.231586Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T22:30:54.234570Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T22:30:54.235501Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T22:35:54.239078Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T22:35:54.240373Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T22:40:54.244092Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T22:40:54.244999Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T22:45:54.247874Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T22:45:54.249976Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T22:50:54.187951Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T22:50:54.188316Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T22:55:54.186926Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T22:55:54.187275Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T23:00:54.186851Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T23:00:54.188341Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T23:05:54.188132Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T23:05:54.189068Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T23:10:54.185531Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T23:10:54.186754Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T23:15:54.185269Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T23:15:54.186450Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T23:20:54.184510Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T23:20:54.185562Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T23:25:54.182642Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T23:25:54.183587Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T23:30:54.182686Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T23:30:54.183753Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T23:35:54.209353Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T23:35:54.210315Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T23:40:54.210080Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T23:40:54.212180Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T23:45:54.211270Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T23:45:54.212966Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T23:50:54.210543Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T23:50:54.212064Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T23:55:54.211857Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-25T23:55:54.213916Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T00:00:54.251488Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T00:00:54.253295Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T00:05:54.255358Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T00:05:54.258904Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T00:10:54.261384Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T00:10:54.263473Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T00:15:54.265468Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T00:15:54.267971Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T00:20:54.284047Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T00:20:54.285971Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T00:25:54.289499Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T00:25:54.291240Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T00:30:54.294513Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T00:30:54.296855Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T00:35:54.301434Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T00:35:54.303015Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T00:40:54.306860Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T00:40:54.308455Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T00:45:54.293937Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T00:45:54.294407Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T00:50:54.295047Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T00:50:54.296489Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T00:55:54.299409Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T00:55:54.300135Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T01:00:54.302639Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T01:00:54.303271Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T01:05:54.277983Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T01:05:54.278592Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T01:10:54.276599Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T01:10:54.276715Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T01:15:54.276422Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T01:15:54.279137Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T01:20:54.279469Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T01:20:54.280502Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T01:25:54.279480Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T01:25:54.281402Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T01:30:54.310211Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T01:30:54.311605Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T01:35:54.313738Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T01:35:54.313864Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T01:40:54.315962Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T01:40:54.316835Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T01:45:54.324837Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T01:45:54.330633Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T01:50:54.334365Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T01:50:54.335290Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T01:55:54.294965Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T01:55:54.295892Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T02:00:54.293334Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T02:00:54.294134Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T02:05:54.293572Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T02:05:54.294364Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T02:10:54.294293Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T02:10:54.295265Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T02:15:54.333676Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T02:15:54.334106Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T02:20:54.336584Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T02:20:54.337571Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T02:25:54.339670Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T02:25:54.340775Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T02:30:54.344085Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T02:30:54.345386Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T02:35:54.347846Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T02:35:54.348738Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T02:40:54.312331Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T02:40:54.313413Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T02:45:54.312353Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T02:45:54.313370Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T02:50:54.313458Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T02:50:54.314484Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T02:55:54.314233Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T02:55:54.315320Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T03:00:54.333498Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T03:00:54.334478Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T03:05:54.342248Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T03:05:54.343474Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T03:10:54.343552Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T03:10:54.344495Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T03:15:54.346417Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T03:15:54.347314Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T03:20:54.349596Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T03:20:54.352063Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T03:25:54.346880Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T03:25:54.347718Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T03:30:54.348166Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T03:30:54.348991Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T03:35:54.350493Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T03:35:54.353106Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T03:40:54.355179Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T03:40:54.356057Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T03:45:54.357433Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T03:45:54.360076Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T03:50:54.353177Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T03:50:54.354001Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T03:55:54.354504Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T03:55:54.357254Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T04:00:54.359251Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T04:00:54.360224Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T04:05:54.361501Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T04:05:54.362167Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T04:10:54.386342Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T04:10:54.387238Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T04:15:54.388521Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T04:15:54.391167Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T04:20:54.394653Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T04:20:54.398155Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T04:25:54.400442Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T04:25:54.401381Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T04:30:54.404448Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T04:30:54.405442Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T04:35:54.440270Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T04:35:54.442161Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T04:40:54.447816Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T04:40:54.448174Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T04:45:54.451783Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T04:45:54.453253Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T04:50:54.458417Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T04:50:54.461087Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T04:55:54.467095Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T04:55:54.468135Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T05:00:54.481508Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T05:00:54.484387Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T05:05:54.490137Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T05:05:54.491153Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T05:10:54.497258Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T05:10:54.498597Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T05:15:54.504299Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T05:15:54.505343Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T05:20:54.452659Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T05:20:54.455018Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T05:25:54.456166Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T05:25:54.460176Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T05:30:54.460872Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T05:30:54.463604Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T05:35:54.463056Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T05:35:54.465643Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T05:40:54.466803Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T05:40:54.469416Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T05:45:54.462750Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T05:45:54.466394Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T05:50:54.465297Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T05:50:54.468125Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T05:55:54.469618Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T05:55:54.470459Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T06:00:54.471988Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T06:00:54.474928Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T06:05:54.460155Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T06:05:54.460953Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T06:10:54.453535Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T06:10:54.457924Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T06:15:54.456734Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T06:15:54.459589Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T06:20:54.459114Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T06:20:54.461558Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T06:25:54.461354Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T06:25:54.462283Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T06:30:54.481104Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T06:30:54.482723Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T06:35:54.482587Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T06:35:54.483395Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T06:40:54.484524Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T06:40:54.485485Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T06:45:54.485712Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T06:45:54.488931Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T06:50:54.489052Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T06:50:54.491976Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T06:55:54.490171Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T06:55:54.491130Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T07:00:54.491121Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T07:00:54.494511Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T07:05:54.495037Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T07:05:54.497654Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T07:10:54.497439Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T07:10:54.498280Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T07:15:54.554643Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T07:15:54.555613Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T07:20:54.560469Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T07:20:54.561817Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T07:25:54.565348Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T07:25:54.566291Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T07:30:54.570491Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T07:30:54.571548Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T07:35:54.575607Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T07:35:54.578288Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T07:40:54.587696Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T07:40:54.590312Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T07:45:54.594775Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T07:45:54.598286Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T07:50:54.603439Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T07:50:54.606324Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T07:55:54.611934Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T07:55:54.613206Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T08:00:54.619422Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T08:00:54.623023Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T08:05:54.641654Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T08:05:54.644490Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T08:10:54.649634Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T08:10:54.652115Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T08:15:54.657630Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T08:15:54.658640Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T08:20:54.664601Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T08:20:54.666874Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T08:25:54.613349Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T08:25:54.614320Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T08:30:54.615507Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T08:30:54.617636Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T08:35:54.618806Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T08:35:54.619162Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T08:40:54.618969Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T08:40:54.621180Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T08:45:54.621146Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T08:45:54.624244Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T08:50:54.655619Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T08:50:54.656269Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T08:55:54.658379Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T08:55:54.659062Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T09:00:54.662048Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T09:00:54.662770Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T09:05:54.665617Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T09:05:54.666015Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T09:10:54.667424Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T09:10:54.668109Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T09:15:54.641142Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T09:15:54.641755Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T09:20:54.641803Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T09:20:54.642319Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T09:25:54.642596Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T09:25:54.643293Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T09:30:54.643765Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T09:30:54.644142Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T09:35:54.669241Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T09:35:54.669622Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T09:40:54.674806Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T09:40:54.675289Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T09:45:54.679032Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T09:45:54.681876Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T09:50:54.685143Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T09:50:54.685599Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T09:55:54.688676Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T09:55:54.689100Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T10:00:54.648514Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T10:00:54.649283Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T10:05:54.648927Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T10:05:54.649378Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T10:10:54.648902Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T10:10:54.649734Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T10:15:54.649550Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T10:15:54.650334Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T10:20:54.650754Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T10:20:54.653444Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T10:25:54.653532Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T10:25:54.654555Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T10:30:54.654342Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T10:30:54.655038Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T10:35:54.653472Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T10:35:54.654204Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T10:40:54.701588Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T10:40:54.704450Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T10:45:54.706334Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T10:45:54.706993Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T10:50:54.708567Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T10:50:54.709335Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T10:55:54.712550Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T10:55:54.713634Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T11:00:54.708688Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T11:00:54.709421Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T11:05:54.712569Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T11:05:54.713559Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T11:10:54.715857Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T11:10:54.716286Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T11:15:54.718447Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T11:15:54.720184Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T11:20:54.721413Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T11:20:54.724009Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T11:25:54.739978Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T11:25:54.742082Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T11:30:54.745000Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T11:30:54.747687Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T11:35:54.751082Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T11:35:54.752371Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T11:40:54.755889Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T11:40:54.756744Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T11:45:54.760520Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T11:45:54.760655Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T11:50:54.764107Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T11:50:54.765186Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T11:55:54.768382Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T11:55:54.769321Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T12:00:54.770760Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T12:00:54.771088Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T12:05:54.773453Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T12:05:54.773610Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T12:10:54.735330Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T12:10:54.736674Z"}
INFO:     127.0.0.1:55441 - "GET /health HTTP/1.1" 200 OK
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T12:15:54.735788Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T12:15:54.736160Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T12:20:54.736492Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T12:20:54.736816Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T12:25:54.735776Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T12:25:54.737721Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T12:30:54.737424Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T12:30:54.739070Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T12:35:54.721873Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T12:35:54.723347Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T12:40:54.721875Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T12:40:54.722272Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T12:45:54.720215Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T12:45:54.720418Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T12:50:54.718614Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T12:50:54.718993Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T12:55:54.734693Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T12:55:54.734859Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T13:00:54.734348Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T13:00:54.734641Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T13:05:54.734708Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T13:05:54.734840Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T13:10:54.735010Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T13:10:54.735476Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T13:15:54.736390Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T13:15:54.736679Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T13:20:54.711278Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T13:20:54.713668Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T13:25:54.711553Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T13:25:54.711783Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T13:30:54.709200Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T13:30:54.709357Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T13:35:54.707695Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T13:35:54.708555Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T13:40:54.703802Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T13:40:54.704869Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T13:45:54.701764Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T13:45:54.702436Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T13:50:54.700126Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T13:50:54.700277Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T13:55:54.697412Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T13:55:54.697557Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T14:00:54.694423Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T14:00:54.694568Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T14:05:54.755841Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T14:05:54.756014Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T14:10:54.757940Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T14:10:54.758087Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T14:15:54.760685Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T14:15:54.760990Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T14:20:54.763689Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T14:20:54.763971Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T14:25:54.766326Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T14:25:54.767787Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T14:30:54.739142Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T14:30:54.739495Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T14:35:54.739007Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T14:35:54.739242Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T14:40:54.738822Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T14:40:54.739276Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T14:45:54.739361Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T14:45:54.739521Z"}
WARNING:  WatchFiles detected changes in 'ai-service/intent_classifier.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [94949]
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
WARNING:  WatchFiles detected changes in 'ai-service/intent_classifier.py'. Reloading...
WARNING:  WatchFiles detected changes in 'ai-service/intent_classifier.py'. Reloading...
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 10:48:38,166 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
INFO:     Started server process [87099]
INFO:     Waiting for application startup.
{"error": "Invalid argument(s) 'pool_size','max_overflow','pool_timeout' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/StaticPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.", "event": "\u6578\u64da\u5eab\u9023\u63a5\u521d\u59cb\u5316\u5931\u6557", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T14:48:38.875054Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T14:48:38.876402Z"}
2025-06-26 10:48:38,876 - app.core.init_data - ERROR - 示範數據初始化失敗 | error=數據庫連接未初始化
{"error": "\u6578\u64da\u5eab\u9023\u63a5\u672a\u521d\u59cb\u5316", "event": "\u793a\u7bc4\u6578\u64da\u521d\u59cb\u5316\u5931\u6557", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T14:48:38.876526Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u9023\u63a5\u5931\u6557", "logger": "app.core.elasticsearch", "level": "error", "timestamp": "2025-06-26T14:48:38.882725Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u521d\u59cb\u5316\u5931\u6557\uff0c\u641c\u7d22\u529f\u80fd\u5c07\u4e0d\u53ef\u7528", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T14:48:38.882862Z"}
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/data_collection_pipeline.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [87099]
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:10:18,461 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
WARNING:  WatchFiles detected changes in 'app/services/data_collection_pipeline.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/data_collection_pipeline.py'. Reloading...
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:10:20,040 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
INFO:     Started server process [2870]
INFO:     Waiting for application startup.
{"error": "Invalid argument(s) 'pool_size','max_overflow','pool_timeout' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/StaticPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.", "event": "\u6578\u64da\u5eab\u9023\u63a5\u521d\u59cb\u5316\u5931\u6557", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:10:20.759783Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:10:20.760623Z"}
2025-06-26 11:10:20,760 - app.core.init_data - ERROR - 示範數據初始化失敗 | error=數據庫連接未初始化
{"error": "\u6578\u64da\u5eab\u9023\u63a5\u672a\u521d\u59cb\u5316", "event": "\u793a\u7bc4\u6578\u64da\u521d\u59cb\u5316\u5931\u6557", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:10:20.760727Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u9023\u63a5\u5931\u6557", "logger": "app.core.elasticsearch", "level": "error", "timestamp": "2025-06-26T15:10:20.765581Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u521d\u59cb\u5316\u5931\u6557\uff0c\u641c\u7d22\u529f\u80fd\u5c07\u4e0d\u53ef\u7528", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:10:20.765675Z"}
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/collectors/__init__.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [2870]
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:10:29,730 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
WARNING:  WatchFiles detected changes in 'app/services/collectors/__init__.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/collectors/__init__.py'. Reloading...
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:10:31,161 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
INFO:     Started server process [2997]
INFO:     Waiting for application startup.
{"error": "Invalid argument(s) 'pool_size','max_overflow','pool_timeout' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/StaticPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.", "event": "\u6578\u64da\u5eab\u9023\u63a5\u521d\u59cb\u5316\u5931\u6557", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:10:31.894178Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:10:31.894975Z"}
2025-06-26 11:10:31,895 - app.core.init_data - ERROR - 示範數據初始化失敗 | error=數據庫連接未初始化
{"error": "\u6578\u64da\u5eab\u9023\u63a5\u672a\u521d\u59cb\u5316", "event": "\u793a\u7bc4\u6578\u64da\u521d\u59cb\u5316\u5931\u6557", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:10:31.895068Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u9023\u63a5\u5931\u6557", "logger": "app.core.elasticsearch", "level": "error", "timestamp": "2025-06-26T15:10:31.900292Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u521d\u59cb\u5316\u5931\u6557\uff0c\u641c\u7d22\u529f\u80fd\u5c07\u4e0d\u53ef\u7528", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:10:31.900385Z"}
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/collectors/base_collector.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [2997]
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:10:49,233 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
WARNING:  WatchFiles detected changes in 'app/services/collectors/base_collector.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/collectors/base_collector.py'. Reloading...
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:10:50,640 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
INFO:     Started server process [3213]
INFO:     Waiting for application startup.
{"error": "Invalid argument(s) 'pool_size','max_overflow','pool_timeout' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/StaticPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.", "event": "\u6578\u64da\u5eab\u9023\u63a5\u521d\u59cb\u5316\u5931\u6557", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:10:51.330247Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:10:51.331023Z"}
2025-06-26 11:10:51,331 - app.core.init_data - ERROR - 示範數據初始化失敗 | error=數據庫連接未初始化
{"error": "\u6578\u64da\u5eab\u9023\u63a5\u672a\u521d\u59cb\u5316", "event": "\u793a\u7bc4\u6578\u64da\u521d\u59cb\u5316\u5931\u6557", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:10:51.331114Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u9023\u63a5\u5931\u6557", "logger": "app.core.elasticsearch", "level": "error", "timestamp": "2025-06-26T15:10:51.336609Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u521d\u59cb\u5316\u5931\u6557\uff0c\u641c\u7d22\u529f\u80fd\u5c07\u4e0d\u53ef\u7528", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:10:51.336697Z"}
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/collectors/web_search_collector.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [3213]
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:11:13,558 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
WARNING:  WatchFiles detected changes in 'app/services/collectors/web_search_collector.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/collectors/web_search_collector.py'. Reloading...
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:11:15,128 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
INFO:     Started server process [3462]
INFO:     Waiting for application startup.
{"error": "Invalid argument(s) 'pool_size','max_overflow','pool_timeout' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/StaticPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.", "event": "\u6578\u64da\u5eab\u9023\u63a5\u521d\u59cb\u5316\u5931\u6557", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:11:15.853719Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:11:15.854635Z"}
2025-06-26 11:11:15,854 - app.core.init_data - ERROR - 示範數據初始化失敗 | error=數據庫連接未初始化
{"error": "\u6578\u64da\u5eab\u9023\u63a5\u672a\u521d\u59cb\u5316", "event": "\u793a\u7bc4\u6578\u64da\u521d\u59cb\u5316\u5931\u6557", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:11:15.854805Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u9023\u63a5\u5931\u6557", "logger": "app.core.elasticsearch", "level": "error", "timestamp": "2025-06-26T15:11:15.869279Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u521d\u59cb\u5316\u5931\u6557\uff0c\u641c\u7d22\u529f\u80fd\u5c07\u4e0d\u53ef\u7528", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:11:15.869525Z"}
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/collectors/api_collector.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [3462]
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:11:40,742 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
WARNING:  WatchFiles detected changes in 'app/services/collectors/api_collector.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/collectors/api_collector.py'. Reloading...
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:11:42,450 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
INFO:     Started server process [3752]
INFO:     Waiting for application startup.
{"error": "Invalid argument(s) 'pool_size','max_overflow','pool_timeout' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/StaticPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.", "event": "\u6578\u64da\u5eab\u9023\u63a5\u521d\u59cb\u5316\u5931\u6557", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:11:43.124923Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:11:43.127556Z"}
2025-06-26 11:11:43,127 - app.core.init_data - ERROR - 示範數據初始化失敗 | error=數據庫連接未初始化
{"error": "\u6578\u64da\u5eab\u9023\u63a5\u672a\u521d\u59cb\u5316", "event": "\u793a\u7bc4\u6578\u64da\u521d\u59cb\u5316\u5931\u6557", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:11:43.128006Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u9023\u63a5\u5931\u6557", "logger": "app.core.elasticsearch", "level": "error", "timestamp": "2025-06-26T15:11:43.145697Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u521d\u59cb\u5316\u5931\u6557\uff0c\u641c\u7d22\u529f\u80fd\u5c07\u4e0d\u53ef\u7528", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:11:43.145990Z"}
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/collectors/third_party_collector.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [3752]
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:12:31,814 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
WARNING:  WatchFiles detected changes in 'app/services/collectors/third_party_collector.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/collectors/third_party_collector.py'. Reloading...
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:12:33,686 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
INFO:     Started server process [4322]
INFO:     Waiting for application startup.
{"error": "Invalid argument(s) 'pool_size','max_overflow','pool_timeout' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/StaticPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.", "event": "\u6578\u64da\u5eab\u9023\u63a5\u521d\u59cb\u5316\u5931\u6557", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:12:34.321156Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:12:34.321906Z"}
2025-06-26 11:12:34,321 - app.core.init_data - ERROR - 示範數據初始化失敗 | error=數據庫連接未初始化
{"error": "\u6578\u64da\u5eab\u9023\u63a5\u672a\u521d\u59cb\u5316", "event": "\u793a\u7bc4\u6578\u64da\u521d\u59cb\u5316\u5931\u6557", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:12:34.321996Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u9023\u63a5\u5931\u6557", "logger": "app.core.elasticsearch", "level": "error", "timestamp": "2025-06-26T15:12:34.326972Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u521d\u59cb\u5316\u5931\u6557\uff0c\u641c\u7d22\u529f\u80fd\u5c07\u4e0d\u53ef\u7528", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:12:34.327057Z"}
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/processors/__init__.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [4322]
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:12:35,908 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
WARNING:  WatchFiles detected changes in 'app/services/processors/__init__.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/processors/__init__.py'. Reloading...
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:12:37,352 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
INFO:     Started server process [4371]
INFO:     Waiting for application startup.
{"error": "Invalid argument(s) 'pool_size','max_overflow','pool_timeout' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/StaticPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.", "event": "\u6578\u64da\u5eab\u9023\u63a5\u521d\u59cb\u5316\u5931\u6557", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:12:38.054902Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:12:38.055730Z"}
2025-06-26 11:12:38,055 - app.core.init_data - ERROR - 示範數據初始化失敗 | error=數據庫連接未初始化
{"error": "\u6578\u64da\u5eab\u9023\u63a5\u672a\u521d\u59cb\u5316", "event": "\u793a\u7bc4\u6578\u64da\u521d\u59cb\u5316\u5931\u6557", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:12:38.055826Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u9023\u63a5\u5931\u6557", "logger": "app.core.elasticsearch", "level": "error", "timestamp": "2025-06-26T15:12:38.061630Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u521d\u59cb\u5316\u5931\u6557\uff0c\u641c\u7d22\u529f\u80fd\u5c07\u4e0d\u53ef\u7528", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:12:38.061732Z"}
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/processors/data_cleaning_processor.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [4371]
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:12:56,735 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
WARNING:  WatchFiles detected changes in 'app/services/processors/data_cleaning_processor.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/processors/data_cleaning_processor.py'. Reloading...
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:12:58,075 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
INFO:     Started server process [4595]
INFO:     Waiting for application startup.
{"error": "Invalid argument(s) 'pool_size','max_overflow','pool_timeout' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/StaticPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.", "event": "\u6578\u64da\u5eab\u9023\u63a5\u521d\u59cb\u5316\u5931\u6557", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:12:58.790944Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:12:58.791715Z"}
2025-06-26 11:12:58,791 - app.core.init_data - ERROR - 示範數據初始化失敗 | error=數據庫連接未初始化
{"error": "\u6578\u64da\u5eab\u9023\u63a5\u672a\u521d\u59cb\u5316", "event": "\u793a\u7bc4\u6578\u64da\u521d\u59cb\u5316\u5931\u6557", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:12:58.791804Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u9023\u63a5\u5931\u6557", "logger": "app.core.elasticsearch", "level": "error", "timestamp": "2025-06-26T15:12:58.796632Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u521d\u59cb\u5316\u5931\u6557\uff0c\u641c\u7d22\u529f\u80fd\u5c07\u4e0d\u53ef\u7528", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:12:58.796699Z"}
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/processors/data_enrichment_processor.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [4595]
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:13:44,264 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
WARNING:  WatchFiles detected changes in 'app/services/processors/data_enrichment_processor.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/processors/data_enrichment_processor.py'. Reloading...
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:13:45,750 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
INFO:     Started server process [5074]
INFO:     Waiting for application startup.
{"error": "Invalid argument(s) 'pool_size','max_overflow','pool_timeout' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/StaticPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.", "event": "\u6578\u64da\u5eab\u9023\u63a5\u521d\u59cb\u5316\u5931\u6557", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:13:46.517324Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:13:46.518258Z"}
2025-06-26 11:13:46,518 - app.core.init_data - ERROR - 示範數據初始化失敗 | error=數據庫連接未初始化
{"error": "\u6578\u64da\u5eab\u9023\u63a5\u672a\u521d\u59cb\u5316", "event": "\u793a\u7bc4\u6578\u64da\u521d\u59cb\u5316\u5931\u6557", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:13:46.518399Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u9023\u63a5\u5931\u6557", "logger": "app.core.elasticsearch", "level": "error", "timestamp": "2025-06-26T15:13:46.523947Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u521d\u59cb\u5316\u5931\u6557\uff0c\u641c\u7d22\u529f\u80fd\u5c07\u4e0d\u53ef\u7528", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:13:46.524048Z"}
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/processors/intent_classification_processor.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [5074]
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:14:16,229 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
WARNING:  WatchFiles detected changes in 'app/services/processors/intent_classification_processor.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/processors/intent_classification_processor.py'. Reloading...
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:14:17,756 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
INFO:     Started server process [5407]
INFO:     Waiting for application startup.
{"error": "Invalid argument(s) 'pool_size','max_overflow','pool_timeout' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/StaticPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.", "event": "\u6578\u64da\u5eab\u9023\u63a5\u521d\u59cb\u5316\u5931\u6557", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:14:18.487527Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:14:18.488273Z"}
2025-06-26 11:14:18,488 - app.core.init_data - ERROR - 示範數據初始化失敗 | error=數據庫連接未初始化
{"error": "\u6578\u64da\u5eab\u9023\u63a5\u672a\u521d\u59cb\u5316", "event": "\u793a\u7bc4\u6578\u64da\u521d\u59cb\u5316\u5931\u6557", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:14:18.488365Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u9023\u63a5\u5931\u6557", "logger": "app.core.elasticsearch", "level": "error", "timestamp": "2025-06-26T15:14:18.494108Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u521d\u59cb\u5316\u5931\u6557\uff0c\u641c\u7d22\u529f\u80fd\u5c07\u4e0d\u53ef\u7528", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:14:18.494196Z"}
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/processors/topic_extraction_processor.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [5407]
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:14:52,552 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
WARNING:  WatchFiles detected changes in 'app/services/processors/topic_extraction_processor.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/processors/topic_extraction_processor.py'. Reloading...
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:14:53,998 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
INFO:     Started server process [5781]
INFO:     Waiting for application startup.
{"error": "Invalid argument(s) 'pool_size','max_overflow','pool_timeout' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/StaticPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.", "event": "\u6578\u64da\u5eab\u9023\u63a5\u521d\u59cb\u5316\u5931\u6557", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:14:54.744761Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:14:54.745546Z"}
2025-06-26 11:14:54,745 - app.core.init_data - ERROR - 示範數據初始化失敗 | error=數據庫連接未初始化
{"error": "\u6578\u64da\u5eab\u9023\u63a5\u672a\u521d\u59cb\u5316", "event": "\u793a\u7bc4\u6578\u64da\u521d\u59cb\u5316\u5931\u6557", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:14:54.745634Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u9023\u63a5\u5931\u6557", "logger": "app.core.elasticsearch", "level": "error", "timestamp": "2025-06-26T15:14:54.750365Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u521d\u59cb\u5316\u5931\u6557\uff0c\u641c\u7d22\u529f\u80fd\u5c07\u4e0d\u53ef\u7528", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:14:54.750458Z"}
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/api/v1/endpoints/data_pipeline.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [5781]
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:18:52,448 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
WARNING:  WatchFiles detected changes in 'app/api/v1/endpoints/data_pipeline.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/v1/endpoints/data_pipeline.py'. Reloading...
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:18:54,208 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
INFO:     Started server process [8362]
INFO:     Waiting for application startup.
{"error": "Invalid argument(s) 'pool_size','max_overflow','pool_timeout' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/StaticPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.", "event": "\u6578\u64da\u5eab\u9023\u63a5\u521d\u59cb\u5316\u5931\u6557", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:18:54.875881Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:18:54.876616Z"}
2025-06-26 11:18:54,876 - app.core.init_data - ERROR - 示範數據初始化失敗 | error=數據庫連接未初始化
{"error": "\u6578\u64da\u5eab\u9023\u63a5\u672a\u521d\u59cb\u5316", "event": "\u793a\u7bc4\u6578\u64da\u521d\u59cb\u5316\u5931\u6557", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:18:54.876701Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u9023\u63a5\u5931\u6557", "logger": "app.core.elasticsearch", "level": "error", "timestamp": "2025-06-26T15:18:54.881834Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u521d\u59cb\u5316\u5931\u6557\uff0c\u641c\u7d22\u529f\u80fd\u5c07\u4e0d\u53ef\u7528", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:18:54.881914Z"}
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/api/v1/endpoints/pipeline_dashboard.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [8362]
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:20:06,087 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
WARNING:  WatchFiles detected changes in 'app/api/v1/endpoints/pipeline_dashboard.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/api/v1/endpoints/pipeline_dashboard.py'. Reloading...
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:20:07,968 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
INFO:     Started server process [9109]
INFO:     Waiting for application startup.
{"error": "Invalid argument(s) 'pool_size','max_overflow','pool_timeout' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/StaticPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.", "event": "\u6578\u64da\u5eab\u9023\u63a5\u521d\u59cb\u5316\u5931\u6557", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:20:08.700195Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:20:08.700941Z"}
2025-06-26 11:20:08,700 - app.core.init_data - ERROR - 示範數據初始化失敗 | error=數據庫連接未初始化
{"error": "\u6578\u64da\u5eab\u9023\u63a5\u672a\u521d\u59cb\u5316", "event": "\u793a\u7bc4\u6578\u64da\u521d\u59cb\u5316\u5931\u6557", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:20:08.701034Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u9023\u63a5\u5931\u6557", "logger": "app.core.elasticsearch", "level": "error", "timestamp": "2025-06-26T15:20:08.705823Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u521d\u59cb\u5316\u5931\u6557\uff0c\u641c\u7d22\u529f\u80fd\u5c07\u4e0d\u53ef\u7528", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:20:08.705897Z"}
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/pipeline_optimizer.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [9109]
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:22:42,545 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
WARNING:  WatchFiles detected changes in 'app/services/pipeline_optimizer.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/pipeline_optimizer.py'. Reloading...
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:22:44,368 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
INFO:     Started server process [10748]
INFO:     Waiting for application startup.
{"error": "Invalid argument(s) 'pool_size','max_overflow','pool_timeout' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/StaticPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.", "event": "\u6578\u64da\u5eab\u9023\u63a5\u521d\u59cb\u5316\u5931\u6557", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:22:45.079064Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:22:45.079762Z"}
2025-06-26 11:22:45,079 - app.core.init_data - ERROR - 示範數據初始化失敗 | error=數據庫連接未初始化
{"error": "\u6578\u64da\u5eab\u9023\u63a5\u672a\u521d\u59cb\u5316", "event": "\u793a\u7bc4\u6578\u64da\u521d\u59cb\u5316\u5931\u6557", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:22:45.079846Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u9023\u63a5\u5931\u6557", "logger": "app.core.elasticsearch", "level": "error", "timestamp": "2025-06-26T15:22:45.084833Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u521d\u59cb\u5316\u5931\u6557\uff0c\u641c\u7d22\u529f\u80fd\u5c07\u4e0d\u53ef\u7528", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:22:45.084924Z"}
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/pipeline_optimizer.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [10748]
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:23:38,058 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
WARNING:  WatchFiles detected changes in 'app/services/pipeline_optimizer.py'. Reloading...
WARNING:  WatchFiles detected changes in 'app/services/pipeline_optimizer.py'. Reloading...
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:23:40,006 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
INFO:     Started server process [11309]
INFO:     Waiting for application startup.
{"error": "Invalid argument(s) 'pool_size','max_overflow','pool_timeout' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/StaticPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.", "event": "\u6578\u64da\u5eab\u9023\u63a5\u521d\u59cb\u5316\u5931\u6557", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:23:40.728659Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T15:23:40.729420Z"}
2025-06-26 11:23:40,729 - app.core.init_data - ERROR - 示範數據初始化失敗 | error=數據庫連接未初始化
{"error": "\u6578\u64da\u5eab\u9023\u63a5\u672a\u521d\u59cb\u5316", "event": "\u793a\u7bc4\u6578\u64da\u521d\u59cb\u5316\u5931\u6557", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:23:40.729507Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u9023\u63a5\u5931\u6557", "logger": "app.core.elasticsearch", "level": "error", "timestamp": "2025-06-26T15:23:40.736125Z"}
{"error": "Connection error caused by: ConnectionError(Connection error caused by: ClientConnectorError(Cannot connect to host localhost:9200 ssl:default [Connection refused]))", "event": "Elasticsearch \u521d\u59cb\u5316\u5931\u6557\uff0c\u641c\u7d22\u529f\u80fd\u5c07\u4e0d\u53ef\u7528", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T15:23:40.736234Z"}
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/api/v1/api.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [11309]
WARNING:  WatchFiles detected changes in 'app/api/v1/api.py'. Reloading...
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:23:54,552 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
Process SpawnProcess-51:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/Users/<USER>/projects/AISEOking/backend-fastapi/app/main.py", line 29, in <module>
    from app.api.v1.api import api_router
  File "/Users/<USER>/projects/AISEOking/backend-fastapi/app/api/v1/api.py", line 7, in <module>
    from app.api.v1.endpoints import seo, links, reports, auth, health, monitoring, vector_search, vector_pipeline, search, data_pipeline, pipeline_dashboard
  File "/Users/<USER>/projects/AISEOking/backend-fastapi/app/api/v1/endpoints/data_pipeline.py", line 14, in <module>
    from app.core.security import get_current_user
ImportError: cannot import name 'get_current_user' from 'app.core.security' (/Users/<USER>/projects/AISEOking/backend-fastapi/app/core/security.py)
WARNING:  WatchFiles detected changes in 'app/api/v1/api.py'. Reloading...
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:24:02,759 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
Process SpawnProcess-52:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/Users/<USER>/projects/AISEOking/backend-fastapi/app/main.py", line 29, in <module>
    from app.api.v1.api import api_router
  File "/Users/<USER>/projects/AISEOking/backend-fastapi/app/api/v1/api.py", line 7, in <module>
    from app.api.v1.endpoints import seo, links, reports, auth, health, monitoring, vector_search, vector_pipeline, search, data_pipeline, pipeline_dashboard
  File "/Users/<USER>/projects/AISEOking/backend-fastapi/app/api/v1/endpoints/data_pipeline.py", line 14, in <module>
    from app.core.security import get_current_user
ImportError: cannot import name 'get_current_user' from 'app.core.security' (/Users/<USER>/projects/AISEOking/backend-fastapi/app/core/security.py)
WARNING:  WatchFiles detected changes in 'app/api/v1/api.py'. Reloading...
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 11:24:04,050 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
Process SpawnProcess-53:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/Users/<USER>/projects/AISEOking/backend-fastapi/app/main.py", line 29, in <module>
    from app.api.v1.api import api_router
  File "/Users/<USER>/projects/AISEOking/backend-fastapi/app/api/v1/api.py", line 7, in <module>
    from app.api.v1.endpoints import seo, links, reports, auth, health, monitoring, vector_search, vector_pipeline, search, data_pipeline, pipeline_dashboard
  File "/Users/<USER>/projects/AISEOking/backend-fastapi/app/api/v1/endpoints/data_pipeline.py", line 14, in <module>
    from app.core.security import get_current_user
ImportError: cannot import name 'get_current_user' from 'app.core.security' (/Users/<USER>/projects/AISEOking/backend-fastapi/app/core/security.py)
WARNING:  WatchFiles detected changes in 'app/api/v1/endpoints/data_pipeline.py'. Reloading...
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 13:26:09,431 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
WARNING:  WatchFiles detected changes in 'app/api/v1/endpoints/data_pipeline.py'. Reloading...
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 13:26:10,505 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
Process SpawnProcess-55:
Traceback (most recent call last):
  File "/opt/homebrew/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/opt/homebrew/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.11/3.11.12/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/Users/<USER>/projects/AISEOking/backend-fastapi/app/main.py", line 29, in <module>
    from app.api.v1.api import api_router
  File "/Users/<USER>/projects/AISEOking/backend-fastapi/app/api/v1/api.py", line 7, in <module>
    from app.api.v1.endpoints import seo, links, reports, auth, health, monitoring, vector_search, vector_pipeline, search, data_pipeline, pipeline_dashboard
  File "/Users/<USER>/projects/AISEOking/backend-fastapi/app/api/v1/endpoints/pipeline_dashboard.py", line 13, in <module>
    from app.core.security import get_current_user
ImportError: cannot import name 'get_current_user' from 'app.core.security' (/Users/<USER>/projects/AISEOking/backend-fastapi/app/core/security.py)
WARNING:  WatchFiles detected changes in 'app/api/v1/endpoints/pipeline_dashboard.py'. Reloading...
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 13:26:13,418 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
WARNING:  WatchFiles detected changes in 'app/api/v1/endpoints/pipeline_dashboard.py'. Reloading...
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_used" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/Users/<USER>/projects/AISEOking/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:184: UserWarning: Field name "schema" shadows an attribute in parent "BaseModel"; 
  warnings.warn(
⚠️ 回退到 SQLite 內存數據庫
2025-06-26 13:26:14,404 - app.core.security - WARNING - 使用臨時生成的加密密鑰，生產環境請設置 ENCRYPTION_KEY
INFO:     Started server process [89682]
INFO:     Waiting for application startup.
{"error": "Invalid argument(s) 'pool_size','max_overflow','pool_timeout' sent to create_engine(), using configuration SQLiteDialect_aiosqlite/StaticPool/Engine.  Please check that the keyword arguments are appropriate for this combination of components.", "event": "\u6578\u64da\u5eab\u9023\u63a5\u521d\u59cb\u5316\u5931\u6557", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T17:26:15.295600Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T17:26:15.299519Z"}
2025-06-26 13:26:15,299 - app.core.init_data - ERROR - 示範數據初始化失敗 | error=數據庫連接未初始化
{"error": "\u6578\u64da\u5eab\u9023\u63a5\u672a\u521d\u59cb\u5316", "event": "\u793a\u7bc4\u6578\u64da\u521d\u59cb\u5316\u5931\u6557", "logger": "app.main", "level": "warning", "timestamp": "2025-06-26T17:26:15.299706Z"}
{"error": "BadRequestError(400, 'illegal_argument_exception', 'Custom Analyzer [chinese_analyzer] failed to find tokenizer under name [ik_max_word]')", "event": "\u5275\u5efa\u7d22\u5f15 seo_analysis \u5931\u6557", "logger": "app.core.elasticsearch", "level": "error", "timestamp": "2025-06-26T17:26:15.509908Z"}
{"error": "BadRequestError(400, 'illegal_argument_exception', 'Custom Analyzer [chinese_analyzer] failed to find tokenizer under name [ik_max_word]')", "event": "\u5275\u5efa\u7d22\u5f15 seo_keywords \u5931\u6557", "logger": "app.core.elasticsearch", "level": "error", "timestamp": "2025-06-26T17:26:15.515250Z"}
{"error": "BadRequestError(400, 'illegal_argument_exception', 'Custom Analyzer [chinese_analyzer] failed to find tokenizer under name [ik_max_word]')", "event": "\u5275\u5efa\u7d22\u5f15 seo_competitors \u5931\u6557", "logger": "app.core.elasticsearch", "level": "error", "timestamp": "2025-06-26T17:26:15.520072Z"}
{"event": "\u7d22\u5f15 seo_analysis \u521d\u59cb\u5316\u5931\u6557", "logger": "app.services.elasticsearch_service", "level": "error", "timestamp": "2025-06-26T17:26:15.520202Z"}
{"event": "\u7d22\u5f15 seo_keywords \u521d\u59cb\u5316\u5931\u6557", "logger": "app.services.elasticsearch_service", "level": "error", "timestamp": "2025-06-26T17:26:15.520242Z"}
{"event": "\u7d22\u5f15 seo_competitors \u521d\u59cb\u5316\u5931\u6557", "logger": "app.services.elasticsearch_service", "level": "error", "timestamp": "2025-06-26T17:26:15.520274Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T17:26:15.520379Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T17:26:15.520410Z"}
INFO:     Application startup complete.
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T17:31:15.518867Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T17:31:15.519427Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T17:36:15.534530Z"}
{"event": "\u6578\u64da\u5eab\u672a\u521d\u59cb\u5316", "logger": "app.core.database", "level": "error", "timestamp": "2025-06-26T17:36:15.535965Z"}
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [89682]
INFO:     Stopping reloader process [94942]
