
> ai-seo-king-express@1.0.0 dev
> nodemon --exec "ts-node -r tsconfig-paths/register src/server.ts"

[33m[nodemon] 3.1.10[39m
[33m[nodemon] to restart at any time, enter `rs`[39m
[33m[nodemon] watching path(s): *.*[39m
[33m[nodemon] watching extensions: js,mjs,cjs,json[39m
[32m[nodemon] starting `ts-node -r tsconfig-paths/register src/server.ts`[39m
node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: read EIO
    at TTY.onStreamRead (node:internal/stream_base_commons:216:20)
Emitted 'error' event on ReadStream instance at:
    at emitErrorNT (node:internal/streams/destroy:170:8)
    at emitErrorCloseNT (node:internal/streams/destroy:129:3)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  errno: -5,
  code: '<PERSON><PERSON>',
  syscall: 'read'
}

Node.js v22.14.0
{
  errno: -5,
  code: '<PERSON><PERSON>',
  syscall: 'read'
}

Node.js v22.14.0
