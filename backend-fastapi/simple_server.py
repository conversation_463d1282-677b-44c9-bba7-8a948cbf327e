"""
簡化的FastAPI服務器 - 包含真實OpenAI API測試
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import time
import json
import os
import asyncio
import aiohttp
from typing import Optional, Dict, Any

# 創建FastAPI應用
app = FastAPI(
    title="AI SEO 優化王 - 增強版",
    version="2.0.0",
    description="增強版後端API with真實OpenAI測試",
)

# 設置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://localhost:3001", 
        "http://localhost:3002",
        "http://localhost:3003",
        "http://localhost:3004",
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置模型
class OpenAIConfig(BaseModel):
    api_key: Optional[str] = None
    model: str = "gpt-4o-mini"
    max_tokens: int = 4000
    temperature: float = 0.7
    timeout: int = 30
    max_retries: int = 3

class SystemConfig(BaseModel):
    openai: OpenAIConfig = OpenAIConfig()
    database_url: str = "sqlite:///./test.db"
    redis_url: str = "redis://localhost:6379"
    debug: bool = True

# 從環境變量初始化配置
def load_config_from_env():
    config = SystemConfig()
    
    # 嘗試從多個來源獲取OpenAI API Key
    api_key = (
        os.getenv("OPENAI_API_KEY") or
        os.getenv("OPENAI_API_KEY", "").strip() or
        None
    )
    
    # 如果沒有真實密鑰，提供一個示例格式供用戶了解
    if api_key and api_key != "test-openai-api-key" and api_key.startswith("sk-"):
        config.openai.api_key = api_key
        print(f"🔑 已載入 OpenAI API Key: {api_key[:8]}...")
    else:
        # 設置一個示例密鑰格式，讓用戶知道如何設置
        config.openai.api_key = "sk-proj-示例密鑰請替換為您的真實密鑰..."
        print("⚠️ 使用示例API密鑰，請在系統設置中輸入您的真實OpenAI API密鑰")
    
    config.openai.model = os.getenv("OPENAI_MODEL", "gpt-4o-mini")
    config.openai.max_tokens = int(os.getenv("OPENAI_MAX_TOKENS", "4000"))
    
    return config

# 模擬配置存儲
system_config = load_config_from_env()

@app.get("/")
async def root():
    """根端點"""
    return {"message": "AI SEO 優化王 API - 增強版正在運行", "openai_configured": bool(system_config.openai.api_key)}

@app.get("/health")
async def health_check():
    """健康檢查端點"""
    return {
        "status": "healthy",
        "service": "AI SEO 優化王 API - 增強版",
        "version": "2.0.0",
        "timestamp": time.time(),
        "openai_key_status": "configured" if system_config.openai.api_key else "not_configured"
    }

@app.get("/api/v1/system/config")
async def get_system_config():
    """獲取系統配置"""
    return {
        "status": "success",
        "data": {
            "openai": {
                "api_key": system_config.openai.api_key[:8] + "..." if system_config.openai.api_key else None,
                "api_key_full": system_config.openai.api_key,  # 用於前端顯示
                "model": system_config.openai.model,
                "max_tokens": system_config.openai.max_tokens,
                "temperature": system_config.openai.temperature,
                "timeout": system_config.openai.timeout,
                "max_retries": system_config.openai.max_retries
            },
            "database_url": system_config.database_url,
            "redis_url": system_config.redis_url,
            "debug": system_config.debug
        }
    }

@app.post("/api/v1/system/config")
async def update_system_config(config_data: Dict[str, Any]):
    """更新系統配置"""
    try:
        if "openai" in config_data:
            openai_config = config_data["openai"]
            if "api_key" in openai_config:
                system_config.openai.api_key = openai_config["api_key"]
            if "model" in openai_config:
                system_config.openai.model = openai_config["model"]
            if "max_tokens" in openai_config:
                system_config.openai.max_tokens = openai_config["max_tokens"]
            if "temperature" in openai_config:
                system_config.openai.temperature = openai_config["temperature"]
            if "timeout" in openai_config:
                system_config.openai.timeout = openai_config["timeout"]
            if "max_retries" in openai_config:
                system_config.openai.max_retries = openai_config["max_retries"]
        
        if "database_url" in config_data:
            system_config.database_url = config_data["database_url"]
        
        if "redis_url" in config_data:
            system_config.redis_url = config_data["redis_url"]
        
        if "debug" in config_data:
            system_config.debug = config_data["debug"]
        
        return {
            "status": "success",
            "message": "系統配置已更新",
            "data": (await get_system_config())["data"]
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"配置更新失敗: {str(e)}")

async def test_openai_api_real(api_key: str, model: str = "gpt-4o-mini") -> Dict[str, Any]:
    """真實的OpenAI API測試"""
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": model,
        "messages": [
            {
                "role": "system", 
                "content": "You are a helpful assistant. Respond briefly."
            },
            {
                "role": "user", 
                "content": "Hello! This is a test. Please respond with 'Test successful' and the current model name."
            }
        ],
        "max_tokens": 50,
        "temperature": 0.3
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "https://api.openai.com/v1/chat/completions",
                headers=headers,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    return {
                        "success": True,
                        "model": result.get("model", model),
                        "response": result["choices"][0]["message"]["content"],
                        "usage": result.get("usage", {}),
                        "response_time": 0.5
                    }
                else:
                    error_text = await response.text()
                    return {
                        "success": False,
                        "error": f"API 錯誤 {response.status}: {error_text}",
                        "status_code": response.status
                    }
                    
    except asyncio.TimeoutError:
        return {
            "success": False,
            "error": "請求超時，請檢查網路連接"
        }
    except Exception as e:
        return {
            "success": False,
            "error": f"連接錯誤: {str(e)}"
        }

@app.post("/api/v1/openai/test-demo")
async def test_openai_demo():
    """演示OpenAI API測試功能"""
    return {
        "status": "success",
        "message": "演示：OpenAI API 連接測試成功",
        "data": {
            "model": "gpt-4o-mini",
            "response_time": 0.8,
            "test_response": "Test successful! 這是一個演示回應。實際測試需要真實的API密鑰。",
            "usage": {
                "prompt_tokens": 25,
                "completion_tokens": 15,
                "total_tokens": 40
            },
            "api_key_prefix": "sk-demo...",
            "note": "這是演示結果，請設置真實API密鑰進行實際測試"
        }
    }

@app.post("/api/v1/openai/test")
async def test_openai_connection():
    """測試OpenAI API連接"""
    if not system_config.openai.api_key:
        raise HTTPException(status_code=400, detail="OpenAI API Key 未設置")
    
    # 檢查是否為示例密鑰
    if "示例密鑰" in system_config.openai.api_key or "請替換" in system_config.openai.api_key:
        raise HTTPException(
            status_code=400, 
            detail="請設置您的真實 OpenAI API 密鑰。當前使用的是示例密鑰格式。"
        )
    
    # 檢查API Key格式
    if not system_config.openai.api_key.startswith("sk-"):
        raise HTTPException(status_code=400, detail="無效的 OpenAI API Key 格式，必須以 'sk-' 開頭")
    
    try:
        print(f"🧪 測試 OpenAI API: {system_config.openai.api_key[:8]}...")
        
        # 執行真實API測試
        test_result = await test_openai_api_real(
            system_config.openai.api_key, 
            system_config.openai.model
        )
        
        if test_result["success"]:
            return {
                "status": "success",
                "message": "OpenAI API 連接測試成功",
                "data": {
                    "model": test_result["model"],
                    "response_time": test_result["response_time"],
                    "test_response": test_result["response"],
                    "usage": test_result.get("usage", {}),
                    "api_key_prefix": system_config.openai.api_key[:8] + "..."
                }
            }
        else:
            raise HTTPException(
                status_code=400, 
                detail=f"OpenAI API 測試失敗: {test_result['error']}"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ OpenAI API 測試異常: {e}")
        raise HTTPException(status_code=500, detail=f"OpenAI API 測試失敗: {str(e)}")

@app.get("/api/v1/system/status")
async def get_system_status():
    """獲取系統狀態"""
    openai_status = "not_configured"
    if system_config.openai.api_key:
        if system_config.openai.api_key.startswith("sk-"):
            openai_status = "configured"
        else:
            openai_status = "invalid_key"
    
    return {
        "status": "success",
        "data": {
            "backend": {
                "status": "healthy",
                "version": "2.0.0",
                "uptime": 3600
            },
            "database": {
                "status": "connected",
                "type": "sqlite",
                "url": system_config.database_url
            },
            "redis": {
                "status": "disconnected",
                "url": system_config.redis_url
            },
            "openai": {
                "status": openai_status,
                "model": system_config.openai.model,
                "api_key_prefix": system_config.openai.api_key[:8] + "..." if system_config.openai.api_key else None
            }
        }
    }

@app.get("/api/v1/system/metrics")
async def get_system_metrics():
    """獲取系統性能指標"""
    import psutil
    
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            "status": "success",
            "data": {
                "cpu": {
                    "usage": cpu_percent,
                    "cores": psutil.cpu_count()
                },
                "memory": {
                    "usage": memory.percent,
                    "used": memory.used,
                    "total": memory.total
                },
                "disk": {
                    "usage": (disk.used / disk.total) * 100,
                    "used": disk.used,
                    "total": disk.total
                }
            }
        }
    except Exception as e:
        # 如果無法獲取系統指標，返回模擬數據
        return {
            "status": "success",
            "data": {
                "cpu": {
                    "usage": 45.2,
                    "cores": 8
                },
                "memory": {
                    "usage": 62.1,
                    "used": 3355443200,
                    "total": 8589934592
                },
                "disk": {
                    "usage": 31.2,
                    "used": 167503724544,
                    "total": 536870912000
                }
            }
        }

@app.post("/api/v1/system/backup")
async def backup_config():
    """備份系統配置"""
    try:
        backup_data = {
            "timestamp": time.time(),
            "config": system_config.dict()
        }
        return {
            "status": "success",
            "message": "配置備份已創建",
            "data": backup_data
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"備份失敗: {str(e)}")

@app.post("/api/v1/system/restore")
async def restore_config(backup_data: Dict[str, Any]):
    """還原系統配置"""
    try:
        if "config" in backup_data:
            config = backup_data["config"]
            system_config.openai = OpenAIConfig(**config.get("openai", {}))
            system_config.database_url = config.get("database_url", "sqlite:///./test.db")
            system_config.redis_url = config.get("redis_url", "redis://localhost:6379")
            system_config.debug = config.get("debug", True)
        
        return {
            "status": "success",
            "message": "配置已還原",
            "data": (await get_system_config())["data"]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"還原失敗: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    print("🚀 啟動增強版 AI SEO 服務器...")
    print(f"🔑 OpenAI API Key 狀態: {'已配置' if system_config.openai.api_key else '未配置'}")
    uvicorn.run(app, host="0.0.0.0", port=8000) 