#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI 意圖分類微服務
提供查詢意圖分類和主題提取功能
"""

from flask import Flask, request, jsonify
from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification
import torch
import logging
import os
from datetime import datetime
import json
import re
from typing import Dict, List, Tuple, Any

# 配置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

class IntentClassifier:
    """意圖分類器"""
    
    def __init__(self):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"使用設備: {self.device}")
        
        # 初始化模型
        self.classifier = None
        self.tokenizer = None
        self.model = None
        
        # 意圖類別定義
        self.intent_labels = [
            "product_research",      # 產品研究
            "seo_optimization",      # SEO優化
            "competitor_analysis",   # 競爭對手分析
            "pricing_inquiry",       # 價格查詢
            "technical_support",     # 技術支援
            "content_strategy",      # 內容策略
            "market_analysis",       # 市場分析
            "brand_monitoring",      # 品牌監控
            "keyword_research",      # 關鍵字研究
            "performance_tracking"   # 性能追蹤
        ]
        
        # 中文意圖標籤映射
        self.intent_labels_zh = {
            "product_research": "產品研究",
            "seo_optimization": "SEO優化", 
            "competitor_analysis": "競爭對手分析",
            "pricing_inquiry": "價格查詢",
            "technical_support": "技術支援",
            "content_strategy": "內容策略",
            "market_analysis": "市場分析",
            "brand_monitoring": "品牌監控",
            "keyword_research": "關鍵字研究",
            "performance_tracking": "性能追蹤"
        }
        
        self.load_models()
    
    def load_models(self):
        """載入預訓練模型"""
        try:
            # 使用 Facebook BART 進行零樣本分類
            self.classifier = pipeline(
                "zero-shot-classification",
                model="facebook/bart-large-mnli",
                device=0 if self.device == "cuda" else -1
            )
            
            logger.info("意圖分類模型載入成功")
            
        except Exception as e:
            logger.error(f"模型載入失敗: {e}")
            # 使用較小的模型作為備選
            try:
                self.classifier = pipeline(
                    "zero-shot-classification",
                    model="microsoft/DialoGPT-medium",
                    device=-1
                )
                logger.info("使用備選模型載入成功")
            except Exception as e2:
                logger.error(f"備選模型也載入失敗: {e2}")
    
    def preprocess_query(self, query: str) -> str:
        """預處理查詢文本"""
        # 移除特殊字符
        query = re.sub(r'[^\w\s\u4e00-\u9fff]', ' ', query)
        # 移除多餘空格
        query = re.sub(r'\s+', ' ', query).strip()
        # 轉換為小寫（保留中文）
        return query.lower()
    
    def classify_intent(self, query: str) -> Dict[str, Any]:
        """分類查詢意圖"""
        try:
            if not self.classifier:
                return self._fallback_classification(query)
            
            # 預處理查詢
            processed_query = self.preprocess_query(query)
            
            # 執行分類
            result = self.classifier(
                processed_query, 
                candidate_labels=self.intent_labels
            )
            
            # 處理結果
            top_intent = result['labels'][0]
            confidence = float(result['scores'][0])
            
            # 獲取所有分數
            all_scores = {
                label: float(score) 
                for label, score in zip(result['labels'], result['scores'])
            }
            
            return {
                'intent': top_intent,
                'intent_zh': self.intent_labels_zh.get(top_intent, top_intent),
                'confidence': confidence,
                'all_scores': all_scores,
                'processed_query': processed_query,
                'model_version': 'bart-large-mnli-v1.0'
            }
            
        except Exception as e:
            logger.error(f"意圖分類錯誤: {e}")
            return self._fallback_classification(query)
    
    def _fallback_classification(self, query: str) -> Dict[str, Any]:
        """備用分類方法（基於關鍵字）"""
        query_lower = query.lower()
        
        # 關鍵字模式匹配
        patterns = {
            'product_research': ['產品', '研究', '市場', '調研', 'product', 'research'],
            'seo_optimization': ['seo', '優化', '排名', '搜索引擎', 'optimization'],
            'competitor_analysis': ['競爭', '對手', '比較', 'competitor', 'analysis'],
            'pricing_inquiry': ['價格', '費用', '價錢', 'price', 'pricing', 'cost'],
            'technical_support': ['問題', '錯誤', '幫助', '支援', 'support', 'error', 'help'],
            'content_strategy': ['內容', '策略', '文案', 'content', 'strategy'],
            'market_analysis': ['市場', '分析', '趨勢', 'market', 'trend'],
            'brand_monitoring': ['品牌', '監控', '提及', 'brand', 'monitoring'],
            'keyword_research': ['關鍵字', '關鍵詞', 'keyword', 'search terms'],
            'performance_tracking': ['性能', '追蹤', '監測', 'performance', 'tracking']
        }
        
        scores = {}
        for intent, keywords in patterns.items():
            score = sum(1 for keyword in keywords if keyword in query_lower)
            scores[intent] = score / len(keywords)
        
        # 找出最高分的意圖
        best_intent = max(scores, key=scores.get)
        confidence = scores[best_intent]
        
        # 如果所有分數都很低，歸類為一般查詢
        if confidence < 0.1:
            best_intent = 'general_inquiry'
            confidence = 0.5
        
        return {
            'intent': best_intent,
            'intent_zh': self.intent_labels_zh.get(best_intent, '一般查詢'),
            'confidence': float(confidence),
            'all_scores': scores,
            'processed_query': query_lower,
            'model_version': 'keyword-based-v1.0'
        }

class TopicExtractor:
    """主題提取器"""
    
    def __init__(self):
        self.common_topics = [
            "AI人工智能", "SEO搜索引擎優化", "社交媒體營銷", "內容營銷",
            "數據分析", "用戶體驗", "移動應用", "電子商務",
            "品牌建設", "競爭情報", "市場趨勢", "技術創新"
        ]
    
    def extract_topics(self, query: str, max_topics: int = 5) -> List[Dict[str, Any]]:
        """提取查詢主題"""
        try:
            # 簡化的主題提取（基於關鍵字匹配）
            topics = []
            query_lower = query.lower()
            
            # 檢查預定義主題
            for topic in self.common_topics:
                topic_keywords = topic.lower().split()
                relevance = sum(1 for keyword in topic_keywords if keyword in query_lower)
                
                if relevance > 0:
                    topics.append({
                        'topic': topic,
                        'relevance_score': float(relevance / len(topic_keywords)),
                        'extraction_method': 'keyword_matching'
                    })
            
            # 按相關性排序
            topics.sort(key=lambda x: x['relevance_score'], reverse=True)
            
            return topics[:max_topics]
            
        except Exception as e:
            logger.error(f"主題提取錯誤: {e}")
            return []

# 初始化全局實例
intent_classifier = IntentClassifier()
topic_extractor = TopicExtractor()

@app.route('/health', methods=['GET'])
def health_check():
    """健康檢查端點"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'model_loaded': intent_classifier.classifier is not None
    })

@app.route('/classify-intent', methods=['POST'])
def classify_intent():
    """意圖分類端點"""
    try:
        data = request.get_json()
        
        if not data or 'query' not in data:
            return jsonify({
                'error': '缺少查詢參數'
            }), 400
        
        query = data['query']
        
        if not query or not isinstance(query, str):
            return jsonify({
                'error': '查詢必須是非空字符串'
            }), 400
        
        # 執行意圖分類
        result = intent_classifier.classify_intent(query)
        
        # 添加時間戳
        result['timestamp'] = datetime.now().isoformat()
        result['original_query'] = query
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"意圖分類 API 錯誤: {e}")
        return jsonify({
            'error': '服務器內部錯誤'
        }), 500

@app.route('/extract-topics', methods=['POST'])
def extract_topics():
    """主題提取端點"""
    try:
        data = request.get_json()
        
        if not data or 'query' not in data:
            return jsonify({
                'error': '缺少查詢參數'
            }), 400
        
        query = data['query']
        max_topics = data.get('max_topics', 5)
        
        # 執行主題提取
        topics = topic_extractor.extract_topics(query, max_topics)
        
        return jsonify({
            'success': True,
            'query': query,
            'topics': topics,
            'count': len(topics),
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"主題提取 API 錯誤: {e}")
        return jsonify({
            'error': '服務器內部錯誤'
        }), 500

@app.route('/analyze', methods=['POST'])
def analyze_comprehensive():
    """綜合分析端點（意圖分類 + 主題提取）"""
    try:
        data = request.get_json()
        
        if not data or 'query' not in data:
            return jsonify({
                'error': '缺少查詢參數'
            }), 400
        
        query = data['query']
        max_topics = data.get('max_topics', 5)
        
        # 執行意圖分類
        intent_result = intent_classifier.classify_intent(query)
        
        # 執行主題提取
        topics = topic_extractor.extract_topics(query, max_topics)
        
        # 組合結果
        result = {
            'success': True,
            'query': query,
            'intent': intent_result,
            'topics': topics,
            'timestamp': datetime.now().isoformat(),
            'analysis_id': f"analysis_{int(datetime.now().timestamp())}"
        }
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"綜合分析 API 錯誤: {e}")
        return jsonify({
            'error': '服務器內部錯誤'
        }), 500

@app.route('/batch-analyze', methods=['POST'])
def batch_analyze():
    """批量分析端點"""
    try:
        data = request.get_json()
        
        if not data or 'queries' not in data:
            return jsonify({
                'error': '缺少查詢列表參數'
            }), 400
        
        queries = data['queries']
        
        if not isinstance(queries, list):
            return jsonify({
                'error': '查詢必須是列表格式'
            }), 400
        
        if len(queries) > 100:
            return jsonify({
                'error': '批量處理最多支援100個查詢'
            }), 400
        
        results = []
        
        for i, query in enumerate(queries):
            try:
                # 意圖分類
                intent_result = intent_classifier.classify_intent(query)
                
                # 主題提取
                topics = topic_extractor.extract_topics(query)
                
                results.append({
                    'index': i,
                    'query': query,
                    'intent': intent_result,
                    'topics': topics
                })
                
            except Exception as e:
                logger.error(f"處理查詢 {i} 時錯誤: {e}")
                results.append({
                    'index': i,
                    'query': query,
                    'error': str(e)
                })
        
        return jsonify({
            'success': True,
            'total_queries': len(queries),
            'processed': len(results),
            'results': results,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"批量分析 API 錯誤: {e}")
        return jsonify({
            'error': '服務器內部錯誤'
        }), 500

@app.route('/model-info', methods=['GET'])
def model_info():
    """獲取模型信息"""
    return jsonify({
        'classifier_loaded': intent_classifier.classifier is not None,
        'device': intent_classifier.device,
        'intent_labels': intent_classifier.intent_labels,
        'intent_labels_zh': intent_classifier.intent_labels_zh,
        'available_topics': topic_extractor.common_topics,
        'model_version': 'v1.0.0',
        'last_loaded': datetime.now().isoformat()
    })

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('DEBUG', 'False').lower() == 'true'
    
    logger.info(f"啟動 AI 意圖分類服務，端口: {port}")
    
    app.run(
        host='0.0.0.0',
        port=port,
        debug=debug,
        threaded=True
    ) 