"""
數據庫配置和連接管理
"""

import asyncio
from typing import AsyncGenerator, Optional
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import structlog

from app.core.config import settings

logger = structlog.get_logger(__name__)

# 數據庫基類
Base = declarative_base()

# 異步引擎
async_engine: Optional[object] = None
AsyncSessionLocal: Optional[sessionmaker] = None

def get_database_url() -> str:
    """獲取數據庫連接 URL，優先使用 Supabase"""
    
    # 檢查 Supabase 數據庫配置
    if (settings.SUPABASE_DB_HOST and 
        settings.SUPABASE_DB_USER and 
        settings.SUPABASE_DB_PASSWORD):
        
        db_url = (f"postgresql+asyncpg://{settings.SUPABASE_DB_USER}:"
                 f"{settings.SUPABASE_DB_PASSWORD}@{settings.SUPABASE_DB_HOST}:"
                 f"{settings.SUPABASE_DB_PORT}/{settings.SUPABASE_DB_NAME}")
        
        logger.info("使用 Supabase 數據庫配置", host=settings.SUPABASE_DB_HOST)
        return db_url
    
    # 檢查是否有 SQLALCHEMY_DATABASE_URI
    if settings.SQLALCHEMY_DATABASE_URI:
        logger.info("使用配置的數據庫 URI")
        return str(settings.SQLALCHEMY_DATABASE_URI)
    
    # 使用本地 PostgreSQL（如果配置存在）
    if settings.POSTGRES_SERVER:
        if settings.POSTGRES_PASSWORD:
            db_url = (f"postgresql+asyncpg://{settings.POSTGRES_USER}:"
                     f"{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_SERVER}:"
                     f"{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}")
        else:
            db_url = (f"postgresql+asyncpg://{settings.POSTGRES_USER}@"
                     f"{settings.POSTGRES_SERVER}:{settings.POSTGRES_PORT}/"
                     f"{settings.POSTGRES_DB}")
        
        logger.info("使用本地 PostgreSQL 配置", server=settings.POSTGRES_SERVER)
        return db_url
    
    # 如果沒有任何數據庫配置，使用內存 SQLite（僅用於測試）
    logger.warning("未找到任何數據庫配置，使用內存數據庫（僅適用於測試）")
    return "sqlite+aiosqlite:///:memory:"

async def init_db() -> None:
    """初始化數據庫連接"""
    global async_engine, AsyncSessionLocal
    
    try:
        database_url = get_database_url()
        logger.info("正在初始化數據庫連接", url=database_url.split('@')[0] + '@***')
        
        # 創建異步引擎
        async_engine = create_async_engine(
            database_url,
            echo=settings.DEBUG,
            pool_pre_ping=True,
            pool_size=5,
            max_overflow=10,
            pool_timeout=30,
            pool_recycle=1800,
        )
        
        # 創建會話工廠
        AsyncSessionLocal = sessionmaker(
            bind=async_engine,
            class_=AsyncSession,
            expire_on_commit=False,
            autoflush=True,
            autocommit=False,
        )
        
        # 測試數據庫連接
        await test_connection()
        
        logger.info("數據庫連接初始化成功")
        
    except Exception as e:
        logger.error("數據庫連接初始化失敗", error=str(e))
        # 不要直接 raise，讓應用繼續運行但記錄錯誤
        async_engine = None
        AsyncSessionLocal = None

async def test_connection() -> bool:
    """測試數據庫連接"""
    if not async_engine:
        return False
        
    try:
        async with async_engine.begin() as conn:
            result = await conn.execute(text("SELECT 1"))
            result.fetchone()
            logger.info("數據庫連接測試成功")
            return True
    except Exception as e:
        logger.error("數據庫連接測試失敗", error=str(e))
        return False

async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """獲取數據庫會話"""
    if not AsyncSessionLocal:
        logger.warning("數據庫未初始化，返回空會話")
        # 創建一個空會話生成器而不是拋出異常
        class MockSession:
            async def __aenter__(self):
                return self
            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass
            async def rollback(self):
                pass
            async def close(self):
                pass
        
        yield MockSession()
        return
    
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            logger.error("數據庫會話錯誤", error=str(e))
            raise
        finally:
            await session.close()

# 別名函數，為了向後兼容
async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """獲取異步數據庫會話（別名函數）"""
    async for session in get_db():
        yield session

async def close_db() -> None:
    """關閉數據庫連接"""
    global async_engine, AsyncSessionLocal
    
    if async_engine:
        await async_engine.dispose()
        async_engine = None
        AsyncSessionLocal = None
        logger.info("數據庫連接已關閉")

def create_tables():
    """創建數據庫表（同步版本，用於遷移）"""
    try:
        database_url = get_database_url()
        # 將異步 URL 轉換為同步版本
        if "postgresql+asyncpg://" in database_url:
            sync_database_url = database_url.replace("postgresql+asyncpg://", "postgresql://")
        elif "sqlite+aiosqlite://" in database_url:
            sync_database_url = database_url.replace("sqlite+aiosqlite://", "sqlite://")
        else:
            sync_database_url = database_url
            
        sync_engine = create_engine(sync_database_url)
        Base.metadata.create_all(bind=sync_engine)
        logger.info("數據庫表創建成功")
        
    except Exception as e:
        logger.error("創建數據庫表失敗", error=str(e))
        raise

async def get_pool_status() -> dict:
    """獲取連接池狀態"""
    if not async_engine:
        return {"status": "not_initialized"}
    
    try:
        pool = async_engine.pool
        return {
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid(),
        }
    except Exception as e:
        logger.error("獲取連接池狀態失敗", error=str(e))
        return {"status": "error", "error": str(e)}
