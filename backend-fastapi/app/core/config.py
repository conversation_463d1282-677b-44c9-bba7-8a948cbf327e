"""
應用程式配置設定
"""

import secrets
from typing import Any, Dict, List, Optional, Union

from pydantic import AnyHttpUrl, EmailStr, HttpUrl, PostgresDsn, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """應用程式設定"""
    
    # 基本設定
    PROJECT_NAME: str = "AI SEO 優化王"
    VERSION: str = "2.0.0"
    DESCRIPTION: str = "新一代 AI 搜尋引擎優化平台"
    API_V1_STR: str = "/api/v1"
    
    # 服務器設定
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    DEBUG: bool = True
    
    # 安全設定
    SECRET_KEY: str = "dev-secret-key-for-testing-only"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    ALGORITHM: str = "HS256"
    
    # CORS 設定
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:3002",
        "http://localhost:3003",
        "http://localhost:3004",
        "https://localhost:3000",
        "https://localhost:3001",
        "https://localhost:3002",
        "https://localhost:3003",
        "https://localhost:3004",
    ]

    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> List[str]:
        if isinstance(v, str) and not v.startswith("["):
            origins = [i.strip().rstrip('/') for i in v.split(",")]
            return origins
        elif isinstance(v, list):
            return [str(origin).rstrip('/') for origin in v]
        elif isinstance(v, str):
            return [v.rstrip('/')]
        raise ValueError(v)

    # 數據庫設定 (本地 PostgreSQL 備用)
    POSTGRES_SERVER: Optional[str] = None
    POSTGRES_USER: Optional[str] = None
    POSTGRES_PASSWORD: Optional[str] = None
    POSTGRES_DB: Optional[str] = None
    POSTGRES_PORT: int = 5432
    
    # Supabase 設定
    SUPABASE_URL: Optional[str] = None
    SUPABASE_ANON_KEY: Optional[str] = None
    SUPABASE_SERVICE_ROLE_KEY: Optional[str] = None

    # 數據庫連接設定
    SQLALCHEMY_DATABASE_URI: Optional[str] = None

    # Supabase 數據庫連接設定
    SUPABASE_DB_HOST: Optional[str] = None
    SUPABASE_DB_PORT: int = 5432
    SUPABASE_DB_NAME: Optional[str] = None
    SUPABASE_DB_USER: Optional[str] = None
    SUPABASE_DB_PASSWORD: Optional[str] = None

    @validator("SQLALCHEMY_DATABASE_URI", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        # 如果已經有 SQLALCHEMY_DATABASE_URI 且不是測試 URL，使用它
        if isinstance(v, str) and not v.startswith("postgresql://test_user"):
            return v

        # 強制優先使用 Supabase 數據庫連接
        if values.get("SUPABASE_DB_HOST") and values.get("SUPABASE_DB_PASSWORD"):
            user = values.get("SUPABASE_DB_USER")
            password = values.get("SUPABASE_DB_PASSWORD")
            host = values.get("SUPABASE_DB_HOST")
            port = values.get("SUPABASE_DB_PORT", 5432)
            db_name = values.get("SUPABASE_DB_NAME", "postgres")
            url = f"postgresql+asyncpg://{user}:{password}@{host}:{port}/{db_name}?sslmode=require"
            print(f"📊 使用 Supabase 數據庫: {host}")
            return url

        # 檢查是否有直接的 DATABASE_URL（忽略測試 URL）
        database_url = values.get("DATABASE_URL")
        if database_url and not database_url.startswith("postgresql://test_user"):
            return database_url.replace("postgresql://", "postgresql+asyncpg://")

        # 使用本地 PostgreSQL（如果配置完整）
        if values.get("POSTGRES_SERVER"):
            user = values.get("POSTGRES_USER")
            password = values.get("POSTGRES_PASSWORD") or ""
            host = values.get("POSTGRES_SERVER")
            port = values.get("POSTGRES_PORT", 5432)
            db_name = values.get("POSTGRES_DB") or ""

            if password:
                return f"postgresql+asyncpg://{user}:{password}@{host}:{port}/{db_name}"
            else:
                return f"postgresql+asyncpg://{user}@{host}:{port}/{db_name}"
        
        # 如果沒有數據庫配置，使用內存 SQLite
        print("⚠️ 回退到 SQLite 內存數據庫")
        return "sqlite+aiosqlite:///:memory:"

    # Redis 設定
    REDIS_URL: Optional[str] = None
    REDIS_HOST: Optional[str] = None
    REDIS_PORT: Optional[int] = None
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    
    # 快取設定
    CACHE_TTL_DEFAULT: int = 300  # 5 分鐘
    CACHE_TTL_LINKS: int = 600    # 10 分鐘
    CACHE_TTL_SEO: int = 1800     # 30 分鐘
    CACHE_TTL_REPORTS: int = 3600 # 1 小時
    
    # 外部服務設定
    NEXTJS_FRONTEND_URL: str = "http://localhost:3004"
    EXPRESS_SERVICE_URL: str = "http://localhost:3001"
    
    # 連結驗證設定
    LINK_VALIDATION_TIMEOUT: int = 30
    LINK_VALIDATION_MAX_CONCURRENT: int = 10
    LINK_VALIDATION_RETRY_ATTEMPTS: int = 3
    LINK_VALIDATION_USER_AGENT: str = "AI-SEO-King-Bot/2.0"
    
    # SEO 分析設定
    SEO_ANALYSIS_TIMEOUT: int = 60
    SEO_MAX_PAGES_PER_ANALYSIS: int = 1000
    
    # 報告生成設定
    REPORT_MAX_SIZE_MB: int = 50
    REPORT_STORAGE_PATH: str = "/tmp/reports"
    
    # 監控設定
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090
    
    # 日誌設定
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"
    
    # Sentry 設定
    SENTRY_DSN: Optional[HttpUrl] = None
    
    # 郵件設定
    SMTP_TLS: bool = True
    SMTP_PORT: Optional[int] = None
    SMTP_HOST: Optional[str] = None
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    EMAILS_FROM_EMAIL: Optional[EmailStr] = None
    EMAILS_FROM_NAME: Optional[str] = None

    @validator("EMAILS_FROM_NAME")
    def get_project_name(cls, v: Optional[str], values: Dict[str, Any]) -> str:
        if not v:
            return values["PROJECT_NAME"]
        return v

    # 測試設定
    EMAIL_TEST_USER: EmailStr = "<EMAIL>"
    
    # 限流設定
    RATE_LIMIT_PER_MINUTE: int = 60
    RATE_LIMIT_BURST: int = 100
    
    # 背景任務設定
    CELERY_BROKER_URL: str = "redis://localhost:6379/1"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/2"
    
    # 檔案上傳設定
    MAX_UPLOAD_SIZE: int = 10485760  # 10MB
    ALLOWED_EXTENSIONS: List[str] = [".pdf", ".csv", ".xlsx", ".json"]
    UPLOAD_DIRECTORY: str = "uploads"
    UPLOAD_BASE_URL: str = "http://localhost:8000/uploads"
    
    @validator("ALLOWED_EXTENSIONS", pre=True)
    def parse_allowed_extensions(cls, v):
        """解析允許的文件擴展名"""
        if isinstance(v, str):
            # 如果是字符串，按逗號分割
            return [ext.strip() for ext in v.split(",") if ext.strip()]
        elif isinstance(v, list):
            return v
        else:
            # 返回默認值
            return [".pdf", ".csv", ".xlsx", ".json"]
    
    # 健康檢查設定
    HEALTH_CHECK_INTERVAL: int = 30
    
    # 環境變量
    ENVIRONMENT: str = "development"
    DATABASE_URL: Optional[str] = None

    # JWT 設定 - 256位強隨機密鑰
    JWT_SECRET_KEY: str = "dev-secret-key-for-testing-only"
    JWT_REFRESH_SECRET_KEY: str = "dev-refresh-secret-key-for-testing-only"
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 1440  # 24小時
    JWT_REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    JWT_ISSUER: str = "ai-seo-king"
    JWT_AUDIENCE: str = "ai-seo-king-users"

    # OpenAI 設定
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_MODEL: str = "gpt-4o-mini"
    OPENAI_MAX_TOKENS: int = 4000
    OPENAI_TEMPERATURE: float = 0.7
    OPENAI_TIMEOUT: int = 30000
    OPENAI_MAX_RETRIES: int = 3

    # 快取設定
    CACHE_TTL_EXTERNAL: int = 300
    CACHE_TTL_INTERNAL: int = 600
    CACHE_TTL_ASSETS: int = 1800
    CACHE_TTL_SEO: int = 3600

    # Elasticsearch 設定
    ELASTICSEARCH_URL: Optional[str] = None
    ELASTICSEARCH_USERNAME: Optional[str] = None
    ELASTICSEARCH_PASSWORD: Optional[str] = None
    ELASTICSEARCH_USE_SSL: bool = False
    ELASTICSEARCH_VERIFY_CERTS: bool = False
    ELASTICSEARCH_MAX_RETRIES: int = 3
    ELASTICSEARCH_TIMEOUT: int = 30
    
    # Elasticsearch 索引設定
    ES_SEO_CONTENT_INDEX: str = "seo_content"
    ES_SEO_ANALYSIS_INDEX: str = "seo_analysis"
    ES_SEO_KEYWORDS_INDEX: str = "seo_keywords"
    ES_SEO_COMPETITORS_INDEX: str = "seo_competitors"
    
    # 搜索設定
    SEARCH_DEFAULT_SIZE: int = 20
    SEARCH_MAX_SIZE: int = 100
    SEARCH_TIMEOUT: int = 5000  # 毫秒
    SEARCH_CACHE_TTL: int = 300  # 5 分鐘

    # 監控設定
    PROMETHEUS_ENABLED: bool = True
    HEALTH_CHECK_TIMEOUT: int = 10

    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "allow"


# 創建全域設定實例
settings = Settings()


def get_settings() -> Settings:
    """獲取應用程式設定"""
    return settings
