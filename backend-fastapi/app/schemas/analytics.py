"""
查詢記錄和統計分析 Schema
定義查詢統計、關鍵詞分析、趨勢分析等相關模型
"""

from typing import Dict, List, Any, Optional, Union
from datetime import datetime, date
from enum import Enum
from pydantic import BaseModel, Field, validator
from app.schemas.base import PaginatedResponse, SuccessResponse


class QuerySourceType(str, Enum):
    """查詢來源類型"""
    WEB_SEARCH = "web_search"
    API_REQUEST = "api_request"
    MOBILE_APP = "mobile_app"
    CUSTOMER_SERVICE = "customer_service"
    THIRD_PARTY = "third_party"
    SOCIAL_MEDIA = "social_media"
    SEO_TOOLS = "seo_tools"


class QueryIntentType(str, Enum):
    """查詢意圖類型"""
    INFORMATIONAL = "informational"
    NAVIGATIONAL = "navigational"
    TRANSACTIONAL = "transactional"
    COMMERCIAL = "commercial"
    LOCAL = "local"
    BRAND = "brand"
    QUESTION = "question"
    UNKNOWN = "unknown"


class StatisticsTimeframe(str, Enum):
    """統計時間範圍"""
    HOUR = "hour"
    DAY = "day"
    WEEK = "week"
    MONTH = "month"
    QUARTER = "quarter"
    YEAR = "year"


class TrendDirection(str, Enum):
    """趨勢方向"""
    UP = "up"
    DOWN = "down"
    STABLE = "stable"
    VOLATILE = "volatile"


# 查詢記錄相關Schema
class QueryRecordBase(BaseModel):
    """查詢記錄基礎模型"""
    query: str = Field(..., min_length=1, max_length=1000, description="查詢內容")
    normalized_query: Optional[str] = Field(None, max_length=1000, description="標準化後的查詢")
    source: QuerySourceType = Field(..., description="查詢來源")
    intent: Optional[QueryIntentType] = Field(None, description="查詢意圖")
    user_id: Optional[str] = Field(None, description="用戶ID")
    session_id: Optional[str] = Field(None, description="會話ID")
    ip_address: Optional[str] = Field(None, description="IP地址")
    user_agent: Optional[str] = Field(None, description="用戶代理")
    device_type: Optional[str] = Field(None, description="設備類型")
    language: Optional[str] = Field(None, description="語言")
    location: Optional[Dict[str, Any]] = Field(None, description="地理位置")
    response_time: Optional[float] = Field(None, ge=0, description="響應時間(毫秒)")
    results_count: Optional[int] = Field(None, ge=0, description="結果數量")
    clicked_results: Optional[List[Dict[str, Any]]] = Field(None, description="點擊的結果")
    metadata: Optional[Dict[str, Any]] = Field(None, description="額外元數據")


class QueryRecordCreate(QueryRecordBase):
    """創建查詢記錄"""
    timestamp: Optional[datetime] = Field(default_factory=datetime.utcnow, description="查詢時間")


class QueryRecordResponse(QueryRecordBase):
    """查詢記錄響應"""
    id: str
    timestamp: datetime
    processed_at: Optional[datetime] = None
    keywords: Optional[List[str]] = Field(None, description="提取的關鍵詞")
    topics: Optional[List[str]] = Field(None, description="相關主題")
    sentiment: Optional[Dict[str, Any]] = Field(None, description="情感分析")
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# 查詢統計Schema
class QueryStatsBase(BaseModel):
    """查詢統計基礎模型"""
    total_queries: int = Field(..., ge=0, description="總查詢數")
    unique_queries: int = Field(..., ge=0, description="唯一查詢數")
    unique_users: int = Field(..., ge=0, description="唯一用戶數")
    avg_response_time: float = Field(..., ge=0, description="平均響應時間")
    query_success_rate: float = Field(..., ge=0, le=100, description="查詢成功率")


class HourlyQueryStats(QueryStatsBase):
    """小時查詢統計"""
    hour: datetime = Field(..., description="統計小時")
    top_queries: List[Dict[str, Any]] = Field(default_factory=list, description="熱門查詢")
    source_distribution: Dict[str, int] = Field(default_factory=dict, description="來源分佈")
    intent_distribution: Dict[str, int] = Field(default_factory=dict, description="意圖分佈")
    device_distribution: Dict[str, int] = Field(default_factory=dict, description="設備分佈")


class DailyQueryStats(QueryStatsBase):
    """每日查詢統計"""
    date: date = Field(..., description="統計日期")
    peak_hour: int = Field(..., ge=0, le=23, description="高峰時段")
    hourly_distribution: List[int] = Field(default_factory=list, description="小時分佈")
    top_queries: List[Dict[str, Any]] = Field(default_factory=list, description="熱門查詢")
    source_distribution: Dict[str, int] = Field(default_factory=dict, description="來源分佈")
    intent_distribution: Dict[str, int] = Field(default_factory=dict, description="意圖分佈")
    geographic_distribution: Dict[str, int] = Field(default_factory=dict, description="地理分佈")


class WeeklyQueryStats(QueryStatsBase):
    """每週查詢統計"""
    week_start: date = Field(..., description="週開始日期")
    week_end: date = Field(..., description="週結束日期")
    daily_distribution: List[int] = Field(default_factory=list, description="每日分佈")
    growth_rate: float = Field(..., description="增長率")
    top_queries: List[Dict[str, Any]] = Field(default_factory=list, description="熱門查詢")
    trending_queries: List[Dict[str, Any]] = Field(default_factory=list, description="趨勢查詢")


class MonthlyQueryStats(QueryStatsBase):
    """每月查詢統計"""
    year: int = Field(..., ge=2020, description="年份")
    month: int = Field(..., ge=1, le=12, description="月份")
    weekly_distribution: List[int] = Field(default_factory=list, description="每週分佈")
    growth_rate: float = Field(..., description="增長率")
    seasonal_trends: Dict[str, Any] = Field(default_factory=dict, description="季節性趨勢")
    top_queries: List[Dict[str, Any]] = Field(default_factory=list, description="熱門查詢")


# 關鍵詞分析Schema
class KeywordMetrics(BaseModel):
    """關鍵詞指標"""
    keyword: str = Field(..., description="關鍵詞")
    frequency: int = Field(..., ge=0, description="出現頻率")
    unique_sessions: int = Field(..., ge=0, description="唯一會話數")
    avg_position: Optional[float] = Field(None, description="平均位置")
    click_through_rate: Optional[float] = Field(None, ge=0, le=100, description="點擊率")
    conversion_rate: Optional[float] = Field(None, ge=0, le=100, description="轉換率")
    search_volume: Optional[int] = Field(None, ge=0, description="搜索量")
    competition: Optional[float] = Field(None, ge=0, le=1, description="競爭度")
    cpc: Optional[float] = Field(None, ge=0, description="每次點擊成本")
    trend_direction: Optional[TrendDirection] = Field(None, description="趨勢方向")
    related_keywords: List[str] = Field(default_factory=list, description="相關關鍵詞")


class KeywordAnalysisResult(BaseModel):
    """關鍵詞分析結果"""
    analysis_id: str = Field(..., description="分析ID")
    timeframe: StatisticsTimeframe = Field(..., description="時間範圍")
    start_date: datetime = Field(..., description="開始時間")
    end_date: datetime = Field(..., description="結束時間")
    total_keywords: int = Field(..., ge=0, description="總關鍵詞數")
    top_keywords: List[KeywordMetrics] = Field(..., description="熱門關鍵詞")
    rising_keywords: List[KeywordMetrics] = Field(default_factory=list, description="上升關鍵詞")
    declining_keywords: List[KeywordMetrics] = Field(default_factory=list, description="下降關鍵詞")
    keyword_clusters: List[Dict[str, Any]] = Field(default_factory=list, description="關鍵詞聚類")
    semantic_groups: List[Dict[str, Any]] = Field(default_factory=list, description="語義分組")
    intent_distribution: Dict[str, int] = Field(default_factory=dict, description="意圖分佈")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="創建時間")


class KeywordTrendData(BaseModel):
    """關鍵詞趨勢數據"""
    keyword: str = Field(..., description="關鍵詞")
    data_points: List[Dict[str, Any]] = Field(..., description="趨勢數據點")
    trend_direction: TrendDirection = Field(..., description="趨勢方向")
    growth_rate: float = Field(..., description="增長率")
    volatility: float = Field(..., ge=0, description="波動性")
    seasonal_pattern: Optional[Dict[str, Any]] = Field(None, description="季節性模式")
    forecasts: Optional[List[Dict[str, Any]]] = Field(None, description="預測數據")


# 趨勢分析Schema
class TrendAnalysisRequest(BaseModel):
    """趨勢分析請求"""
    keywords: Optional[List[str]] = Field(None, description="特定關鍵詞")
    sources: Optional[List[QuerySourceType]] = Field(None, description="指定來源")
    timeframe: StatisticsTimeframe = Field(StatisticsTimeframe.WEEK, description="時間範圍")
    start_date: Optional[datetime] = Field(None, description="開始時間")
    end_date: Optional[datetime] = Field(None, description="結束時間")
    include_predictions: bool = Field(False, description="包含預測")
    min_frequency: int = Field(10, ge=1, description="最小頻率")

    @validator("end_date")
    def validate_date_range(cls, v, values):
        if v and values.get("start_date") and v <= values["start_date"]:
            raise ValueError("結束時間必須晚於開始時間")
        return v


class TrendAnalysisResult(BaseModel):
    """趨勢分析結果"""
    analysis_id: str = Field(..., description="分析ID")
    timeframe: StatisticsTimeframe = Field(..., description="時間範圍")
    start_date: datetime = Field(..., description="開始時間")
    end_date: datetime = Field(..., description="結束時間")
    overall_trend: TrendDirection = Field(..., description="整體趨勢")
    growth_rate: float = Field(..., description="增長率")
    keyword_trends: List[KeywordTrendData] = Field(..., description="關鍵詞趨勢")
    emerging_topics: List[Dict[str, Any]] = Field(default_factory=list, description="新興主題")
    declining_topics: List[Dict[str, Any]] = Field(default_factory=list, description="下降主題")
    seasonal_insights: Dict[str, Any] = Field(default_factory=dict, description="季節性洞察")
    anomalies: List[Dict[str, Any]] = Field(default_factory=list, description="異常檢測")
    predictions: Optional[List[Dict[str, Any]]] = Field(None, description="趨勢預測")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="創建時間")


# 實時統計Schema
class RealTimeStats(BaseModel):
    """實時統計"""
    current_qps: float = Field(..., ge=0, description="當前每秒查詢數")
    active_sessions: int = Field(..., ge=0, description="活躍會話數")
    top_queries_last_hour: List[Dict[str, Any]] = Field(..., description="最近一小時熱門查詢")
    source_distribution: Dict[str, int] = Field(..., description="來源分佈")
    geographic_distribution: Dict[str, int] = Field(..., description="地理分佈")
    alert_threshold_breached: List[Dict[str, Any]] = Field(default_factory=list, description="超過閾值的警報")
    last_updated: datetime = Field(default_factory=datetime.utcnow, description="最後更新時間")


# 報告Schema
class AnalyticsReportRequest(BaseModel):
    """分析報告請求"""
    report_type: str = Field(..., description="報告類型")
    timeframe: StatisticsTimeframe = Field(..., description="時間範圍")
    start_date: datetime = Field(..., description="開始時間")
    end_date: datetime = Field(..., description="結束時間")
    include_keywords: bool = Field(True, description="包含關鍵詞分析")
    include_trends: bool = Field(True, description="包含趨勢分析")
    include_geographic: bool = Field(False, description="包含地理分析")
    format: str = Field("json", description="報告格式")
    email_to: Optional[List[str]] = Field(None, description="郵件發送至")


class AnalyticsReportResponse(BaseModel):
    """分析報告響應"""
    report_id: str = Field(..., description="報告ID")
    status: str = Field(..., description="報告狀態")
    download_url: Optional[str] = Field(None, description="下載連結")
    created_at: datetime = Field(..., description="創建時間")
    completed_at: Optional[datetime] = Field(None, description="完成時間")


# 配置Schema
class AnalyticsConfig(BaseModel):
    """分析配置"""
    retention_days: int = Field(365, ge=1, description="數據保留天數")
    sampling_rate: float = Field(1.0, ge=0.1, le=1.0, description="採樣率")
    enable_real_time: bool = Field(True, description="啟用實時統計")
    alert_thresholds: Dict[str, Any] = Field(default_factory=dict, description="警報閾值")
    auto_report_schedule: Optional[Dict[str, Any]] = Field(None, description="自動報告計劃")


# 響應包裝器
class QueryRecordListResponse(PaginatedResponse):
    """查詢記錄列表響應"""
    items: List[QueryRecordResponse]


class QueryStatsResponse(SuccessResponse):
    """查詢統計響應"""
    data: Union[HourlyQueryStats, DailyQueryStats, WeeklyQueryStats, MonthlyQueryStats]


class KeywordAnalysisResponse(SuccessResponse):
    """關鍵詞分析響應"""
    data: KeywordAnalysisResult


class TrendAnalysisResponse(SuccessResponse):
    """趨勢分析響應"""
    data: TrendAnalysisResult


class RealTimeStatsResponse(SuccessResponse):
    """實時統計響應"""
    data: RealTimeStats


# Dashboard統計Schema
class AnalyticsDashboardStats(BaseModel):
    """分析儀表板統計"""
    total_queries_today: int = Field(..., description="今日總查詢數")
    total_queries_yesterday: int = Field(..., description="昨日總查詢數")
    growth_rate_daily: float = Field(..., description="日增長率")
    unique_users_today: int = Field(..., description="今日唯一用戶數")
    avg_response_time: float = Field(..., description="平均響應時間")
    top_sources: List[Dict[str, Any]] = Field(..., description="主要來源")
    top_keywords: List[Dict[str, Any]] = Field(..., description="熱門關鍵詞")
    recent_trends: List[Dict[str, Any]] = Field(..., description="最近趨勢")
    alerts: List[Dict[str, Any]] = Field(default_factory=list, description="系統警報")
    last_updated: datetime = Field(default_factory=datetime.utcnow, description="最後更新時間") 