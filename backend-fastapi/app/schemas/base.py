"""
基礎 Pydantic 模型和實用工具
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Generic, TypeVar
from enum import Enum
import uuid

from pydantic import BaseModel, Field, validator


class ResponseStatus(str, Enum):
    """響應狀態枚舉"""
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"


class TaskStatus(str, Enum):
    """任務狀態枚舉"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class Priority(int, Enum):
    """優先級枚舉"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


class Status(str, Enum):
    """狀態枚舉"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"


class BaseResponse(BaseModel):
    """基礎響應模型"""
    success: bool = Field(..., description="操作是否成功")
    message: Optional[str] = Field(None, description="響應消息")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="響應時間戳")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() + "Z"
        }


class SuccessResponse(BaseResponse):
    """成功響應模型"""
    success: bool = Field(True, description="操作成功")
    data: Optional[Any] = Field(None, description="響應數據")


class ErrorResponse(BaseResponse):
    """錯誤響應模型"""
    success: bool = Field(False, description="操作失敗")
    error: Dict[str, Any] = Field(..., description="錯誤信息")


class ErrorDetail(BaseModel):
    """錯誤詳情模型"""
    code: str = Field(..., description="錯誤代碼")
    message: str = Field(..., description="錯誤消息")
    details: Optional[Dict[str, Any]] = Field(None, description="錯誤詳情")


class PaginationParams(BaseModel):
    """分頁參數模型"""
    page: int = Field(1, ge=1, description="頁碼")
    limit: int = Field(20, ge=1, le=100, description="每頁數量")
    
    @property
    def offset(self) -> int:
        """計算偏移量"""
        return (self.page - 1) * self.limit


class PaginationResponse(BaseModel):
    """分頁響應模型"""
    page: int = Field(..., description="當前頁碼")
    limit: int = Field(..., description="每頁數量")
    total: int = Field(..., description="總記錄數")
    pages: int = Field(..., description="總頁數")
    has_next: bool = Field(..., description="是否有下一頁")
    has_prev: bool = Field(..., description="是否有上一頁")
    
    @validator("pages", pre=True, always=True)
    def calculate_pages(cls, v, values):
        """計算總頁數"""
        total = values.get("total", 0)
        limit = values.get("limit", 20)
        return (total + limit - 1) // limit if total > 0 else 0
    
    @validator("has_next", pre=True, always=True)
    def calculate_has_next(cls, v, values):
        """計算是否有下一頁"""
        page = values.get("page", 1)
        pages = values.get("pages", 0)
        return page < pages
    
    @validator("has_prev", pre=True, always=True)
    def calculate_has_prev(cls, v, values):
        """計算是否有上一頁"""
        page = values.get("page", 1)
        return page > 1


class PaginatedResponse(SuccessResponse):
    """分頁數據響應模型"""
    data: List[Any] = Field(..., description="數據列表")
    pagination: PaginationResponse = Field(..., description="分頁信息")


class HealthCheckResponse(BaseModel):
    """健康檢查響應模型"""
    status: str = Field(..., description="服務狀態")
    service: str = Field(..., description="服務名稱")
    version: str = Field(..., description="服務版本")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="檢查時間")
    dependencies: Optional[Dict[str, str]] = Field(None, description="依賴服務狀態")
    uptime: Optional[float] = Field(None, description="運行時間（秒）")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() + "Z"
        }


class TaskProgress(BaseModel):
    """任務進度模型"""
    total: int = Field(..., ge=0, description="總任務數")
    completed: int = Field(..., ge=0, description="已完成數")
    failed: int = Field(0, ge=0, description="失敗數")
    percentage: float = Field(..., ge=0, le=100, description="完成百分比")
    
    @validator("percentage", pre=True, always=True)
    def calculate_percentage(cls, v, values):
        """計算完成百分比"""
        total = values.get("total", 0)
        completed = values.get("completed", 0)
        if total == 0:
            return 0.0
        return round((completed / total) * 100, 2)


class TaskInfo(BaseModel):
    """任務信息模型"""
    task_id: str = Field(..., description="任務 ID")
    task_type: str = Field(..., description="任務類型")
    status: TaskStatus = Field(..., description="任務狀態")
    priority: Priority = Field(Priority.MEDIUM, description="任務優先級")
    progress: Optional[TaskProgress] = Field(None, description="任務進度")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="創建時間")
    started_at: Optional[datetime] = Field(None, description="開始時間")
    completed_at: Optional[datetime] = Field(None, description="完成時間")
    error_message: Optional[str] = Field(None, description="錯誤消息")
    result: Optional[Dict[str, Any]] = Field(None, description="任務結果")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() + "Z"
        }


class FileInfo(BaseModel):
    """檔案信息模型"""
    file_id: str = Field(..., description="檔案 ID")
    filename: str = Field(..., description="檔案名稱")
    size: int = Field(..., ge=0, description="檔案大小（字節）")
    content_type: str = Field(..., description="檔案類型")
    url: Optional[str] = Field(None, description="檔案 URL")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="創建時間")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() + "Z"
        }


class MetricsData(BaseModel):
    """指標數據模型"""
    name: str = Field(..., description="指標名稱")
    value: Union[int, float] = Field(..., description="指標值")
    unit: Optional[str] = Field(None, description="單位")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="時間戳")
    labels: Optional[Dict[str, str]] = Field(None, description="標籤")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() + "Z"
        }


class CacheInfo(BaseModel):
    """快取信息模型"""
    key: str = Field(..., description="快取鍵")
    ttl: int = Field(..., description="剩餘生存時間（秒）")
    size: Optional[int] = Field(None, description="數據大小（字節）")
    hit_count: Optional[int] = Field(None, description="命中次數")
    created_at: Optional[datetime] = Field(None, description="創建時間")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() + "Z"
        }


class ValidationError(BaseModel):
    """驗證錯誤模型"""
    field: str = Field(..., description="錯誤字段")
    message: str = Field(..., description="錯誤消息")
    type: str = Field(..., description="錯誤類型")
    value: Optional[Any] = Field(None, description="錯誤值")


class BulkOperationResult(BaseModel):
    """批量操作結果模型"""
    total: int = Field(..., ge=0, description="總操作數")
    success: int = Field(..., ge=0, description="成功數")
    failed: int = Field(..., ge=0, description="失敗數")
    errors: List[ValidationError] = Field(default_factory=list, description="錯誤列表")
    
    @validator("failed", pre=True, always=True)
    def calculate_failed(cls, v, values):
        """計算失敗數"""
        total = values.get("total", 0)
        success = values.get("success", 0)
        return total - success


# 泛型數據類型
DataT = TypeVar('DataT')

class BaseSchema(BaseModel):
    """基礎 Schema 類"""
    class Config:
        from_attributes = True
        use_enum_values = True

class TimestampMixin(BaseModel):
    """時間戳混入類"""
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class IDMixin(BaseModel):
    """ID 混入類"""
    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()))

DataT = TypeVar('DataT')

class ResponseModel(BaseModel, Generic[DataT]):
    """統一響應模型"""
    success: bool = Field(True, description="操作是否成功")
    message: str = Field("", description="響應消息")
    data: Optional[DataT] = Field(None, description="響應數據")
    error: Optional[str] = Field(None, description="錯誤信息")
    
    class Config:
        from_attributes = True

class SortParams(BaseModel):
    """排序參數"""
    sort_by: str = Field("created_at", description="排序字段")
    sort_order: str = Field("desc", pattern="^(asc|desc)$", description="排序方向")

class FilterBase(BaseModel):
    """基礎過濾器"""
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    status: Optional[Status] = None

class HealthCheck(BaseModel):
    """健康檢查響應"""
    status: str
    timestamp: datetime
    version: str
    uptime: Optional[float] = None
