"""
數據收集管道監控儀表板 API
提供實時監控數據和統計分析功能
"""

from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.dependencies import get_current_user
from app.models.user import User
from app.services.data_collection_pipeline import get_data_collection_pipeline
from app.core.redis import get_redis
import json
import structlog

logger = structlog.get_logger()
router = APIRouter()


class DashboardMetrics(BaseModel):
    """儀表板指標"""
    total_queries_processed: int
    queries_per_hour: float
    avg_processing_time: float
    error_rate: float
    top_query_sources: List[Dict[str, Any]]
    top_query_types: List[Dict[str, Any]]
    processing_pipeline_stats: Dict[str, Any]
    system_health: Dict[str, Any]


class TimeSeriesData(BaseModel):
    """時間序列數據"""
    timestamp: datetime
    value: float
    label: Optional[str] = None


class AlertConfig(BaseModel):
    """告警配置"""
    metric_name: str
    threshold: float
    operator: str  # "gt", "lt", "eq"
    enabled: bool = True
    notification_channels: List[str] = []


@router.get("/overview", response_model=DashboardMetrics)
async def get_dashboard_overview(
    current_user: User = Depends(get_current_user),
    hours: int = Query(24, ge=1, le=168, description="時間範圍（小時）")
) -> DashboardMetrics:
    """獲取儀表板概覽數據"""
    try:
        pipeline = await get_data_collection_pipeline()
        redis_client = await get_redis()
        
        # 獲取管道基礎指標
        metrics = await pipeline.get_pipeline_metrics()
        
        # 計算處理率
        time_window = timedelta(hours=hours)
        queries_per_hour = metrics.get('total_processed', 0) / hours if hours > 0 else 0
        
        # 計算錯誤率
        total_processed = metrics.get('total_processed', 0)
        total_errors = metrics.get('total_errors', 0)
        error_rate = (total_errors / total_processed * 100) if total_processed > 0 else 0
        
        # 獲取平均處理時間
        processing_latency = metrics.get('processing_latency', [])
        avg_processing_time = sum(processing_latency) / len(processing_latency) if processing_latency else 0
        
        # 獲取熱門查詢來源統計
        top_sources = await _get_top_query_sources(redis_client, hours)
        
        # 獲取熱門查詢類型統計
        top_types = await _get_top_query_types(redis_client, hours)
        
        # 獲取處理管道統計
        pipeline_stats = await _get_pipeline_stats(pipeline)
        
        # 獲取系統健康狀態
        system_health = await _get_system_health(pipeline)
        
        return DashboardMetrics(
            total_queries_processed=total_processed,
            queries_per_hour=queries_per_hour,
            avg_processing_time=avg_processing_time,
            error_rate=error_rate,
            top_query_sources=top_sources,
            top_query_types=top_types,
            processing_pipeline_stats=pipeline_stats,
            system_health=system_health
        )
        
    except Exception as e:
        logger.error("獲取儀表板概覽失敗", error=str(e))
        raise HTTPException(status_code=500, detail="無法獲取儀表板數據")


@router.get("/timeseries/throughput")
async def get_throughput_timeseries(
    current_user: User = Depends(get_current_user),
    hours: int = Query(24, ge=1, le=168),
    interval: str = Query("1h", regex="^(5m|15m|1h|6h|1d)$")
) -> List[TimeSeriesData]:
    """獲取吞吐量時間序列數據"""
    try:
        redis_client = await get_redis()
        
        # 計算時間間隔
        intervals = _calculate_time_intervals(hours, interval)
        
        timeseries_data = []
        for start_time, end_time in intervals:
            # 從 Redis 獲取該時間段的處理數量
            key = f"pipeline_throughput:{start_time.strftime('%Y%m%d%H%M')}"
            count = await redis_client.get(key)
            count = int(count) if count else 0
            
            timeseries_data.append(TimeSeriesData(
                timestamp=start_time,
                value=count,
                label=f"{start_time.strftime('%H:%M')}-{end_time.strftime('%H:%M')}"
            ))
        
        return timeseries_data
        
    except Exception as e:
        logger.error("獲取吞吐量時間序列失敗", error=str(e))
        raise HTTPException(status_code=500, detail="無法獲取吞吐量數據")


@router.get("/timeseries/latency")
async def get_latency_timeseries(
    current_user: User = Depends(get_current_user),
    hours: int = Query(24, ge=1, le=168),
    interval: str = Query("1h", regex="^(5m|15m|1h|6h|1d)$")
) -> List[TimeSeriesData]:
    """獲取延遲時間序列數據"""
    try:
        redis_client = await get_redis()
        
        intervals = _calculate_time_intervals(hours, interval)
        
        timeseries_data = []
        for start_time, end_time in intervals:
            # 從 Redis 獲取該時間段的平均延遲
            key = f"pipeline_latency:{start_time.strftime('%Y%m%d%H%M')}"
            latency = await redis_client.get(key)
            latency = float(latency) if latency else 0
            
            timeseries_data.append(TimeSeriesData(
                timestamp=start_time,
                value=latency,
                label=f"平均延遲 {latency:.2f}ms"
            ))
        
        return timeseries_data
        
    except Exception as e:
        logger.error("獲取延遲時間序列失敗", error=str(e))
        raise HTTPException(status_code=500, detail="無法獲取延遲數據")


@router.get("/timeseries/errors")
async def get_errors_timeseries(
    current_user: User = Depends(get_current_user),
    hours: int = Query(24, ge=1, le=168),
    interval: str = Query("1h", regex="^(5m|15m|1h|6h|1d)$")
) -> List[TimeSeriesData]:
    """獲取錯誤率時間序列數據"""
    try:
        redis_client = await get_redis()
        
        intervals = _calculate_time_intervals(hours, interval)
        
        timeseries_data = []
        for start_time, end_time in intervals:
            # 從 Redis 獲取該時間段的錯誤數量
            error_key = f"pipeline_errors:{start_time.strftime('%Y%m%d%H%M')}"
            total_key = f"pipeline_throughput:{start_time.strftime('%Y%m%d%H%M')}"
            
            errors = await redis_client.get(error_key)
            total = await redis_client.get(total_key)
            
            errors = int(errors) if errors else 0
            total = int(total) if total else 0
            
            error_rate = (errors / total * 100) if total > 0 else 0
            
            timeseries_data.append(TimeSeriesData(
                timestamp=start_time,
                value=error_rate,
                label=f"錯誤率 {error_rate:.1f}%"
            ))
        
        return timeseries_data
        
    except Exception as e:
        logger.error("獲取錯誤率時間序列失敗", error=str(e))
        raise HTTPException(status_code=500, detail="無法獲取錯誤數據")


@router.get("/collectors/stats")
async def get_collectors_detailed_stats(
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """獲取收集器詳細統計"""
    try:
        pipeline = await get_data_collection_pipeline()
        redis_client = await get_redis()
        
        collectors_stats = {}
        
        for name, collector in pipeline.collectors.items():
            # 獲取收集器基礎統計
            base_stats = getattr(collector, 'stats', {})
            
            # 從 Redis 獲取詳細統計
            stats_key = f"collector_stats:{name}"
            redis_stats = await redis_client.hgetall(stats_key)
            
            # 解析 Redis 數據
            redis_stats = {k.decode(): v.decode() for k, v in redis_stats.items()} if redis_stats else {}
            
            collectors_stats[name] = {
                **base_stats,
                "requests_per_minute": float(redis_stats.get("requests_per_minute", 0)),
                "success_rate": float(redis_stats.get("success_rate", 0)),
                "avg_response_time": float(redis_stats.get("avg_response_time", 0)),
                "last_activity": redis_stats.get("last_activity"),
                "total_requests": int(redis_stats.get("total_requests", 0)),
                "total_errors": int(redis_stats.get("total_errors", 0))
            }
        
        return {
            "collectors": collectors_stats,
            "summary": {
                "total_collectors": len(collectors_stats),
                "active_collectors": len([c for c in collectors_stats.values() if c.get("requests_per_minute", 0) > 0]),
                "total_requests": sum(c.get("total_requests", 0) for c in collectors_stats.values()),
                "overall_success_rate": _calculate_overall_success_rate(collectors_stats)
            }
        }
        
    except Exception as e:
        logger.error("獲取收集器統計失敗", error=str(e))
        raise HTTPException(status_code=500, detail="無法獲取收集器統計")


@router.get("/processors/performance")
async def get_processors_performance(
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """獲取處理器性能統計"""
    try:
        pipeline = await get_data_collection_pipeline()
        redis_client = await get_redis()
        
        processors_performance = []
        
        for processor in pipeline.processors:
            processor_name = processor.__class__.__name__
            
            # 從 Redis 獲取處理器統計
            stats_key = f"processor_stats:{processor_name}"
            redis_stats = await redis_client.hgetall(stats_key)
            redis_stats = {k.decode(): v.decode() for k, v in redis_stats.items()} if redis_stats else {}
            
            performance = {
                "name": processor_name,
                "enabled": getattr(processor, 'enabled', True),
                "processed_count": int(redis_stats.get("processed_count", 0)),
                "avg_processing_time": float(redis_stats.get("avg_processing_time", 0)),
                "error_count": int(redis_stats.get("error_count", 0)),
                "success_rate": float(redis_stats.get("success_rate", 100)),
                "memory_usage": float(redis_stats.get("memory_usage", 0)),
                "cpu_usage": float(redis_stats.get("cpu_usage", 0))
            }
            
            processors_performance.append(performance)
        
        return {
            "processors": processors_performance,
            "pipeline_summary": {
                "total_stages": len(processors_performance),
                "overall_throughput": sum(p["processed_count"] for p in processors_performance),
                "bottleneck_stage": _identify_bottleneck(processors_performance),
                "overall_success_rate": _calculate_pipeline_success_rate(processors_performance)
            }
        }
        
    except Exception as e:
        logger.error("獲取處理器性能失敗", error=str(e))
        raise HTTPException(status_code=500, detail="無法獲取處理器性能")


@router.get("/alerts")
async def get_active_alerts(
    current_user: User = Depends(get_current_user)
) -> List[Dict[str, Any]]:
    """獲取活躍告警"""
    try:
        redis_client = await get_redis()
        
        # 從 Redis 獲取活躍告警
        alerts_key = "pipeline_alerts:active"
        active_alerts = await redis_client.lrange(alerts_key, 0, -1)
        
        alerts = []
        for alert_data in active_alerts:
            try:
                alert = json.loads(alert_data.decode())
                alerts.append(alert)
            except:
                continue
        
        # 按嚴重程度排序
        severity_order = {"critical": 0, "warning": 1, "info": 2}
        alerts.sort(key=lambda x: severity_order.get(x.get("severity", "info"), 3))
        
        return alerts
        
    except Exception as e:
        logger.error("獲取告警失敗", error=str(e))
        raise HTTPException(status_code=500, detail="無法獲取告警信息")


@router.post("/alerts/config")
async def configure_alert(
    alert_config: AlertConfig,
    current_user: User = Depends(get_current_user)
) -> Dict[str, str]:
    """配置告警規則"""
    try:
        redis_client = await get_redis()
        
        # 保存告警配置到 Redis
        config_key = f"alert_config:{alert_config.metric_name}"
        config_data = alert_config.dict()
        config_data["created_by"] = str(current_user.id)
        config_data["created_at"] = datetime.utcnow().isoformat()
        
        await redis_client.set(config_key, json.dumps(config_data))
        
        logger.info("告警規則已配置", 
                   metric=alert_config.metric_name,
                   threshold=alert_config.threshold,
                   user_id=current_user.id)
        
        return {"message": "告警規則配置成功", "metric": alert_config.metric_name}
        
    except Exception as e:
        logger.error("配置告警失敗", error=str(e), user_id=current_user.id)
        raise HTTPException(status_code=500, detail="無法配置告警規則")


@router.get("/export/metrics")
async def export_metrics(
    current_user: User = Depends(get_current_user),
    format: str = Query("json", regex="^(json|csv|prometheus)$"),
    hours: int = Query(24, ge=1, le=168)
) -> Dict[str, Any]:
    """導出指標數據"""
    try:
        pipeline = await get_data_collection_pipeline()
        redis_client = await get_redis()
        
        # 獲取完整指標
        metrics = await pipeline.get_pipeline_metrics()
        
        # 獲取時間序列數據
        throughput_data = await get_throughput_timeseries(current_user, hours, "1h")
        latency_data = await get_latency_timeseries(current_user, hours, "1h")
        error_data = await get_errors_timeseries(current_user, hours, "1h")
        
        export_data = {
            "metadata": {
                "export_time": datetime.utcnow().isoformat(),
                "time_range_hours": hours,
                "format": format,
                "exported_by": str(current_user.id)
            },
            "summary_metrics": metrics,
            "timeseries": {
                "throughput": [{"timestamp": d.timestamp.isoformat(), "value": d.value} for d in throughput_data],
                "latency": [{"timestamp": d.timestamp.isoformat(), "value": d.value} for d in latency_data],
                "errors": [{"timestamp": d.timestamp.isoformat(), "value": d.value} for d in error_data]
            }
        }
        
        logger.info("指標數據已導出", format=format, hours=hours, user_id=current_user.id)
        
        return export_data
        
    except Exception as e:
        logger.error("導出指標失敗", error=str(e), user_id=current_user.id)
        raise HTTPException(status_code=500, detail="無法導出指標數據")


# 輔助函數

async def _get_top_query_sources(redis_client, hours: int) -> List[Dict[str, Any]]:
    """獲取熱門查詢來源"""
    key = f"query_sources_stats:{hours}h"
    sources_data = await redis_client.hgetall(key)
    
    if not sources_data:
        return []
    
    sources = []
    for source, count in sources_data.items():
        sources.append({
            "source": source.decode(),
            "count": int(count.decode()),
            "percentage": 0  # 將在後面計算
        })
    
    # 計算百分比
    total = sum(s["count"] for s in sources)
    if total > 0:
        for source in sources:
            source["percentage"] = round(source["count"] / total * 100, 1)
    
    return sorted(sources, key=lambda x: x["count"], reverse=True)[:10]


async def _get_top_query_types(redis_client, hours: int) -> List[Dict[str, Any]]:
    """獲取熱門查詢類型"""
    key = f"query_types_stats:{hours}h"
    types_data = await redis_client.hgetall(key)
    
    if not types_data:
        return []
    
    types = []
    for query_type, count in types_data.items():
        types.append({
            "type": query_type.decode(),
            "count": int(count.decode()),
            "percentage": 0
        })
    
    # 計算百分比
    total = sum(t["count"] for t in types)
    if total > 0:
        for query_type in types:
            query_type["percentage"] = round(query_type["count"] / total * 100, 1)
    
    return sorted(types, key=lambda x: x["count"], reverse=True)[:10]


async def _get_pipeline_stats(pipeline) -> Dict[str, Any]:
    """獲取處理管道統計"""
    return {
        "stages_count": len(pipeline.processors),
        "collectors_count": len(pipeline.collectors),
        "is_running": pipeline.is_running,
        "pipeline_config": pipeline.config
    }


async def _get_system_health(pipeline) -> Dict[str, Any]:
    """獲取系統健康狀態"""
    health = {
        "overall_status": "healthy" if pipeline.is_running else "unhealthy",
        "components": {}
    }
    
    # 檢查各組件狀態
    try:
        if pipeline.kafka_service:
            health["components"]["kafka"] = "healthy"
        else:
            health["components"]["kafka"] = "unavailable"
    except:
        health["components"]["kafka"] = "error"
    
    try:
        if pipeline.elasticsearch_service:
            health["components"]["elasticsearch"] = "healthy"
        else:
            health["components"]["elasticsearch"] = "unavailable"
    except:
        health["components"]["elasticsearch"] = "error"
    
    try:
        if pipeline.redis_client:
            health["components"]["redis"] = "healthy"
        else:
            health["components"]["redis"] = "unavailable"
    except:
        health["components"]["redis"] = "error"
    
    return health


def _calculate_time_intervals(hours: int, interval: str) -> List[tuple]:
    """計算時間間隔"""
    end_time = datetime.utcnow()
    start_time = end_time - timedelta(hours=hours)
    
    # 根據間隔類型計算步長
    if interval == "5m":
        step = timedelta(minutes=5)
    elif interval == "15m":
        step = timedelta(minutes=15)
    elif interval == "1h":
        step = timedelta(hours=1)
    elif interval == "6h":
        step = timedelta(hours=6)
    elif interval == "1d":
        step = timedelta(days=1)
    else:
        step = timedelta(hours=1)
    
    intervals = []
    current = start_time
    while current < end_time:
        next_time = min(current + step, end_time)
        intervals.append((current, next_time))
        current = next_time
    
    return intervals


def _calculate_overall_success_rate(collectors_stats: Dict) -> float:
    """計算整體成功率"""
    total_requests = sum(c.get("total_requests", 0) for c in collectors_stats.values())
    total_errors = sum(c.get("total_errors", 0) for c in collectors_stats.values())
    
    if total_requests == 0:
        return 100.0
    
    return round((total_requests - total_errors) / total_requests * 100, 2)


def _identify_bottleneck(processors_performance: List[Dict]) -> Optional[str]:
    """識別處理管道瓶頸"""
    if not processors_performance:
        return None
    
    # 找出處理時間最長的處理器
    bottleneck = max(processors_performance, key=lambda p: p["avg_processing_time"])
    return bottleneck["name"]


def _calculate_pipeline_success_rate(processors_performance: List[Dict]) -> float:
    """計算管道整體成功率"""
    if not processors_performance:
        return 100.0
    
    # 使用最低的成功率作為整體成功率
    success_rates = [p["success_rate"] for p in processors_performance]
    return min(success_rates) if success_rates else 100.0 