"""
數據收集管道管理 API
提供管道狀態監控、控制和配置管理功能
"""

from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.dependencies import get_current_user
from app.models.user import User
from app.services.data_collection_pipeline import (
    get_data_collection_pipeline,
    DataCollectionPipeline,
    QueryData,
    DataSourceType,
    CollectionStatus
)
import structlog

logger = structlog.get_logger()
router = APIRouter()


class PipelineStatusResponse(BaseModel):
    """管道狀態響應"""
    is_running: bool
    uptime: Optional[str] = None
    collectors_count: int
    processors_count: int
    metrics: Dict[str, Any]
    last_updated: datetime


class CollectorConfigRequest(BaseModel):
    """收集器配置請求"""
    collector_type: str
    config: Dict[str, Any]
    enabled: bool = True


class QueryDataRequest(BaseModel):
    """查詢數據請求"""
    query: str
    source: DataSourceType
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    url: Optional[str] = None
    referrer: Optional[str] = None
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None
    filters: Optional[Dict] = None
    metadata: Optional[Dict] = None


@router.get("/status", response_model=PipelineStatusResponse)
async def get_pipeline_status(
    current_user: User = Depends(get_current_user)
) -> PipelineStatusResponse:
    """獲取數據收集管道狀態"""
    try:
        pipeline = await get_data_collection_pipeline()
        metrics = await pipeline.get_pipeline_metrics()
        
        # 計算運行時間
        uptime = None
        if pipeline.is_running and 'last_reset' in metrics:
            uptime_delta = datetime.utcnow() - metrics['last_reset']
            uptime = str(uptime_delta)
        
        return PipelineStatusResponse(
            is_running=pipeline.is_running,
            uptime=uptime,
            collectors_count=len(pipeline.collectors),
            processors_count=len(pipeline.processors),
            metrics=metrics,
            last_updated=datetime.utcnow()
        )
    except Exception as e:
        logger.error("獲取管道狀態失敗", error=str(e))
        raise HTTPException(status_code=500, detail="無法獲取管道狀態")


@router.post("/start")
async def start_pipeline(
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)
) -> Dict[str, str]:
    """啟動數據收集管道"""
    try:
        pipeline = await get_data_collection_pipeline()
        
        if pipeline.is_running:
            return {"message": "管道已經在運行中", "status": "running"}
        
        # 在背景任務中初始化管道
        background_tasks.add_task(pipeline.initialize)
        
        logger.info("數據收集管道啟動請求已提交", user_id=current_user.id)
        return {"message": "管道啟動中，請稍候檢查狀態", "status": "starting"}
        
    except Exception as e:
        logger.error("啟動管道失敗", error=str(e), user_id=current_user.id)
        raise HTTPException(status_code=500, detail="無法啟動管道")


@router.post("/stop")
async def stop_pipeline(
    current_user: User = Depends(get_current_user)
) -> Dict[str, str]:
    """停止數據收集管道"""
    try:
        pipeline = await get_data_collection_pipeline()
        
        if not pipeline.is_running:
            return {"message": "管道未在運行", "status": "stopped"}
        
        await pipeline.stop()
        
        logger.info("數據收集管道已停止", user_id=current_user.id)
        return {"message": "管道已成功停止", "status": "stopped"}
        
    except Exception as e:
        logger.error("停止管道失敗", error=str(e), user_id=current_user.id)
        raise HTTPException(status_code=500, detail="無法停止管道")


@router.post("/restart")
async def restart_pipeline(
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)
) -> Dict[str, str]:
    """重啟數據收集管道"""
    try:
        pipeline = await get_data_collection_pipeline()
        
        # 停止現有管道
        if pipeline.is_running:
            await pipeline.stop()
        
        # 重置統計指標
        await pipeline.reset_metrics()
        
        # 在背景任務中重新初始化
        background_tasks.add_task(pipeline.initialize)
        
        logger.info("數據收集管道重啟請求已提交", user_id=current_user.id)
        return {"message": "管道重啟中，請稍候檢查狀態", "status": "restarting"}
        
    except Exception as e:
        logger.error("重啟管道失敗", error=str(e), user_id=current_user.id)
        raise HTTPException(status_code=500, detail="無法重啟管道")


@router.get("/metrics")
async def get_pipeline_metrics(
    current_user: User = Depends(get_current_user),
    hours: int = Query(24, ge=1, le=168, description="獲取過去N小時的指標")
) -> Dict[str, Any]:
    """獲取管道詳細指標"""
    try:
        pipeline = await get_data_collection_pipeline()
        metrics = await pipeline.get_pipeline_metrics()
        
        # 添加時間範圍信息
        metrics['time_range'] = {
            'hours': hours,
            'start_time': (datetime.utcnow() - timedelta(hours=hours)).isoformat(),
            'end_time': datetime.utcnow().isoformat()
        }
        
        return metrics
        
    except Exception as e:
        logger.error("獲取管道指標失敗", error=str(e))
        raise HTTPException(status_code=500, detail="無法獲取管道指標")


@router.post("/metrics/reset")
async def reset_pipeline_metrics(
    current_user: User = Depends(get_current_user)
) -> Dict[str, str]:
    """重置管道統計指標"""
    try:
        pipeline = await get_data_collection_pipeline()
        await pipeline.reset_metrics()
        
        logger.info("管道統計指標已重置", user_id=current_user.id)
        return {"message": "統計指標已重置", "reset_time": datetime.utcnow().isoformat()}
        
    except Exception as e:
        logger.error("重置指標失敗", error=str(e), user_id=current_user.id)
        raise HTTPException(status_code=500, detail="無法重置指標")


@router.post("/collect")
async def collect_query_data(
    query_request: QueryDataRequest,
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """收集單個查詢數據"""
    try:
        pipeline = await get_data_collection_pipeline()
        
        if not pipeline.is_running:
            raise HTTPException(status_code=503, detail="數據收集管道未運行")
        
        # 創建查詢數據對象
        query_data = QueryData(
            id=None,  # 將自動生成
            query=query_request.query,
            source=query_request.source,
            user_id=query_request.user_id or str(current_user.id),
            session_id=query_request.session_id,
            url=query_request.url,
            referrer=query_request.referrer,
            user_agent=query_request.user_agent,
            ip_address=query_request.ip_address,
            filters=query_request.filters,
            metadata=query_request.metadata
        )
        
        # 收集數據
        collection_id = await pipeline.collect_query(query_data)
        
        logger.info("查詢數據已提交收集", 
                   collection_id=collection_id, 
                   query=query_request.query,
                   user_id=current_user.id)
        
        return {
            "collection_id": collection_id,
            "status": "submitted",
            "message": "查詢數據已提交處理",
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("收集查詢數據失敗", error=str(e), user_id=current_user.id)
        raise HTTPException(status_code=500, detail="無法收集查詢數據")


@router.post("/collect/batch")
async def collect_batch_queries(
    queries: List[QueryDataRequest],
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """批次收集查詢數據"""
    try:
        if len(queries) > 100:
            raise HTTPException(status_code=400, detail="批次大小不能超過100")
        
        pipeline = await get_data_collection_pipeline()
        
        if not pipeline.is_running:
            raise HTTPException(status_code=503, detail="數據收集管道未運行")
        
        # 轉換為 QueryData 對象
        query_data_list = []
        for query_request in queries:
            query_data = QueryData(
                id=None,
                query=query_request.query,
                source=query_request.source,
                user_id=query_request.user_id or str(current_user.id),
                session_id=query_request.session_id,
                url=query_request.url,
                referrer=query_request.referrer,
                user_agent=query_request.user_agent,
                ip_address=query_request.ip_address,
                filters=query_request.filters,
                metadata=query_request.metadata
            )
            query_data_list.append(query_data)
        
        # 批次收集
        collection_ids = await pipeline.collect_batch_queries(query_data_list)
        
        logger.info("批次查詢數據已提交收集", 
                   batch_size=len(queries),
                   collection_ids=collection_ids,
                   user_id=current_user.id)
        
        return {
            "collection_ids": collection_ids,
            "batch_size": len(queries),
            "status": "submitted",
            "message": "批次查詢數據已提交處理",
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("批次收集失敗", error=str(e), user_id=current_user.id)
        raise HTTPException(status_code=500, detail="無法批次收集數據")


@router.get("/collectors")
async def get_collectors_status(
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """獲取所有收集器狀態"""
    try:
        pipeline = await get_data_collection_pipeline()
        
        collectors_status = {}
        for name, collector in pipeline.collectors.items():
            collectors_status[name] = {
                "enabled": getattr(collector, 'enabled', True),
                "stats": getattr(collector, 'stats', {}),
                "last_activity": getattr(collector, 'last_activity', None)
            }
        
        return {
            "collectors": collectors_status,
            "total_count": len(pipeline.collectors),
            "active_count": len([c for c in collectors_status.values() if c.get('enabled', False)])
        }
        
    except Exception as e:
        logger.error("獲取收集器狀態失敗", error=str(e))
        raise HTTPException(status_code=500, detail="無法獲取收集器狀態")


@router.get("/processors")
async def get_processors_status(
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """獲取所有處理器狀態"""
    try:
        pipeline = await get_data_collection_pipeline()
        
        processors_status = []
        for processor in pipeline.processors:
            processor_info = {
                "name": processor.__class__.__name__,
                "enabled": getattr(processor, 'enabled', True),
                "stats": getattr(processor, 'stats', {})
            }
            processors_status.append(processor_info)
        
        return {
            "processors": processors_status,
            "total_count": len(pipeline.processors),
            "pipeline_order": [p["name"] for p in processors_status]
        }
        
    except Exception as e:
        logger.error("獲取處理器狀態失敗", error=str(e))
        raise HTTPException(status_code=500, detail="無法獲取處理器狀態")


@router.get("/health")
async def pipeline_health_check() -> Dict[str, Any]:
    """管道健康檢查（無需認證）"""
    try:
        pipeline = await get_data_collection_pipeline()
        
        health_status = {
            "status": "healthy" if pipeline.is_running else "unhealthy",
            "pipeline_running": pipeline.is_running,
            "timestamp": datetime.utcnow().isoformat(),
            "version": "1.0.0"
        }
        
        # 檢查組件健康狀態
        components = {
            "kafka": False,
            "elasticsearch": False,
            "redis": False
        }
        
        try:
            # 檢查 Kafka
            if pipeline.kafka_service and hasattr(pipeline.kafka_service, 'is_connected'):
                components["kafka"] = pipeline.kafka_service.is_connected()
        except:
            pass
            
        try:
            # 檢查 Elasticsearch  
            if pipeline.elasticsearch_service:
                es_health = await pipeline.elasticsearch_service.health_check()
                components["elasticsearch"] = es_health.get("status") == "green"
        except:
            pass
            
        try:
            # 檢查 Redis
            if pipeline.redis_client:
                await pipeline.redis_client.ping()
                components["redis"] = True
        except:
            pass
        
        health_status["components"] = components
        health_status["overall_healthy"] = all(components.values()) and pipeline.is_running
        
        status_code = 200 if health_status["overall_healthy"] else 503
        return JSONResponse(content=health_status, status_code=status_code)
        
    except Exception as e:
        logger.error("健康檢查失敗", error=str(e))
        return JSONResponse(
            content={
                "status": "error",
                "message": "健康檢查失敗",
                "timestamp": datetime.utcnow().isoformat()
            },
            status_code=503
        ) 