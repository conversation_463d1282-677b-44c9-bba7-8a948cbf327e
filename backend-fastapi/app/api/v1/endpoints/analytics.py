"""
查詢分析 API 端點
提供查詢記錄、統計分析、關鍵詞分析和實時統計的API接口
"""

from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, Depends, Query, BackgroundTasks
from fastapi.responses import JSONResponse

from app.core.dependencies import get_current_user
from app.models.user import User
from app.services.query_analytics_service import QueryAnalyticsService
from app.schemas.analytics import (
    QueryRecordCreate, QueryRecordResponse, QueryRecordListResponse,
    QueryStatsResponse, KeywordAnalysisResponse, TrendAnalysisRequest, TrendAnalysisResponse,
    RealTimeStatsResponse, AnalyticsDashboardStats,
    StatisticsTimeframe, QuerySourceType
)
import structlog

logger = structlog.get_logger()
router = APIRouter()

# 全局服務實例
analytics_service = None

async def get_analytics_service() -> QueryAnalyticsService:
    """獲取查詢分析服務實例"""
    global analytics_service
    if not analytics_service:
        analytics_service = QueryAnalyticsService()
        await analytics_service.initialize()
    return analytics_service


@router.post("/queries/record")
async def record_query(
    query_data: QueryRecordCreate,
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """記錄查詢數據"""
    try:
        service = await get_analytics_service()
        query_id = await service.record_query(query_data)
        
        logger.info("查詢記錄成功", query_id=query_id, user_id=current_user.id)
        
        return {
            "query_id": query_id,
            "status": "recorded",
            "message": "查詢記錄成功",
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("記錄查詢失敗", error=str(e), user_id=current_user.id)
        raise HTTPException(status_code=500, detail="無法記錄查詢")


@router.get("/queries", response_model=QueryRecordListResponse)
async def get_query_records(
    page: int = Query(1, ge=1, description="頁碼"),
    page_size: int = Query(20, ge=1, le=100, description="每頁大小"),
    source: Optional[QuerySourceType] = Query(None, description="查詢來源篩選"),
    start_date: Optional[datetime] = Query(None, description="開始時間"),
    end_date: Optional[datetime] = Query(None, description="結束時間"),
    search: Optional[str] = Query(None, description="搜索關鍵詞"),
    current_user: User = Depends(get_current_user)
) -> QueryRecordListResponse:
    """獲取查詢記錄列表"""
    try:
        service = await get_analytics_service()
        
        # 構建查詢參數
        filters = {}
        if source:
            filters["source"] = source.value
        if start_date:
            filters["start_date"] = start_date
        if end_date:
            filters["end_date"] = end_date
        if search:
            filters["search"] = search
        
        # 獲取查詢記錄（這裡需要實現分頁查詢方法）
        records, total = await service.get_query_records(
            page=page,
            page_size=page_size,
            filters=filters
        )
        
        return QueryRecordListResponse(
            items=records,
            total=total,
            page=page,
            page_size=page_size,
            pages=(total + page_size - 1) // page_size
        )
        
    except Exception as e:
        logger.error("獲取查詢記錄失敗", error=str(e))
        raise HTTPException(status_code=500, detail="無法獲取查詢記錄")


@router.get("/stats/{timeframe}", response_model=QueryStatsResponse)
async def get_query_stats(
    timeframe: StatisticsTimeframe,
    start_date: Optional[datetime] = Query(None, description="開始時間"),
    end_date: Optional[datetime] = Query(None, description="結束時間"),
    current_user: User = Depends(get_current_user)
) -> QueryStatsResponse:
    """獲取查詢統計"""
    try:
        service = await get_analytics_service()
        stats = await service.get_query_stats(timeframe, start_date, end_date)
        
        return QueryStatsResponse(
            success=True,
            message="查詢統計獲取成功",
            data=stats
        )
        
    except Exception as e:
        logger.error("獲取查詢統計失敗", error=str(e), timeframe=timeframe)
        raise HTTPException(status_code=500, detail="無法獲取查詢統計")


@router.get("/keywords/analysis", response_model=KeywordAnalysisResponse)
async def analyze_keywords(
    timeframe: StatisticsTimeframe = Query(StatisticsTimeframe.WEEK, description="時間範圍"),
    start_date: Optional[datetime] = Query(None, description="開始時間"),
    end_date: Optional[datetime] = Query(None, description="結束時間"),
    min_frequency: int = Query(10, ge=1, description="最小頻率"),
    limit: int = Query(100, ge=1, le=1000, description="結果數量限制"),
    current_user: User = Depends(get_current_user)
) -> KeywordAnalysisResponse:
    """關鍵詞分析"""
    try:
        service = await get_analytics_service()
        analysis = await service.analyze_keywords(
            timeframe=timeframe,
            start_date=start_date,
            end_date=end_date,
            min_frequency=min_frequency,
            limit=limit
        )
        
        return KeywordAnalysisResponse(
            success=True,
            message="關鍵詞分析完成",
            data=analysis
        )
        
    except Exception as e:
        logger.error("關鍵詞分析失敗", error=str(e))
        raise HTTPException(status_code=500, detail="關鍵詞分析失敗")


@router.post("/trends/analysis", response_model=TrendAnalysisResponse)
async def analyze_trends(
    request: TrendAnalysisRequest,
    current_user: User = Depends(get_current_user)
) -> TrendAnalysisResponse:
    """趨勢分析"""
    try:
        service = await get_analytics_service()
        analysis = await service.get_trend_analysis(request)
        
        return TrendAnalysisResponse(
            success=True,
            message="趨勢分析完成",
            data=analysis
        )
        
    except Exception as e:
        logger.error("趨勢分析失敗", error=str(e))
        raise HTTPException(status_code=500, detail="趨勢分析失敗")


@router.get("/realtime", response_model=RealTimeStatsResponse)
async def get_realtime_stats(
    current_user: User = Depends(get_current_user)
) -> RealTimeStatsResponse:
    """獲取實時統計"""
    try:
        service = await get_analytics_service()
        stats = await service.get_realtime_stats()
        
        return RealTimeStatsResponse(
            success=True,
            message="實時統計獲取成功",
            data=stats
        )
        
    except Exception as e:
        logger.error("獲取實時統計失敗", error=str(e))
        raise HTTPException(status_code=500, detail="無法獲取實時統計")


@router.get("/dashboard")
async def get_dashboard_stats(
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """獲取儀表板統計"""
    try:
        service = await get_analytics_service()
        stats = await service.get_dashboard_stats()
        
        return {
            "success": True,
            "message": "儀表板統計獲取成功",
            "data": stats.dict()
        }
        
    except Exception as e:
        logger.error("獲取儀表板統計失敗", error=str(e))
        raise HTTPException(status_code=500, detail="無法獲取儀表板統計")


@router.get("/keywords/trending")
async def get_trending_keywords(
    hours: int = Query(24, ge=1, le=168, description="時間範圍（小時）"),
    limit: int = Query(50, ge=1, le=200, description="結果數量"),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """獲取趨勢關鍵詞"""
    try:
        service = await get_analytics_service()
        
        # 獲取趨勢關鍵詞（需要實現該方法）
        trending_keywords = await service.get_trending_keywords(hours, limit)
        
        return {
            "success": True,
            "message": "趨勢關鍵詞獲取成功",
            "data": {
                "keywords": trending_keywords,
                "timeframe_hours": hours,
                "last_updated": datetime.utcnow().isoformat()
            }
        }
        
    except Exception as e:
        logger.error("獲取趨勢關鍵詞失敗", error=str(e))
        raise HTTPException(status_code=500, detail="無法獲取趨勢關鍵詞")


@router.get("/sources/distribution")
async def get_source_distribution(
    timeframe: StatisticsTimeframe = Query(StatisticsTimeframe.DAY, description="時間範圍"),
    start_date: Optional[datetime] = Query(None, description="開始時間"),
    end_date: Optional[datetime] = Query(None, description="結束時間"),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """獲取查詢來源分佈"""
    try:
        service = await get_analytics_service()
        distribution = await service.get_source_distribution(timeframe, start_date, end_date)
        
        return {
            "success": True,
            "message": "來源分佈獲取成功",
            "data": distribution
        }
        
    except Exception as e:
        logger.error("獲取來源分佈失敗", error=str(e))
        raise HTTPException(status_code=500, detail="無法獲取來源分佈")


@router.get("/geographic/distribution")
async def get_geographic_distribution(
    timeframe: StatisticsTimeframe = Query(StatisticsTimeframe.DAY, description="時間範圍"),
    start_date: Optional[datetime] = Query(None, description="開始時間"),
    end_date: Optional[datetime] = Query(None, description="結束時間"),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """獲取地理分佈"""
    try:
        service = await get_analytics_service()
        distribution = await service.get_geographic_distribution(timeframe, start_date, end_date)
        
        return {
            "success": True,
            "message": "地理分佈獲取成功",
            "data": distribution
        }
        
    except Exception as e:
        logger.error("獲取地理分佈失敗", error=str(e))
        raise HTTPException(status_code=500, detail="無法獲取地理分佈")


@router.get("/performance/metrics")
async def get_performance_metrics(
    timeframe: StatisticsTimeframe = Query(StatisticsTimeframe.HOUR, description="時間範圍"),
    start_date: Optional[datetime] = Query(None, description="開始時間"),
    end_date: Optional[datetime] = Query(None, description="結束時間"),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """獲取性能指標"""
    try:
        service = await get_analytics_service()
        metrics = await service.get_performance_metrics(timeframe, start_date, end_date)
        
        return {
            "success": True,
            "message": "性能指標獲取成功",
            "data": metrics
        }
        
    except Exception as e:
        logger.error("獲取性能指標失敗", error=str(e))
        raise HTTPException(status_code=500, detail="無法獲取性能指標")


@router.post("/cleanup")
async def cleanup_old_data(
    background_tasks: BackgroundTasks,
    retention_days: int = Query(90, ge=1, le=3650, description="數據保留天數"),
    current_user: User = Depends(get_current_user)
) -> Dict[str, str]:
    """清理舊數據"""
    try:
        service = await get_analytics_service()
        
        # 在背景任務中執行清理
        background_tasks.add_task(service.cleanup_old_data, retention_days)
        
        logger.info("數據清理任務已提交", retention_days=retention_days, user_id=current_user.id)
        
        return {
            "message": "數據清理任務已提交",
            "retention_days": str(retention_days),
            "status": "scheduled"
        }
        
    except Exception as e:
        logger.error("提交數據清理任務失敗", error=str(e))
        raise HTTPException(status_code=500, detail="無法提交數據清理任務")


@router.get("/health")
async def analytics_health_check() -> Dict[str, Any]:
    """分析服務健康檢查"""
    try:
        service = await get_analytics_service()
        
        # 檢查服務狀態
        health_status = {
            "service": "healthy",
            "redis_connected": service.redis_client is not None,
            "elasticsearch_connected": service.elasticsearch_service is not None,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # 簡單的功能測試
        try:
            await service.get_realtime_stats()
            health_status["realtime_stats"] = "healthy"
        except Exception:
            health_status["realtime_stats"] = "error"
            health_status["service"] = "degraded"
        
        return health_status
        
    except Exception as e:
        logger.error("分析服務健康檢查失敗", error=str(e))
        return {
            "service": "error",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }


@router.get("/export/data")
async def export_analytics_data(
    background_tasks: BackgroundTasks,
    format: str = Query("csv", description="導出格式"),
    timeframe: StatisticsTimeframe = Query(StatisticsTimeframe.WEEK, description="時間範圍"),
    start_date: Optional[datetime] = Query(None, description="開始時間"),
    end_date: Optional[datetime] = Query(None, description="結束時間"),
    current_user: User = Depends(get_current_user)
) -> Dict[str, str]:
    """導出分析數據"""
    try:
        service = await get_analytics_service()
        
        # 在背景任務中執行導出
        export_id = f"export_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        background_tasks.add_task(
            service.export_data,
            export_id=export_id,
            format=format,
            timeframe=timeframe,
            start_date=start_date,
            end_date=end_date,
            user_id=current_user.id
        )
        
        logger.info("數據導出任務已提交", export_id=export_id, user_id=current_user.id)
        
        return {
            "message": "數據導出任務已提交",
            "export_id": export_id,
            "status": "processing"
        }
        
    except Exception as e:
        logger.error("提交數據導出任務失敗", error=str(e))
        raise HTTPException(status_code=500, detail="無法提交數據導出任務") 