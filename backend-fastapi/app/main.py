"""
AI SEO 優化王 - FastAPI 主應用程式
"""

import asyncio
import logging
import time
from contextlib import asynccontextmanager
from typing import AsyncGenerator

import structlog
from fastapi import Fast<PERSON><PERSON>, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import J<PERSON><PERSON><PERSON>ponse
from prometheus_client import Counter, Histogram, generate_latest, CONTENT_TYPE_LATEST
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.errors import RateLimitExceeded
from slowapi.util import get_remote_address

from app.core.config import get_settings
from app.core.database import init_db
from app.core.redis import init_redis, close_redis
from app.core.elasticsearch import init_elasticsearch, close_elasticsearch
from app.services.supabase import init_supabase, close_supabase
from app.services.monitoring import init_monitoring
from app.services.elasticsearch_service import seo_search_service
from app.services.sync_service import sync_service
from app.api.v1.api import api_router
from app.core.exceptions import setup_exception_handlers
from app.core.middleware import setup_middleware
from app.middleware.monitoring import (
    MonitoringMiddleware,
    HealthCheckMiddleware,
    RequestSizeLimitMiddleware,
    SecurityHeadersMiddleware
)

# 設置結構化日誌
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Prometheus 指標
REQUEST_COUNT = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status']
)

REQUEST_DURATION = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration in seconds',
    ['method', 'endpoint']
)

# 限流器
limiter = Limiter(key_func=get_remote_address)

settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """應用程式生命週期管理"""
    logger.info("啟動 AI SEO 優化王 FastAPI 服務")
    
    # 啟動時初始化
    try:
        # 嘗試初始化數據庫
        try:
            await init_db()
            logger.info("數據庫連接初始化成功")
        except Exception as e:
            logger.warning(f"數據庫初始化失敗，將使用內存模式: {e}")

        # 檢查是否使用 Supabase（可選）
        try:
            if settings.SUPABASE_URL and "supabase.co" in str(settings.SUPABASE_URL):
                logger.info("檢測到 Supabase 配置")
                from app.services.supabase import get_supabase
                supabase = await get_supabase()
                logger.info("Supabase 服務初始化成功")
        except Exception as e:
            logger.warning(f"Supabase 服務初始化失敗: {e}")

        # 嘗試創建數據庫表（可選）
        try:
            from app.core.database import create_tables
            create_tables()
            logger.info("數據庫表創建成功")
        except Exception as e:
            logger.warning(f"創建數據庫表失敗: {e}")

        # 嘗試初始化示範數據（可選）
        try:
            from app.core.init_data import init_demo_data
            await init_demo_data()
            logger.info("示範數據初始化成功")
        except Exception as e:
            logger.warning(f"示範數據初始化失敗: {e}")

        # 嘗試初始化 Elasticsearch（可選）
        try:
            await init_elasticsearch()
            logger.info("Elasticsearch 初始化成功")
            
            # 初始化搜索索引
            await seo_search_service.initialize_indices()
            logger.info("SEO 搜索索引初始化成功")
            
            # 啟動數據同步服務
            asyncio.create_task(sync_service.start_continuous_sync())
            logger.info("數據同步服務啟動成功")
            
        except Exception as e:
            logger.warning(f"Elasticsearch 初始化失敗，搜索功能將不可用: {e}")

        # 跳過其他可選服務
        logger.info("FastAPI 服務器啟動成功")
        
    except Exception as e:
        logger.error(f"服務初始化失敗: {e}")
        # 不要拋出異常，讓服務器繼續運行

    yield

    # 關閉時清理
    try:
        await close_elasticsearch()
        logger.info("FastAPI 服務器關閉")
    except Exception as e:
        logger.error(f"清理資源時發生錯誤: {e}")


def create_app() -> FastAPI:
    """創建 FastAPI 應用程式實例"""
    
    app = FastAPI(
        title=settings.PROJECT_NAME,
        version=settings.VERSION,
        description=settings.DESCRIPTION,
        openapi_url=f"{settings.API_V1_STR}/openapi.json",
        docs_url=f"{settings.API_V1_STR}/docs",
        redoc_url=f"{settings.API_V1_STR}/redoc",
        lifespan=lifespan,
    )
    
    # 設置 CORS - 增強配置解決 webpack 熱更新問題
    cors_origins = [
        "http://localhost:3000",
        "http://localhost:3001", 
        "http://localhost:3002",
        "http://localhost:3003",
        "http://localhost:3004",
        "https://localhost:3000",
        "https://localhost:3001",
        "https://localhost:3002",
        "https://localhost:3003",
        "https://localhost:3004",
        "http://127.0.0.1:3000",
        "https://127.0.0.1:3000"
    ]
    
    app.add_middleware(
        CORSMiddleware,
        allow_origins=cors_origins + (settings.BACKEND_CORS_ORIGINS if settings.BACKEND_CORS_ORIGINS else []),
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
        allow_headers=["*"],
        expose_headers=["*"],
        max_age=3600,  # 1小時預檢快取
    )
    
    # 設置信任的主機
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["localhost", "127.0.0.1", "0.0.0.0", "*"]
    )
    
    # 設置限流
    app.state.limiter = limiter
    app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)
    
    # 設置監控中間件（按順序添加）
    app.add_middleware(SecurityHeadersMiddleware)
    app.add_middleware(RequestSizeLimitMiddleware, max_size=10 * 1024 * 1024)  # 10MB
    # 暫時禁用監控中間件（需要 Redis）
    # app.add_middleware(MonitoringMiddleware)
    app.add_middleware(HealthCheckMiddleware)

    # 設置其他中間件
    setup_middleware(app)

    # 設置異常處理器
    setup_exception_handlers(app)
    
    # 添加請求處理中間件
    @app.middleware("http")
    async def process_request(request: Request, call_next):
        start_time = time.time()
        
        # 記錄請求
        logger.info(
            "收到請求",
            method=request.method,
            url=str(request.url),
            client_ip=get_remote_address(request)
        )
        
        response = await call_next(request)
        
        # 計算處理時間
        process_time = time.time() - start_time
        
        # 更新 Prometheus 指標
        REQUEST_COUNT.labels(
            method=request.method,
            endpoint=request.url.path,
            status=response.status_code
        ).inc()
        
        REQUEST_DURATION.labels(
            method=request.method,
            endpoint=request.url.path
        ).observe(process_time)
        
        # 添加響應頭
        response.headers["X-Process-Time"] = str(process_time)
        response.headers["X-API-Version"] = settings.VERSION
        
        # 記錄響應
        logger.info(
            "請求處理完成",
            method=request.method,
            url=str(request.url),
            status_code=response.status_code,
            process_time=process_time
        )
        
        return response
    
    # 包含 API 路由
    app.include_router(api_router, prefix=settings.API_V1_STR)
    
    # 健康檢查端點
    @app.get("/health")
    async def health_check():
        """健康檢查端點"""
        return {
            "status": "healthy",
            "service": "AI SEO 優化王 API",
            "version": settings.VERSION,
            "timestamp": time.time()
        }
    
    # Prometheus 指標端點
    @app.get("/metrics")
    async def metrics():
        """Prometheus 指標端點"""
        return Response(
            generate_latest(),
            media_type=CONTENT_TYPE_LATEST
        )
    
    return app


# 創建應用程式實例
app = create_app()


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
        access_log=True,
    )
