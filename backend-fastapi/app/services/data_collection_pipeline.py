"""
AI SEO 數據收集管道主服務
整合多種數據來源，實現統一的數據收集、處理和存儲
"""

import asyncio
import json
import uuid
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, asdict
from enum import Enum
import structlog

from app.core.config import get_settings
from app.services.kafka_service import KafkaService
from app.services.elasticsearch_service import ElasticsearchSEOService
from app.core.redis import get_redis
from app.core.database import get_db
from app.schemas.base import Priority

logger = structlog.get_logger()
settings = get_settings()


class DataSourceType(Enum):
    """數據來源類型"""
    WEB_SEARCH = "web_search"
    API_REQUEST = "api_request"
    CUSTOMER_SERVICE = "customer_service"
    MOBILE_APP = "mobile_app"
    THIRD_PARTY = "third_party"
    SOCIAL_MEDIA = "social_media"
    SEO_TOOLS = "seo_tools"


class CollectionStatus(Enum):
    """收集狀態"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"


@dataclass
class QueryData:
    """查詢數據結構"""
    id: str
    query: str
    source: DataSourceType
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    timestamp: datetime = None
    url: Optional[str] = None
    referrer: Optional[str] = None
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None
    filters: Optional[Dict] = None
    results_count: Optional[int] = None
    response_time: Optional[float] = None
    metadata: Optional[Dict] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()
        if self.id is None:
            self.id = str(uuid.uuid4())


@dataclass
class ProcessedQueryData:
    """處理後的查詢數據"""
    original: QueryData
    normalized_query: str
    query_type: str
    intent: str
    intent_confidence: float
    topics: List[str]
    sentiment: Dict[str, Any]
    location: Optional[Dict] = None
    device_info: Optional[Dict] = None
    user_profile: Optional[Dict] = None
    processing_time: float = 0.0


class DataCollectionPipeline:
    """數據收集管道主類"""
    
    def __init__(self):
        self.kafka_service = None
        self.elasticsearch_service = None
        self.redis_client = None
        self.collectors: Dict[str, 'BaseCollector'] = {}
        self.processors: List['BaseProcessor'] = []
        self.is_running = False
        
        # 管道配置
        self.config = {
            'batch_size': 100,
            'flush_interval': 5000,  # 5秒
            'max_retries': 3,
            'retry_delay': 1000,     # 1秒
            'parallelism': 4,
            'queue_prefix': 'data-collection'
        }
        
        # 統計指標
        self.metrics = {
            'total_collected': 0,
            'total_processed': 0,
            'total_errors': 0,
            'processing_latency': [],
            'throughput': {'current': 0, 'peak': 0},
            'last_reset': datetime.utcnow()
        }

    async def initialize(self) -> bool:
        """初始化管道組件"""
        try:
            logger.info("正在初始化數據收集管道...")
            
            # 初始化 Kafka 服務
            self.kafka_service = KafkaService()
            await self.kafka_service.start()
            
            # 註冊消息處理器
            self._register_kafka_handlers()
            
            # 初始化 Elasticsearch 服務
            self.elasticsearch_service = ElasticsearchSEOService()
            await self.elasticsearch_service.initialize_indices()
            
            # 初始化 Redis 客戶端
            self.redis_client = await get_redis()
            
            # 初始化數據收集器
            await self._initialize_collectors()
            
            # 初始化數據處理器
            self._initialize_processors()
            
            # 啟動消費者
            await self._start_consumers()
            
            self.is_running = True
            logger.info("數據收集管道初始化成功")
            return True
            
        except Exception as e:
            logger.error("數據收集管道初始化失敗", error=str(e))
            return False

    def _register_kafka_handlers(self):
        """註冊 Kafka 消息處理器"""
        # 原始查詢數據處理
        self.kafka_service.register_message_handler(
            f"{self.config['queue_prefix']}-raw-queries",
            self._handle_raw_query_data
        )
        
        # 處理後數據存儲
        self.kafka_service.register_message_handler(
            f"{self.config['queue_prefix']}-processed-queries",
            self._handle_processed_query_data
        )
        
        # 錯誤重試處理
        self.kafka_service.register_message_handler(
            f"{self.config['queue_prefix']}-retry-queue",
            self._handle_retry_data
        )

    async def _initialize_collectors(self):
        """初始化數據收集器"""
        # Web 搜索收集器
        web_collector = WebSearchCollector(self.config)
        await web_collector.initialize()
        self.collectors['web_search'] = web_collector
        
        # API 收集器
        api_collector = APICollector(self.config)
        await api_collector.initialize()
        self.collectors['api'] = api_collector
        
        # 第三方平台收集器
        third_party_collector = ThirdPartyCollector(self.config)
        await third_party_collector.initialize()
        self.collectors['third_party'] = third_party_collector
        
        logger.info("數據收集器初始化完成", collectors=list(self.collectors.keys()))

    def _initialize_processors(self):
        """初始化數據處理器"""
        # 數據清洗
        self.processors.append(DataCleaningProcessor())
        
        # 數據富化
        self.processors.append(DataEnrichmentProcessor())
        
        # 意圖分類
        self.processors.append(IntentClassificationProcessor())
        
        # 主題提取
        self.processors.append(TopicExtractionProcessor())
        
        logger.info("數據處理器初始化完成", processors=len(self.processors))

    async def _start_consumers(self):
        """啟動 Kafka 消費者"""
        topics = [
            f"{self.config['queue_prefix']}-raw-queries",
            f"{self.config['queue_prefix']}-processed-queries",
            f"{self.config['queue_prefix']}-retry-queue"
        ]
        
        await self.kafka_service.start_consumer(topics)
        logger.info("Kafka 消費者啟動完成", topics=topics)

    async def collect_query(self, query_data: Union[QueryData, Dict]) -> str:
        """收集單個查詢數據"""
        if isinstance(query_data, dict):
            # 轉換字典為 QueryData 對象
            query_data = QueryData(**query_data)
        
        try:
            # 發送到原始數據隊列
            await self.kafka_service.send_message(
                f"{self.config['queue_prefix']}-raw-queries",
                asdict(query_data),
                key=query_data.id
            )
            
            # 更新統計
            self.metrics['total_collected'] += 1
            
            # 緩存到 Redis（用於實時統計）
            await self._cache_query_stats(query_data)
            
            logger.debug("查詢數據收集成功", query_id=query_data.id, source=query_data.source.value)
            return query_data.id
            
        except Exception as e:
            self.metrics['total_errors'] += 1
            logger.error("查詢數據收集失敗", query_id=query_data.id, error=str(e))
            raise

    async def collect_batch_queries(self, queries: List[Union[QueryData, Dict]]) -> List[str]:
        """批量收集查詢數據"""
        processed_queries = []
        
        for query in queries:
            if isinstance(query, dict):
                query = QueryData(**query)
            processed_queries.append(query)
        
        try:
            # 批量發送到 Kafka
            messages = [asdict(query) for query in processed_queries]
            keys = [query.id for query in processed_queries]
            
            await self.kafka_service.send_batch_messages(
                f"{self.config['queue_prefix']}-raw-queries",
                messages,
                keys
            )
            
            # 更新統計
            self.metrics['total_collected'] += len(processed_queries)
            
            # 批量緩存統計
            await self._cache_batch_stats(processed_queries)
            
            logger.info("批量查詢數據收集成功", count=len(processed_queries))
            return [query.id for query in processed_queries]
            
        except Exception as e:
            self.metrics['total_errors'] += len(processed_queries)
            logger.error("批量查詢數據收集失敗", count=len(processed_queries), error=str(e))
            raise

    async def _handle_raw_query_data(self, topic: str, key: str, message: Dict, raw_msg):
        """處理原始查詢數據"""
        start_time = time.time()
        
        try:
            # 轉換為 QueryData 對象
            query_data = QueryData(**message)
            
            # 通過處理管道
            processed_data = await self._process_query_pipeline(query_data)
            
            # 發送到處理後數據隊列
            await self.kafka_service.send_message(
                f"{self.config['queue_prefix']}-processed-queries",
                asdict(processed_data),
                key=key
            )
            
            # 記錄處理時間
            processing_time = time.time() - start_time
            self.metrics['processing_latency'].append(processing_time)
            
            # 保持延遲記錄在合理範圍內
            if len(self.metrics['processing_latency']) > 1000:
                self.metrics['processing_latency'] = self.metrics['processing_latency'][-500:]
            
            logger.debug("原始查詢數據處理完成", query_id=key, processing_time=processing_time)
            
        except Exception as e:
            self.metrics['total_errors'] += 1
            logger.error("原始查詢數據處理失敗", query_id=key, error=str(e))
            
            # 發送到重試隊列
            await self._send_to_retry_queue(message, str(e))

    async def _handle_processed_query_data(self, topic: str, key: str, message: Dict, raw_msg):
        """處理已處理的查詢數據並存儲"""
        try:
            # 存儲到 Elasticsearch
            await self._store_to_elasticsearch(message)
            
            # 存儲到 PostgreSQL（結構化數據）
            await self._store_to_postgres(message)
            
            # 更新 Redis 統計
            await self._update_redis_statistics(message)
            
            self.metrics['total_processed'] += 1
            logger.debug("處理後查詢數據存儲完成", query_id=key)
            
        except Exception as e:
            self.metrics['total_errors'] += 1
            logger.error("處理後查詢數據存儲失敗", query_id=key, error=str(e))
            
            # 發送到重試隊列
            await self._send_to_retry_queue(message, str(e))

    async def _handle_retry_data(self, topic: str, key: str, message: Dict, raw_msg):
        """處理重試數據"""
        try:
            retry_count = message.get('retry_count', 0)
            
            if retry_count < self.config['max_retries']:
                # 增加重試次數
                message['retry_count'] = retry_count + 1
                
                # 延遲重試
                await asyncio.sleep(self.config['retry_delay'] / 1000.0)
                
                # 重新發送到原始隊列
                await self.kafka_service.send_message(
                    f"{self.config['queue_prefix']}-raw-queries",
                    message['original_data'],
                    key=key
                )
                
                logger.info("重試數據發送成功", query_id=key, retry_count=retry_count + 1)
                
            else:
                # 超過最大重試次數，記錄到錯誤日誌
                logger.error("數據處理最終失敗", query_id=key, max_retries=self.config['max_retries'])
                await self._log_failed_data(message)
                
        except Exception as e:
            logger.error("重試數據處理失敗", query_id=key, error=str(e))

    async def _process_query_pipeline(self, query_data: QueryData) -> ProcessedQueryData:
        """通過數據處理管道處理查詢"""
        start_time = time.time()
        
        # 創建處理結果對象
        processed = ProcessedQueryData(
            original=query_data,
            normalized_query="",
            query_type="",
            intent="",
            intent_confidence=0.0,
            topics=[],
            sentiment={}
        )
        
        # 依序通過所有處理器
        for processor in self.processors:
            try:
                processed = await processor.process(processed)
            except Exception as e:
                logger.error("處理器執行失敗", processor=processor.__class__.__name__, error=str(e))
                # 繼續處理，不中斷管道
        
        # 記錄處理時間
        processed.processing_time = time.time() - start_time
        
        return processed

    async def _store_to_elasticsearch(self, data: Dict):
        """存儲到 Elasticsearch"""
        try:
            # 準備索引文檔
            doc = {
                'query_id': data['original']['id'],
                'query': data['original']['query'],
                'normalized_query': data['normalized_query'],
                'source': data['original']['source'],
                'user_id': data['original'].get('user_id'),
                'timestamp': data['original']['timestamp'],
                'intent': data['intent'],
                'intent_confidence': data['intent_confidence'],
                'topics': data['topics'],
                'sentiment': data['sentiment'],
                'location': data.get('location'),
                'device_info': data.get('device_info'),
                'user_profile': data.get('user_profile'),
                'processing_time': data['processing_time']
            }
            
            # 存儲到查詢分析索引
            await self.elasticsearch_service.sync_seo_content(doc)
            
        except Exception as e:
            logger.error("Elasticsearch 存儲失敗", error=str(e))
            raise

    async def _store_to_postgres(self, data: Dict):
        """存儲到 PostgreSQL"""
        try:
            async with get_db() as db:
                # 這裡需要根據您的數據模型進行調整
                # 示例：創建查詢記錄
                query_record = {
                    'id': data['original']['id'],
                    'query_text': data['original']['query'],
                    'normalized_query': data['normalized_query'],
                    'source': data['original']['source'],
                    'user_id': data['original'].get('user_id'),
                    'intent': data['intent'],
                    'metadata': json.dumps({
                        'topics': data['topics'],
                        'sentiment': data['sentiment'],
                        'processing_time': data['processing_time']
                    }),
                    'created_at': datetime.fromisoformat(data['original']['timestamp'])
                }
                
                # 插入數據庫
                # await db.execute(insert_query, query_record)
                # await db.commit()
                
        except Exception as e:
            logger.error("PostgreSQL 存儲失敗", error=str(e))
            raise

    async def _update_redis_statistics(self, data: Dict):
        """更新 Redis 統計數據"""
        try:
            current_hour = datetime.utcnow().strftime('%Y-%m-%d-%H')
            
            # 更新小時統計
            await self.redis_client.hincrby(f"query_stats:hourly:{current_hour}", 'total', 1)
            await self.redis_client.hincrby(f"query_stats:hourly:{current_hour}", data['original']['source'], 1)
            
            # 更新熱門查詢
            await self.redis_client.zincrby('trending_queries', 1, data['normalized_query'])
            
            # 更新意圖統計
            await self.redis_client.hincrby(f"intent_stats:hourly:{current_hour}", data['intent'], 1)
            
            # 設置過期時間
            await self.redis_client.expire(f"query_stats:hourly:{current_hour}", 86400 * 7)  # 7天
            await self.redis_client.expire('trending_queries', 3600)  # 1小時
            await self.redis_client.expire(f"intent_stats:hourly:{current_hour}", 86400 * 7)  # 7天
            
        except Exception as e:
            logger.error("Redis 統計更新失敗", error=str(e))

    async def _cache_query_stats(self, query_data: QueryData):
        """緩存查詢統計"""
        try:
            stats_key = f"real_time_stats:{datetime.utcnow().strftime('%Y-%m-%d-%H-%M')}"
            
            await self.redis_client.hincrby(stats_key, 'total', 1)
            await self.redis_client.hincrby(stats_key, query_data.source.value, 1)
            await self.redis_client.expire(stats_key, 3600)  # 1小時
            
        except Exception as e:
            logger.error("查詢統計緩存失敗", error=str(e))

    async def _cache_batch_stats(self, queries: List[QueryData]):
        """批量緩存統計"""
        try:
            stats_key = f"real_time_stats:{datetime.utcnow().strftime('%Y-%m-%d-%H-%M')}"
            
            # 統計各來源數量
            source_counts = {}
            for query in queries:
                source_counts[query.source.value] = source_counts.get(query.source.value, 0) + 1
            
            # 批量更新
            pipeline = self.redis_client.pipeline()
            pipeline.hincrby(stats_key, 'total', len(queries))
            
            for source, count in source_counts.items():
                pipeline.hincrby(stats_key, source, count)
            
            pipeline.expire(stats_key, 3600)
            await pipeline.execute()
            
        except Exception as e:
            logger.error("批量統計緩存失敗", error=str(e))

    async def _send_to_retry_queue(self, original_data: Dict, error_message: str):
        """發送到重試隊列"""
        retry_data = {
            'original_data': original_data,
            'error_message': error_message,
            'retry_count': original_data.get('retry_count', 0),
            'failed_at': datetime.utcnow().isoformat()
        }
        
        await self.kafka_service.send_message(
            f"{self.config['queue_prefix']}-retry-queue",
            retry_data,
            key=original_data.get('id', str(uuid.uuid4()))
        )

    async def _log_failed_data(self, data: Dict):
        """記錄最終失敗的數據"""
        failed_record = {
            'data': data,
            'final_failure_at': datetime.utcnow().isoformat(),
            'retry_count': data.get('retry_count', 0)
        }
        
        # 存儲到專門的失敗記錄表或文件
        logger.error("數據最終處理失敗", failed_data=failed_record)

    async def get_pipeline_metrics(self) -> Dict[str, Any]:
        """獲取管道指標"""
        current_time = datetime.utcnow()
        time_diff = (current_time - self.metrics['last_reset']).total_seconds()
        
        # 計算吞吐量
        if time_diff > 0:
            current_throughput = self.metrics['total_processed'] / time_diff
            self.metrics['throughput']['current'] = current_throughput
            
            if current_throughput > self.metrics['throughput']['peak']:
                self.metrics['throughput']['peak'] = current_throughput
        
        # 計算平均延遲
        avg_latency = 0
        if self.metrics['processing_latency']:
            avg_latency = sum(self.metrics['processing_latency']) / len(self.metrics['processing_latency'])
        
        return {
            'total_collected': self.metrics['total_collected'],
            'total_processed': self.metrics['total_processed'],
            'total_errors': self.metrics['total_errors'],
            'error_rate': self.metrics['total_errors'] / max(1, self.metrics['total_collected']),
            'average_latency': avg_latency,
            'current_throughput': self.metrics['throughput']['current'],
            'peak_throughput': self.metrics['throughput']['peak'],
            'collectors_status': {name: collector.get_status() for name, collector in self.collectors.items()},
            'is_running': self.is_running,
            'uptime_seconds': time_diff
        }

    async def reset_metrics(self):
        """重置指標"""
        self.metrics = {
            'total_collected': 0,
            'total_processed': 0,
            'total_errors': 0,
            'processing_latency': [],
            'throughput': {'current': 0, 'peak': 0},
            'last_reset': datetime.utcnow()
        }

    async def stop(self):
        """停止管道"""
        try:
            logger.info("正在停止數據收集管道...")
            
            # 停止收集器
            for collector in self.collectors.values():
                await collector.stop()
            
            # 停止 Kafka 服務
            if self.kafka_service:
                await self.kafka_service.stop()
            
            self.is_running = False
            logger.info("數據收集管道已停止")
            
        except Exception as e:
            logger.error("停止數據收集管道時發生錯誤", error=str(e))


# 全局管道實例
pipeline_instance = None

async def get_data_collection_pipeline() -> DataCollectionPipeline:
    """獲取數據收集管道實例"""
    global pipeline_instance
    
    if pipeline_instance is None:
        pipeline_instance = DataCollectionPipeline()
        await pipeline_instance.initialize()
    
    return pipeline_instance 