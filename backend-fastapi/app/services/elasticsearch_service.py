"""
Elasticsearch SEO 搜索服務
包含索引設計、數據同步機制、SEO 專用分析器配置和搜索功能
"""

import asyncio
import json
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Union
from urllib.parse import urlparse

import structlog
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.config import get_settings
from app.core.elasticsearch import es_client, get_elasticsearch
from app.core.database import get_db
from app.models.optimization import OptimizationRecommendation
from app.schemas.base import Priority

logger = structlog.get_logger()
settings = get_settings()


class ElasticsearchSEOService:
    """Elasticsearch SEO 搜索服務"""
    
    def __init__(self):
        self.seo_content_index = settings.ES_SEO_CONTENT_INDEX
        self.seo_analysis_index = settings.ES_SEO_ANALYSIS_INDEX
        self.seo_keywords_index = settings.ES_SEO_KEYWORDS_INDEX
        self.seo_competitors_index = settings.ES_SEO_COMPETITORS_INDEX
    
    async def initialize_indices(self) -> bool:
        """初始化所有 SEO 相關索引"""
        try:
            # 確保 Elasticsearch 連接
            if not es_client.is_connected:
                await es_client.connect()
            
            # 創建各個索引
            indices_created = []
            
            # 1. SEO 內容索引
            content_success = await self._create_seo_content_index()
            indices_created.append(("seo_content", content_success))
            
            # 2. SEO 分析索引
            analysis_success = await self._create_seo_analysis_index()
            indices_created.append(("seo_analysis", analysis_success))
            
            # 3. SEO 關鍵字索引
            keywords_success = await self._create_seo_keywords_index()
            indices_created.append(("seo_keywords", keywords_success))
            
            # 4. SEO 競爭對手索引
            competitors_success = await self._create_seo_competitors_index()
            indices_created.append(("seo_competitors", competitors_success))
            
            # 記錄結果
            for index_name, success in indices_created:
                if success:
                    logger.info(f"索引 {index_name} 初始化成功")
                else:
                    logger.error(f"索引 {index_name} 初始化失敗")
            
            return all(success for _, success in indices_created)
            
        except Exception as e:
            logger.error("索引初始化失敗", error=str(e))
            return False
    
    async def _create_seo_content_index(self) -> bool:
        """創建 SEO 內容索引 - 儲存網頁內容、關鍵字、meta 標籤、結構化數據"""
        mapping = {
            "properties": {
                "id": {"type": "keyword"},
                "url": {
                    "type": "text",
                    "analyzer": "seo_analyzer",
                    "fields": {
                        "keyword": {"type": "keyword"}
                    }
                },
                "title": {
                    "type": "text",
                    "analyzer": "chinese_analyzer",
                    "fields": {
                        "keyword": {"type": "keyword"},
                        "suggest": {
                            "type": "completion",
                            "analyzer": "chinese_analyzer"
                        }
                    }
                },
                "meta_description": {
                    "type": "text",
                    "analyzer": "chinese_analyzer"
                },
                "meta_keywords": {
                    "type": "text",
                    "analyzer": "chinese_analyzer"
                },
                "content": {
                    "type": "text",
                    "analyzer": "chinese_analyzer"
                },
                "h1_tags": {
                    "type": "text",
                    "analyzer": "chinese_analyzer"
                },
                "h2_tags": {
                    "type": "text",
                    "analyzer": "chinese_analyzer"
                },
                "h3_tags": {
                    "type": "text",
                    "analyzer": "chinese_analyzer"
                },
                "alt_texts": {
                    "type": "text",
                    "analyzer": "chinese_analyzer"
                },
                "internal_links": {
                    "type": "keyword"
                },
                "external_links": {
                    "type": "keyword"
                },
                "images": {
                    "type": "nested",
                    "properties": {
                        "src": {"type": "keyword"},
                        "alt": {"type": "text", "analyzer": "chinese_analyzer"},
                        "title": {"type": "text", "analyzer": "chinese_analyzer"}
                    }
                },
                "structured_data": {
                    "type": "object",
                    "enabled": False
                },
                "word_count": {"type": "integer"},
                "language": {"type": "keyword"},
                "domain": {"type": "keyword"},
                "path": {"type": "keyword"},
                "status_code": {"type": "integer"},
                "response_time": {"type": "float"},
                "last_modified": {"type": "date"},
                "crawled_at": {"type": "date"},
                "updated_at": {"type": "date"}
            }
        }
        
        # SEO 專用分析器配置
        settings_config = {
            "analysis": {
                "analyzer": {
                    "chinese_analyzer": {
                        "type": "custom",
                        "tokenizer": "standard",
                        "filter": ["lowercase", "stop", "trim", "seo_synonyms"]
                    },
                    "seo_analyzer": {
                        "type": "custom",
                        "tokenizer": "keyword",
                        "filter": ["lowercase", "trim"]
                    }
                },
                "filter": {
                    "seo_synonyms": {
                        "type": "synonym",
                        "synonyms": [
                            "SEO,搜尋引擎優化,搜索引擎優化",
                            "SEM,搜尋引擎行銷,搜索引擎行銷",
                            "關鍵字,關鍵詞,keyword",
                            "排名,排序,ranking",
                            "流量,訪問量,traffic",
                            "點擊率,CTR,click through rate",
                            "轉換率,conversion rate,轉化率"
                        ]
                    }
                }
            }
        }
        
        return await es_client.create_index(
            self.seo_content_index,
            mapping,
            settings_config
        )
    
    async def _create_seo_analysis_index(self) -> bool:
        """創建 SEO 分析結果索引 - 儲存關鍵字排名、競爭對手分析、技術 SEO 檢測結果"""
        mapping = {
            "properties": {
                "id": {"type": "keyword"},
                "url": {"type": "keyword"},
                "analysis_type": {"type": "keyword"},
                "target_keywords": {
                    "type": "text",
                    "analyzer": "chinese_analyzer",
                    "fields": {
                        "keyword": {"type": "keyword"}
                    }
                },
                "seo_score": {"type": "float"},
                "performance_score": {"type": "float"},
                "accessibility_score": {"type": "float"},
                "content_quality_score": {"type": "float"},
                "mobile_score": {"type": "float"},
                "security_score": {"type": "float"},
                "insights": {
                    "type": "nested",
                    "properties": {
                        "type": {"type": "keyword"},
                        "category": {"type": "keyword"},
                        "title": {"type": "text", "analyzer": "chinese_analyzer"},
                        "message": {"type": "text", "analyzer": "chinese_analyzer"},
                        "recommendation": {"type": "text", "analyzer": "chinese_analyzer"},
                        "priority": {"type": "keyword"},
                        "impact_score": {"type": "float"},
                        "difficulty_score": {"type": "float"}
                    }
                },
                "keyword_analysis": {
                    "type": "object",
                    "properties": {
                        "density": {"type": "float"},
                        "frequency": {"type": "integer"},
                        "positions": {"type": "integer"},
                        "prominence": {"type": "float"}
                    }
                },
                "content_metrics": {
                    "type": "object",
                    "properties": {
                        "word_count": {"type": "integer"},
                        "sentence_count": {"type": "integer"},
                        "paragraph_count": {"type": "integer"},
                        "readability_score": {"type": "float"},
                        "heading_structure_score": {"type": "float"}
                    }
                },
                "technical_metrics": {
                    "type": "object",
                    "properties": {
                        "page_load_time": {"type": "float"},
                        "mobile_friendly": {"type": "boolean"},
                        "https_enabled": {"type": "boolean"},
                        "meta_tags_present": {"type": "boolean"},
                        "structured_data_present": {"type": "boolean"}
                    }
                },
                "recommendations": {
                    "type": "text",
                    "analyzer": "chinese_analyzer"
                },
                "analyzed_at": {"type": "date"},
                "created_by": {"type": "keyword"},
                "updated_at": {"type": "date"}
            }
        }
        
        return await es_client.create_index(
            self.seo_analysis_index,
            mapping
        )
    
    async def _create_seo_keywords_index(self) -> bool:
        """創建 SEO 關鍵字索引 - 包含搜索建議功能"""
        mapping = {
            "properties": {
                "id": {"type": "keyword"},
                "keyword": {
                    "type": "text",
                    "analyzer": "chinese_analyzer",
                    "fields": {
                        "keyword": {"type": "keyword"},
                        "suggest": {
                            "type": "completion",
                            "analyzer": "chinese_analyzer"
                        }
                    }
                },
                "search_volume": {"type": "integer"},
                "competition": {"type": "keyword"},
                "difficulty": {"type": "float"},
                "cpc": {"type": "float"},
                "trends": {
                    "type": "nested",
                    "properties": {
                        "date": {"type": "date"},
                        "volume": {"type": "integer"}
                    }
                },
                "related_keywords": {
                    "type": "text",
                    "analyzer": "chinese_analyzer"
                },
                "long_tail_variations": {
                    "type": "text",
                    "analyzer": "chinese_analyzer"
                },
                "serp_features": {"type": "keyword"},
                "industry": {"type": "keyword"},
                "category": {"type": "keyword"},
                "intent": {"type": "keyword"},
                "season_trends": {
                    "type": "object",
                    "properties": {
                        "month": {"type": "keyword"},
                        "relative_volume": {"type": "float"}
                    }
                },
                "ranking_urls": {
                    "type": "nested",
                    "properties": {
                        "url": {"type": "keyword"},
                        "position": {"type": "integer"},
                        "title": {"type": "text", "analyzer": "chinese_analyzer"},
                        "meta_description": {"type": "text", "analyzer": "chinese_analyzer"}
                    }
                },
                "last_updated": {"type": "date"},
                "created_at": {"type": "date"}
            }
        }
        
        return await es_client.create_index(
            self.seo_keywords_index,
            mapping
        )
    
    async def _create_seo_competitors_index(self) -> bool:
        """創建 SEO 競爭對手索引"""
        mapping = {
            "properties": {
                "id": {"type": "keyword"},
                "domain": {"type": "keyword"},
                "url": {"type": "keyword"},
                "competitor_name": {
                    "type": "text",
                    "analyzer": "chinese_analyzer",
                    "fields": {
                        "keyword": {"type": "keyword"}
                    }
                },
                "industry": {"type": "keyword"},
                "domain_authority": {"type": "float"},
                "page_authority": {"type": "float"},
                "trust_score": {"type": "float"},
                "organic_traffic": {"type": "integer"},
                "organic_keywords": {"type": "integer"},
                "backlinks": {"type": "integer"},
                "referring_domains": {"type": "integer"},
                "top_keywords": {
                    "type": "nested",
                    "properties": {
                        "keyword": {"type": "text", "analyzer": "chinese_analyzer"},
                        "position": {"type": "integer"},
                        "search_volume": {"type": "integer"},
                        "traffic": {"type": "integer"}
                    }
                },
                "content_gaps": {
                    "type": "nested",
                    "properties": {
                        "keyword": {"type": "text", "analyzer": "chinese_analyzer"},
                        "competitor_position": {"type": "integer"},
                        "our_position": {"type": "integer"},
                        "opportunity_score": {"type": "float"}
                    }
                },
                "backlink_profile": {
                    "type": "object",
                    "properties": {
                        "total_backlinks": {"type": "integer"},
                        "dofollow_backlinks": {"type": "integer"},
                        "referring_domains": {"type": "integer"},
                        "anchor_text_distribution": {"type": "object"}
                    }
                },
                "technical_seo": {
                    "type": "object",
                    "properties": {
                        "mobile_friendly": {"type": "boolean"},
                        "https_enabled": {"type": "boolean"},
                        "page_speed_score": {"type": "float"},
                        "structured_data_usage": {"type": "boolean"}
                    }
                },
                "content_analysis": {
                    "type": "object",
                    "properties": {
                        "avg_content_length": {"type": "integer"},
                        "content_freshness": {"type": "float"},
                        "multimedia_usage": {"type": "float"}
                    }
                },
                "last_analyzed": {"type": "date"},
                "created_at": {"type": "date"},
                "updated_at": {"type": "date"}
            }
        }
        
        return await es_client.create_index(
            self.seo_competitors_index,
            mapping
        )
    
    async def sync_seo_content(self, content_data: Dict[str, Any]) -> bool:
        """同步 SEO 內容數據到 Elasticsearch"""
        try:
            # 準備索引文檔
            doc = {
                "id": content_data.get("id"),
                "url": content_data.get("url"),
                "title": content_data.get("title", ""),
                "meta_description": content_data.get("meta_description", ""),
                "meta_keywords": content_data.get("meta_keywords", ""),
                "content": content_data.get("content", ""),
                "h1_tags": content_data.get("h1_tags", []),
                "h2_tags": content_data.get("h2_tags", []),
                "h3_tags": content_data.get("h3_tags", []),
                "alt_texts": content_data.get("alt_texts", []),
                "internal_links": content_data.get("internal_links", []),
                "external_links": content_data.get("external_links", []),
                "images": content_data.get("images", []),
                "structured_data": content_data.get("structured_data", {}),
                "word_count": content_data.get("word_count", 0),
                "language": content_data.get("language", "zh-TW"),
                "domain": urlparse(content_data.get("url", "")).netloc,
                "path": urlparse(content_data.get("url", "")).path,
                "status_code": content_data.get("status_code", 200),
                "response_time": content_data.get("response_time", 0.0),
                "last_modified": content_data.get("last_modified"),
                "crawled_at": datetime.now(timezone.utc).isoformat(),
                "updated_at": datetime.now(timezone.utc).isoformat()
            }
            
            # 索引文檔
            success = await es_client.index_document(
                self.seo_content_index,
                content_data.get("id"),
                doc
            )
            
            if success:
                logger.info("SEO 內容同步成功", url=content_data.get("url"))
            
            return success
            
        except Exception as e:
            logger.error("SEO 內容同步失敗", error=str(e), url=content_data.get("url"))
            return False
    
    async def search_seo_content(self, query: str, size: int = 10, offset: int = 0, 
                                filters: Optional[Dict] = None) -> Dict[str, Any]:
        """搜索 SEO 內容"""
        try:
            # 構建搜索查詢
            search_query = {
                "bool": {
                    "must": [
                        {
                            "multi_match": {
                                "query": query,
                                "fields": [
                                    "title^3",
                                    "meta_description^2",
                                    "meta_keywords^2",
                                    "content",
                                    "h1_tags^2",
                                    "h2_tags",
                                    "h3_tags"
                                ],
                                "type": "best_fields",
                                "fuzziness": "AUTO"
                            }
                        }
                    ]
                }
            }
            
            # 添加過濾條件
            if filters:
                filter_clauses = []
                for field, value in filters.items():
                    if field == "domain" and value:
                        filter_clauses.append({"term": {"domain": value}})
                    elif field == "language" and value:
                        filter_clauses.append({"term": {"language": value}})
                    elif field == "date_range" and value:
                        filter_clauses.append({
                            "range": {
                                "updated_at": {
                                    "gte": value.get("start"),
                                    "lte": value.get("end")
                                }
                            }
                        })
                
                if filter_clauses:
                    search_query["bool"]["filter"] = filter_clauses
            
            # 執行搜索
            result = await es_client.search(
                self.seo_content_index,
                search_query,
                size=size,
                from_=offset,
                highlight={
                    "fields": {
                        "title": {},
                        "content": {"fragment_size": 200, "number_of_fragments": 3},
                        "meta_description": {}
                    }
                },
                sort=[
                    {"_score": {"order": "desc"}},
                    {"updated_at": {"order": "desc"}}
                ]
            )
            
            # 格式化結果
            hits = result.get("hits", {})
            total = hits.get("total", {}).get("value", 0)
            documents = []
            
            for hit in hits.get("hits", []):
                doc = hit["_source"]
                doc["score"] = hit["_score"]
                if "highlight" in hit:
                    doc["highlight"] = hit["highlight"]
                documents.append(doc)
            
            return {
                "total": total,
                "documents": documents,
                "took": result.get("took", 0)
            }
            
        except Exception as e:
            logger.error("SEO 內容搜索失敗", error=str(e), query=query)
            return {"total": 0, "documents": [], "took": 0}
    
    async def search_keywords(self, query: str, size: int = 10, filters: Optional[Dict] = None) -> Dict[str, Any]:
        """搜索關鍵字"""
        try:
            search_body = {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "multi_match": {
                                    "query": query,
                                    "fields": ["keyword^2", "related_keywords", "long_tail_variations"],
                                    "type": "best_fields",
                                    "fuzziness": "AUTO"
                                }
                            }
                        ]
                    }
                },
                "size": size,
                "sort": [
                    {"search_volume": {"order": "desc"}},
                    {"_score": {"order": "desc"}}
                ]
            }
            
            if filters:
                filter_clauses = []
                for field, value in filters.items():
                    if field == "difficulty_range" and value:
                        filter_clauses.append({
                            "range": {
                                "difficulty": {
                                    "gte": value.get("min", 0),
                                    "lte": value.get("max", 100)
                                }
                            }
                        })
                    elif field == "volume_range" and value:
                        filter_clauses.append({
                            "range": {
                                "search_volume": {
                                    "gte": value.get("min", 0),
                                    "lte": value.get("max", 1000000)
                                }
                            }
                        })
                
                if filter_clauses:
                    search_body["query"]["bool"]["filter"] = filter_clauses
            
            result = await es_client.search(self.seo_keywords_index, search_body)
            
            hits = result.get("hits", {})
            return {
                "total": hits.get("total", {}).get("value", 0),
                "keywords": [hit["_source"] for hit in hits.get("hits", [])],
                "took": result.get("took", 0)
            }
            
        except Exception as e:
            logger.error("關鍵字搜索失敗", error=str(e), query=query)
            return {"total": 0, "keywords": [], "took": 0}
    
    async def get_keyword_suggestions(self, prefix: str, size: int = 10) -> List[str]:
        """獲取關鍵字建議"""
        try:
            search_body = {
                "suggest": {
                    "keyword_suggest": {
                        "prefix": prefix,
                        "completion": {
                            "field": "keyword.suggest",
                            "size": size,
                            "skip_duplicates": True
                        }
                    }
                }
            }
            
            result = await es_client.search(self.seo_keywords_index, search_body)
            
            suggestions = []
            for suggestion in result.get("suggest", {}).get("keyword_suggest", []):
                for option in suggestion.get("options", []):
                    suggestions.append(option["text"])
            
            return suggestions
            
        except Exception as e:
            logger.error("關鍵字建議獲取失敗", error=str(e), prefix=prefix)
            return []
    
    async def analyze_content_gaps(self, target_keywords: List[str], 
                                 competitor_domains: List[str]) -> Dict[str, Any]:
        """分析內容空白"""
        try:
            gaps = []
            
            for keyword in target_keywords:
                # 搜索競爭對手的相關內容
                competitor_query = {
                    "query": {
                        "bool": {
                            "must": [
                                {"match": {"content": keyword}},
                                {"terms": {"domain": competitor_domains}}
                            ]
                        }
                    },
                    "size": 5
                }
                
                # 搜索我們的相關內容
                our_query = {
                    "query": {
                        "bool": {
                            "must": [
                                {"match": {"content": keyword}}
                            ],
                            "must_not": [
                                {"terms": {"domain": competitor_domains}}
                            ]
                        }
                    },
                    "size": 5
                }
                
                competitor_result = await es_client.search(self.seo_content_index, competitor_query)
                our_result = await es_client.search(self.seo_content_index, our_query)
                
                competitor_count = competitor_result.get("hits", {}).get("total", {}).get("value", 0)
                our_count = our_result.get("hits", {}).get("total", {}).get("value", 0)
                
                if competitor_count > our_count:
                    gap_score = (competitor_count - our_count) / max(competitor_count, 1)
                    gaps.append({
                        "keyword": keyword,
                        "competitor_content_count": competitor_count,
                        "our_content_count": our_count,
                        "gap_score": gap_score,
                        "priority": "high" if gap_score > 0.7 else "medium" if gap_score > 0.3 else "low"
                    })
            
            return {
                "gaps": sorted(gaps, key=lambda x: x["gap_score"], reverse=True),
                "total_gaps": len(gaps)
            }
            
        except Exception as e:
            logger.error("內容空白分析失敗", error=str(e))
            return {"gaps": [], "total_gaps": 0}
    
    async def get_search_analytics(self, date_range: Optional[Dict] = None) -> Dict[str, Any]:
        """獲取搜索分析統計"""
        try:
            # 構建聚合查詢
            agg_body = {
                "size": 0,
                "aggs": {
                    "total_content": {
                        "value_count": {"field": "id"}
                    },
                    "domains": {
                        "terms": {"field": "domain", "size": 10}
                    },
                    "languages": {
                        "terms": {"field": "language", "size": 5}
                    },
                    "avg_word_count": {
                        "avg": {"field": "word_count"}
                    },
                    "content_by_date": {
                        "date_histogram": {
                            "field": "updated_at",
                            "calendar_interval": "day",
                            "min_doc_count": 1
                        }
                    }
                }
            }
            
            # 添加日期範圍過濾
            if date_range:
                agg_body["query"] = {
                    "range": {
                        "updated_at": {
                            "gte": date_range.get("start"),
                            "lte": date_range.get("end")
                        }
                    }
                }
            
            result = await es_client.search(self.seo_content_index, agg_body)
            
            aggs = result.get("aggregations", {})
            
            return {
                "total_content": aggs.get("total_content", {}).get("value", 0),
                "top_domains": [
                    {"domain": bucket["key"], "count": bucket["doc_count"]}
                    for bucket in aggs.get("domains", {}).get("buckets", [])
                ],
                "languages": [
                    {"language": bucket["key"], "count": bucket["doc_count"]}
                    for bucket in aggs.get("languages", {}).get("buckets", [])
                ],
                "avg_word_count": aggs.get("avg_word_count", {}).get("value", 0),
                "content_timeline": [
                    {
                        "date": bucket["key_as_string"],
                        "count": bucket["doc_count"]
                    }
                    for bucket in aggs.get("content_by_date", {}).get("buckets", [])
                ]
            }
            
        except Exception as e:
            logger.error("搜索分析統計獲取失敗", error=str(e))
            return {
                "total_content": 0,
                "top_domains": [],
                "languages": [],
                "avg_word_count": 0,
                "content_timeline": []
            }


# 全域服務實例
seo_search_service = ElasticsearchSEOService() 