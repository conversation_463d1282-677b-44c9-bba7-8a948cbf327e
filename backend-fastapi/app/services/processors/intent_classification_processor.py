"""
意圖分類處理器
使用規則和機器學習方法對查詢意圖進行分類
"""

import re
from typing import Dict, List, Tuple
import structlog

from app.services.data_collection_pipeline import ProcessedQueryData

logger = structlog.get_logger()


class IntentClassificationProcessor:
    """意圖分類處理器"""
    
    def __init__(self):
        self.name = "IntentClassification"
        
        # 意圖分類規則
        self.intent_patterns = {
            'search': {
                'patterns': [
                    r'搜[尋索]',
                    r'找.*[商產]品',
                    r'search',
                    r'find',
                    r'looking for'
                ],
                'keywords': ['搜尋', '搜索', '查找', '尋找', 'search', 'find']
            },
            'information': {
                'patterns': [
                    r'什麼是',
                    r'如何.*',
                    r'怎麼.*',
                    r'為什麼',
                    r'what is',
                    r'how to',
                    r'why'
                ],
                'keywords': ['什麼', '如何', '怎麼', '為什麼', 'what', 'how', 'why']
            },
            'comparison': {
                'patterns': [
                    r'比較',
                    r'vs',
                    r'對比',
                    r'差異',
                    r'compare',
                    r'difference'
                ],
                'keywords': ['比較', '對比', '差異', 'vs', 'compare', 'versus']
            },
            'purchase': {
                'patterns': [
                    r'購買',
                    r'買',
                    r'訂購',
                    r'價格',
                    r'多少錢',
                    r'buy',
                    r'purchase',
                    r'price'
                ],
                'keywords': ['購買', '買', '訂購', '價格', 'buy', 'price', 'cost']
            },
            'review': {
                'patterns': [
                    r'評[價論]',
                    r'心得',
                    r'使用.*經驗',
                    r'review',
                    r'rating'
                ],
                'keywords': ['評價', '評論', '心得', '經驗', 'review', 'rating']
            },
            'troubleshooting': {
                'patterns': [
                    r'問題',
                    r'錯誤',
                    r'故障',
                    r'不.*工作',
                    r'problem',
                    r'error',
                    r'issue'
                ],
                'keywords': ['問題', '錯誤', '故障', 'problem', 'error', 'issue']
            },
            'navigation': {
                'patterns': [
                    r'哪裡.*[買找]',
                    r'地[址點]',
                    r'位置',
                    r'where',
                    r'location'
                ],
                'keywords': ['哪裡', '地址', '位置', 'where', 'location']
            },
            'recommendation': {
                'patterns': [
                    r'推薦',
                    r'建議',
                    r'最好.*',
                    r'recommend',
                    r'suggest',
                    r'best'
                ],
                'keywords': ['推薦', '建議', '最好', 'recommend', 'best', 'suggest']
            }
        }
        
        # SEO 相關意圖
        self.seo_patterns = {
            'seo_analysis': {
                'patterns': [
                    r'SEO.*分析',
                    r'搜尋.*優化',
                    r'關鍵[字詞].*分析',
                    r'seo.*analysis'
                ],
                'keywords': ['SEO', '優化', '分析', '關鍵字']
            },
            'keyword_research': {
                'patterns': [
                    r'關鍵[字詞].*研究',
                    r'關鍵[字詞].*工具',
                    r'keyword.*research'
                ],
                'keywords': ['關鍵字研究', '關鍵詞工具', 'keyword research']
            },
            'competitor_analysis': {
                'patterns': [
                    r'競爭.*分析',
                    r'對手.*分析',
                    r'competitor.*analysis'
                ],
                'keywords': ['競爭分析', '對手分析', 'competitor']
            },
            'content_optimization': {
                'patterns': [
                    r'內容.*優化',
                    r'文章.*SEO',
                    r'content.*optimization'
                ],
                'keywords': ['內容優化', '文章SEO', 'content optimization']
            }
        }

    async def process(self, data: ProcessedQueryData) -> ProcessedQueryData:
        """處理意圖分類"""
        try:
            query = data.normalized_query
            
            if not query:
                data.intent = 'unknown'
                data.intent_confidence = 0.0
                return data
            
            # 執行意圖分類
            intent, confidence = self._classify_intent(query)
            
            data.intent = intent
            data.intent_confidence = confidence
            
            logger.debug(
                "意圖分類完成",
                query=query,
                intent=intent,
                confidence=confidence
            )
            
            return data
            
        except Exception as e:
            logger.error("意圖分類處理失敗", error=str(e))
            data.intent = 'unknown'
            data.intent_confidence = 0.0
            return data

    def _classify_intent(self, query: str) -> Tuple[str, float]:
        """執行意圖分類"""
        query_lower = query.lower()
        intent_scores = {}
        
        # 檢查一般意圖
        for intent, config in self.intent_patterns.items():
            score = self._calculate_intent_score(query_lower, config)
            if score > 0:
                intent_scores[intent] = score
        
        # 檢查 SEO 特定意圖
        for intent, config in self.seo_patterns.items():
            score = self._calculate_intent_score(query_lower, config)
            if score > 0:
                intent_scores[intent] = score
        
        # 如果沒有匹配到任何意圖
        if not intent_scores:
            return 'unknown', 0.0
        
        # 選擇得分最高的意圖
        best_intent = max(intent_scores.items(), key=lambda x: x[1])
        intent, raw_score = best_intent
        
        # 計算信心度（0-1之間）
        confidence = min(raw_score / 10.0, 1.0)
        
        return intent, confidence

    def _calculate_intent_score(self, query: str, config: Dict) -> float:
        """計算意圖得分"""
        score = 0.0
        
        # 檢查模式匹配
        for pattern in config.get('patterns', []):
            if re.search(pattern, query):
                score += 5.0  # 模式匹配得到高分
        
        # 檢查關鍵詞匹配
        for keyword in config.get('keywords', []):
            if keyword.lower() in query:
                score += 2.0  # 關鍵詞匹配得到中等分數
        
        return score

    def _classify_by_context(self, query: str, metadata: Dict) -> Tuple[str, float]:
        """基於上下文的意圖分類"""
        # 檢查來源類型
        source = metadata.get('source', '')
        if source == 'api_request':
            endpoint = metadata.get('endpoint', '')
            if 'analyze' in endpoint:
                return 'seo_analysis', 0.8
            elif 'keyword' in endpoint:
                return 'keyword_research', 0.8
        
        # 檢查設備類型
        device_type = metadata.get('device_type', '')
        if device_type == 'mobile' and any(word in query for word in ['附近', '地址', 'near', 'location']):
            return 'navigation', 0.7
        
        # 檢查時間上下文
        time_info = metadata.get('time_info', {})
        if time_info.get('is_business_hours', False) and 'price' in query:
            return 'purchase', 0.6
        
        return 'unknown', 0.0

    def get_intent_explanation(self, intent: str) -> str:
        """獲取意圖解釋"""
        explanations = {
            'search': '用戶正在搜索特定的產品或信息',
            'information': '用戶想要了解某個概念或獲取知識',
            'comparison': '用戶想要比較不同的選項或產品',
            'purchase': '用戶有購買意向或詢問價格',
            'review': '用戶想要查看評價或分享經驗',
            'troubleshooting': '用戶遇到問題需要解決方案',
            'navigation': '用戶需要位置或導航信息',
            'recommendation': '用戶尋求推薦或建議',
            'seo_analysis': '用戶需要SEO分析服務',
            'keyword_research': '用戶進行關鍵字研究',
            'competitor_analysis': '用戶需要競爭對手分析',
            'content_optimization': '用戶需要內容優化建議',
            'unknown': '無法確定用戶意圖'
        }
        
        return explanations.get(intent, '未知意圖類型')

    def get_intent_statistics(self) -> Dict[str, int]:
        """獲取意圖分類統計"""
        return {
            'total_patterns': len(self.intent_patterns) + len(self.seo_patterns),
            'general_intents': len(self.intent_patterns),
            'seo_intents': len(self.seo_patterns)
        } 