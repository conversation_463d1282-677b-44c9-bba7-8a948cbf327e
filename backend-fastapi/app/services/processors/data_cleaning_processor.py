"""
數據清洗處理器
對原始查詢數據進行清理和標準化
"""

import re
from typing import List, Set
import structlog

from app.services.data_collection_pipeline import ProcessedQueryData

logger = structlog.get_logger()


class DataCleaningProcessor:
    """數據清洗處理器"""
    
    def __init__(self):
        self.name = "DataCleaning"
        
        # 中英文停用詞
        self.stop_words = {
            'chinese': {
                '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一個',
                '上', '也', '很', '到', '說', '要', '去', '你', '會', '對', '可以', '這個',
                '這', '那', '什麼', '怎麼', '為什麼', '如何', '哪裡', '時候', '還', '又'
            },
            'english': {
                'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
                'of', 'with', 'by', 'from', 'as', 'is', 'was', 'are', 'were', 'be',
                'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would',
                'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these',
                'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him',
                'her', 'us', 'them', 'my', 'your', 'his', 'its', 'our', 'their'
            }
        }
        
        # 特殊字符清理模式
        self.cleaning_patterns = [
            (r'[^\w\s\u4e00-\u9fa5]', ' '),      # 保留字母數字中文
            (r'\s+', ' '),                        # 多個空格合併
            (r'^\s+|\s+$', ''),                   # 去除首尾空格
        ]

    async def process(self, data: ProcessedQueryData) -> ProcessedQueryData:
        """處理數據清洗"""
        try:
            original_query = data.original.query
            
            if not original_query:
                data.normalized_query = ""
                return data
            
            # 執行清洗步驟
            cleaned_query = self._clean_query(original_query)
            normalized_query = self._normalize_query(cleaned_query)
            
            # 更新數據
            data.normalized_query = normalized_query
            
            logger.debug(
                "查詢清洗完成",
                original=original_query,
                cleaned=cleaned_query,
                normalized=normalized_query
            )
            
            return data
            
        except Exception as e:
            logger.error("數據清洗處理失敗", error=str(e))
            # 發生錯誤時使用原始查詢
            data.normalized_query = data.original.query
            return data

    def _clean_query(self, query: str) -> str:
        """基礎清理"""
        if not query:
            return ""
        
        # 轉換為小寫
        query = query.lower()
        
        # 應用清理模式
        for pattern, replacement in self.cleaning_patterns:
            query = re.sub(pattern, replacement, query)
        
        return query.strip()

    def _normalize_query(self, query: str) -> str:
        """標準化查詢"""
        if not query:
            return ""
        
        # 分詞（簡化版本）
        words = query.split()
        
        # 移除停用詞
        filtered_words = []
        for word in words:
            if not self._is_stop_word(word) and len(word) > 1:
                filtered_words.append(word)
        
        # 去重但保持順序
        unique_words = []
        seen = set()
        for word in filtered_words:
            if word not in seen:
                unique_words.append(word)
                seen.add(word)
        
        return ' '.join(unique_words)

    def _is_stop_word(self, word: str) -> bool:
        """檢查是否為停用詞"""
        word_lower = word.lower()
        
        # 檢查中文停用詞
        if word_lower in self.stop_words['chinese']:
            return True
        
        # 檢查英文停用詞
        if word_lower in self.stop_words['english']:
            return True
        
        # 檢查是否為純數字或單個字符
        if word_lower.isdigit() and len(word_lower) == 1:
            return True
        
        return False

    def _classify_query_type(self, query: str) -> str:
        """分類查詢類型"""
        if not query:
            return 'empty'
        
        words = query.split()
        
        # 短查詢
        if len(words) == 1 and len(query) < 3:
            return 'short'
        
        # 問題查詢
        question_indicators = ['什麼', '如何', '怎麼', '為什麼', '哪裡', '何時', '誰', 'what', 'how', 'why', 'where', 'when', 'who']
        if any(indicator in query.lower() for indicator in question_indicators):
            return 'question'
        
        # 產品ID模式
        if re.match(r'^[A-Z0-9]{6,}$', query.upper().replace(' ', '')):
            return 'product_id'
        
        # 品牌查詢（單一大寫開頭的詞）
        if len(words) == 1 and words[0].istitle():
            return 'brand'
        
        # 長尾關鍵詞
        if len(words) > 4:
            return 'long_tail'
        
        # 短語查詢
        if len(words) > 1:
            return 'phrase'
        
        # 關鍵詞查詢
        return 'keyword'

    def get_cleaning_stats(self, original: str, cleaned: str, normalized: str) -> dict:
        """獲取清理統計"""
        return {
            'original_length': len(original),
            'cleaned_length': len(cleaned),
            'normalized_length': len(normalized),
            'words_removed': len(original.split()) - len(normalized.split()),
            'compression_ratio': len(normalized) / len(original) if original else 0
        } 