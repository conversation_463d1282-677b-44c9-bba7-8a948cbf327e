"""
主題提取處理器
從查詢中提取相關主題和關鍵概念
"""

import re
from typing import List, Dict, Set
from collections import Counter
import structlog

from app.services.data_collection_pipeline import ProcessedQueryData

logger = structlog.get_logger()


class TopicExtractionProcessor:
    """主題提取處理器"""
    
    def __init__(self):
        self.name = "TopicExtraction"
        
        # 主題關鍵詞庫
        self.topic_keywords = {
            'seo': {
                'keywords': ['seo', '搜尋引擎優化', '搜索引擎優化', '關鍵字', '關鍵詞', 'keywords', 
                           '排名', 'ranking', '優化', 'optimization', '搜索排名'],
                'weight': 1.0
            },
            'digital_marketing': {
                'keywords': ['數位行銷', '數字營銷', 'digital marketing', '網路行銷', '網絡營銷',
                           '社群行銷', 'social media marketing', '內容行銷', 'content marketing'],
                'weight': 1.0
            },
            'web_development': {
                'keywords': ['網站開發', '網頁設計', 'web development', 'website', '前端', 'frontend',
                           '後端', 'backend', 'html', 'css', 'javascript', 'react', 'vue'],
                'weight': 0.9
            },
            'analytics': {
                'keywords': ['分析', 'analytics', '數據分析', 'data analysis', '統計', 'statistics',
                           'google analytics', 'ga', '流量分析', 'traffic analysis'],
                'weight': 0.9
            },
            'ecommerce': {
                'keywords': ['電商', '電子商務', 'ecommerce', 'e-commerce', '網店', '購物網站',
                           '線上購物', 'online shopping', '商城'],
                'weight': 0.8
            },
            'content': {
                'keywords': ['內容', 'content', '文章', 'article', '部落格', 'blog', '寫作', 'writing',
                           '編輯', 'editing', '內容創作'],
                'weight': 0.8
            },
            'social_media': {
                'keywords': ['社群媒體', 'social media', 'facebook', 'instagram', 'twitter', 'linkedin',
                           'youtube', '粉絲專頁', '社群經營'],
                'weight': 0.7
            },
            'mobile': {
                'keywords': ['手機', 'mobile', '行動裝置', 'app', '應用程式', 'ios', 'android',
                           '行動版', 'responsive'],
                'weight': 0.7
            },
            'advertising': {
                'keywords': ['廣告', 'advertising', '投放', 'google ads', 'facebook ads', 'ppc',
                           '點擊付費', 'cpc', 'cpm', '廣告投放'],
                'weight': 0.8
            },
            'branding': {
                'keywords': ['品牌', 'branding', '品牌形象', 'brand image', '商標', 'logo',
                           '品牌策略', 'brand strategy'],
                'weight': 0.7
            }
        }
        
        # 行業特定主題
        self.industry_topics = {
            'retail': ['零售', '零售業', 'retail', '販售', '商品'],
            'finance': ['金融', '銀行', 'finance', 'banking', '投資'],
            'healthcare': ['醫療', '健康', 'healthcare', 'medical', '診所'],
            'education': ['教育', '學習', 'education', 'learning', '培訓'],
            'technology': ['科技', '技術', 'technology', 'tech', 'IT'],
            'travel': ['旅遊', '旅行', 'travel', '觀光', 'tourism'],
            'food': ['美食', '餐廳', 'food', 'restaurant', '料理'],
            'fashion': ['時尚', '服裝', 'fashion', '穿搭', '服飾']
        }
        
        # 停用詞（不應該被識別為主題的詞）
        self.topic_stopwords = {
            '是', '的', '了', '在', '有', '和', '要', '可以', '這個', '那個',
            'is', 'the', 'a', 'an', 'and', 'or', 'but', 'with', 'for'
        }

    async def process(self, data: ProcessedQueryData) -> ProcessedQueryData:
        """處理主題提取"""
        try:
            query = data.normalized_query
            
            if not query:
                data.topics = []
                return data
            
            # 提取主題
            topics = self._extract_topics(query)
            
            # 如果沒有找到預定義主題，嘗試提取關鍵概念
            if not topics:
                topics = self._extract_key_concepts(query)
            
            data.topics = topics[:5]  # 限制主題數量
            
            logger.debug(
                "主題提取完成",
                query=query,
                topics=topics
            )
            
            return data
            
        except Exception as e:
            logger.error("主題提取處理失敗", error=str(e))
            data.topics = []
            return data

    def _extract_topics(self, query: str) -> List[str]:
        """提取主題"""
        query_lower = query.lower()
        topic_scores = {}
        
        # 檢查預定義主題
        for topic, config in self.topic_keywords.items():
            score = 0
            weight = config.get('weight', 1.0)
            
            for keyword in config['keywords']:
                if keyword.lower() in query_lower:
                    # 完整匹配得到更高分數
                    if keyword.lower() == query_lower:
                        score += 10 * weight
                    # 詞邊界匹配
                    elif re.search(r'\b' + re.escape(keyword.lower()) + r'\b', query_lower):
                        score += 5 * weight
                    # 包含匹配
                    else:
                        score += 2 * weight
            
            if score > 0:
                topic_scores[topic] = score
        
        # 檢查行業主題
        for industry, keywords in self.industry_topics.items():
            score = 0
            for keyword in keywords:
                if keyword.lower() in query_lower:
                    score += 3
            
            if score > 0:
                topic_scores[f"industry_{industry}"] = score
        
        # 按分數排序並返回主題
        sorted_topics = sorted(topic_scores.items(), key=lambda x: x[1], reverse=True)
        
        return [topic for topic, score in sorted_topics if score >= 2]

    def _extract_key_concepts(self, query: str) -> List[str]:
        """提取關鍵概念（當沒有預定義主題時）"""
        words = query.split()
        concepts = []
        
        for word in words:
            # 過濾停用詞和短詞
            if (len(word) > 2 and 
                word.lower() not in self.topic_stopwords and
                not word.isdigit()):
                concepts.append(word)
        
        # 如果詞太少，嘗試提取詞組
        if len(concepts) < 2 and len(words) > 1:
            # 生成2-gram
            for i in range(len(words) - 1):
                phrase = f"{words[i]} {words[i+1]}"
                if len(phrase) > 4:
                    concepts.append(phrase)
        
        return concepts[:3]  # 限制概念數量

    def _analyze_topic_sentiment(self, query: str, topics: List[str]) -> Dict[str, str]:
        """分析主題情感"""
        positive_indicators = ['好', '棒', '優秀', '推薦', 'good', 'great', 'best', 'awesome']
        negative_indicators = ['差', '糟', '爛', '問題', 'bad', 'terrible', 'problem', 'issue']
        
        query_lower = query.lower()
        
        topic_sentiments = {}
        for topic in topics:
            sentiment = 'neutral'
            
            # 檢查正面情感
            if any(pos in query_lower for pos in positive_indicators):
                sentiment = 'positive'
            # 檢查負面情感
            elif any(neg in query_lower for neg in negative_indicators):
                sentiment = 'negative'
            
            topic_sentiments[topic] = sentiment
        
        return topic_sentiments

    def _extract_entities(self, query: str) -> List[Dict[str, str]]:
        """提取命名實體"""
        entities = []
        
        # 簡化的實體提取（實際應用中可以使用 NER 模型）
        
        # 品牌名稱（大寫開頭的單詞）
        brand_pattern = r'\b[A-Z][a-z]+\b'
        brands = re.findall(brand_pattern, query)
        for brand in brands:
            if len(brand) > 2:
                entities.append({
                    'text': brand,
                    'type': 'brand',
                    'confidence': 0.7
                })
        
        # 數字（可能是價格、數量等）
        number_pattern = r'\b\d+(?:,\d{3})*(?:\.\d+)?\b'
        numbers = re.findall(number_pattern, query)
        for number in numbers:
            entities.append({
                'text': number,
                'type': 'number',
                'confidence': 0.9
            })
        
        # URL或域名
        url_pattern = r'(?:https?://)?(?:www\.)?[\w.-]+\.[a-z]{2,}'
        urls = re.findall(url_pattern, query, re.IGNORECASE)
        for url in urls:
            entities.append({
                'text': url,
                'type': 'url',
                'confidence': 1.0
            })
        
        return entities

    def get_topic_hierarchy(self) -> Dict[str, List[str]]:
        """獲取主題層次結構"""
        return {
            'marketing': ['seo', 'digital_marketing', 'advertising', 'social_media'],
            'technology': ['web_development', 'mobile', 'analytics'],
            'business': ['ecommerce', 'branding', 'content'],
            'industry': [f"industry_{industry}" for industry in self.industry_topics.keys()]
        }

    def get_topic_statistics(self) -> Dict[str, int]:
        """獲取主題統計信息"""
        return {
            'total_topics': len(self.topic_keywords),
            'industry_topics': len(self.industry_topics),
            'total_keywords': sum(len(config['keywords']) for config in self.topic_keywords.values())
        } 