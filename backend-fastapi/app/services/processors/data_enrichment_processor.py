"""
數據富化處理器
為查詢數據添加地理位置、用戶資料、時間等額外信息
"""

from datetime import datetime
from typing import Dict, Any, Optional
import structlog

from app.services.data_collection_pipeline import ProcessedQueryData
from app.core.redis import get_redis

logger = structlog.get_logger()


class DataEnrichmentProcessor:
    """數據富化處理器"""
    
    def __init__(self):
        self.name = "DataEnrichment"
        
        # 地理位置數據庫（簡化版本）
        self.geo_database = {
            # 台灣主要城市IP段（示例）
            '台北': {'lat': 25.0330, 'lng': 121.5654, 'timezone': 'Asia/Taipei'},
            '高雄': {'lat': 22.6273, 'lng': 120.3014, 'timezone': 'Asia/Taipei'},
            '台中': {'lat': 24.1477, 'lng': 120.6736, 'timezone': 'Asia/Taipei'},
            # 其他地區
            '香港': {'lat': 22.3193, 'lng': 114.1694, 'timezone': 'Asia/Hong_Kong'},
            '新加坡': {'lat': 1.3521, 'lng': 103.8198, 'timezone': 'Asia/Singapore'}
        }

    async def process(self, data: ProcessedQueryData) -> ProcessedQueryData:
        """處理數據富化"""
        try:
            # 地理位置富化
            if data.original.ip_address:
                data.location = await self._enrich_location(data.original.ip_address)
            
            # 時間資訊富化
            data.original.metadata = data.original.metadata or {}
            data.original.metadata['time_info'] = self._enrich_time_info(data.original.timestamp)
            
            # 設備資訊富化
            if data.original.user_agent:
                data.device_info = self._enrich_device_info(data.original.user_agent)
            
            # 用戶資料富化
            if data.original.user_id:
                data.user_profile = await self._enrich_user_profile(data.original.user_id)
            
            # 查詢複雜度分析
            data.original.metadata['query_complexity'] = self._analyze_query_complexity(data.normalized_query)
            
            logger.debug("數據富化完成", query_id=data.original.id)
            return data
            
        except Exception as e:
            logger.error("數據富化處理失敗", error=str(e))
            return data

    async def _enrich_location(self, ip_address: str) -> Optional[Dict[str, Any]]:
        """地理位置富化"""
        try:
            # 簡化的IP地理位置查找
            # 實際實現應該使用 MaxMind GeoIP2 或類似服務
            
            # 示例：基於IP前綴判斷地區
            if ip_address.startswith('220.'):  # 台灣電信IP段示例
                location = {
                    'country': 'TW',
                    'country_name': '台灣',
                    'city': '台北',
                    'region': '台北市',
                    'latitude': 25.0330,
                    'longitude': 121.5654,
                    'timezone': 'Asia/Taipei',
                    'isp': 'Chunghwa Telecom'
                }
            elif ip_address.startswith('113.'):  # 香港IP段示例
                location = {
                    'country': 'HK',
                    'country_name': '香港',
                    'city': '香港',
                    'region': '香港特別行政區',
                    'latitude': 22.3193,
                    'longitude': 114.1694,
                    'timezone': 'Asia/Hong_Kong',
                    'isp': 'PCCW'
                }
            else:
                # 默認位置
                location = {
                    'country': 'UNKNOWN',
                    'country_name': '未知',
                    'city': '未知',
                    'region': '未知',
                    'latitude': None,
                    'longitude': None,
                    'timezone': 'UTC',
                    'isp': '未知'
                }
            
            return location
            
        except Exception as e:
            logger.error("地理位置富化失敗", ip=ip_address, error=str(e))
            return None

    def _enrich_time_info(self, timestamp: datetime) -> Dict[str, Any]:
        """時間資訊富化"""
        if isinstance(timestamp, str):
            timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        
        return {
            'hour': timestamp.hour,
            'day_of_week': timestamp.weekday(),  # 0=Monday, 6=Sunday
            'is_weekend': timestamp.weekday() >= 5,
            'is_business_hours': 9 <= timestamp.hour < 18,
            'is_peak_hours': timestamp.hour in [9, 10, 11, 14, 15, 16],
            'season': self._get_season(timestamp.month),
            'month': timestamp.month,
            'year': timestamp.year
        }

    def _get_season(self, month: int) -> str:
        """獲取季節"""
        if month in [12, 1, 2]:
            return 'winter'
        elif month in [3, 4, 5]:
            return 'spring'
        elif month in [6, 7, 8]:
            return 'summer'
        else:
            return 'autumn'

    def _enrich_device_info(self, user_agent: str) -> Dict[str, Any]:
        """設備資訊富化"""
        device_info = {
            'device_type': 'unknown',
            'os': 'unknown',
            'browser': 'unknown',
            'is_mobile': False,
            'is_tablet': False,
            'is_desktop': False,
            'is_bot': False
        }
        
        if not user_agent:
            return device_info
        
        user_agent_lower = user_agent.lower()
        
        # 檢測機器人
        bot_indicators = ['bot', 'crawler', 'spider', 'scraper', 'index']
        if any(indicator in user_agent_lower for indicator in bot_indicators):
            device_info['is_bot'] = True
            device_info['device_type'] = 'bot'
            return device_info
        
        # 檢測設備類型
        if any(mobile in user_agent_lower for mobile in ['mobile', 'android', 'iphone']):
            device_info['device_type'] = 'mobile'
            device_info['is_mobile'] = True
        elif any(tablet in user_agent_lower for tablet in ['tablet', 'ipad']):
            device_info['device_type'] = 'tablet'
            device_info['is_tablet'] = True
        else:
            device_info['device_type'] = 'desktop'
            device_info['is_desktop'] = True
        
        # 檢測作業系統
        if 'windows' in user_agent_lower:
            device_info['os'] = 'Windows'
        elif 'mac' in user_agent_lower:
            device_info['os'] = 'macOS'
        elif 'linux' in user_agent_lower:
            device_info['os'] = 'Linux'
        elif 'android' in user_agent_lower:
            device_info['os'] = 'Android'
        elif 'ios' in user_agent_lower or 'iphone' in user_agent_lower:
            device_info['os'] = 'iOS'
        
        # 檢測瀏覽器
        if 'chrome' in user_agent_lower:
            device_info['browser'] = 'Chrome'
        elif 'firefox' in user_agent_lower:
            device_info['browser'] = 'Firefox'
        elif 'safari' in user_agent_lower:
            device_info['browser'] = 'Safari'
        elif 'edge' in user_agent_lower:
            device_info['browser'] = 'Edge'
        
        return device_info

    async def _enrich_user_profile(self, user_id: str) -> Optional[Dict[str, Any]]:
        """用戶資料富化"""
        try:
            # 從 Redis 快取獲取用戶資料
            redis_client = await get_redis()
            cached_profile = await redis_client.get(f"user_profile:{user_id}")
            
            if cached_profile:
                import json
                return json.loads(cached_profile)
            
            # 如果快取中沒有，從數據庫查詢（示例）
            user_profile = await self._fetch_user_from_database(user_id)
            
            if user_profile:
                # 快取用戶資料
                import json
                await redis_client.setex(
                    f"user_profile:{user_id}",
                    3600,  # 1小時過期
                    json.dumps(user_profile)
                )
            
            return user_profile
            
        except Exception as e:
            logger.error("用戶資料富化失敗", user_id=user_id, error=str(e))
            return None

    async def _fetch_user_from_database(self, user_id: str) -> Optional[Dict[str, Any]]:
        """從數據庫獲取用戶資料"""
        # 這裡應該實現實際的數據庫查詢
        # 暫時返回示例數據
        if user_id == 'anonymous':
            return {
                'is_anonymous': True,
                'user_type': 'guest',
                'registration_date': None,
                'total_searches': 0,
                'preferred_language': 'zh-TW'
            }
        
        return {
            'is_anonymous': False,
            'user_type': 'registered',
            'registration_date': '2024-01-01',
            'total_searches': 156,
            'preferred_language': 'zh-TW',
            'subscription_tier': 'premium',
            'interests': ['SEO', '數位行銷', '網站優化']
        }

    def _analyze_query_complexity(self, query: str) -> Dict[str, Any]:
        """分析查詢複雜度"""
        if not query:
            return {'level': 'empty', 'score': 0}
        
        words = query.split()
        word_count = len(words)
        char_count = len(query)
        
        # 計算複雜度分數
        complexity_score = 0
        
        # 字數複雜度
        if word_count == 1:
            complexity_score += 1
        elif word_count <= 3:
            complexity_score += 2
        elif word_count <= 5:
            complexity_score += 3
        else:
            complexity_score += 4
        
        # 字符複雜度
        if char_count > 50:
            complexity_score += 2
        elif char_count > 20:
            complexity_score += 1
        
        # 特殊字符
        import re
        if re.search(r'[^\w\s\u4e00-\u9fa5]', query):
            complexity_score += 1
        
        # 混合語言
        has_chinese = bool(re.search(r'[\u4e00-\u9fa5]', query))
        has_english = bool(re.search(r'[a-zA-Z]', query))
        if has_chinese and has_english:
            complexity_score += 1
        
        # 確定複雜度等級
        if complexity_score <= 2:
            level = 'simple'
        elif complexity_score <= 4:
            level = 'medium'
        elif complexity_score <= 6:
            level = 'complex'
        else:
            level = 'very_complex'
        
        return {
            'level': level,
            'score': complexity_score,
            'word_count': word_count,
            'char_count': char_count,
            'has_mixed_language': has_chinese and has_english
        } 