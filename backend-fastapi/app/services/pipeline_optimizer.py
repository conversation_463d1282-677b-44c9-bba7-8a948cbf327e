"""
數據收集管道性能優化器
提供自動調優、瓶頸檢測和資源分配優化功能
"""

import asyncio
import time
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import structlog

from app.core.redis import get_redis
from app.services.data_collection_pipeline import get_data_collection_pipeline
import json

logger = structlog.get_logger()


class OptimizationLevel(Enum):
    """優化級別"""
    CONSERVATIVE = "conservative"  # 保守優化
    BALANCED = "balanced"         # 平衡優化  
    AGGRESSIVE = "aggressive"     # 激進優化


class MetricType(Enum):
    """指標類型"""
    THROUGHPUT = "throughput"
    LATENCY = "latency"
    ERROR_RATE = "error_rate"
    MEMORY_USAGE = "memory_usage"
    CPU_USAGE = "cpu_usage"


@dataclass
class PerformanceMetric:
    """性能指標"""
    metric_type: MetricType
    current_value: float
    target_value: float
    threshold_min: float
    threshold_max: float
    trend: str  # "increasing", "decreasing", "stable"
    severity: str  # "low", "medium", "high", "critical"


@dataclass
class OptimizationAction:
    """優化動作"""
    action_type: str
    target_component: str
    parameters: Dict[str, Any]
    expected_impact: str
    risk_level: str
    estimated_time: int  # 秒


@dataclass
class OptimizationResult:
    """優化結果"""
    action: OptimizationAction
    success: bool
    before_metrics: Dict[str, float]
    after_metrics: Dict[str, float]
    improvement: Dict[str, float]
    execution_time: float
    notes: str


class PipelineOptimizer:
    """數據收集管道性能優化器"""
    
    def __init__(self, optimization_level: OptimizationLevel = OptimizationLevel.BALANCED):
        self.optimization_level = optimization_level
        self.redis_client = None
        self.pipeline = None
        
        # 優化配置
        self.config = {
            'monitoring_interval': 30,  # 監控間隔(秒)
            'metric_history_size': 100,  # 指標歷史大小
            'min_samples_for_optimization': 10,  # 最少樣本數
            'optimization_cooldown': 300,  # 優化冷卻時間(秒)
        }
        
        # 性能閾值配置
        self.thresholds = {
            OptimizationLevel.CONSERVATIVE: {
                'throughput_min': 0.8,  # 80% 目標吞吐量
                'latency_max': 2.0,     # 2倍目標延遲
                'error_rate_max': 0.05,  # 5% 錯誤率
                'memory_usage_max': 0.8, # 80% 內存使用
                'cpu_usage_max': 0.7,   # 70% CPU 使用
            },
            OptimizationLevel.BALANCED: {
                'throughput_min': 0.7,
                'latency_max': 1.5,
                'error_rate_max': 0.03,
                'memory_usage_max': 0.85,
                'cpu_usage_max': 0.8,
            },
            OptimizationLevel.AGGRESSIVE: {
                'throughput_min': 0.6,
                'latency_max': 1.2,
                'error_rate_max': 0.02,
                'memory_usage_max': 0.9,
                'cpu_usage_max': 0.85,
            }
        }
        
        # 優化策略
        self.optimization_strategies = {
            'increase_batch_size': self._optimize_batch_size,
            'adjust_parallelism': self._optimize_parallelism,
            'tune_flush_interval': self._optimize_flush_interval,
            'optimize_memory': self._optimize_memory_usage,
            'adjust_retry_policy': self._optimize_retry_policy,
        }
        
        # 優化歷史
        self.optimization_history: List[OptimizationResult] = []
        self.last_optimization_time = None

    async def initialize(self):
        """初始化優化器"""
        try:
            self.redis_client = await get_redis()
            self.pipeline = await get_data_collection_pipeline()
            logger.info("管道優化器初始化完成", level=self.optimization_level.value)
        except Exception as e:
            logger.error("優化器初始化失敗", error=str(e))
            raise

    async def start_monitoring(self):
        """啟動性能監控"""
        logger.info("開始性能監控...")
        
        while True:
            try:
                # 收集性能指標
                metrics = await self._collect_performance_metrics()
                
                # 分析性能問題
                issues = await self._analyze_performance_issues(metrics)
                
                # 如果發現問題且符合優化條件，執行優化
                if issues and self._should_optimize():
                    await self._execute_optimization(issues)
                
                # 記錄指標到 Redis
                await self._store_metrics(metrics)
                
                await asyncio.sleep(self.config['monitoring_interval'])
                
            except Exception as e:
                logger.error("性能監控出錯", error=str(e))
                await asyncio.sleep(self.config['monitoring_interval'])

    async def _collect_performance_metrics(self) -> Dict[str, PerformanceMetric]:
        """收集性能指標"""
        metrics = {}
        
        try:
            # 獲取管道指標
            pipeline_metrics = await self.pipeline.get_pipeline_metrics()
            
            # 吞吐量指標
            throughput = pipeline_metrics.get('throughput', {}).get('current', 0)
            target_throughput = pipeline_metrics.get('throughput', {}).get('peak', throughput * 1.2)
            
            metrics['throughput'] = PerformanceMetric(
                metric_type=MetricType.THROUGHPUT,
                current_value=throughput,
                target_value=target_throughput,
                threshold_min=target_throughput * self.thresholds[self.optimization_level]['throughput_min'],
                threshold_max=target_throughput * 1.1,
                trend=await self._calculate_trend('throughput'),
                severity=self._calculate_severity('throughput', throughput, target_throughput)
            )
            
            # 延遲指標
            latency_list = pipeline_metrics.get('processing_latency', [])
            avg_latency = statistics.mean(latency_list) if latency_list else 0
            target_latency = 100  # 目標100ms
            
            metrics['latency'] = PerformanceMetric(
                metric_type=MetricType.LATENCY,
                current_value=avg_latency,
                target_value=target_latency,
                threshold_min=0,
                threshold_max=target_latency * self.thresholds[self.optimization_level]['latency_max'],
                trend=await self._calculate_trend('latency'),
                severity=self._calculate_severity('latency', avg_latency, target_latency)
            )
            
            # 錯誤率指標
            total_processed = pipeline_metrics.get('total_processed', 0)
            total_errors = pipeline_metrics.get('total_errors', 0)
            error_rate = (total_errors / total_processed) if total_processed > 0 else 0
            
            metrics['error_rate'] = PerformanceMetric(
                metric_type=MetricType.ERROR_RATE,
                current_value=error_rate,
                target_value=0.01,  # 目標1%錯誤率
                threshold_min=0,
                threshold_max=self.thresholds[self.optimization_level]['error_rate_max'],
                trend=await self._calculate_trend('error_rate'),
                severity=self._calculate_severity('error_rate', error_rate, 0.01)
            )
            
            # 內存使用率（模擬值）
            memory_usage = await self._get_memory_usage()
            metrics['memory_usage'] = PerformanceMetric(
                metric_type=MetricType.MEMORY_USAGE,
                current_value=memory_usage,
                target_value=0.6,  # 目標60%
                threshold_min=0,
                threshold_max=self.thresholds[self.optimization_level]['memory_usage_max'],
                trend=await self._calculate_trend('memory_usage'),
                severity=self._calculate_severity('memory_usage', memory_usage, 0.6)
            )
            
            # CPU 使用率（模擬值）
            cpu_usage = await self._get_cpu_usage()
            metrics['cpu_usage'] = PerformanceMetric(
                metric_type=MetricType.CPU_USAGE,
                current_value=cpu_usage,
                target_value=0.5,  # 目標50%
                threshold_min=0,
                threshold_max=self.thresholds[self.optimization_level]['cpu_usage_max'],
                trend=await self._calculate_trend('cpu_usage'),
                severity=self._calculate_severity('cpu_usage', cpu_usage, 0.5)
            )
            
        except Exception as e:
            logger.error("收集性能指標失敗", error=str(e))
        
        return metrics

    async def _analyze_performance_issues(self, metrics: Dict[str, PerformanceMetric]) -> List[str]:
        """分析性能問題"""
        issues = []
        
        for metric_name, metric in metrics.items():
            if metric.severity in ['high', 'critical']:
                if metric.metric_type == MetricType.THROUGHPUT:
                    if metric.current_value < metric.threshold_min:
                        issues.append(f"throughput_low_{metric.severity}")
                        
                elif metric.metric_type == MetricType.LATENCY:
                    if metric.current_value > metric.threshold_max:
                        issues.append(f"latency_high_{metric.severity}")
                        
                elif metric.metric_type == MetricType.ERROR_RATE:
                    if metric.current_value > metric.threshold_max:
                        issues.append(f"error_rate_high_{metric.severity}")
                        
                elif metric.metric_type == MetricType.MEMORY_USAGE:
                    if metric.current_value > metric.threshold_max:
                        issues.append(f"memory_usage_high_{metric.severity}")
                        
                elif metric.metric_type == MetricType.CPU_USAGE:
                    if metric.current_value > metric.threshold_max:
                        issues.append(f"cpu_usage_high_{metric.severity}")
        
        return issues

    async def _execute_optimization(self, issues: List[str]):
        """執行優化"""
        logger.info("檢測到性能問題，開始優化", issues=issues)
        
        # 選擇優化策略
        optimization_actions = self._select_optimization_strategies(issues)
        
        for action in optimization_actions:
            try:
                # 記錄優化前的指標
                before_metrics = await self._get_current_metrics_snapshot()
                
                # 執行優化
                start_time = time.time()
                success = await self._execute_optimization_action(action)
                execution_time = time.time() - start_time
                
                if success:
                    # 等待一段時間讓改變生效
                    await asyncio.sleep(30)
                    
                    # 記錄優化後的指標
                    after_metrics = await self._get_current_metrics_snapshot()
                    
                    # 計算改進
                    improvement = self._calculate_improvement(before_metrics, after_metrics)
                    
                    # 記錄優化結果
                    result = OptimizationResult(
                        action=action,
                        success=True,
                        before_metrics=before_metrics,
                        after_metrics=after_metrics,
                        improvement=improvement,
                        execution_time=execution_time,
                        notes="優化成功執行"
                    )
                    
                    self.optimization_history.append(result)
                    await self._store_optimization_result(result)
                    
                    logger.info("優化執行成功", 
                              action=action.action_type,
                              improvement=improvement)
                else:
                    logger.warning("優化執行失敗", action=action.action_type)
                    
            except Exception as e:
                logger.error("優化執行出錯", action=action.action_type, error=str(e))
        
        self.last_optimization_time = datetime.utcnow()

    def _select_optimization_strategies(self, issues: List[str]) -> List[OptimizationAction]:
        """選擇優化策略"""
        actions = []
        
        for issue in issues:
            if "throughput_low" in issue:
                # 吞吐量低：增加批次大小或並行度
                actions.append(OptimizationAction(
                    action_type="increase_batch_size",
                    target_component="pipeline",
                    parameters={"increment": 20},
                    expected_impact="提高吞吐量 15-25%",
                    risk_level="low",
                    estimated_time=30
                ))
                
            elif "latency_high" in issue:
                # 延遲高：調整刷新間隔或減少批次大小
                actions.append(OptimizationAction(
                    action_type="tune_flush_interval",
                    target_component="pipeline",
                    parameters={"reduction_factor": 0.8},
                    expected_impact="降低延遲 20-30%",
                    risk_level="medium",
                    estimated_time=15
                ))
                
            elif "error_rate_high" in issue:
                # 錯誤率高：調整重試策略
                actions.append(OptimizationAction(
                    action_type="adjust_retry_policy",
                    target_component="pipeline",
                    parameters={"max_retries_increment": 1, "delay_increment": 500},
                    expected_impact="降低錯誤率 30-50%",
                    risk_level="low",
                    estimated_time=10
                ))
                
            elif "memory_usage_high" in issue:
                # 內存使用率高：優化內存使用
                actions.append(OptimizationAction(
                    action_type="optimize_memory",
                    target_component="pipeline",
                    parameters={"gc_frequency": 0.5},
                    expected_impact="降低內存使用 15-25%",
                    risk_level="medium",
                    estimated_time=20
                ))
                
            elif "cpu_usage_high" in issue:
                # CPU 使用率高：調整並行度
                actions.append(OptimizationAction(
                    action_type="adjust_parallelism",
                    target_component="pipeline",
                    parameters={"reduction_factor": 0.8},
                    expected_impact="降低 CPU 使用 20-30%",
                    risk_level="medium",
                    estimated_time=25
                ))
        
        return actions

    async def _execute_optimization_action(self, action: OptimizationAction) -> bool:
        """執行具體的優化動作"""
        try:
            strategy_func = self.optimization_strategies.get(action.action_type)
            if strategy_func:
                return await strategy_func(action.parameters)
            else:
                logger.warning("未知的優化策略", strategy=action.action_type)
                return False
        except Exception as e:
            logger.error("執行優化動作失敗", action=action.action_type, error=str(e))
            return False

    async def _optimize_batch_size(self, parameters: Dict[str, Any]) -> bool:
        """優化批次大小"""
        increment = parameters.get('increment', 20)
        current_batch_size = self.pipeline.config.get('batch_size', 100)
        new_batch_size = min(current_batch_size + increment, 500)  # 最大500
        
        self.pipeline.config['batch_size'] = new_batch_size
        logger.info("批次大小已調整", old=current_batch_size, new=new_batch_size)
        return True

    async def _optimize_parallelism(self, parameters: Dict[str, Any]) -> bool:
        """優化並行度"""
        reduction_factor = parameters.get('reduction_factor', 0.8)
        current_parallelism = self.pipeline.config.get('parallelism', 4)
        new_parallelism = max(int(current_parallelism * reduction_factor), 1)
        
        self.pipeline.config['parallelism'] = new_parallelism
        logger.info("並行度已調整", old=current_parallelism, new=new_parallelism)
        return True

    async def _optimize_flush_interval(self, parameters: Dict[str, Any]) -> bool:
        """優化刷新間隔"""
        reduction_factor = parameters.get('reduction_factor', 0.8)
        current_interval = self.pipeline.config.get('flush_interval', 5000)
        new_interval = max(int(current_interval * reduction_factor), 1000)  # 最小1秒
        
        self.pipeline.config['flush_interval'] = new_interval
        logger.info("刷新間隔已調整", old=current_interval, new=new_interval)
        return True

    async def _optimize_memory_usage(self, parameters: Dict[str, Any]) -> bool:
        """優化內存使用"""
        # 模擬內存優化：觸發垃圾回收、清理緩存等
        import gc
        gc.collect()
        
        # 清理 Redis 中的過期數據
        try:
            if self.redis_client:
                # 清理過期的統計數據
                cursor = 0
                while True:
                    cursor, keys = await self.redis_client.scan(
                        cursor=cursor,
                        match="pipeline_*:*",
                        count=100
                    )
                    
                    for key in keys:
                        # 檢查鍵的年齡，刪除超過1小時的數據
                        ttl = await self.redis_client.ttl(key)
                        if ttl == -1:  # 沒有過期時間
                            await self.redis_client.expire(key, 3600)  # 設置1小時過期
                    
                    if cursor == 0:
                        break
        except Exception as e:
            logger.warning("Redis 清理失敗", error=str(e))
        
        logger.info("內存優化已執行")
        return True

    async def _optimize_retry_policy(self, parameters: Dict[str, Any]) -> bool:
        """優化重試策略"""
        max_retries_increment = parameters.get('max_retries_increment', 1)
        delay_increment = parameters.get('delay_increment', 500)
        
        current_max_retries = self.pipeline.config.get('max_retries', 3)
        current_delay = self.pipeline.config.get('retry_delay', 1000)
        
        new_max_retries = min(current_max_retries + max_retries_increment, 10)
        new_delay = current_delay + delay_increment
        
        self.pipeline.config['max_retries'] = new_max_retries
        self.pipeline.config['retry_delay'] = new_delay
        
        logger.info("重試策略已調整", 
                   max_retries=f"{current_max_retries}->{new_max_retries}",
                   delay=f"{current_delay}->{new_delay}")
        return True

    def _should_optimize(self) -> bool:
        """判斷是否應該執行優化"""
        if self.last_optimization_time is None:
            return True
        
        # 檢查冷卻時間
        cooldown_period = timedelta(seconds=self.config['optimization_cooldown'])
        if datetime.utcnow() - self.last_optimization_time < cooldown_period:
            return False
        
        return True

    async def _calculate_trend(self, metric_name: str) -> str:
        """計算指標趨勢"""
        try:
            # 從 Redis 獲取歷史數據
            history_key = f"metric_history:{metric_name}"
            history = await self.redis_client.lrange(history_key, -10, -1)
            
            if len(history) < 3:
                return "stable"
            
            values = [float(val.decode()) for val in history]
            
            # 簡單趨勢計算
            if values[-1] > values[-3] * 1.1:
                return "increasing"
            elif values[-1] < values[-3] * 0.9:
                return "decreasing"
            else:
                return "stable"
        except:
            return "stable"

    def _calculate_severity(self, metric_type: str, current: float, target: float) -> str:
        """計算指標嚴重程度"""
        if metric_type == "throughput":
            ratio = current / target if target > 0 else 1
            if ratio < 0.5: return "critical"
            elif ratio < 0.7: return "high"
            elif ratio < 0.9: return "medium"
            else: return "low"
        
        elif metric_type in ["latency", "error_rate", "memory_usage", "cpu_usage"]:
            ratio = current / target if target > 0 else 1
            if ratio > 3: return "critical"
            elif ratio > 2: return "high"
            elif ratio > 1.5: return "medium"
            else: return "low"
        
        return "low"

    async def _get_memory_usage(self) -> float:
        """獲取內存使用率（模擬）"""
        # 實際實現中應該獲取真實的內存使用率
        import random
        return random.uniform(0.4, 0.9)

    async def _get_cpu_usage(self) -> float:
        """獲取 CPU 使用率（模擬）"""
        # 實際實現中應該獲取真實的 CPU 使用率
        import random
        return random.uniform(0.3, 0.8)

    async def _get_current_metrics_snapshot(self) -> Dict[str, float]:
        """獲取當前指標快照"""
        pipeline_metrics = await self.pipeline.get_pipeline_metrics()
        
        return {
            'total_processed': pipeline_metrics.get('total_processed', 0),
            'total_errors': pipeline_metrics.get('total_errors', 0),
            'avg_latency': statistics.mean(pipeline_metrics.get('processing_latency', [0])),
            'throughput': pipeline_metrics.get('throughput', {}).get('current', 0)
        }

    def _calculate_improvement(self, before: Dict[str, float], after: Dict[str, float]) -> Dict[str, float]:
        """計算改進程度"""
        improvement = {}
        
        for metric, after_value in after.items():
            before_value = before.get(metric, 0)
            
            if metric in ['total_processed', 'throughput']:
                # 數值越大越好
                if before_value > 0:
                    improvement[metric] = ((after_value - before_value) / before_value) * 100
                else:
                    improvement[metric] = 0
            else:
                # 數值越小越好（錯誤、延遲等）
                if before_value > 0:
                    improvement[metric] = ((before_value - after_value) / before_value) * 100
                else:
                    improvement[metric] = 0
        
        return improvement

    async def _store_metrics(self, metrics: Dict[str, PerformanceMetric]):
        """存儲指標到 Redis"""
        try:
            timestamp = datetime.utcnow().isoformat()
            
            for metric_name, metric in metrics.items():
                # 存儲當前值
                current_key = f"current_metric:{metric_name}"
                await self.redis_client.set(current_key, metric.current_value, ex=3600)
                
                # 存儲歷史值
                history_key = f"metric_history:{metric_name}"
                await self.redis_client.lpush(history_key, metric.current_value)
                await self.redis_client.ltrim(history_key, 0, self.config['metric_history_size'] - 1)
                
                # 存儲詳細指標
                detail_key = f"metric_detail:{metric_name}:{timestamp}"
                await self.redis_client.set(detail_key, json.dumps(asdict(metric)), ex=86400)
                
        except Exception as e:
            logger.error("存儲指標失敗", error=str(e))

    async def _store_optimization_result(self, result: OptimizationResult):
        """存儲優化結果"""
        try:
            result_key = f"optimization_result:{datetime.utcnow().strftime('%Y%m%d%H%M%S')}"
            result_data = {
                'action_type': result.action.action_type,
                'target_component': result.action.target_component,
                'success': result.success,
                'improvement': result.improvement,
                'execution_time': result.execution_time,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            await self.redis_client.set(result_key, json.dumps(result_data), ex=86400)
            
            # 維護優化結果列表
            results_list_key = "optimization_results_list"
            await self.redis_client.lpush(results_list_key, result_key)
            await self.redis_client.ltrim(results_list_key, 0, 99)  # 保留最近100次優化
            
        except Exception as e:
            logger.error("存儲優化結果失敗", error=str(e))

    async def get_optimization_report(self) -> Dict[str, Any]:
        """獲取優化報告"""
        try:
            total_optimizations = len(self.optimization_history)
            successful_optimizations = len([r for r in self.optimization_history if r.success])
            success_rate = (successful_optimizations / total_optimizations * 100) if total_optimizations > 0 else 0
            
            return {
                'summary': {
                    'total_optimizations': total_optimizations,
                    'successful_optimizations': successful_optimizations,
                    'success_rate': success_rate,
                    'optimization_level': self.optimization_level.value
                },
                'last_optimization': self.last_optimization_time.isoformat() if self.last_optimization_time else None
            }
            
        except Exception as e:
            logger.error("獲取優化報告失敗", error=str(e))
            return {}


# 全局優化器實例
_pipeline_optimizer = None

async def get_pipeline_optimizer() -> PipelineOptimizer:
    """獲取管道優化器實例"""
    global _pipeline_optimizer
    if _pipeline_optimizer is None:
        _pipeline_optimizer = PipelineOptimizer()
        await _pipeline_optimizer.initialize()
    return _pipeline_optimizer 