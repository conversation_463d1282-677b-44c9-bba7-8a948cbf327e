"""
數據收集器基礎類
提供統一的收集器接口和基本功能
"""

import asyncio
import time
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Any, Optional
import structlog

from app.services.data_collection_pipeline import QueryData, DataSourceType

logger = structlog.get_logger()


class BaseCollector(ABC):
    """數據收集器基礎類"""
    
    def __init__(self, config: Dict[str, Any]):
        self.name = self.__class__.__name__
        self.config = config
        self.enabled = config.get('enabled', True)
        self.batch_size = config.get('batch_size', 100)
        self.flush_interval = config.get('flush_interval', 5000)  # 毫秒
        
        # 內部緩衝區
        self.buffer: List[QueryData] = []
        self.buffer_lock = asyncio.Lock()
        
        # 統計指標
        self.metrics = {
            'collected': 0,
            'errors': 0,
            'last_collected_at': None,
            'last_flushed_at': None,
            'buffer_size': 0
        }
        
        # 刷新定時器
        self.flush_timer = None
        self.is_running = False

    async def initialize(self):
        """初始化收集器"""
        if not self.enabled:
            logger.info(f"收集器 {self.name} 已禁用")
            return
        
        try:
            await self._setup()
            self._start_flush_timer()
            self.is_running = True
            logger.info(f"收集器 {self.name} 初始化成功")
        except Exception as e:
            logger.error(f"收集器 {self.name} 初始化失敗", error=str(e))
            raise

    @abstractmethod
    async def _setup(self):
        """子類需要實現的設置方法"""
        pass

    @abstractmethod
    async def process_data(self, raw_data: Any) -> QueryData:
        """子類需要實現的數據處理方法"""
        pass

    async def collect(self, raw_data: Any) -> bool:
        """收集數據到緩衝區"""
        if not self.is_running:
            return False
        
        try:
            # 處理原始數據
            processed_data = await self.process_data(raw_data)
            
            # 添加到緩衝區
            async with self.buffer_lock:
                self.buffer.append(processed_data)
                self.metrics['buffer_size'] = len(self.buffer)
            
            # 更新統計
            self.metrics['collected'] += 1
            self.metrics['last_collected_at'] = datetime.utcnow()
            
            # 檢查是否需要刷新
            if len(self.buffer) >= self.batch_size:
                await self.flush()
                
            return True
            
        except Exception as e:
            self.metrics['errors'] += 1
            logger.error(f"收集器 {self.name} 收集數據失敗", error=str(e))
            return False

    async def flush(self):
        """刷新緩衝區到管道"""
        if not self.buffer:
            return
        
        async with self.buffer_lock:
            if not self.buffer:
                return
                
            # 複製緩衝區數據
            batch_data = self.buffer.copy()
            self.buffer.clear()
            self.metrics['buffer_size'] = 0
        
        try:
            # 發送到數據收集管道
            await self._send_to_pipeline(batch_data)
            self.metrics['last_flushed_at'] = datetime.utcnow()
            
            logger.debug(f"收集器 {self.name} 刷新完成", batch_size=len(batch_data))
            
        except Exception as e:
            # 失敗時將數據放回緩衝區
            async with self.buffer_lock:
                self.buffer.extend(batch_data)
                self.metrics['buffer_size'] = len(self.buffer)
            
            logger.error(f"收集器 {self.name} 刷新失敗", error=str(e))
            raise

    async def _send_to_pipeline(self, batch_data: List[QueryData]):
        """發送數據到收集管道"""
        from app.services.data_collection_pipeline import get_data_collection_pipeline
        
        pipeline = await get_data_collection_pipeline()
        await pipeline.collect_batch_queries(batch_data)

    def _start_flush_timer(self):
        """啟動定時刷新"""
        async def timer_flush():
            while self.is_running:
                await asyncio.sleep(self.flush_interval / 1000.0)
                if self.buffer:
                    try:
                        await self.flush()
                    except Exception as e:
                        logger.error(f"定時刷新失敗", collector=self.name, error=str(e))
        
        self.flush_timer = asyncio.create_task(timer_flush())

    async def stop(self):
        """停止收集器"""
        self.is_running = False
        
        # 停止定時器
        if self.flush_timer:
            self.flush_timer.cancel()
            try:
                await self.flush_timer
            except asyncio.CancelledError:
                pass
        
        # 最後一次刷新
        await self.flush()
        
        # 清理資源
        await self._cleanup()
        
        logger.info(f"收集器 {self.name} 已停止")

    async def _cleanup(self):
        """子類可以重寫的清理方法"""
        pass

    def get_status(self) -> Dict[str, Any]:
        """獲取收集器狀態"""
        return {
            'name': self.name,
            'enabled': self.enabled,
            'is_running': self.is_running,
            'metrics': self.metrics.copy(),
            'config': {
                'batch_size': self.batch_size,
                'flush_interval': self.flush_interval
            }
        } 