"""
網站搜索收集器
收集來自網站內部搜索的查詢數據
"""

import re
from datetime import datetime
from typing import Dict, Any, Optional
from urllib.parse import urlparse, parse_qs
import structlog
import asyncio

from .base_collector import BaseCollector
from app.services.data_collection_pipeline import QueryData, DataSourceType

logger = structlog.get_logger()


class WebSearchCollector(BaseCollector):
    """網站搜索收集器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.search_endpoints = config.get('search_endpoints', ['/api/search', '/search'])
        self.user_agent_patterns = {
            'mobile': re.compile(r'Mobile|Android|iPhone', re.I),
            'tablet': re.compile(r'Tablet|iPad', re.I),
            'desktop': re.compile(r'Windows|Macintosh|Linux', re.I)
        }

    async def _setup(self):
        """設置搜索攔截器"""
        # 這裡會在實際集成時設置 FastAPI 中間件
        logger.info("網站搜索收集器設置完成")

    async def process_data(self, raw_data: Dict[str, Any]) -> QueryData:
        """處理網站搜索數據"""
        # 提取查詢參數
        query = raw_data.get('query', '').strip()
        url = raw_data.get('url', '')
        
        # 清理和標準化查詢
        normalized_query = self._normalize_query(query)
        
        # 檢測設備類型
        user_agent = raw_data.get('user_agent', '')
        device_type = self._detect_device_type(user_agent)
        
        # 解析URL參數
        filters = self._extract_filters(url)
        
        # 創建QueryData對象
        query_data = QueryData(
            id=None,  # 將自動生成
            query=normalized_query,
            source=DataSourceType.WEB_SEARCH,
            user_id=raw_data.get('user_id'),
            session_id=raw_data.get('session_id'),
            timestamp=datetime.utcnow(),
            url=url,
            referrer=raw_data.get('referrer'),
            user_agent=user_agent,
            ip_address=raw_data.get('ip_address'),
            filters=filters,
            results_count=raw_data.get('results_count'),
            response_time=raw_data.get('response_time'),
            metadata={
                'device_type': device_type,
                'search_type': self._classify_search_type(normalized_query),
                'has_filters': bool(filters),
                'query_length': len(normalized_query)
            }
        )
        
        return query_data

    def _normalize_query(self, query: str) -> str:
        """標準化搜索查詢"""
        if not query:
            return ''
        
        # 移除多餘空格
        query = re.sub(r'\s+', ' ', query).strip()
        
        # 轉換為小寫
        query = query.lower()
        
        # 移除特殊字符（保留中文、英文、數字）
        query = re.sub(r'[^\w\s\u4e00-\u9fa5]', ' ', query)
        
        # 再次清理空格
        query = re.sub(r'\s+', ' ', query).strip()
        
        return query

    def _detect_device_type(self, user_agent: str) -> str:
        """檢測設備類型"""
        if not user_agent:
            return 'unknown'
        
        for device_type, pattern in self.user_agent_patterns.items():
            if pattern.search(user_agent):
                return device_type
        
        return 'unknown'

    def _extract_filters(self, url: str) -> Dict[str, Any]:
        """從URL中提取過濾器參數"""
        try:
            parsed_url = urlparse(url)
            query_params = parse_qs(parsed_url.query)
            
            filters = {}
            
            # 提取常見的過濾器參數
            filter_params = ['category', 'brand', 'price_min', 'price_max', 'sort', 'tags']
            
            for param in filter_params:
                if param in query_params:
                    value = query_params[param][0] if query_params[param] else None
                    if value:
                        filters[param] = value
            
            return filters
            
        except Exception as e:
            logger.error("提取過濾器失敗", url=url, error=str(e))
            return {}

    def _classify_search_type(self, query: str) -> str:
        """分類搜索類型"""
        if not query:
            return 'empty'
        
        # 短查詢
        if len(query) < 3:
            return 'short'
        
        # 產品ID模式
        if re.match(r'^[A-Z0-9]{6,}$', query.upper()):
            return 'product_id'
        
        # 問題模式
        if any(word in query for word in ['什麼', '如何', '怎麼', '為什麼', '?', '？']):
            return 'question'
        
        # 品牌搜索
        if re.search(r'^[A-Z][a-z]+$', query):
            return 'brand'
        
        # 長尾關鍵詞
        if len(query.split()) > 3:
            return 'long_tail'
        
        # 默認為關鍵詞搜索
        return 'keyword'

    async def collect_search_request(self, request_data: Dict[str, Any]) -> bool:
        """收集搜索請求（供中間件調用）"""
        return await self.collect(request_data)

    def setup_fastapi_middleware(self, app):
        """設置FastAPI中間件來攔截搜索請求"""
        from fastapi import Request
        
        @app.middleware("http")
        async def search_collection_middleware(request: Request, call_next):
            # 檢查是否為搜索端點
            if any(endpoint in str(request.url.path) for endpoint in self.search_endpoints):
                start_time = datetime.utcnow()
                
                # 收集請求數據
                search_data = {
                    'query': request.query_params.get('q', ''),
                    'url': str(request.url),
                    'user_agent': request.headers.get('user-agent', ''),
                    'ip_address': request.client.host,
                    'referrer': request.headers.get('referer', ''),
                    'user_id': getattr(request.state, 'user_id', None),
                    'session_id': request.headers.get('x-session-id', '')
                }
                
                # 執行請求
                response = await call_next(request)
                
                # 記錄響應時間
                response_time = (datetime.utcnow() - start_time).total_seconds()
                search_data['response_time'] = response_time
                
                # 異步收集數據
                asyncio.create_task(self.collect_search_request(search_data))
            else:
                response = await call_next(request)
            
            return response 