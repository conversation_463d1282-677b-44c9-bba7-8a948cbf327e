"""
API 收集器
收集來自API請求的查詢數據
"""

import asyncio
from datetime import datetime
from typing import Dict, Any, Set, Optional
import structlog

from .base_collector import BaseCollector
from app.services.data_collection_pipeline import QueryData, DataSourceType

logger = structlog.get_logger()


class APICollector(BaseCollector):
    """API 收集器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_endpoints = set(config.get('api_endpoints', [
            '/api/v1/search',
            '/api/v1/query', 
            '/api/v1/analyze'
        ]))
        self.api_keys = {}  # API密鑰到客戶端映射
        self.rate_limits = config.get('rate_limits', {})

    async def _setup(self):
        """設置API收集器"""
        # 加載API密鑰配置
        await self._load_api_keys()
        logger.info("API收集器設置完成")

    async def _load_api_keys(self):
        """加載API密鑰配置"""
        # 這裡應該從數據庫或配置文件加載
        # 示例配置
        self.api_keys = {
            'demo_key_123': {
                'client_id': 'client_1',
                'client_name': 'Demo Client',
                'tier': 'premium',
                'rate_limit': 1000
            }
        }

    async def process_data(self, raw_data: Dict[str, Any]) -> QueryData:
        """處理API請求數據"""
        # 提取API請求信息
        endpoint = raw_data.get('endpoint', '')
        method = raw_data.get('method', 'GET')
        query_params = raw_data.get('query_params', {})
        request_body = raw_data.get('request_body', {})
        
        # 提取查詢內容
        query = self._extract_query_content(endpoint, method, query_params, request_body)
        
        # 檢測API客戶端
        api_key = raw_data.get('api_key', '')
        client_info = self.api_keys.get(api_key, {})
        
        # 分析請求意圖
        intent = self._extract_api_intent(endpoint, method)
        
        # 計算請求複雜度
        complexity = self._calculate_request_complexity(query_params, request_body)
        
        # 創建QueryData對象
        query_data = QueryData(
            id=None,  # 將自動生成
            query=query,
            source=DataSourceType.API_REQUEST,
            user_id=client_info.get('client_id'),
            session_id=raw_data.get('request_id'),
            timestamp=datetime.utcnow(),
            url=endpoint,
            user_agent=raw_data.get('user_agent'),
            ip_address=raw_data.get('ip_address'),
            filters=query_params,
            response_time=raw_data.get('response_time'),
            metadata={
                'method': method,
                'api_key': api_key,
                'client_name': client_info.get('client_name', 'unknown'),
                'client_tier': client_info.get('tier', 'basic'),
                'intent': intent,
                'complexity': complexity,
                'status_code': raw_data.get('status_code', 200),
                'request_size': len(str(request_body)) if request_body else 0
            }
        )
        
        return query_data

    def _extract_query_content(self, endpoint: str, method: str, 
                             query_params: Dict, request_body: Dict) -> str:
        """提取查詢內容"""
        query_fields = ['q', 'query', 'search', 'keyword', 'term']
        
        # 從查詢參數中提取
        for field in query_fields:
            if field in query_params:
                return str(query_params[field])
        
        # 從請求體中提取
        for field in query_fields:
            if field in request_body:
                return str(request_body[field])
        
        # 從嵌套對象中提取
        if 'data' in request_body:
            data = request_body['data']
            if isinstance(data, dict):
                for field in query_fields:
                    if field in data:
                        return str(data[field])
        
        # 如果都沒有，返回端點作為查詢
        return endpoint

    def _extract_api_intent(self, endpoint: str, method: str) -> str:
        """提取API請求意圖"""
        intent_patterns = {
            ('GET', '/api/v1/search'): 'search',
            ('GET', '/api/v1/products'): 'browse',
            ('POST', '/api/v1/analyze'): 'analyze',
            ('GET', '/api/v1/recommendations'): 'recommend',
            ('POST', '/api/v1/seo/analyze'): 'seo_analyze',
            ('GET', '/api/v1/keywords'): 'keyword_research',
            ('POST', '/api/v1/content/generate'): 'content_generate'
        }
        
        return intent_patterns.get((method, endpoint), 'unknown')

    def _calculate_request_complexity(self, query_params: Dict, request_body: Dict) -> int:
        """計算請求複雜度"""
        complexity = 0
        
        # 查詢參數複雜度
        complexity += len(query_params)
        
        # 特殊參數加權
        if 'filters' in query_params:
            complexity += 2
        if 'sort' in query_params:
            complexity += 1
        if 'aggregations' in query_params:
            complexity += 3
        
        # 請求體複雜度
        if request_body:
            # 基於JSON大小的複雜度
            complexity += len(str(request_body)) // 100
            
            # 嵌套層級複雜度
            def count_nesting(obj, level=0):
                if level > 10:  # 防止過深嵌套
                    return 0
                if isinstance(obj, dict):
                    return level + sum(count_nesting(v, level + 1) for v in obj.values())
                elif isinstance(obj, list):
                    return level + sum(count_nesting(item, level + 1) for item in obj)
                return level
            
            complexity += count_nesting(request_body) // 10
        
        return min(complexity, 100)  # 限制最大複雜度

    async def collect_api_request(self, request_data: Dict[str, Any]) -> bool:
        """收集API請求（供中間件調用）"""
        return await self.collect(request_data)

    def setup_fastapi_middleware(self, app):
        """設置FastAPI中間件來攔截API請求"""
        from fastapi import Request
        import json
        
        @app.middleware("http")
        async def api_collection_middleware(request: Request, call_next):
            # 檢查是否為監控的API端點
            if request.url.path in self.api_endpoints:
                start_time = datetime.utcnow()
                
                # 讀取請求體（需要特殊處理）
                request_body = {}
                if request.method in ['POST', 'PUT', 'PATCH']:
                    try:
                        body = await request.body()
                        if body:
                            request_body = json.loads(body.decode('utf-8'))
                    except Exception as e:
                        logger.warning("無法解析請求體", error=str(e))
                
                # 收集請求數據
                api_data = {
                    'endpoint': request.url.path,
                    'method': request.method,
                    'query_params': dict(request.query_params),
                    'request_body': request_body,
                    'api_key': request.headers.get('x-api-key', ''),
                    'user_agent': request.headers.get('user-agent', ''),
                    'ip_address': request.client.host,
                    'request_id': request.headers.get('x-request-id', '')
                }
                
                # 執行請求
                response = await call_next(request)
                
                # 記錄響應信息
                response_time = (datetime.utcnow() - start_time).total_seconds()
                api_data['response_time'] = response_time
                api_data['status_code'] = response.status_code
                
                # 異步收集數據
                asyncio.create_task(self.collect_api_request(api_data))
            else:
                response = await call_next(request)
            
            return response

    def get_api_statistics(self) -> Dict[str, Any]:
        """獲取API統計信息"""
        return {
            'monitored_endpoints': list(self.api_endpoints),
            'registered_clients': len(self.api_keys),
            'collection_metrics': self.metrics.copy()
        } 