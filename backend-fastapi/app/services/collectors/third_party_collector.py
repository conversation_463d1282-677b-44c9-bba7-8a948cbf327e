"""
第三方平台收集器
收集來自第三方平台（Google Search Console、社交媒體等）的數據
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import structlog

from .base_collector import BaseCollector
from app.services.data_collection_pipeline import QueryData, DataSourceType

logger = structlog.get_logger()


class ThirdPartyCollector(BaseCollector):
    """第三方平台收集器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.platforms = config.get('platforms', {})
        self.collection_tasks = []
        
        # 平台客戶端
        self.gsc_client = None  # Google Search Console
        self.twitter_client = None
        self.facebook_client = None

    async def _setup(self):
        """設置第三方平台連接"""
        # 設置 Google Search Console
        if self.platforms.get('google_search_console', {}).get('enabled', False):
            await self._setup_google_search_console()
        
        # 設置社交媒體監聽
        if self.platforms.get('twitter', {}).get('enabled', False):
            await self._setup_twitter()
        
        # 設置其他平台
        if self.platforms.get('facebook', {}).get('enabled', False):
            await self._setup_facebook()
        
        logger.info("第三方平台收集器設置完成")

    async def _setup_google_search_console(self):
        """設置 Google Search Console"""
        try:
            # 這裡需要實際的 GSC API 設置
            logger.info("Google Search Console 設置完成")
            
            # 啟動定期數據拉取任務
            fetch_interval = self.platforms['google_search_console'].get('fetch_interval', 3600)  # 1小時
            task = asyncio.create_task(self._periodic_gsc_fetch(fetch_interval))
            self.collection_tasks.append(task)
            
        except Exception as e:
            logger.error("Google Search Console 設置失敗", error=str(e))

    async def _setup_twitter(self):
        """設置 Twitter 監聽"""
        try:
            # 這裡需要實際的 Twitter API 設置
            logger.info("Twitter 監聽設置完成")
            
            # 啟動流式監聽任務
            task = asyncio.create_task(self._twitter_stream_listener())
            self.collection_tasks.append(task)
            
        except Exception as e:
            logger.error("Twitter 設置失敗", error=str(e))

    async def _setup_facebook(self):
        """設置 Facebook 監聽"""
        try:
            # 這裡需要實際的 Facebook API 設置
            logger.info("Facebook 監聽設置完成")
            
        except Exception as e:
            logger.error("Facebook 設置失敗", error=str(e))

    async def process_data(self, raw_data: Dict[str, Any]) -> QueryData:
        """處理第三方平台數據"""
        platform = raw_data.get('platform', 'unknown')
        
        # 根據平台類型處理數據
        if platform == 'google_search_console':
            return await self._process_gsc_data(raw_data)
        elif platform == 'twitter':
            return await self._process_twitter_data(raw_data)
        elif platform == 'facebook':
            return await self._process_facebook_data(raw_data)
        else:
            return await self._process_generic_data(raw_data)

    async def _process_gsc_data(self, raw_data: Dict[str, Any]) -> QueryData:
        """處理 Google Search Console 數據"""
        query_data = QueryData(
            id=None,
            query=raw_data.get('query', ''),
            source=DataSourceType.SEO_TOOLS,
            timestamp=datetime.utcnow(),
            metadata={
                'platform': 'google_search_console',
                'clicks': raw_data.get('clicks', 0),
                'impressions': raw_data.get('impressions', 0),
                'ctr': raw_data.get('ctr', 0.0),
                'position': raw_data.get('position', 0.0),
                'country': raw_data.get('country', ''),
                'device': raw_data.get('device', ''),
                'search_volume': raw_data.get('impressions', 0),
                'relevance_score': raw_data.get('ctr', 0) * 100
            }
        )
        
        return query_data

    async def _process_twitter_data(self, raw_data: Dict[str, Any]) -> QueryData:
        """處理 Twitter 數據"""
        text = raw_data.get('text', '')
        
        # 提取提及的關鍵詞
        keywords = self._extract_keywords_from_text(text)
        
        query_data = QueryData(
            id=None,
            query=' '.join(keywords) if keywords else text[:100],
            source=DataSourceType.SOCIAL_MEDIA,
            timestamp=datetime.fromisoformat(raw_data.get('created_at', datetime.utcnow().isoformat())),
            metadata={
                'platform': 'twitter',
                'tweet_id': raw_data.get('id', ''),
                'author_id': raw_data.get('author_id', ''),
                'full_text': text,
                'hashtags': raw_data.get('hashtags', []),
                'mentions': raw_data.get('mentions', []),
                'retweet_count': raw_data.get('retweet_count', 0),
                'like_count': raw_data.get('like_count', 0),
                'reply_count': raw_data.get('reply_count', 0),
                'sentiment': await self._analyze_sentiment(text)
            }
        )
        
        return query_data

    async def _process_facebook_data(self, raw_data: Dict[str, Any]) -> QueryData:
        """處理 Facebook 數據"""
        query_data = QueryData(
            id=None,
            query=raw_data.get('message', '')[:100],
            source=DataSourceType.SOCIAL_MEDIA,
            timestamp=datetime.utcnow(),
            metadata={
                'platform': 'facebook',
                'post_id': raw_data.get('id', ''),
                'page_id': raw_data.get('page_id', ''),
                'message': raw_data.get('message', ''),
                'likes': raw_data.get('likes', 0),
                'comments': raw_data.get('comments', 0),
                'shares': raw_data.get('shares', 0)
            }
        )
        
        return query_data

    async def _process_generic_data(self, raw_data: Dict[str, Any]) -> QueryData:
        """處理通用第三方數據"""
        query_data = QueryData(
            id=None,
            query=raw_data.get('query', raw_data.get('content', ''))[:100],
            source=DataSourceType.THIRD_PARTY,
            timestamp=datetime.utcnow(),
            metadata=raw_data
        )
        
        return query_data

    async def _periodic_gsc_fetch(self, interval: int):
        """定期拉取 GSC 數據"""
        while self.is_running:
            try:
                await self._fetch_gsc_data()
                await asyncio.sleep(interval)
            except Exception as e:
                logger.error("GSC 數據拉取失敗", error=str(e))
                await asyncio.sleep(60)  # 失敗後等待1分鐘再重試

    async def _fetch_gsc_data(self):
        """拉取 Google Search Console 數據"""
        try:
            # 模擬 GSC API 調用
            # 實際實現需要使用 Google Search Console API
            
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=1)  # 拉取昨天的數據
            
            # 示例數據
            sample_data = [
                {
                    'platform': 'google_search_console',
                    'query': 'SEO優化',
                    'clicks': 150,
                    'impressions': 2000,
                    'ctr': 0.075,
                    'position': 3.2,
                    'country': 'TW',
                    'device': 'desktop'
                },
                {
                    'platform': 'google_search_console',
                    'query': '搜尋引擎優化',
                    'clicks': 89,
                    'impressions': 1500,
                    'ctr': 0.059,
                    'position': 4.1,
                    'country': 'TW',
                    'device': 'mobile'
                }
            ]
            
            # 批量收集數據
            for data_item in sample_data:
                await self.collect(data_item)
                
            logger.info(f"GSC 數據拉取完成", count=len(sample_data))
            
        except Exception as e:
            logger.error("GSC 數據拉取失敗", error=str(e))

    async def _twitter_stream_listener(self):
        """Twitter 流式監聽"""
        while self.is_running:
            try:
                # 模擬 Twitter 流式數據
                # 實際實現需要使用 Twitter API v2
                
                await asyncio.sleep(30)  # 每30秒模擬一次數據
                
                sample_tweets = [
                    {
                        'platform': 'twitter',
                        'id': '1234567890',
                        'text': '最新的SEO優化技巧分享 #SEO #數位行銷',
                        'author_id': 'user123',
                        'created_at': datetime.utcnow().isoformat(),
                        'hashtags': ['SEO', '數位行銷'],
                        'retweet_count': 15,
                        'like_count': 45,
                        'reply_count': 3
                    }
                ]
                
                for tweet in sample_tweets:
                    if self._is_relevant_tweet(tweet['text']):
                        await self.collect(tweet)
                        
            except Exception as e:
                logger.error("Twitter 流式監聽失敗", error=str(e))
                await asyncio.sleep(60)

    def _is_relevant_tweet(self, text: str) -> bool:
        """檢查推文是否相關"""
        keywords = self.platforms.get('twitter', {}).get('keywords', [])
        if not keywords:
            return True
        
        text_lower = text.lower()
        return any(keyword.lower() in text_lower for keyword in keywords)

    def _extract_keywords_from_text(self, text: str) -> List[str]:
        """從文本中提取關鍵詞"""
        import re
        
        # 提取hashtags
        hashtags = re.findall(r'#(\w+)', text)
        
        # 提取其他關鍵詞（簡化版本）
        words = re.findall(r'\b\w+\b', text.lower())
        keywords = [word for word in words if len(word) > 3]
        
        return hashtags + keywords[:5]  # 限制關鍵詞數量

    async def _analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """分析文本情感"""
        # 簡化的情感分析
        positive_words = ['好', '棒', '優秀', '讚', '推薦', 'good', 'great', 'awesome']
        negative_words = ['糟', '差', '爛', '失望', 'bad', 'terrible', 'awful']
        
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        if positive_count > negative_count:
            sentiment = 'positive'
            score = 0.6 + (positive_count - negative_count) * 0.1
        elif negative_count > positive_count:
            sentiment = 'negative'
            score = 0.4 - (negative_count - positive_count) * 0.1
        else:
            sentiment = 'neutral'
            score = 0.5
        
        return {
            'sentiment': sentiment,
            'score': max(0.0, min(1.0, score)),
            'positive_count': positive_count,
            'negative_count': negative_count
        }

    async def collect_social_mention(self, platform: str, data: Dict[str, Any]) -> bool:
        """收集社交媒體提及（外部調用接口）"""
        data['platform'] = platform
        return await self.collect(data)

    async def collect_seo_data(self, data: Dict[str, Any]) -> bool:
        """收集SEO工具數據（外部調用接口）"""
        data['platform'] = 'seo_tools'
        return await self.collect(data)

    async def _cleanup(self):
        """清理資源"""
        # 取消所有收集任務
        for task in self.collection_tasks:
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
        
        self.collection_tasks.clear()
        logger.info("第三方平台收集器資源清理完成")

    def get_platform_status(self) -> Dict[str, Any]:
        """獲取平台狀態"""
        return {
            'enabled_platforms': [
                platform for platform, config in self.platforms.items() 
                if config.get('enabled', False)
            ],
            'active_tasks': len(self.collection_tasks),
            'collection_metrics': self.metrics.copy()
        } 