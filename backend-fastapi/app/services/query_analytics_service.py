"""
查詢分析服務
提供查詢記錄、統計分析、關鍵詞分析和實時統計功能
"""

import asyncio
import json
import uuid
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta, date
from collections import Counter, defaultdict
import re
import structlog
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import get_settings
from app.core.database import get_db
from app.core.redis import get_redis
from app.services.elasticsearch_service import ElasticsearchSEOService
from app.schemas.analytics import (
    QueryRecordCreate, QueryRecordResponse,
    QueryStatsBase, HourlyQueryStats, DailyQueryStats, WeeklyQueryStats, MonthlyQueryStats,
    KeywordAnalysisResult, KeywordMetrics, KeywordTrendData,
    TrendAnalysisRequest, TrendAnalysisResult,
    RealTimeStats, AnalyticsDashboardStats,
    QuerySourceType, QueryIntentType, StatisticsTimeframe, TrendDirection
)

logger = structlog.get_logger()
settings = get_settings()


class QueryAnalyticsService:
    """查詢分析服務"""
    
    def __init__(self):
        self.redis_client = None
        self.elasticsearch_service = None
        self.db_session = None
        self.cache_ttl = 3600  # 1小時緩存
        self.real_time_window = 300  # 5分鐘實時窗口
        
    async def initialize(self):
        """初始化服務"""
        self.redis_client = await get_redis()
        self.elasticsearch_service = ElasticsearchSEOService()
        await self.elasticsearch_service.initialize()
        logger.info("查詢分析服務初始化完成")
    
    async def record_query(self, query_data: QueryRecordCreate) -> str:
        """記錄查詢"""
        try:
            # 生成查詢ID
            query_id = str(uuid.uuid4())
            
            # 標準化查詢
            normalized_query = self._normalize_query(query_data.query)
            
            # 分析查詢意圖
            intent = self._analyze_query_intent(query_data.query)
            
            # 提取關鍵詞
            keywords = self._extract_keywords(query_data.query)
            
            # 檢測設備類型
            device_type = self._detect_device_type(query_data.user_agent)
            
            # 構建完整記錄
            record = {
                "id": query_id,
                "query": query_data.query,
                "normalized_query": normalized_query,
                "source": query_data.source.value,
                "intent": intent.value if intent else None,
                "user_id": query_data.user_id,
                "session_id": query_data.session_id,
                "ip_address": query_data.ip_address,
                "user_agent": query_data.user_agent,
                "device_type": device_type,
                "language": query_data.language,
                "location": query_data.location,
                "response_time": query_data.response_time,
                "results_count": query_data.results_count,
                "clicked_results": query_data.clicked_results,
                "keywords": keywords,
                "timestamp": query_data.timestamp or datetime.utcnow(),
                "metadata": query_data.metadata or {}
            }
            
            # 存儲到Elasticsearch
            await self._store_to_elasticsearch(record)
            
            # 更新實時統計
            await self._update_realtime_stats(record)
            
            # 更新Redis緩存統計
            await self._update_cache_stats(record)
            
            logger.debug("查詢記錄成功", query_id=query_id, query=query_data.query)
            return query_id
            
        except Exception as e:
            logger.error("記錄查詢失敗", error=str(e), query=query_data.query)
            raise
    
    async def get_query_stats(
        self, 
        timeframe: StatisticsTimeframe,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Union[HourlyQueryStats, DailyQueryStats, WeeklyQueryStats, MonthlyQueryStats]:
        """獲取查詢統計"""
        try:
            # 設置默認時間範圍
            if not end_date:
                end_date = datetime.utcnow()
            if not start_date:
                start_date = self._get_default_start_date(timeframe, end_date)
            
            # 檢查緩存
            cache_key = f"query_stats:{timeframe.value}:{start_date.date()}:{end_date.date()}"
            cached_stats = await self.redis_client.get(cache_key)
            if cached_stats:
                return self._parse_cached_stats(json.loads(cached_stats), timeframe)
            
            # 從Elasticsearch獲取數據
            query_body = self._build_stats_query(timeframe, start_date, end_date)
            results = await self.elasticsearch_service.search(
                index="query_records", 
                body=query_body
            )
            
            # 處理結果
            stats = self._process_stats_results(results, timeframe, start_date, end_date)
            
            # 緩存結果
            await self.redis_client.setex(
                cache_key, 
                self.cache_ttl, 
                json.dumps(stats, default=str)
            )
            
            return stats
            
        except Exception as e:
            logger.error("獲取查詢統計失敗", error=str(e), timeframe=timeframe)
            raise
    
    async def analyze_keywords(
        self,
        timeframe: StatisticsTimeframe = StatisticsTimeframe.WEEK,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        min_frequency: int = 10,
        limit: int = 100
    ) -> KeywordAnalysisResult:
        """分析關鍵詞"""
        try:
            analysis_id = str(uuid.uuid4())
            
            # 設置時間範圍
            if not end_date:
                end_date = datetime.utcnow()
            if not start_date:
                start_date = self._get_default_start_date(timeframe, end_date)
            
            # 檢查緩存
            cache_key = f"keyword_analysis:{timeframe.value}:{start_date.date()}:{end_date.date()}:{min_frequency}"
            cached_analysis = await self.redis_client.get(cache_key)
            if cached_analysis:
                return KeywordAnalysisResult(**json.loads(cached_analysis))
            
            # 從Elasticsearch獲取關鍵詞數據
            query_body = self._build_keyword_query(start_date, end_date, min_frequency, limit)
            results = await self.elasticsearch_service.search(
                index="query_records",
                body=query_body
            )
            
            # 處理關鍵詞統計
            keyword_stats = self._process_keyword_results(results)
            
            # 執行關鍵詞聚類
            keyword_clusters = self._cluster_keywords(keyword_stats)
            
            # 語義分組
            semantic_groups = self._group_keywords_semantically(keyword_stats)
            
            # 分析趨勢
            rising_keywords, declining_keywords = await self._analyze_keyword_trends(
                keyword_stats, timeframe, start_date, end_date
            )
            
            # 構建結果
            analysis_result = KeywordAnalysisResult(
                analysis_id=analysis_id,
                timeframe=timeframe,
                start_date=start_date,
                end_date=end_date,
                total_keywords=len(keyword_stats),
                top_keywords=keyword_stats[:limit],
                rising_keywords=rising_keywords,
                declining_keywords=declining_keywords,
                keyword_clusters=keyword_clusters,
                semantic_groups=semantic_groups,
                intent_distribution=self._calculate_intent_distribution(keyword_stats)
            )
            
            # 緩存結果
            await self.redis_client.setex(
                cache_key,
                self.cache_ttl,
                analysis_result.json()
            )
            
            return analysis_result
            
        except Exception as e:
            logger.error("關鍵詞分析失敗", error=str(e))
            raise
    
    async def get_trend_analysis(self, request: TrendAnalysisRequest) -> TrendAnalysisResult:
        """獲取趨勢分析"""
        try:
            analysis_id = str(uuid.uuid4())
            
            # 設置時間範圍
            end_date = request.end_date or datetime.utcnow()
            start_date = request.start_date or self._get_default_start_date(request.timeframe, end_date)
            
            # 檢查緩存
            cache_key = f"trend_analysis:{hash(str(request.dict()))}:{start_date.date()}:{end_date.date()}"
            cached_analysis = await self.redis_client.get(cache_key)
            if cached_analysis:
                return TrendAnalysisResult(**json.loads(cached_analysis))
            
            # 獲取趨勢數據
            trend_data = await self._get_trend_data(request, start_date, end_date)
            
            # 分析整體趨勢
            overall_trend, growth_rate = self._calculate_overall_trend(trend_data)
            
            # 分析關鍵詞趨勢
            keyword_trends = self._analyze_keyword_trends_detailed(trend_data, request.timeframe)
            
            # 識別新興和下降主題
            emerging_topics, declining_topics = self._identify_topic_trends(trend_data)
            
            # 季節性分析
            seasonal_insights = self._analyze_seasonal_patterns(trend_data, request.timeframe)
            
            # 異常檢測
            anomalies = self._detect_anomalies(trend_data)
            
            # 預測（如果需要）
            predictions = None
            if request.include_predictions:
                predictions = self._generate_predictions(trend_data, request.timeframe)
            
            # 構建結果
            analysis_result = TrendAnalysisResult(
                analysis_id=analysis_id,
                timeframe=request.timeframe,
                start_date=start_date,
                end_date=end_date,
                overall_trend=overall_trend,
                growth_rate=growth_rate,
                keyword_trends=keyword_trends,
                emerging_topics=emerging_topics,
                declining_topics=declining_topics,
                seasonal_insights=seasonal_insights,
                anomalies=anomalies,
                predictions=predictions
            )
            
            # 緩存結果
            await self.redis_client.setex(
                cache_key,
                self.cache_ttl,
                analysis_result.json()
            )
            
            return analysis_result
            
        except Exception as e:
            logger.error("趨勢分析失敗", error=str(e))
            raise
    
    async def get_realtime_stats(self) -> RealTimeStats:
        """獲取實時統計"""
        try:
            # 從Redis獲取實時數據
            current_minute = datetime.utcnow().replace(second=0, microsecond=0)
            
            # 獲取當前QPS
            qps_key = f"realtime:qps:{current_minute.isoformat()}"
            current_qps = float(await self.redis_client.get(qps_key) or 0)
            
            # 獲取活躍會話數
            active_sessions = await self.redis_client.scard("active_sessions")
            
            # 獲取最近一小時熱門查詢
            top_queries = await self._get_top_queries_last_hour()
            
            # 獲取來源分佈
            source_distribution = await self._get_source_distribution()
            
            # 獲取地理分佈
            geographic_distribution = await self._get_geographic_distribution()
            
            # 檢查警報閾值
            alerts = await self._check_alert_thresholds()
            
            return RealTimeStats(
                current_qps=current_qps,
                active_sessions=active_sessions,
                top_queries_last_hour=top_queries,
                source_distribution=source_distribution,
                geographic_distribution=geographic_distribution,
                alert_threshold_breached=alerts
            )
            
        except Exception as e:
            logger.error("獲取實時統計失敗", error=str(e))
            raise
    
    async def get_dashboard_stats(self) -> AnalyticsDashboardStats:
        """獲取儀表板統計"""
        try:
            # 檢查緩存
            cache_key = "dashboard_stats"
            cached_stats = await self.redis_client.get(cache_key)
            if cached_stats:
                return AnalyticsDashboardStats(**json.loads(cached_stats))
            
            # 獲取今日和昨日統計
            today = datetime.utcnow().date()
            yesterday = today - timedelta(days=1)
            
            today_stats = await self._get_daily_basic_stats(today)
            yesterday_stats = await self._get_daily_basic_stats(yesterday)
            
            # 計算增長率
            growth_rate = self._calculate_growth_rate(
                today_stats.get("total_queries", 0),
                yesterday_stats.get("total_queries", 0)
            )
            
            # 獲取熱門來源
            top_sources = await self._get_top_sources()
            
            # 獲取熱門關鍵詞
            top_keywords = await self._get_top_keywords()
            
            # 獲取最近趨勢
            recent_trends = await self._get_recent_trends()
            
            # 獲取系統警報
            alerts = await self._get_system_alerts()
            
            # 構建儀表板統計
            dashboard_stats = AnalyticsDashboardStats(
                total_queries_today=today_stats.get("total_queries", 0),
                total_queries_yesterday=yesterday_stats.get("total_queries", 0),
                growth_rate_daily=growth_rate,
                unique_users_today=today_stats.get("unique_users", 0),
                avg_response_time=today_stats.get("avg_response_time", 0),
                top_sources=top_sources,
                top_keywords=top_keywords,
                recent_trends=recent_trends,
                alerts=alerts
            )
            
            # 緩存結果（5分鐘）
            await self.redis_client.setex(
                cache_key,
                300,
                dashboard_stats.json()
            )
            
            return dashboard_stats
            
        except Exception as e:
            logger.error("獲取儀表板統計失敗", error=str(e))
            raise
    
    # 私有方法
    def _normalize_query(self, query: str) -> str:
        """標準化查詢"""
        # 移除多餘空格
        normalized = re.sub(r'\s+', ' ', query.strip())
        # 轉換為小寫
        normalized = normalized.lower()
        # 移除特殊字符（可選）
        normalized = re.sub(r'[^\w\s\u4e00-\u9fff]', '', normalized)
        return normalized
    
    def _analyze_query_intent(self, query: str) -> Optional[QueryIntentType]:
        """分析查詢意圖"""
        query_lower = query.lower()
        
        # 問題意圖
        question_patterns = ['什麼', '如何', '怎麼', '為什麼', '哪裡', '何時', '誰', 
                           'what', 'how', 'why', 'where', 'when', 'who', '?', '？']
        if any(pattern in query_lower for pattern in question_patterns):
            return QueryIntentType.INFORMATIONAL
        
        # 交易意圖
        transaction_patterns = ['買', '購買', '訂購', '下單', 'buy', 'purchase', 'order']
        if any(pattern in query_lower for pattern in transaction_patterns):
            return QueryIntentType.TRANSACTIONAL
        
        # 導航意圖
        navigation_patterns = ['登入', '註冊', '首頁', 'login', 'register', 'home']
        if any(pattern in query_lower for pattern in navigation_patterns):
            return QueryIntentType.NAVIGATIONAL
        
        # 商業意圖
        commercial_patterns = ['價格', '比較', '評價', 'price', 'compare', 'review']
        if any(pattern in query_lower for pattern in commercial_patterns):
            return QueryIntentType.COMMERCIAL
        
        # 本地意圖
        local_patterns = ['附近', '地址', '位置', 'near', 'address', 'location']
        if any(pattern in query_lower for pattern in local_patterns):
            return QueryIntentType.LOCAL
        
        return QueryIntentType.INFORMATIONAL  # 默認為信息型
    
    def _extract_keywords(self, query: str) -> List[str]:
        """提取關鍵詞"""
        # 基礎關鍵詞提取
        words = re.findall(r'\b\w+\b', query.lower())
        
        # 過濾停用詞
        stop_words = {'的', '是', '在', '有', '和', '或', '但', '與', '或者', 
                     'the', 'is', 'in', 'and', 'or', 'but', 'with', 'for'}
        keywords = [word for word in words if word not in stop_words and len(word) > 1]
        
        return keywords[:10]  # 限制數量
    
    def _detect_device_type(self, user_agent: Optional[str]) -> Optional[str]:
        """檢測設備類型"""
        if not user_agent:
            return None
        
        user_agent_lower = user_agent.lower()
        
        if any(device in user_agent_lower for device in ['mobile', 'android', 'iphone']):
            return 'mobile'
        elif any(device in user_agent_lower for device in ['tablet', 'ipad']):
            return 'tablet'
        else:
            return 'desktop'
    
    async def _store_to_elasticsearch(self, record: Dict[str, Any]):
        """存儲到Elasticsearch"""
        try:
            await self.elasticsearch_service.index_document(
                index="query_records",
                document=record,
                doc_id=record["id"]
            )
        except Exception as e:
            logger.error("存儲到Elasticsearch失敗", error=str(e))
            raise
    
    async def _update_realtime_stats(self, record: Dict[str, Any]):
        """更新實時統計"""
        try:
            current_minute = datetime.utcnow().replace(second=0, microsecond=0)
            
            # 更新QPS
            qps_key = f"realtime:qps:{current_minute.isoformat()}"
            await self.redis_client.incr(qps_key)
            await self.redis_client.expire(qps_key, 300)  # 5分鐘過期
            
            # 更新活躍會話
            if record.get("session_id"):
                await self.redis_client.sadd("active_sessions", record["session_id"])
                await self.redis_client.expire("active_sessions", 1800)  # 30分鐘過期
            
            # 更新熱門查詢
            await self.redis_client.zincrby("trending_queries", 1, record["query"])
            await self.redis_client.expire("trending_queries", 3600)  # 1小時過期
            
        except Exception as e:
            logger.error("更新實時統計失敗", error=str(e))
    
    async def _update_cache_stats(self, record: Dict[str, Any]):
        """更新緩存統計"""
        try:
            timestamp = record["timestamp"]
            hour_key = f"stats:hour:{timestamp.strftime('%Y-%m-%d-%H')}"
            day_key = f"stats:day:{timestamp.strftime('%Y-%m-%d')}"
            
            # 更新小時統計
            await self.redis_client.hincrby(hour_key, "total_queries", 1)
            await self.redis_client.hincrby(hour_key, f"source_{record['source']}", 1)
            await self.redis_client.expire(hour_key, 86400 * 7)  # 7天過期
            
            # 更新日統計
            await self.redis_client.hincrby(day_key, "total_queries", 1)
            await self.redis_client.hincrby(day_key, f"source_{record['source']}", 1)
            await self.redis_client.expire(day_key, 86400 * 30)  # 30天過期
            
        except Exception as e:
            logger.error("更新緩存統計失敗", error=str(e))
    
    def _get_default_start_date(self, timeframe: StatisticsTimeframe, end_date: datetime) -> datetime:
        """獲取默認開始日期"""
        if timeframe == StatisticsTimeframe.HOUR:
            return end_date - timedelta(hours=24)
        elif timeframe == StatisticsTimeframe.DAY:
            return end_date - timedelta(days=7)
        elif timeframe == StatisticsTimeframe.WEEK:
            return end_date - timedelta(weeks=4)
        elif timeframe == StatisticsTimeframe.MONTH:
            return end_date - timedelta(days=365)
        else:
            return end_date - timedelta(days=30)
    
    def _calculate_growth_rate(self, current: int, previous: int) -> float:
        """計算增長率"""
        if previous == 0:
            return 100.0 if current > 0 else 0.0
        return ((current - previous) / previous) * 100
    
    # 更多私有方法將在後續實現...
    
    async def cleanup_old_data(self, retention_days: int = 90):
        """清理舊數據"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
            
            # 清理Elasticsearch舊數據
            query = {
                "query": {
                    "range": {
                        "timestamp": {
                            "lt": cutoff_date.isoformat()
                        }
                    }
                }
            }
            
            await self.elasticsearch_service.delete_by_query(
                index="query_records",
                body=query
            )
            
            logger.info("清理舊數據完成", retention_days=retention_days)
            
        except Exception as e:
            logger.error("清理舊數據失敗", error=str(e)) 