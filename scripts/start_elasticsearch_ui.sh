#!/bin/bash

# AI SEO 優化王 - Elasticsearch Web UI 啟動腳本

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[HEADER]${NC} $1"
}

# 檢查 Docker 和 Docker Compose
check_docker() {
    log_info "檢查 Docker 環境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安裝，請先安裝 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安裝，請先安裝 Docker Compose"
        exit 1
    fi
    
    # 檢查 Docker 是否運行
    if ! docker info &> /dev/null; then
        log_error "Docker 服務未運行，請啟動 Docker"
        exit 1
    fi
    
    log_success "Docker 環境檢查通過"
}

# 檢查 Elasticsearch 服務
check_elasticsearch() {
    log_info "檢查 Elasticsearch 服務..."
    
    # 檢查本地 Elasticsearch
    if curl -s http://localhost:9200 > /dev/null 2>&1; then
        local es_info=$(curl -s http://localhost:9200)
        local cluster_name=$(echo $es_info | grep -o '"cluster_name":"[^"]*"' | cut -d'"' -f4)
        log_success "Elasticsearch 已運行 (集群: $cluster_name)"
        return 0
    fi
    
    log_warning "Elasticsearch 未運行，將嘗試啟動..."
    return 1
}

# 啟動 Elasticsearch（如果需要）
start_elasticsearch() {
    log_info "啟動 Elasticsearch 服務..."
    
    # 使用 Docker Compose 啟動 Elasticsearch
    docker-compose up -d elasticsearch
    
    # 等待 Elasticsearch 啟動
    log_info "等待 Elasticsearch 啟動..."
    for i in {1..60}; do
        if curl -s http://localhost:9200 > /dev/null 2>&1; then
            log_success "Elasticsearch 啟動成功"
            return 0
        fi
        echo -n "."
        sleep 2
    done
    
    log_error "Elasticsearch 啟動超時"
    return 1
}

# 啟動 Kibana
start_kibana() {
    log_info "啟動 Kibana 服務..."
    
    # 確保 Kibana 配置目錄存在
    mkdir -p kibana
    
    # 使用 Docker Compose 啟動 Kibana
    docker-compose up -d kibana
    
    # 等待 Kibana 啟動
    log_info "等待 Kibana 啟動（這可能需要 1-2 分鐘）..."
    for i in {1..120}; do
        if curl -s http://localhost:5601/api/status > /dev/null 2>&1; then
            log_success "Kibana 啟動成功"
            return 0
        fi
        echo -n "."
        sleep 2
    done
    
    log_error "Kibana 啟動超時"
    return 1
}

# 啟動 Elasticvue
start_elasticvue() {
    log_info "啟動 Elasticvue 服務..."
    
    # 使用 Docker Compose 啟動 Elasticvue
    docker-compose up -d elasticvue
    
    # 等待 Elasticvue 啟動
    log_info "等待 Elasticvue 啟動..."
    for i in {1..30}; do
        if curl -s http://localhost:8080 > /dev/null 2>&1; then
            log_success "Elasticvue 啟動成功"
            return 0
        fi
        echo -n "."
        sleep 2
    done
    
    log_error "Elasticvue 啟動超時"
    return 1
}

# 初始化 Elasticsearch 索引
initialize_indices() {
    log_info "初始化 Elasticsearch 索引..."
    
    if python3 scripts/init_elasticsearch.py; then
        log_success "Elasticsearch 索引初始化成功"
    else
        log_warning "Elasticsearch 索引初始化失敗，但繼續啟動 UI"
    fi
}

# 健康檢查
health_check() {
    log_info "執行服務健康檢查..."
    
    local all_healthy=true
    
    # 檢查 Elasticsearch
    if curl -s http://localhost:9200/_cluster/health > /dev/null 2>&1; then
        local health=$(curl -s http://localhost:9200/_cluster/health | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
        log_success "Elasticsearch: 健康 (狀態: $health)"
    else
        log_error "Elasticsearch: 不健康"
        all_healthy=false
    fi
    
    # 檢查 Kibana
    if curl -s http://localhost:5601/api/status > /dev/null 2>&1; then
        log_success "Kibana: 健康"
    else
        log_error "Kibana: 不健康"
        all_healthy=false
    fi
    
    # 檢查 Elasticvue
    if curl -s http://localhost:8080 > /dev/null 2>&1; then
        log_success "Elasticvue: 健康"
    else
        log_error "Elasticvue: 不健康"
        all_healthy=false
    fi
    
    if $all_healthy; then
        display_success_info
    else
        log_warning "部分服務可能需要更多時間啟動"
    fi
}

# 顯示成功信息
display_success_info() {
    echo ""
    log_header "🎉 AI SEO 優化王 Elasticsearch Web UI 啟動完成！"
    echo ""
    echo "📊 Web UI 訪問地址："
    echo "   • Kibana (主要分析界面): http://localhost:5601"
    echo "   • Elasticvue (管理界面): http://localhost:8080"
    echo "   • Elasticsearch API: http://localhost:9200"
    echo ""
    echo "🔧 管理功能："
    echo "   • 數據可視化和儀表板: Kibana"
    echo "   • 索引管理和文檔編輯: Elasticvue"
    echo "   • API 直接訪問: curl http://localhost:9200"
    echo ""
    echo "📚 SEO 索引："
    echo "   • seo_content: 網頁內容和 SEO 數據"
    echo "   • seo_analysis: SEO 分析結果"
    echo "   • seo_keywords: 關鍵詞研究數據"
    echo "   • seo_competitors: 競爭對手分析"
    echo ""
    echo "🛠️ 管理命令："
    echo "   • 停止服務: ./scripts/stop_elasticsearch_ui.sh"
    echo "   • 重啟服務: ./scripts/restart_elasticsearch_ui.sh"
    echo "   • 查看日誌: docker-compose logs -f kibana elasticvue"
    echo ""
}

# 主函數
main() {
    log_header "🚀 啟動 AI SEO 優化王 Elasticsearch Web UI"
    echo "========================================================"
    
    # 檢查環境
    check_docker
    
    # 檢查並啟動 Elasticsearch
    if ! check_elasticsearch; then
        start_elasticsearch
    fi
    
    # 初始化索引
    initialize_indices
    
    # 啟動 UI 服務
    start_kibana
    start_elasticvue
    
    # 健康檢查
    health_check
}

# 執行主函數
main "$@"
