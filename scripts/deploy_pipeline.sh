#!/bin/bash

# AI SEO 數據收集管道部署腳本
# 提供完整的管道部署、啟動和監控功能

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置變數
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
DOCKER_COMPOSE_FILE="$PROJECT_DIR/docker-compose.yml"
ENV_FILE="$PROJECT_DIR/.env"

# 命令行參數
ACTION="${1:-deploy}"
ENVIRONMENT="${2:-production}"

# 顯示幫助信息
show_help() {
    cat << EOF
AI SEO 數據收集管道部署腳本

用法: $0 [動作] [環境]

動作:
  deploy       部署完整的數據收集管道
  start        啟動數據收集管道
  stop         停止數據收集管道
  restart      重啟數據收集管道
  status       檢查管道狀態
  logs         查看管道日誌
  health       健康檢查
  update       更新管道組件
  cleanup      清理資源

環境:
  development  開發環境
  staging      測試環境
  production   生產環境 (默認)

範例:
  $0 deploy production     # 部署生產環境
  $0 start development     # 啟動開發環境
  $0 status               # 檢查狀態
  $0 logs                 # 查看日誌

EOF
}

# 檢查必需的工具
check_prerequisites() {
    log_info "檢查必需的工具..."
    
    local missing_tools=()
    
    if ! command -v docker &> /dev/null; then
        missing_tools+=("docker")
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        missing_tools+=("docker-compose")
    fi
    
    if ! command -v curl &> /dev/null; then
        missing_tools+=("curl")
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "缺少必需的工具: ${missing_tools[*]}"
        log_error "請安裝這些工具後重試"
        exit 1
    fi
    
    log_success "所有必需的工具都已安裝"
}

# 檢查環境文件
check_environment() {
    log_info "檢查環境配置..."
    
    if [ ! -f "$ENV_FILE" ]; then
        log_warning "環境文件 .env 不存在，創建模板..."
        cat > "$ENV_FILE" << EOF
# AI SEO 數據收集管道環境配置

# 數據庫配置
DATABASE_URL=******************************************************/ai_seo_king

# Redis 配置
REDIS_PASSWORD=aiseo123

# OpenAI 配置
OPENAI_API_KEY=your_openai_api_key_here

# JWT 配置
JWT_SECRET=your_jwt_secret_here

# Supabase 配置 (可選)
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# 性能配置
ENABLE_DEBUG_MODE=false
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_ERROR_TRACKING=true

# 管道配置
PIPELINE_BATCH_SIZE=100
PIPELINE_FLUSH_INTERVAL=5000
PIPELINE_MAX_RETRIES=3
PIPELINE_PARALLELISM=4

EOF
        log_warning "請編輯 .env 文件並設置正確的配置值"
        exit 1
    fi
    
    # 檢查關鍵配置
    if ! grep -q "OPENAI_API_KEY=" "$ENV_FILE" || grep -q "your_openai_api_key_here" "$ENV_FILE"; then
        log_warning "請在 .env 文件中設置有效的 OPENAI_API_KEY"
    fi
    
    log_success "環境配置檢查完成"
}

# 檢查 Docker 狀態
check_docker() {
    log_info "檢查 Docker 狀態..."
    
    if ! docker info &> /dev/null; then
        log_error "Docker 未運行或無法訪問"
        exit 1
    fi
    
    log_success "Docker 運行正常"
}

# 部署數據收集管道
deploy_pipeline() {
    log_info "開始部署數據收集管道..."
    
    # 檢查先決條件
    check_prerequisites
    check_environment
    check_docker
    
    # 停止現有服務
    log_info "停止現有服務..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" down --remove-orphans
    
    # 清理舊的容器和映像（可選）
    if [ "$ENVIRONMENT" = "production" ]; then
        log_info "清理舊資源..."
        docker system prune -f
    fi
    
    # 構建和啟動服務
    log_info "構建和啟動服務..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d --build
    
    # 等待服務啟動
    log_info "等待服務啟動..."
    wait_for_services
    
    # 初始化數據收集管道
    log_info "初始化數據收集管道..."
    initialize_pipeline
    
    # 執行健康檢查
    health_check
    
    log_success "數據收集管道部署完成！"
}

# 啟動管道
start_pipeline() {
    log_info "啟動數據收集管道..."
    
    check_docker
    
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    wait_for_services
    initialize_pipeline
    
    log_success "數據收集管道已啟動"
}

# 停止管道
stop_pipeline() {
    log_info "停止數據收集管道..."
    
    docker-compose -f "$DOCKER_COMPOSE_FILE" down
    
    log_success "數據收集管道已停止"
}

# 重啟管道
restart_pipeline() {
    log_info "重啟數據收集管道..."
    
    stop_pipeline
    start_pipeline
    
    log_success "數據收集管道已重啟"
}

# 等待服務啟動
wait_for_services() {
    local services=("postgres:5432" "redis:6379" "elasticsearch:9200" "fastapi:8000")
    local max_attempts=30
    local attempt=1
    
    log_info "等待核心服務啟動..."
    
    for service in "${services[@]}"; do
        local host_port=(${service//:/ })
        local host=${host_port[0]}
        local port=${host_port[1]}
        
        log_info "等待 $host:$port..."
        
        attempt=1
        while [ $attempt -le $max_attempts ]; do
            if docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T "$host" sh -c "command -v nc" &> /dev/null; then
                # 使用 netcat 檢查
                if docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T "$host" nc -z localhost "$port" &> /dev/null; then
                    log_success "$host:$port 已就緒"
                    break
                fi
            else
                # 使用 curl 檢查 HTTP 服務
                if [ "$host" = "fastapi" ] || [ "$host" = "elasticsearch" ]; then
                    if curl -f "http://localhost:$port" &> /dev/null; then
                        log_success "$host:$port 已就緒"
                        break
                    fi
                else
                    # 使用 telnet 檢查
                    if timeout 1 bash -c "echo > /dev/tcp/localhost/$port" &> /dev/null; then
                        log_success "$host:$port 已就緒"
                        break
                    fi
                fi
            fi
            
            if [ $attempt -eq $max_attempts ]; then
                log_error "$host:$port 啟動超時"
                return 1
            fi
            
            log_info "等待 $host:$port... (嘗試 $attempt/$max_attempts)"
            sleep 2
            ((attempt++))
        done
    done
    
    log_success "所有核心服務已啟動"
}

# 初始化數據收集管道
initialize_pipeline() {
    log_info "初始化數據收集管道..."
    
    local fastapi_url="http://localhost:8000"
    local max_attempts=10
    local attempt=1
    
    # 等待 FastAPI 服務完全就緒
    while [ $attempt -le $max_attempts ]; do
        if curl -f "$fastapi_url/health" &> /dev/null; then
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            log_error "FastAPI 服務未就緒"
            return 1
        fi
        
        log_info "等待 FastAPI 服務就緒... (嘗試 $attempt/$max_attempts)"
        sleep 3
        ((attempt++))
    done
    
    # 啟動數據收集管道
    log_info "啟動數據收集管道..."
    local auth_token=$(get_admin_token)
    
    if [ -z "$auth_token" ]; then
        log_warning "無法獲取管理員令牌，請手動啟動管道"
        return 0
    fi
    
    local response=$(curl -s -X POST \
        -H "Authorization: Bearer $auth_token" \
        -H "Content-Type: application/json" \
        "$fastapi_url/api/v1/pipeline/start")
    
    if echo "$response" | grep -q '"status":"starting"'; then
        log_success "數據收集管道啟動命令已發送"
    else
        log_warning "管道啟動響應: $response"
    fi
}

# 獲取管理員令牌（簡化版本，實際應用中需要更安全的方式）
get_admin_token() {
    # 這是一個簡化的實現，生產環境中應該使用更安全的認證方式
    local fastapi_url="http://localhost:8000"
    
    # 嘗試使用測試憑據登錄
    local login_response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d '{"email":"<EMAIL>","password":"admin123"}' \
        "$fastapi_url/api/v1/auth/login" 2>/dev/null)
    
    if echo "$login_response" | grep -q '"access_token"'; then
        echo "$login_response" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4
    fi
}

# 檢查管道狀態
check_status() {
    log_info "檢查數據收集管道狀態..."
    
    # 檢查 Docker 容器狀態
    log_info "Docker 容器狀態:"
    docker-compose -f "$DOCKER_COMPOSE_FILE" ps
    
    # 檢查服務健康狀態
    log_info "服務健康狀態:"
    local services=("http://localhost:8000/health" "http://localhost:9200/_cluster/health" "http://localhost:3000/api/health")
    
    for service in "${services[@]}"; do
        if curl -f "$service" &> /dev/null; then
            log_success "✓ $service"
        else
            log_error "✗ $service"
        fi
    done
    
    # 檢查管道狀態
    log_info "管道狀態:"
    local fastapi_url="http://localhost:8000"
    local pipeline_status=$(curl -s "$fastapi_url/api/v1/pipeline/health" 2>/dev/null)
    
    if echo "$pipeline_status" | grep -q '"status":"healthy"'; then
        log_success "✓ 數據收集管道運行正常"
    else
        log_warning "⚠ 數據收集管道狀態異常"
        echo "$pipeline_status"
    fi
}

# 健康檢查
health_check() {
    log_info "執行健康檢查..."
    
    local failed_checks=0
    
    # 檢查 PostgreSQL
    if docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T postgres pg_isready -U ai_seo_user -d ai_seo_king &> /dev/null; then
        log_success "✓ PostgreSQL 健康"
    else
        log_error "✗ PostgreSQL 不健康"
        ((failed_checks++))
    fi
    
    # 檢查 Redis
    if docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T redis redis-cli ping &> /dev/null; then
        log_success "✓ Redis 健康"
    else
        log_error "✗ Redis 不健康"
        ((failed_checks++))
    fi
    
    # 檢查 Elasticsearch
    if curl -f "http://localhost:9200/_cluster/health" &> /dev/null; then
        log_success "✓ Elasticsearch 健康"
    else
        log_error "✗ Elasticsearch 不健康"
        ((failed_checks++))
    fi
    
    # 檢查 FastAPI
    if curl -f "http://localhost:8000/health" &> /dev/null; then
        log_success "✓ FastAPI 健康"
    else
        log_error "✗ FastAPI 不健康"
        ((failed_checks++))
    fi
    
    # 檢查數據收集管道
    local pipeline_health=$(curl -s "http://localhost:8000/api/v1/pipeline/health" 2>/dev/null)
    if echo "$pipeline_health" | grep -q '"overall_healthy":true'; then
        log_success "✓ 數據收集管道健康"
    else
        log_error "✗ 數據收集管道不健康"
        ((failed_checks++))
    fi
    
    if [ $failed_checks -eq 0 ]; then
        log_success "所有組件健康檢查通過"
        return 0
    else
        log_error "$failed_checks 個組件健康檢查失敗"
        return 1
    fi
}

# 查看日誌
view_logs() {
    local service="${2:-}"
    
    if [ -z "$service" ]; then
        log_info "查看所有服務日誌..."
        docker-compose -f "$DOCKER_COMPOSE_FILE" logs -f
    else
        log_info "查看 $service 服務日誌..."
        docker-compose -f "$DOCKER_COMPOSE_FILE" logs -f "$service"
    fi
}

# 更新管道
update_pipeline() {
    log_info "更新數據收集管道..."
    
    # 停止服務
    docker-compose -f "$DOCKER_COMPOSE_FILE" down
    
    # 拉取最新映像
    docker-compose -f "$DOCKER_COMPOSE_FILE" pull
    
    # 重新構建和啟動
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d --build
    
    wait_for_services
    initialize_pipeline
    health_check
    
    log_success "數據收集管道更新完成"
}

# 清理資源
cleanup_resources() {
    log_info "清理數據收集管道資源..."
    
    # 停止並移除容器
    docker-compose -f "$DOCKER_COMPOSE_FILE" down --remove-orphans --volumes
    
    # 清理未使用的映像和容器
    docker system prune -f
    
    # 清理未使用的卷
    docker volume prune -f
    
    log_success "資源清理完成"
}

# 主函數
main() {
    case "$ACTION" in
        "help"|"-h"|"--help")
            show_help
            ;;
        "deploy")
            deploy_pipeline
            ;;
        "start")
            start_pipeline
            ;;
        "stop")
            stop_pipeline
            ;;
        "restart")
            restart_pipeline
            ;;
        "status")
            check_status
            ;;
        "health")
            health_check
            ;;
        "logs")
            view_logs "$@"
            ;;
        "update")
            update_pipeline
            ;;
        "cleanup")
            cleanup_resources
            ;;
        *)
            log_error "未知的動作: $ACTION"
            show_help
            exit 1
            ;;
    esac
}

# 執行主函數
main "$@" 