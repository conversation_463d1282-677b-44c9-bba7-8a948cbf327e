-- 查詢分析系統數據庫遷移腳本
-- 版本: 1.0.0
-- 創建時間: 2024-01-01

-- ============================================
-- 1. 主要查詢表
-- ============================================
CREATE TABLE IF NOT EXISTS queries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    query_text TEXT NOT NULL,
    normalized_query TEXT,
    source VARCHAR(50) DEFAULT 'web',
    user_id VARCHAR(255),
    session_id VARCHAR(255),
    intent_type VARCHAR(100),
    intent_confidence DECIMAL(3,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB
);

-- 查詢表索引
CREATE INDEX IF NOT EXISTS idx_queries_created_at ON queries(created_at);
CREATE INDEX IF NOT EXISTS idx_queries_intent ON queries(intent_type);
CREATE INDEX IF NOT EXISTS idx_queries_source ON queries(source);
CREATE INDEX IF NOT EXISTS idx_queries_user_id ON queries(user_id);
CREATE INDEX IF NOT EXISTS idx_queries_session_id ON queries(session_id);

-- 全文搜索索引
CREATE INDEX IF NOT EXISTS idx_query_text_gin ON queries USING gin(to_tsvector('english', query_text));
CREATE INDEX IF NOT EXISTS idx_normalized_query_gin ON queries USING gin(to_tsvector('english', normalized_query));

-- ============================================
-- 2. 意圖分類表
-- ============================================
CREATE TABLE IF NOT EXISTS query_intents (
    id SERIAL PRIMARY KEY,
    query_id UUID REFERENCES queries(id) ON DELETE CASCADE,
    intent_category VARCHAR(100) NOT NULL,
    confidence_score DECIMAL(3,2) NOT NULL,
    model_version VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 意圖表索引
CREATE INDEX IF NOT EXISTS idx_query_intents_query_id ON query_intents(query_id);
CREATE INDEX IF NOT EXISTS idx_query_intents_category ON query_intents(intent_category);
CREATE INDEX IF NOT EXISTS idx_query_intents_confidence ON query_intents(confidence_score);

-- ============================================
-- 3. 主題標籤表
-- ============================================
CREATE TABLE IF NOT EXISTS query_topics (
    id SERIAL PRIMARY KEY,
    query_id UUID REFERENCES queries(id) ON DELETE CASCADE,
    topic VARCHAR(200) NOT NULL,
    relevance_score DECIMAL(3,2) NOT NULL,
    extraction_method VARCHAR(50) DEFAULT 'keyword',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 主題表索引
CREATE INDEX IF NOT EXISTS idx_query_topics_query_id ON query_topics(query_id);
CREATE INDEX IF NOT EXISTS idx_query_topics_topic ON query_topics(topic);
CREATE INDEX IF NOT EXISTS idx_query_topics_relevance ON query_topics(relevance_score);

-- ============================================
-- 4. 查詢統計表
-- ============================================
CREATE TABLE IF NOT EXISTS query_statistics (
    id SERIAL PRIMARY KEY,
    date_period DATE NOT NULL,
    total_queries INTEGER DEFAULT 0,
    unique_users INTEGER DEFAULT 0,
    avg_confidence DECIMAL(3,2),
    top_intent VARCHAR(100),
    top_source VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 統計表索引
CREATE UNIQUE INDEX IF NOT EXISTS idx_query_stats_date ON query_statistics(date_period);
CREATE INDEX IF NOT EXISTS idx_query_stats_created_at ON query_statistics(created_at);

-- ============================================
-- 5. 實時分析緩存表
-- ============================================
CREATE TABLE IF NOT EXISTS realtime_analytics (
    id SERIAL PRIMARY KEY,
    metric_type VARCHAR(50) NOT NULL,
    metric_value JSONB NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP
);

-- 實時分析表索引
CREATE INDEX IF NOT EXISTS idx_realtime_analytics_type ON realtime_analytics(metric_type);
CREATE INDEX IF NOT EXISTS idx_realtime_analytics_timestamp ON realtime_analytics(timestamp);
CREATE INDEX IF NOT EXISTS idx_realtime_analytics_expires ON realtime_analytics(expires_at);

-- ============================================
-- 6. 用戶會話表
-- ============================================
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255),
    source VARCHAR(50),
    first_query_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_query_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    total_queries INTEGER DEFAULT 0,
    user_agent TEXT,
    ip_address INET,
    metadata JSONB
);

-- 會話表索引
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_sessions_session_id ON user_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_first_query ON user_sessions(first_query_at);

-- ============================================
-- 7. 趨勢分析表
-- ============================================
CREATE TABLE IF NOT EXISTS trend_analysis (
    id SERIAL PRIMARY KEY,
    keyword VARCHAR(200) NOT NULL,
    period_start TIMESTAMP NOT NULL,
    period_end TIMESTAMP NOT NULL,
    mention_count INTEGER DEFAULT 0,
    growth_rate DECIMAL(5,2),
    momentum_score DECIMAL(3,2),
    category VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 趨勢表索引
CREATE INDEX IF NOT EXISTS idx_trend_analysis_keyword ON trend_analysis(keyword);
CREATE INDEX IF NOT EXISTS idx_trend_analysis_period ON trend_analysis(period_start, period_end);
CREATE INDEX IF NOT EXISTS idx_trend_analysis_growth ON trend_analysis(growth_rate);

-- ============================================
-- 8. 洞察報告表
-- ============================================
CREATE TABLE IF NOT EXISTS insights_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(500) NOT NULL,
    category VARCHAR(100) NOT NULL,
    description TEXT,
    impact_level VARCHAR(20) DEFAULT 'medium',
    confidence_score DECIMAL(3,2) NOT NULL,
    data_source JSONB,
    recommendations JSONB,
    status VARCHAR(20) DEFAULT 'draft',
    created_by VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 洞察報告表索引
CREATE INDEX IF NOT EXISTS idx_insights_category ON insights_reports(category);
CREATE INDEX IF NOT EXISTS idx_insights_impact ON insights_reports(impact_level);
CREATE INDEX IF NOT EXISTS idx_insights_status ON insights_reports(status);
CREATE INDEX IF NOT EXISTS idx_insights_created_at ON insights_reports(created_at);

-- ============================================
-- 9. 系統配置表
-- ============================================
CREATE TABLE IF NOT EXISTS system_configurations (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL,
    config_value JSONB NOT NULL,
    description TEXT,
    category VARCHAR(50) DEFAULT 'general',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 配置表索引
CREATE UNIQUE INDEX IF NOT EXISTS idx_system_config_key ON system_configurations(config_key);
CREATE INDEX IF NOT EXISTS idx_system_config_category ON system_configurations(category);

-- ============================================
-- 10. 數據源配置表
-- ============================================
CREATE TABLE IF NOT EXISTS data_sources (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL,
    connection_config JSONB NOT NULL,
    status VARCHAR(20) DEFAULT 'inactive',
    last_sync_at TIMESTAMP,
    sync_frequency VARCHAR(50) DEFAULT 'daily',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 數據源表索引
CREATE INDEX IF NOT EXISTS idx_data_sources_type ON data_sources(type);
CREATE INDEX IF NOT EXISTS idx_data_sources_status ON data_sources(status);

-- ============================================
-- 觸發器和函數
-- ============================================

-- 更新時間戳觸發器函數
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 為需要的表添加更新時間戳觸發器
CREATE TRIGGER update_queries_updated_at 
    BEFORE UPDATE ON queries 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_insights_updated_at 
    BEFORE UPDATE ON insights_reports 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_config_updated_at 
    BEFORE UPDATE ON system_configurations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_data_sources_updated_at 
    BEFORE UPDATE ON data_sources 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================
-- 初始數據插入
-- ============================================

-- 插入默認系統配置
INSERT INTO system_configurations (config_key, config_value, description, category) VALUES
('analytics.retention_days', '90', '查詢數據保留天數', 'analytics'),
('analytics.batch_size', '100', '批處理大小', 'analytics'),
('realtime.update_interval', '5', '實時更新間隔（秒）', 'realtime'),
('ai.intent_threshold', '0.7', 'AI意圖分類信心閾值', 'ai'),
('ai.topic_extraction_enabled', 'true', '是否啟用主題提取', 'ai')
ON CONFLICT (config_key) DO NOTHING;

-- 插入默認數據源配置
INSERT INTO data_sources (name, type, connection_config, status) VALUES
('Internal Search Logs', 'log_file', '{"path": "/var/log/search.log", "format": "json"}', 'active'),
('Google Search Console', 'api', '{"api_key": "", "site_url": ""}', 'inactive'),
('Google Analytics', 'api', '{"client_id": "", "client_secret": ""}', 'inactive')
ON CONFLICT DO NOTHING;

-- ============================================
-- 性能優化
-- ============================================

-- 分區設置（按日期分區查詢表）
-- 注意：這需要根據實際數據量來決定是否實施

-- 定期清理過期數據的函數
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS void AS $$
DECLARE
    retention_days INTEGER;
BEGIN
    -- 獲取數據保留天數配置
    SELECT (config_value::TEXT)::INTEGER INTO retention_days 
    FROM system_configurations 
    WHERE config_key = 'analytics.retention_days';
    
    IF retention_days IS NULL THEN
        retention_days := 90; -- 默認值
    END IF;
    
    -- 刪除過期的查詢數據
    DELETE FROM queries 
    WHERE created_at < NOW() - INTERVAL '1 day' * retention_days;
    
    -- 刪除過期的實時分析數據
    DELETE FROM realtime_analytics 
    WHERE expires_at IS NOT NULL AND expires_at < NOW();
    
    -- 記錄清理操作
    RAISE NOTICE '數據清理完成，保留 % 天內的數據', retention_days;
END;
$$ LANGUAGE plpgsql;

-- ============================================
-- 視圖定義
-- ============================================

-- 查詢統計視圖
CREATE OR REPLACE VIEW query_analytics_summary AS
SELECT 
    DATE(created_at) as query_date,
    COUNT(*) as total_queries,
    COUNT(DISTINCT user_id) as unique_users,
    COUNT(DISTINCT session_id) as unique_sessions,
    AVG(intent_confidence) as avg_confidence,
    MODE() WITHIN GROUP (ORDER BY intent_type) as top_intent,
    MODE() WITHIN GROUP (ORDER BY source) as top_source
FROM queries 
WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(created_at)
ORDER BY query_date DESC;

-- 實時指標視圖
CREATE OR REPLACE VIEW realtime_metrics AS
SELECT 
    'total_today' as metric_name,
    COUNT(*) as metric_value
FROM queries 
WHERE DATE(created_at) = CURRENT_DATE
UNION ALL
SELECT 
    'active_sessions' as metric_name,
    COUNT(DISTINCT session_id) as metric_value
FROM queries 
WHERE created_at >= NOW() - INTERVAL '1 hour'
UNION ALL
SELECT 
    'avg_confidence' as metric_name,
    ROUND(AVG(intent_confidence)::numeric, 2) as metric_value
FROM queries 
WHERE created_at >= NOW() - INTERVAL '24 hours';

-- ============================================
-- 完成提示
-- ============================================
DO $$
BEGIN
    RAISE NOTICE '數據庫遷移完成！';
    RAISE NOTICE '已創建以下表：';
    RAISE NOTICE '- queries: 主要查詢數據表';
    RAISE NOTICE '- query_intents: 意圖分類表';
    RAISE NOTICE '- query_topics: 主題標籤表';
    RAISE NOTICE '- query_statistics: 查詢統計表';
    RAISE NOTICE '- realtime_analytics: 實時分析緩存表';
    RAISE NOTICE '- user_sessions: 用戶會話表';
    RAISE NOTICE '- trend_analysis: 趨勢分析表';
    RAISE NOTICE '- insights_reports: 洞察報告表';
    RAISE NOTICE '- system_configurations: 系統配置表';
    RAISE NOTICE '- data_sources: 數據源配置表';
    RAISE NOTICE '';
    RAISE NOTICE '已創建視圖：';
    RAISE NOTICE '- query_analytics_summary: 查詢統計摘要';
    RAISE NOTICE '- realtime_metrics: 實時指標';
    RAISE NOTICE '';
    RAISE NOTICE '請運行以下命令來設置定期數據清理：';
    RAISE NOTICE 'SELECT cron.schedule(''cleanup-old-data'', ''0 2 * * *'', ''SELECT cleanup_old_data();'');';
END $$; 