#!/bin/bash

# 管理後台服務啟動腳本
# 用於快速啟動所有相關服務

set -e

# 顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查 Docker 是否安裝
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安裝，請先安裝 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安裝，請先安裝 Docker Compose"
        exit 1
    fi
    
    log_success "Docker 環境檢查通過"
}

# 檢查環境變量
check_env() {
    if [ ! -f .env ]; then
        log_warning ".env 文件不存在，創建默認配置..."
        cat > .env << EOF
# 數據庫配置
DATABASE_URL=********************************************/admin_analytics
POSTGRES_DB=admin_analytics
POSTGRES_USER=admin
POSTGRES_PASSWORD=password123

# 應用配置
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-here
SECRET_KEY=your-secret-key-here

# 服務 URL
WEBSOCKET_URL=ws://localhost:3001
AI_SERVICE_URL=http://localhost:5000
EOF
        log_warning "請編輯 .env 文件並設置正確的配置值"
    fi
    
    log_success "環境配置檢查完成"
}

# 清理舊服務
cleanup_services() {
    log_info "清理舊的服務實例..."
    
    # 停止可能運行的服務
    pkill -f "next dev" || true
    pkill -f "uvicorn" || true
    pkill -f "python.*intent_classifier" || true
    
    # 停止 Docker 容器
    docker-compose -f docker-compose.admin.yml down || true
    
    log_success "舊服務清理完成"
}

# 構建和啟動服務
start_services() {
    log_info "構建和啟動管理後台服務..."
    
    # 構建並啟動所有服務
    docker-compose -f docker-compose.admin.yml up --build -d
    
    log_success "服務啟動完成"
}

# 等待服務就緒
wait_for_services() {
    log_info "等待服務就緒..."
    
    # 等待數據庫就緒
    log_info "等待 PostgreSQL 數據庫..."
    while ! docker-compose -f docker-compose.admin.yml exec -T postgres pg_isready -U admin; do
        sleep 2
    done
    
    # 等待 Redis 就緒
    log_info "等待 Redis 緩存..."
    while ! docker-compose -f docker-compose.admin.yml exec -T redis redis-cli ping; do
        sleep 2
    done
    
    # 等待 AI 服務就緒
    log_info "等待 AI 服務..."
    sleep 10 # AI 服務需要更長時間加載模型
    
    log_success "所有服務已就緒"
}

# 運行數據庫遷移
run_migrations() {
    log_info "運行數據庫遷移..."
    
    # 執行遷移腳本
    docker-compose -f docker-compose.admin.yml exec -T postgres psql -U admin -d admin_analytics -f /docker-entrypoint-initdb.d/init.sql || true
    
    log_success "數據庫遷移完成"
}

# 顯示服務狀態
show_status() {
    log_info "服務狀態："
    docker-compose -f docker-compose.admin.yml ps
    
    echo ""
    log_info "服務訪問地址："
    echo "  前端應用: http://localhost:3000"
    echo "  後端 API: http://localhost:8000"
    echo "  AI 服務: http://localhost:5000"
    echo "  管理後台: http://localhost:3000/admin"
    echo ""
    echo "  數據庫: localhost:5432 (用戶: admin)"
    echo "  Redis: localhost:6379"
}

# 測試服務連接
test_services() {
    log_info "測試服務連接..."
    
    # 測試前端
    if curl -f http://localhost:3000 >/dev/null 2>&1; then
        log_success "前端服務連接正常"
    else
        log_warning "前端服務連接失敗"
    fi
    
    # 測試後端 API
    if curl -f http://localhost:8000/health >/dev/null 2>&1; then
        log_success "後端 API 連接正常"
    else
        log_warning "後端 API 連接失敗"
    fi
    
    # 測試 AI 服務
    if curl -f http://localhost:5000/health >/dev/null 2>&1; then
        log_success "AI 服務連接正常"
    else
        log_warning "AI 服務連接失敗"
    fi
}

# 主函數
main() {
    log_info "開始啟動 AI SEO 管理後台服務..."
    
    check_docker
    check_env
    cleanup_services
    start_services
    wait_for_services
    run_migrations
    
    echo ""
    show_status
    
    echo ""
    test_services
    
    echo ""
    log_success "管理後台服務啟動完成！"
    log_info "使用 'docker-compose -f docker-compose.admin.yml logs -f' 查看日誌"
    log_info "使用 'docker-compose -f docker-compose.admin.yml down' 停止服務"
}

# 處理命令行參數
case "${1:-}" in
    "stop")
        log_info "停止所有服務..."
        docker-compose -f docker-compose.admin.yml down
        cleanup_services
        log_success "服務已停止"
        ;;
    "restart")
        log_info "重啟服務..."
        docker-compose -f docker-compose.admin.yml down
        main
        ;;
    "status")
        show_status
        ;;
    "logs")
        docker-compose -f docker-compose.admin.yml logs -f
        ;;
    "test")
        test_services
        ;;
    *)
        main
        ;;
esac 