#!/usr/bin/env python3
"""
AI SEO 優化王 - Web UI 服務器
提供簡單的 HTTP 服務器來運行 Elasticsearch Web 管理界面
"""

import os
import sys
import http.server
import socketserver
import webbrowser
import threading
import time
from pathlib import Path

# 配置
WEB_UI_PORT = 8080
WEB_UI_DIR = "web-ui"
ELASTICSEARCH_URL = "http://localhost:9200"

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """支持 CORS 的 HTTP 請求處理器"""
    
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        super().end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        """自定義日誌格式"""
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def check_elasticsearch():
    """檢查 Elasticsearch 是否運行"""
    try:
        import urllib.request
        response = urllib.request.urlopen(ELASTICSEARCH_URL, timeout=5)
        if response.getcode() == 200:
            return True
    except Exception:
        pass
    return False

def start_web_server():
    """啟動 Web 服務器"""
    # 切換到 web-ui 目錄
    web_ui_path = Path(__file__).parent.parent / WEB_UI_DIR
    if not web_ui_path.exists():
        print(f"❌ Web UI 目錄不存在: {web_ui_path}")
        return False
    
    os.chdir(web_ui_path)
    
    try:
        # 創建服務器
        with socketserver.TCPServer(("", WEB_UI_PORT), CORSHTTPRequestHandler) as httpd:
            print(f"🚀 AI SEO 優化王 Web UI 服務器啟動成功")
            print(f"📊 訪問地址: http://localhost:{WEB_UI_PORT}")
            print(f"🔗 Elasticsearch: {ELASTICSEARCH_URL}")
            print(f"📁 服務目錄: {web_ui_path}")
            print(f"⏹️  按 Ctrl+C 停止服務器")
            print("=" * 60)
            
            # 延遲打開瀏覽器
            def open_browser():
                time.sleep(2)
                try:
                    webbrowser.open(f"http://localhost:{WEB_UI_PORT}")
                    print(f"🌐 已在瀏覽器中打開 Web UI")
                except Exception as e:
                    print(f"⚠️ 無法自動打開瀏覽器: {e}")
            
            browser_thread = threading.Thread(target=open_browser)
            browser_thread.daemon = True
            browser_thread.start()
            
            # 啟動服務器
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 端口 {WEB_UI_PORT} 已被占用")
            print(f"💡 請檢查是否已有服務在運行，或使用其他端口")
            return False
        else:
            print(f"❌ 啟動服務器失敗: {e}")
            return False
    except KeyboardInterrupt:
        print(f"\n🛑 Web UI 服務器已停止")
        return True

def main():
    """主函數"""
    print("🔍 AI SEO 優化王 - Elasticsearch Web UI 啟動器")
    print("=" * 60)
    
    # 檢查 Elasticsearch 連接
    print("📡 檢查 Elasticsearch 連接...")
    if check_elasticsearch():
        print(f"✅ Elasticsearch 連接正常: {ELASTICSEARCH_URL}")
    else:
        print(f"⚠️ 無法連接到 Elasticsearch: {ELASTICSEARCH_URL}")
        print("💡 請確保 Elasticsearch 服務正在運行")
        
        # 詢問是否繼續
        try:
            response = input("是否仍要啟動 Web UI？(y/N): ").strip().lower()
            if response not in ['y', 'yes']:
                print("❌ 已取消啟動")
                return 1
        except KeyboardInterrupt:
            print("\n❌ 已取消啟動")
            return 1
    
    # 啟動 Web 服務器
    print("\n🚀 啟動 Web UI 服務器...")
    success = start_web_server()
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
