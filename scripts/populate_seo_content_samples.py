#!/usr/bin/env python3
"""
AI SEO 優化王 - 填充 seo_content 索引示例數據
"""

import asyncio
import json
import sys
import os
from datetime import datetime, timedelta
import random

# 添加項目路徑
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend-fastapi'))

try:
    from app.core.elasticsearch import es_client
    from app.core.config import get_settings
except ImportError as e:
    print(f"❌ 導入錯誤: {e}")
    sys.exit(1)

settings = get_settings()

class SEOContentSampleGenerator:
    """SEO 內容示例數據生成器"""
    
    def __init__(self):
        self.sample_data = [
            {
                "url": "https://aiseoking.com/ai-seo-guide",
                "title": "AI SEO 優化完整指南：提升網站排名的智能策略",
                "description": "深入了解如何使用人工智能技術進行搜索引擎優化，包含最新的 AI SEO 工具和策略，幫助您的網站在競爭激烈的市場中脫穎而出。",
                "content": """
                人工智能正在革命性地改變搜索引擎優化的方式。本指南將帶您深入了解 AI SEO 的核心概念和實際應用。
                
                ## AI SEO 的核心優勢
                
                1. **智能關鍵詞分析**: AI 可以分析大量數據，識別最有價值的關鍵詞機會
                2. **內容優化建議**: 基於機器學習的內容分析，提供精準的優化建議
                3. **競爭對手分析**: 自動化的競爭對手監控和分析
                4. **用戶意圖理解**: 深度學習技術幫助理解用戶搜索意圖
                
                ## 實施 AI SEO 策略
                
                ### 第一步：數據收集和分析
                使用 AI 工具收集網站數據、用戶行為數據和競爭對手信息。這些數據將成為優化決策的基礎。
                
                ### 第二步：關鍵詞策略制定
                AI 算法可以分析搜索趨勢、競爭程度和轉換潛力，幫助制定最優的關鍵詞策略。
                
                ### 第三步：內容創作和優化
                利用 AI 工具生成內容大綱、優化現有內容，確保內容既符合 SEO 要求又能滿足用戶需求。
                
                ## 未來展望
                
                隨著 AI 技術的不斷發展，SEO 將變得更加智能化和自動化。掌握 AI SEO 技術將成為數字營銷專業人士的必備技能。
                """,
                "keywords": "AI SEO, 人工智能, 搜索引擎優化, 智能營銷, 機器學習",
                "meta_tags": {
                    "title": "AI SEO 優化完整指南 - AI SEO 優化王",
                    "description": "學習如何使用人工智能技術進行搜索引擎優化，提升網站排名和流量。包含實用工具和策略指南。",
                    "keywords": ["AI SEO", "人工智能", "搜索引擎優化", "智能營銷"]
                },
                "domain": "aiseoking.com",
                "seo_score": 92.5,
                "word_count": 1250,
                "status": "active"
            },
            {
                "url": "https://aiseoking.com/keyword-research-tools",
                "title": "2024年最佳關鍵詞研究工具推薦",
                "description": "探索最新的關鍵詞研究工具，包含免費和付費選項，幫助您找到高價值的SEO關鍵詞。",
                "content": """
                關鍵詞研究是 SEO 成功的基石。選擇正確的工具可以大大提高您的關鍵詞研究效率。
                
                ## 頂級關鍵詞研究工具
                
                ### 1. Google Keyword Planner
                Google 官方提供的免費工具，提供準確的搜索量數據和關鍵詞建議。
                
                ### 2. SEMrush
                功能強大的付費工具，提供競爭對手分析和關鍵詞難度評估。
                
                ### 3. Ahrefs Keywords Explorer
                專業級工具，擁有龐大的關鍵詞數據庫和精確的難度評分。
                
                ## 關鍵詞研究最佳實踐
                
                1. 從種子關鍵詞開始
                2. 分析競爭對手的關鍵詞策略
                3. 考慮用戶搜索意圖
                4. 評估關鍵詞商業價值
                """,
                "keywords": "關鍵詞研究, SEO工具, Google Keyword Planner, SEMrush, Ahrefs",
                "meta_tags": {
                    "title": "2024年最佳關鍵詞研究工具 - 完整評測",
                    "description": "比較分析頂級關鍵詞研究工具，包含功能特色、價格和使用建議。",
                    "keywords": ["關鍵詞研究", "SEO工具", "關鍵詞分析"]
                },
                "domain": "aiseoking.com",
                "seo_score": 88.0,
                "word_count": 850,
                "status": "active"
            },
            {
                "url": "https://competitor.com/seo-basics",
                "title": "SEO基礎知識入門",
                "description": "學習SEO的基本概念和技巧",
                "content": """
                SEO是搜索引擎優化的縮寫，是提高網站在搜索引擎中排名的技術。
                
                基本的SEO包括：
                1. 關鍵詞優化
                2. 內容質量
                3. 技術優化
                4. 外部連結
                """,
                "keywords": "SEO, 搜索引擎優化, 基礎知識",
                "meta_tags": {
                    "title": "SEO基礎知識 - 入門指南",
                    "description": "學習SEO的基本概念和實用技巧",
                    "keywords": ["SEO", "搜索引擎優化"]
                },
                "domain": "competitor.com",
                "seo_score": 65.5,
                "word_count": 320,
                "status": "active"
            },
            {
                "url": "https://aiseoking.com/content-optimization",
                "title": "內容優化策略：打造高質量SEO內容",
                "description": "學習如何創作和優化高質量的SEO內容，提升搜索引擎排名和用戶體驗。",
                "content": """
                高質量的內容是SEO成功的關鍵。本文將分享內容優化的核心策略和實用技巧。
                
                ## 內容優化的核心原則
                
                ### 1. 用戶價值優先
                始終以提供用戶價值為核心，創作真正有用的內容。
                
                ### 2. 關鍵詞自然融入
                將目標關鍵詞自然地融入內容中，避免關鍵詞堆砌。
                
                ### 3. 結構化內容
                使用清晰的標題層次和段落結構，提高內容可讀性。
                
                ## 內容優化技巧
                
                1. **標題優化**: 包含主要關鍵詞，控制在60字符以內
                2. **Meta描述**: 吸引人的描述，包含關鍵詞，150-160字符
                3. **內部連結**: 合理的內部連結結構
                4. **圖片優化**: 使用描述性的alt標籤
                
                ## 內容更新策略
                
                定期更新內容，保持信息的時效性和準確性。監控內容表現，根據數據調整優化策略。
                """,
                "keywords": "內容優化, SEO內容, 內容營銷, 搜索引擎優化",
                "meta_tags": {
                    "title": "內容優化策略指南 - 提升SEO效果",
                    "description": "掌握內容優化的核心策略，創作高質量SEO內容，提升網站排名。",
                    "keywords": ["內容優化", "SEO內容", "內容營銷"]
                },
                "domain": "aiseoking.com",
                "seo_score": 89.5,
                "word_count": 980,
                "status": "active"
            },
            {
                "url": "https://testsite.com/old-article",
                "title": "過時的SEO文章",
                "description": "這是一篇需要更新的舊文章",
                "content": "簡短的內容，需要擴充。SEO技巧包括關鍵詞和連結。",
                "keywords": "SEO, 舊文章",
                "meta_tags": {
                    "title": "過時的SEO文章",
                    "keywords": ["SEO"]
                },
                "domain": "testsite.com",
                "seo_score": 45.0,
                "word_count": 180,
                "status": "active"
            }
        ]
    
    async def populate_sample_data(self):
        """填充示例數據到 seo_content 索引"""
        print("🚀 開始填充 seo_content 索引示例數據...")
        
        try:
            await es_client.connect()
            
            success_count = 0
            total_count = len(self.sample_data)
            
            for i, data in enumerate(self.sample_data):
                # 添加時間戳
                base_time = datetime.now() - timedelta(days=random.randint(1, 30))
                data["created_at"] = base_time.isoformat()
                data["updated_at"] = (base_time + timedelta(days=random.randint(0, 5))).isoformat()
                
                # 索引文檔
                doc_id = f"sample_{i+1}"
                success = await es_client.index_document(
                    index_name=settings.ES_SEO_CONTENT_INDEX,
                    doc_id=doc_id,
                    document=data
                )
                
                if success:
                    success_count += 1
                    print(f"   ✅ 已添加: {data['title'][:50]}...")
                else:
                    print(f"   ❌ 添加失敗: {data['title'][:50]}...")
            
            print(f"\n📊 數據填充完成:")
            print(f"   • 成功: {success_count}/{total_count}")
            print(f"   • 失敗: {total_count - success_count}/{total_count}")
            
            # 刷新索引
            await es_client.client.indices.refresh(index=settings.ES_SEO_CONTENT_INDEX)
            print("   • 索引已刷新")
            
            return success_count == total_count
            
        except Exception as e:
            print(f"❌ 填充數據時發生錯誤: {e}")
            return False
        finally:
            await es_client.close()
    
    async def verify_data(self):
        """驗證數據是否成功添加"""
        print("\n🔍 驗證示例數據...")
        
        try:
            await es_client.connect()
            
            # 查詢所有示例數據
            results = await es_client.search(
                index_name=settings.ES_SEO_CONTENT_INDEX,
                query={"match_all": {}},
                size=10
            )
            
            total_hits = results["hits"]["total"]["value"]
            print(f"   📚 索引中共有 {total_hits} 個文檔")
            
            if total_hits > 0:
                print("   📋 示例文檔:")
                for hit in results["hits"]["hits"][:3]:
                    source = hit["_source"]
                    print(f"      • {source['title'][:60]}... (分數: {source['seo_score']})")
                
                # 統計分析
                stats_query = {
                    "size": 0,
                    "aggs": {
                        "avg_score": {"avg": {"field": "seo_score"}},
                        "score_ranges": {
                            "range": {
                                "field": "seo_score",
                                "ranges": [
                                    {"to": 60, "key": "需改進"},
                                    {"from": 60, "to": 80, "key": "中等"},
                                    {"from": 80, "key": "優秀"}
                                ]
                            }
                        }
                    }
                }
                
                stats = await es_client.search(
                    index_name=settings.ES_SEO_CONTENT_INDEX,
                    query={"match_all": {}},
                    aggregations=stats_query["aggs"],
                    size=0
                )
                
                avg_score = stats["aggregations"]["avg_score"]["value"]
                print(f"   📊 平均 SEO 分數: {avg_score:.1f}")
                
                print("   📈 分數分佈:")
                for bucket in stats["aggregations"]["score_ranges"]["buckets"]:
                    print(f"      • {bucket['key']}: {bucket['doc_count']} 個文檔")
            
            return total_hits > 0
            
        except Exception as e:
            print(f"❌ 驗證數據時發生錯誤: {e}")
            return False
        finally:
            await es_client.close()


async def main():
    """主函數"""
    generator = SEOContentSampleGenerator()
    
    try:
        print("📝 AI SEO 優化王 - seo_content 索引示例數據填充工具")
        print("=" * 60)
        
        # 填充示例數據
        success = await generator.populate_sample_data()
        
        if success:
            # 驗證數據
            await generator.verify_data()
            
            print("\n🎉 示例數據填充完成！")
            print("\n🌐 現在您可以:")
            print("   1. 訪問 Web UI: http://localhost:8080")
            print("   2. 登入系統: admin / aiseo2024")
            print("   3. 選擇 seo_content 索引")
            print("   4. 嘗試指南中的查詢範例")
            print("\n📚 參考文檔:")
            print("   • 完整指南: docs/SEO_CONTENT_INDEX_GUIDE.md")
            print("   • 快速參考: docs/SEO_CONTENT_QUICK_REFERENCE.md")
            
            sys.exit(0)
        else:
            print("❌ 示例數據填充失敗")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⏹️ 操作被用戶中斷")
        sys.exit(130)
    except Exception as e:
        print(f"\n❌ 發生未預期錯誤: {e}")
        sys.exit(3)


if __name__ == "__main__":
    asyncio.run(main())
