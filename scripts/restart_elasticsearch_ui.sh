#!/bin/bash

# AI SEO 優化王 - Elasticsearch Web UI 重啟腳本

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[HEADER]${NC} $1"
}

# 顯示幫助信息
show_help() {
    echo "AI SEO 優化王 - Elasticsearch Web UI 重啟腳本"
    echo ""
    echo "用法: $0 [選項]"
    echo ""
    echo "選項:"
    echo "  --with-elasticsearch    同時重啟 Elasticsearch 服務"
    echo "  --cleanup              重啟前清理容器"
    echo "  --help                 顯示此幫助信息"
    echo ""
    echo "示例:"
    echo "  $0                           # 僅重啟 Web UI 服務"
    echo "  $0 --with-elasticsearch      # 重啟 UI 和 Elasticsearch"
    echo "  $0 --cleanup                 # 清理後重啟服務"
}

# 主函數
main() {
    log_header "🔄 重啟 AI SEO 優化王 Elasticsearch Web UI"
    echo "========================================================"
    
    # 解析命令行參數
    local restart_elasticsearch=false
    local cleanup=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --with-elasticsearch)
                restart_elasticsearch=true
                shift
                ;;
            --cleanup)
                cleanup=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知選項: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 構建停止腳本參數
    local stop_args=""
    if $restart_elasticsearch; then
        stop_args="$stop_args --with-elasticsearch"
    fi
    if $cleanup; then
        stop_args="$stop_args --cleanup"
    fi
    
    # 停止服務
    log_info "第一步：停止現有服務..."
    if [ -f "scripts/stop_elasticsearch_ui.sh" ]; then
        ./scripts/stop_elasticsearch_ui.sh $stop_args
    else
        log_error "找不到停止腳本: scripts/stop_elasticsearch_ui.sh"
        exit 1
    fi
    
    # 等待一段時間確保服務完全停止
    log_info "等待服務完全停止..."
    sleep 5
    
    # 啟動服務
    log_info "第二步：啟動服務..."
    if [ -f "scripts/start_elasticsearch_ui.sh" ]; then
        ./scripts/start_elasticsearch_ui.sh
    else
        log_error "找不到啟動腳本: scripts/start_elasticsearch_ui.sh"
        exit 1
    fi
    
    echo ""
    log_success "Elasticsearch Web UI 重啟完成！"
}

# 執行主函數
main "$@"
