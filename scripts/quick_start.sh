#!/bin/bash

# AI SEO 數據收集管道快速啟動腳本
# 解決基礎設施連接問題並啟動完整系統

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查和啟動基礎服務
start_infrastructure() {
    log_info "啟動基礎設施服務..."
    
    # 檢查 Docker
    if ! docker info &> /dev/null; then
        log_warning "Docker 未運行，正在啟動 Docker Desktop..."
        open -a Docker
        
        # 等待 Docker 啟動
        local max_attempts=30
        local attempt=1
        while [ $attempt -le $max_attempts ]; do
            if docker info &> /dev/null; then
                log_success "Docker 已啟動"
                break
            fi
            
            if [ $attempt -eq $max_attempts ]; then
                log_error "Docker 啟動超時，請手動啟動 Docker Desktop"
                exit 1
            fi
            
            log_info "等待 Docker 啟動... (嘗試 $attempt/$max_attempts)"
            sleep 2
            ((attempt++))
        done
    else
        log_success "Docker 已運行"
    fi
    
    # 啟動核心服務（僅啟動必需的服務）
    log_info "啟動核心基礎設施服務..."
    
    # 創建一個最小化的 docker-compose 文件用於測試
    cat > docker-compose.minimal.yml << EOF
version: '3.8'

services:
  # PostgreSQL 數據庫
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=ai_seo_user
      - POSTGRES_PASSWORD=ai_seo_password
      - POSTGRES_DB=ai_seo_king
    volumes:
      - postgres-data:/var/lib/postgresql/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ai_seo_user -d ai_seo_king"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis 快取服務
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru --requirepass aiseo123
    volumes:
      - redis-data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "aiseo123", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Elasticsearch 搜索引擎
  elasticsearch:
    image: elasticsearch:7.17.15
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - cluster.name=ai-seo-king
      - node.name=seo-node-1
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
      - xpack.monitoring.collection.enabled=false
      - http.cors.enabled=true
      - http.cors.allow-origin="*"
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  postgres-data:
  redis-data:
  elasticsearch-data:
EOF
    
    # 停止可能存在的舊容器
    docker-compose -f docker-compose.minimal.yml down 2>/dev/null || true
    
    # 啟動核心服務
    docker-compose -f docker-compose.minimal.yml up -d
    
    log_success "核心基礎設施服務已啟動"
}

# 等待服務就緒
wait_for_services() {
    log_info "等待服務就緒..."
    
    local services=("postgres:5432" "redis:6379" "elasticsearch:9200")
    local max_attempts=30
    
    for service in "${services[@]}"; do
        local host_port=(${service//:/ })
        local host=${host_port[0]}
        local port=${host_port[1]}
        
        log_info "等待 $host:$port..."
        
        local attempt=1
        while [ $attempt -le $max_attempts ]; do
            if [ "$host" = "elasticsearch" ]; then
                if curl -f "http://localhost:$port" &> /dev/null; then
                    log_success "$host:$port 已就緒"
                    break
                fi
            else
                if nc -z localhost "$port" &> /dev/null 2>&1; then
                    log_success "$host:$port 已就緒"
                    break
                fi
            fi
            
            if [ $attempt -eq $max_attempts ]; then
                log_error "$host:$port 啟動超時"
                return 1
            fi
            
            sleep 2
            ((attempt++))
        done
    done
    
    log_success "所有核心服務已就緒"
}

# 配置環境變量
setup_environment() {
    log_info "設置環境變量..."
    
    if [ ! -f ".env" ]; then
        log_info "創建 .env 文件..."
        cat > .env << EOF
# AI SEO 數據收集管道環境配置

# 數據庫配置
DATABASE_URL=postgresql://ai_seo_user:ai_seo_password@localhost:5432/ai_seo_king

# Redis 配置
REDIS_URL=redis://:aiseo123@localhost:6379/0
REDIS_PASSWORD=aiseo123

# Elasticsearch 配置
ELASTICSEARCH_URL=http://localhost:9200

# OpenAI 配置 (請設置您的 API 密鑰)
OPENAI_API_KEY=your_openai_api_key_here

# JWT 配置
JWT_SECRET=ai_seo_jwt_secret_key_2024

# Supabase 配置 (可選)
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# 性能配置
ENABLE_DEBUG_MODE=false
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_ERROR_TRACKING=true

# 管道配置
PIPELINE_BATCH_SIZE=50
PIPELINE_FLUSH_INTERVAL=3000
PIPELINE_MAX_RETRIES=3
PIPELINE_PARALLELISM=2

EOF
        log_success ".env 文件已創建"
    else
        log_info ".env 文件已存在，跳過創建"
    fi
}

# 啟動 FastAPI 後端（僅數據收集管道相關功能）
start_fastapi() {
    log_info "啟動 FastAPI 後端..."
    
    # 檢查 Python 環境
    if [ ! -d ".venv" ]; then
        log_info "創建 Python 虛擬環境..."
        python3 -m venv .venv
        source .venv/bin/activate
        pip install -r backend-fastapi/requirements.txt
    else
        source .venv/bin/activate
    fi
    
    # 設置 Python 路徑
    export PYTHONPATH="${PYTHONPATH}:$(pwd)/backend-fastapi"
    
    # 進入後端目錄並啟動服務
    cd backend-fastapi
    
    log_info "啟動 FastAPI 開發服務器..."
    uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload &
    FASTAPI_PID=$!
    
    cd ..
    
    # 等待 FastAPI 啟動
    local max_attempts=20
    local attempt=1
    while [ $attempt -le $max_attempts ]; do
        if curl -f "http://localhost:8000/health" &> /dev/null; then
            log_success "FastAPI 已啟動並運行在 http://localhost:8000"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            log_error "FastAPI 啟動超時"
            return 1
        fi
        
        log_info "等待 FastAPI 啟動... (嘗試 $attempt/$max_attempts)"
        sleep 3
        ((attempt++))
    done
}

# 測試數據收集管道
test_pipeline() {
    log_info "測試數據收集管道..."
    
    # 檢查管道 API 端點
    local pipeline_health=$(curl -s "http://localhost:8000/api/v1/pipeline/health" 2>/dev/null)
    
    if echo "$pipeline_health" | grep -q '"status"'; then
        log_success "✓ 數據收集管道 API 可用"
        
        # 嘗試獲取管道狀態
        local pipeline_status=$(curl -s -H "Content-Type: application/json" \
            "http://localhost:8000/api/v1/pipeline/health" 2>/dev/null)
        
        log_info "管道狀態: $pipeline_status"
    else
        log_warning "⚠ 數據收集管道 API 不可用，但基礎設施已啟動"
    fi
}

# 顯示使用說明
show_usage() {
    echo ""
    echo "🎉 AI SEO 數據收集管道快速啟動完成！"
    echo ""
    echo "📡 已啟動的服務："
    echo "  • PostgreSQL: localhost:5432"
    echo "  • Redis: localhost:6379"
    echo "  • Elasticsearch: localhost:9200"
    echo "  • FastAPI: http://localhost:8000"
    echo ""
    echo "🔧 API 端點："
    echo "  • 健康檢查: http://localhost:8000/health"
    echo "  • 管道健康: http://localhost:8000/api/v1/pipeline/health"
    echo "  • API 文檔: http://localhost:8000/docs"
    echo ""
    echo "💡 使用說明："
    echo "  • 查看狀態: ./scripts/deploy_pipeline.sh status"
    echo "  • 查看日誌: docker-compose -f docker-compose.minimal.yml logs -f"
    echo "  • 停止服務: docker-compose -f docker-compose.minimal.yml down"
    echo ""
    echo "⚠️ 注意："
    echo "  • 請在 .env 文件中設置有效的 OPENAI_API_KEY"
    echo "  • 使用 Ctrl+C 停止 FastAPI 服務"
    echo ""
}

# 清理函數
cleanup() {
    log_info "正在清理..."
    
    # 停止 FastAPI
    if [ ! -z "$FASTAPI_PID" ]; then
        kill $FASTAPI_PID 2>/dev/null || true
    fi
    
    log_success "清理完成"
}

# 設置陷阱來處理 Ctrl+C
trap cleanup EXIT

# 主執行流程
main() {
    echo "🚀 AI SEO 數據收集管道快速啟動"
    echo "=================================="
    
    setup_environment
    start_infrastructure
    wait_for_services
    start_fastapi
    test_pipeline
    show_usage
    
    log_info "按 Ctrl+C 停止所有服務"
    
    # 保持腳本運行
    while true; do
        sleep 1
    done
}

# 執行主函數
main "$@" 