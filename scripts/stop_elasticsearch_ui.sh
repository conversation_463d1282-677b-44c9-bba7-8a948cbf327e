#!/bin/bash

# AI SEO 優化王 - Elasticsearch Web UI 停止腳本

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[HEADER]${NC} $1"
}

# 停止 Docker 容器
stop_container() {
    local container_name=$1
    local service_name=$2
    
    log_info "停止 $service_name..."
    
    if docker ps -q -f name="$container_name" | grep -q .; then
        docker-compose stop $service_name > /dev/null 2>&1
        log_success "$service_name 已停止"
    else
        log_info "$service_name 未運行"
    fi
}

# 停止所有 UI 服務
stop_ui_services() {
    log_info "停止 Elasticsearch Web UI 服務..."
    
    # 停止 Elasticvue
    stop_container "aiseo-elasticvue" "elasticvue"
    
    # 停止 Kibana
    stop_container "aiseo-kibana" "kibana"
    
    log_success "所有 Web UI 服務已停止"
}

# 可選：停止 Elasticsearch
stop_elasticsearch() {
    log_info "停止 Elasticsearch 服務..."
    
    if docker ps -q -f name="aiseo-elasticsearch" | grep -q .; then
        docker-compose stop elasticsearch > /dev/null 2>&1
        log_success "Elasticsearch 已停止"
    else
        log_info "Elasticsearch 未運行"
    fi
}

# 清理容器（可選）
cleanup_containers() {
    log_info "清理停止的容器..."
    
    # 移除停止的容器
    docker-compose rm -f kibana elasticvue > /dev/null 2>&1 || true
    
    log_success "容器清理完成"
}

# 驗證停止狀態
verify_stop() {
    log_info "驗證服務停止狀態..."
    
    local ports=(5601 8080)
    local services=("Kibana" "Elasticvue")
    local all_stopped=true
    
    for i in "${!ports[@]}"; do
        local port=${ports[$i]}
        local service=${services[$i]}
        
        if lsof -ti:$port > /dev/null 2>&1; then
            log_warning "$service (端口 $port) 仍在運行"
            all_stopped=false
        else
            log_success "$service (端口 $port) 已停止"
        fi
    done
    
    if $all_stopped; then
        log_success "所有 Web UI 服務已成功停止"
    else
        log_warning "部分服務可能仍在運行"
    fi
}

# 顯示幫助信息
show_help() {
    echo "AI SEO 優化王 - Elasticsearch Web UI 停止腳本"
    echo ""
    echo "用法: $0 [選項]"
    echo ""
    echo "選項:"
    echo "  --with-elasticsearch    同時停止 Elasticsearch 服務"
    echo "  --cleanup              停止後清理容器"
    echo "  --help                 顯示此幫助信息"
    echo ""
    echo "示例:"
    echo "  $0                           # 僅停止 Web UI 服務"
    echo "  $0 --with-elasticsearch      # 停止 UI 和 Elasticsearch"
    echo "  $0 --cleanup                 # 停止服務並清理容器"
}

# 主函數
main() {
    log_header "🛑 停止 AI SEO 優化王 Elasticsearch Web UI"
    echo "========================================================"
    
    # 解析命令行參數
    local stop_elasticsearch=false
    local cleanup=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --with-elasticsearch)
                stop_elasticsearch=true
                shift
                ;;
            --cleanup)
                cleanup=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知選項: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 停止 UI 服務
    stop_ui_services
    
    # 可選：停止 Elasticsearch
    if $stop_elasticsearch; then
        stop_elasticsearch
    fi
    
    # 可選：清理容器
    if $cleanup; then
        cleanup_containers
    fi
    
    # 驗證停止狀態
    verify_stop
    
    echo ""
    log_success "Elasticsearch Web UI 停止完成！"
    
    if ! $stop_elasticsearch; then
        echo ""
        log_info "注意：Elasticsearch 服務仍在運行"
        log_info "如需停止 Elasticsearch，請使用: $0 --with-elasticsearch"
    fi
}

# 執行主函數
main "$@"
