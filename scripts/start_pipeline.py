#!/usr/bin/env python3
"""
AI SEO 數據收集管道啟動和管理腳本
提供命令行界面來控制數據收集管道
"""

import asyncio
import sys
import argparse
import signal
import time
from pathlib import Path
from typing import Optional
import json

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 現在可以導入項目模組
from backend_fastapi.app.services.data_collection_pipeline import get_data_collection_pipeline
from backend_fastapi.app.services.pipeline_optimizer import get_pipeline_optimizer, OptimizationLevel
from backend_fastapi.app.core.redis import get_redis


class PipelineManager:
    """數據收集管道管理器"""
    
    def __init__(self):
        self.pipeline = None
        self.optimizer = None
        self.is_running = False
        self.monitoring_task = None
        self.optimization_task = None

    async def initialize(self):
        """初始化管道組件"""
        print("🚀 正在初始化數據收集管道...")
        
        try:
            # 初始化管道
            self.pipeline = await get_data_collection_pipeline()
            print("✓ 數據收集管道已初始化")
            
            # 初始化優化器
            self.optimizer = await get_pipeline_optimizer()
            print("✓ 性能優化器已初始化")
            
            return True
            
        except Exception as e:
            print(f"❌ 初始化失敗: {str(e)}")
            return False

    async def start_pipeline(self, enable_optimization: bool = True):
        """啟動數據收集管道"""
        if not await self.initialize():
            return False
        
        print("🎯 正在啟動數據收集管道...")
        
        try:
            # 啟動管道
            success = await self.pipeline.initialize()
            if not success:
                print("❌ 管道啟動失敗")
                return False
            
            self.is_running = True
            print("✓ 數據收集管道已啟動")
            
            # 啟動性能監控
            if enable_optimization:
                print("📊 啟動性能監控和自動優化...")
                self.monitoring_task = asyncio.create_task(
                    self._monitor_pipeline_performance()
                )
                self.optimization_task = asyncio.create_task(
                    self.optimizer.start_monitoring()
                )
                print("✓ 性能監控已啟動")
            
            return True
            
        except Exception as e:
            print(f"❌ 管道啟動失敗: {str(e)}")
            return False

    async def stop_pipeline(self):
        """停止數據收集管道"""
        print("🛑 正在停止數據收集管道...")
        
        self.is_running = False
        
        # 停止監控任務
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        if self.optimization_task:
            self.optimization_task.cancel()
            try:
                await self.optimization_task
            except asyncio.CancelledError:
                pass
        
        # 停止管道
        if self.pipeline:
            await self.pipeline.stop()
        
        print("✓ 數據收集管道已停止")

    async def show_status(self):
        """顯示管道狀態"""
        if not self.pipeline:
            print("❌ 管道未初始化")
            return
        
        print("\n" + "="*50)
        print("📊 數據收集管道狀態")
        print("="*50)
        
        try:
            metrics = await self.pipeline.get_pipeline_metrics()
            
            print(f"運行狀態: {'🟢 運行中' if self.pipeline.is_running else '🔴 已停止'}")
            print(f"收集器數量: {len(self.pipeline.collectors)}")
            print(f"處理器數量: {len(self.pipeline.processors)}")
            print(f"已處理總數: {metrics.get('total_processed', 0):,}")
            print(f"錯誤總數: {metrics.get('total_errors', 0):,}")
            print(f"當前吞吐量: {metrics.get('throughput', {}).get('current', 0):.2f} 請求/分鐘")
            print(f"峰值吞吐量: {metrics.get('throughput', {}).get('peak', 0):.2f} 請求/分鐘")
            
            # 處理延遲統計
            latency_list = metrics.get('processing_latency', [])
            if latency_list:
                avg_latency = sum(latency_list) / len(latency_list)
                print(f"平均處理延遲: {avg_latency:.2f} ms")
            
            # 收集器狀態
            print(f"\n📡 收集器狀態:")
            for name, collector in self.pipeline.collectors.items():
                status = "🟢 活躍" if getattr(collector, 'enabled', True) else "🔴 停用"
                print(f"  • {name}: {status}")
            
            # 處理器狀態
            print(f"\n⚙️ 處理器狀態:")
            for processor in self.pipeline.processors:
                print(f"  • {processor.__class__.__name__}: 🟢 活躍")
            
        except Exception as e:
            print(f"❌ 獲取狀態失敗: {str(e)}")

    async def show_metrics(self):
        """顯示詳細指標"""
        if not self.pipeline:
            print("❌ 管道未初始化")
            return
        
        try:
            metrics = await self.pipeline.get_pipeline_metrics()
            
            print("\n" + "="*60)
            print("📈 詳細性能指標")
            print("="*60)
            
            # 基礎指標
            total_processed = metrics.get('total_processed', 0)
            total_errors = metrics.get('total_errors', 0)
            error_rate = (total_errors / total_processed * 100) if total_processed > 0 else 0
            
            print(f"處理總數: {total_processed:,}")
            print(f"成功處理: {total_processed - total_errors:,}")
            print(f"失敗處理: {total_errors:,}")
            print(f"成功率: {100 - error_rate:.2f}%")
            print(f"錯誤率: {error_rate:.2f}%")
            
            # 吞吐量指標
            throughput = metrics.get('throughput', {})
            print(f"\n🚀 吞吐量指標:")
            print(f"  當前: {throughput.get('current', 0):.2f} 請求/分鐘")
            print(f"  峰值: {throughput.get('peak', 0):.2f} 請求/分鐘")
            
            # 延遲指標
            latency_list = metrics.get('processing_latency', [])
            if latency_list:
                avg_latency = sum(latency_list) / len(latency_list)
                min_latency = min(latency_list)
                max_latency = max(latency_list)
                
                print(f"\n⏱️ 延遲指標:")
                print(f"  平均: {avg_latency:.2f} ms")
                print(f"  最小: {min_latency:.2f} ms")
                print(f"  最大: {max_latency:.2f} ms")
            
            # 優化報告
            if self.optimizer:
                print(f"\n🎯 優化報告:")
                optimization_report = await self.optimizer.get_optimization_report()
                summary = optimization_report.get('summary', {})
                
                print(f"  優化級別: {summary.get('optimization_level', 'unknown')}")
                print(f"  總優化次數: {summary.get('total_optimizations', 0)}")
                print(f"  成功優化次數: {summary.get('successful_optimizations', 0)}")
                print(f"  成功率: {summary.get('success_rate', 0):.1f}%")
                
                last_optimization = optimization_report.get('last_optimization')
                if last_optimization:
                    print(f"  最後優化: {last_optimization}")
                else:
                    print(f"  最後優化: 無")
            
        except Exception as e:
            print(f"❌ 獲取指標失敗: {str(e)}")

    async def test_pipeline(self, num_samples: int = 10):
        """測試管道功能"""
        if not self.pipeline or not self.pipeline.is_running:
            print("❌ 管道未運行，請先啟動管道")
            return
        
        print(f"🧪 開始測試管道功能（{num_samples} 個測試樣本）...")
        
        from backend_fastapi.app.services.data_collection_pipeline import QueryData, DataSourceType
        from datetime import datetime
        import uuid
        
        test_queries = [
            "SEO 優化技巧",
            "關鍵詞研究方法", 
            "網站排名提升",
            "內容行銷策略",
            "反向連結建設",
            "技術 SEO 檢查",
            "本地 SEO 優化",
            "移動 SEO 優化",
            "語音搜索優化",
            "電商 SEO 策略"
        ]
        
        try:
            for i in range(num_samples):
                query_text = test_queries[i % len(test_queries)]
                
                query_data = QueryData(
                    id=str(uuid.uuid4()),
                    query=f"{query_text} - 測試 {i+1}",
                    source=DataSourceType.API_REQUEST,
                    user_id="test_user",
                    session_id=f"test_session_{i}",
                    timestamp=datetime.utcnow(),
                    metadata={"test": True, "sample_id": i+1}
                )
                
                collection_id = await self.pipeline.collect_query(query_data)
                print(f"✓ 測試樣本 {i+1}: 已提交收集 (ID: {collection_id[:8]}...)")
                
                # 稍微延遲以避免過度負載
                await asyncio.sleep(0.1)
            
            print(f"✅ 已成功提交 {num_samples} 個測試樣本")
            print("💡 請稍候查看處理結果和指標變化")
            
        except Exception as e:
            print(f"❌ 測試失敗: {str(e)}")

    async def _monitor_pipeline_performance(self):
        """監控管道性能"""
        while self.is_running:
            try:
                await asyncio.sleep(60)  # 每分鐘檢查一次
                
                if self.pipeline.is_running:
                    metrics = await self.pipeline.get_pipeline_metrics()
                    
                    # 簡單的性能警告
                    error_rate = 0
                    total_processed = metrics.get('total_processed', 0)
                    total_errors = metrics.get('total_errors', 0)
                    
                    if total_processed > 0:
                        error_rate = (total_errors / total_processed) * 100
                    
                    if error_rate > 5:  # 錯誤率超過 5%
                        print(f"⚠️ 警告: 錯誤率過高 ({error_rate:.1f}%)")
                    
                    # 記錄當前狀態到日誌
                    throughput = metrics.get('throughput', {}).get('current', 0)
                    print(f"📊 {time.strftime('%H:%M:%S')} | 處理: {total_processed} | 錯誤: {total_errors} | 吞吐量: {throughput:.1f}/min")
                
            except Exception as e:
                print(f"⚠️ 監控出錯: {str(e)}")


async def main():
    """主函數"""
    parser = argparse.ArgumentParser(description="AI SEO 數據收集管道管理器")
    parser.add_argument("action", choices=['start', 'stop', 'status', 'metrics', 'test', 'run'], 
                       help="要執行的動作")
    parser.add_argument("--no-optimization", action="store_true", 
                       help="禁用自動優化功能")
    parser.add_argument("--test-samples", type=int, default=10,
                       help="測試樣本數量（默認：10）")
    
    args = parser.parse_args()
    
    manager = PipelineManager()
    
    try:
        if args.action == "start":
            success = await manager.start_pipeline(
                enable_optimization=not args.no_optimization
            )
            if success:
                print("\n🎉 數據收集管道已成功啟動！")
                print("💡 使用 'python start_pipeline.py status' 查看狀態")
                print("💡 使用 'python start_pipeline.py metrics' 查看詳細指標")
                print("💡 使用 Ctrl+C 停止管道")
                
                # 設置信號處理
                def signal_handler(signum, frame):
                    print("\n🛑 收到停止信號，正在關閉管道...")
                    asyncio.create_task(manager.stop_pipeline())
                
                signal.signal(signal.SIGINT, signal_handler)
                signal.signal(signal.SIGTERM, signal_handler)
                
                # 保持運行直到收到停止信號
                try:
                    while manager.is_running:
                        await asyncio.sleep(1)
                except KeyboardInterrupt:
                    await manager.stop_pipeline()
        
        elif args.action == "stop":
            await manager.stop_pipeline()
        
        elif args.action == "status":
            if await manager.initialize():
                await manager.show_status()
        
        elif args.action == "metrics":
            if await manager.initialize():
                await manager.show_metrics()
        
        elif args.action == "test":
            if await manager.initialize():
                await manager.test_pipeline(args.test_samples)
        
        elif args.action == "run":
            # 運行模式：啟動並保持運行
            success = await manager.start_pipeline(
                enable_optimization=not args.no_optimization
            )
            if success:
                print("🎉 管道已啟動，進入運行模式...")
                try:
                    while True:
                        await asyncio.sleep(1)
                except KeyboardInterrupt:
                    print("\n🛑 正在停止管道...")
                    await manager.stop_pipeline()
    
    except Exception as e:
        print(f"❌ 執行失敗: {str(e)}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(asyncio.run(main())) 