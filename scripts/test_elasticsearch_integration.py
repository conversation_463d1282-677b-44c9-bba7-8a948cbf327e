#!/usr/bin/env python3
"""
AI SEO 優化王 - Elasticsearch 整合測試
"""

import asyncio
import json
import sys
import os
from datetime import datetime
from typing import Dict, Any, List

# 添加項目路徑
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend-fastapi'))

try:
    from app.core.elasticsearch import es_client
    from app.core.config import get_settings
    from app.services.elasticsearch_service import ElasticsearchSEOService
except ImportError as e:
    print(f"❌ 導入錯誤: {e}")
    print("請確保已安裝所有依賴並正確配置項目")
    sys.exit(1)

settings = get_settings()


class ElasticsearchIntegrationTest:
    """Elasticsearch 整合測試"""
    
    def __init__(self):
        self.es_service = ElasticsearchSEOService()
        self.test_data = {
            "seo_content": {
                "url": "https://test.aiseoking.com/sample-page",
                "title": "AI SEO 優化王 - 測試頁面標題",
                "description": "這是一個用於測試 Elasticsearch 整合的示例頁面描述，包含豐富的 SEO 相關內容。",
                "content": """
                AI SEO 優化王是一個強大的搜索引擎優化工具，專為提升網站在搜索引擎中的排名而設計。
                我們的平台提供全面的 SEO 分析功能，包括關鍵詞研究、競爭對手分析、內容優化建議等。
                
                主要功能包括：
                1. 智能關鍵詞分析
                2. 網站技術 SEO 檢測
                3. 內容質量評估
                4. 競爭對手監控
                5. 排名追蹤
                
                使用 AI SEO 優化王，您可以輕鬆提升網站的搜索引擎可見性，獲得更多有機流量。
                """,
                "keywords": "AI SEO, 搜索引擎優化, 關鍵詞分析, 網站優化, 排名提升",
                "meta_tags": {
                    "title": "AI SEO 優化王 - 專業的搜索引擎優化工具",
                    "description": "提供全面的 SEO 分析和優化建議，幫助您的網站在搜索引擎中獲得更好的排名",
                    "keywords": ["AI SEO", "搜索引擎優化", "關鍵詞分析", "網站優化"]
                },
                "seo_score": 88.5,
                "word_count": 256,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "status": "active"
            },
            "seo_keywords": [
                {
                    "keyword": "AI SEO 優化",
                    "keyword_text": "AI SEO 優化",
                    "search_volume": 1200,
                    "competition": "medium",
                    "difficulty": 65.5,
                    "cpc": 3.20,
                    "trend_data": [
                        {"date": "2024-01-01", "volume": 1000, "position": 8},
                        {"date": "2024-02-01", "volume": 1100, "position": 6},
                        {"date": "2024-03-01", "volume": 1200, "position": 5}
                    ],
                    "related_keywords": ["SEO工具", "網站優化", "搜索引擎"],
                    "category": "SEO",
                    "intent": "commercial",
                    "language": "zh-TW",
                    "region": "TW",
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat()
                },
                {
                    "keyword": "網站 SEO 分析",
                    "keyword_text": "網站 SEO 分析",
                    "search_volume": 800,
                    "competition": "low",
                    "difficulty": 45.0,
                    "cpc": 2.50,
                    "trend_data": [
                        {"date": "2024-01-01", "volume": 700, "position": 12},
                        {"date": "2024-02-01", "volume": 750, "position": 10},
                        {"date": "2024-03-01", "volume": 800, "position": 8}
                    ],
                    "related_keywords": ["SEO檢測", "網站分析", "SEO報告"],
                    "category": "SEO",
                    "intent": "informational",
                    "language": "zh-TW",
                    "region": "TW",
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat()
                }
            ],
            "seo_competitors": {
                "domain": "competitor-seo.com",
                "competitor_name": "競爭對手 SEO 工具",
                "industry": "SEO Tools",
                "country": "TW",
                "language": "zh-TW",
                "metrics": {
                    "domain_authority": 72.5,
                    "page_authority": 68.0,
                    "organic_traffic": 45000,
                    "organic_keywords": 1800,
                    "backlinks": 8500,
                    "referring_domains": 420
                },
                "top_keywords": [
                    {
                        "keyword": "SEO優化工具",
                        "position": 2,
                        "search_volume": 1500,
                        "url": "https://competitor-seo.com/tools"
                    },
                    {
                        "keyword": "網站SEO檢測",
                        "position": 1,
                        "search_volume": 1200,
                        "url": "https://competitor-seo.com/check"
                    }
                ],
                "content_analysis": {
                    "total_pages": 150,
                    "blog_posts": 80,
                    "product_pages": 25,
                    "avg_content_length": 1200
                },
                "social_metrics": {
                    "facebook_shares": 2500,
                    "twitter_shares": 1800,
                    "linkedin_shares": 900
                },
                "last_crawled": datetime.now().isoformat(),
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "status": "active"
            }
        }
    
    async def run_integration_tests(self):
        """執行整合測試"""
        print("🧪 開始 Elasticsearch 整合測試...")
        print("=" * 60)
        
        try:
            # 連接到 Elasticsearch
            await es_client.connect()
            print("✅ Elasticsearch 連接成功")
            
            # 執行各項測試
            await self.test_content_indexing()
            await self.test_keyword_management()
            await self.test_competitor_analysis()
            await self.test_search_functionality()
            await self.test_aggregation_queries()
            await self.test_bulk_operations()
            
            # 清理測試數據
            await self.cleanup_test_data()
            
            print("\n" + "=" * 60)
            print("🎉 所有整合測試通過！")
            
        except Exception as e:
            print(f"❌ 整合測試失敗: {e}")
            raise
        finally:
            await es_client.close()
    
    async def test_content_indexing(self):
        """測試內容索引功能"""
        print("\n1️⃣ 測試 SEO 內容索引...")
        
        # 索引測試內容
        success = await es_client.index_document(
            index_name=settings.ES_SEO_CONTENT_INDEX,
            doc_id="test_content_1",
            document=self.test_data["seo_content"]
        )
        
        if success:
            print("   ✅ 內容索引成功")
            
            # 驗證索引的內容
            doc = await es_client.get_document(
                index_name=settings.ES_SEO_CONTENT_INDEX,
                doc_id="test_content_1"
            )
            
            if doc and doc["title"] == self.test_data["seo_content"]["title"]:
                print("   ✅ 內容檢索成功")
            else:
                raise Exception("內容檢索失敗")
        else:
            raise Exception("內容索引失敗")
    
    async def test_keyword_management(self):
        """測試關鍵詞管理功能"""
        print("\n2️⃣ 測試關鍵詞管理...")
        
        # 批量索引關鍵詞
        operations = []
        for i, keyword_data in enumerate(self.test_data["seo_keywords"]):
            operations.extend([
                {"index": {"_index": settings.ES_SEO_KEYWORDS_INDEX, "_id": f"test_keyword_{i+1}"}},
                keyword_data
            ])
        
        stats = await es_client.bulk_index(operations)
        
        if stats["successful"] == len(self.test_data["seo_keywords"]):
            print(f"   ✅ 批量索引成功: {stats['successful']} 個關鍵詞")
            
            # 等待索引刷新
            await asyncio.sleep(1)

            # 測試關鍵詞搜索
            results = await es_client.search(
                index_name=settings.ES_SEO_KEYWORDS_INDEX,
                query={
                    "bool": {
                        "must": [
                            {"range": {"search_volume": {"gte": 500}}}
                        ]
                    }
                },
                size=10
            )
            
            if results["hits"]["total"]["value"] > 0:
                print(f"   ✅ 關鍵詞搜索成功: 找到 {results['hits']['total']['value']} 個結果")
            else:
                raise Exception("關鍵詞搜索失敗")
        else:
            raise Exception(f"批量索引失敗: {stats['failed']} 個失敗")
    
    async def test_competitor_analysis(self):
        """測試競爭對手分析功能"""
        print("\n3️⃣ 測試競爭對手分析...")
        
        # 索引競爭對手數據
        success = await es_client.index_document(
            index_name=settings.ES_SEO_COMPETITORS_INDEX,
            doc_id="test_competitor_1",
            document=self.test_data["seo_competitors"]
        )
        
        if success:
            print("   ✅ 競爭對手數據索引成功")
            
            # 等待索引刷新
            await asyncio.sleep(1)

            # 測試競爭對手搜索
            results = await es_client.search(
                index_name=settings.ES_SEO_COMPETITORS_INDEX,
                query={
                    "bool": {
                        "must": [
                            {"range": {"metrics.domain_authority": {"gte": 70}}}
                        ]
                    }
                },
                sort=[{"metrics.domain_authority": {"order": "desc"}}],
                size=5
            )
            
            if results["hits"]["total"]["value"] > 0:
                print(f"   ✅ 競爭對手搜索成功: 找到 {results['hits']['total']['value']} 個結果")
                
                # 顯示競爭對手信息
                for hit in results["hits"]["hits"]:
                    competitor = hit["_source"]
                    print(f"      📊 {competitor['competitor_name']}: DA={competitor['metrics']['domain_authority']}")
            else:
                raise Exception("競爭對手搜索失敗")
        else:
            raise Exception("競爭對手數據索引失敗")
    
    async def test_search_functionality(self):
        """測試搜索功能"""
        print("\n4️⃣ 測試搜索功能...")
        
        # 測試全文搜索
        results = await es_client.search(
            index_name=settings.ES_SEO_CONTENT_INDEX,
            query={
                "multi_match": {
                    "query": "AI SEO 優化",
                    "fields": ["title^2", "description", "content", "keywords"]
                }
            },
            highlight={
                "fields": {
                    "title": {},
                    "description": {},
                    "content": {}
                }
            },
            size=5
        )
        
        if results["hits"]["total"]["value"] > 0:
            print(f"   ✅ 全文搜索成功: 找到 {results['hits']['total']['value']} 個結果")
            
            # 顯示搜索結果
            for hit in results["hits"]["hits"]:
                source = hit["_source"]
                score = hit["_score"]
                print(f"      🔍 {source['title']} (評分: {score:.2f})")
                
                # 顯示高亮片段
                if "highlight" in hit:
                    for field, highlights in hit["highlight"].items():
                        print(f"         💡 {field}: {highlights[0][:100]}...")
        else:
            print("   ⚠️ 全文搜索未找到結果")
    
    async def test_aggregation_queries(self):
        """測試聚合查詢"""
        print("\n5️⃣ 測試聚合查詢...")
        
        # 測試關鍵詞統計聚合
        results = await es_client.search(
            index_name=settings.ES_SEO_KEYWORDS_INDEX,
            query={"match_all": {}},
            aggregations={
                "avg_search_volume": {
                    "avg": {"field": "search_volume"}
                },
                "competition_distribution": {
                    "terms": {"field": "competition.keyword"}
                },
                "difficulty_ranges": {
                    "range": {
                        "field": "difficulty",
                        "ranges": [
                            {"to": 30, "key": "easy"},
                            {"from": 30, "to": 70, "key": "medium"},
                            {"from": 70, "key": "hard"}
                        ]
                    }
                }
            },
            size=0
        )
        
        if "aggregations" in results:
            aggs = results["aggregations"]
            
            # 顯示聚合結果
            avg_volume = aggs["avg_search_volume"]["value"]
            print(f"   📊 平均搜索量: {avg_volume:.0f}")
            
            print("   📈 競爭度分布:")
            for bucket in aggs["competition_distribution"]["buckets"]:
                print(f"      {bucket['key']}: {bucket['doc_count']} 個關鍵詞")
            
            print("   📉 難度分布:")
            for bucket in aggs["difficulty_ranges"]["buckets"]:
                print(f"      {bucket['key']}: {bucket['doc_count']} 個關鍵詞")
            
            print("   ✅ 聚合查詢成功")
        else:
            raise Exception("聚合查詢失敗")
    
    async def test_bulk_operations(self):
        """測試批量操作"""
        print("\n6️⃣ 測試批量操作...")
        
        # 準備批量更新數據
        operations = []
        
        # 更新現有文檔
        operations.extend([
            {"update": {"_index": settings.ES_SEO_CONTENT_INDEX, "_id": "test_content_1"}},
            {"doc": {"seo_score": 92.0, "updated_at": datetime.now().isoformat()}}
        ])
        
        # 添加新的分析數據
        analysis_data = {
            "url": "https://test.aiseoking.com/sample-page",
            "analysis_type": "comprehensive",
            "analysis_data": {
                "title_analysis": {"score": 85, "issues": []},
                "meta_analysis": {"score": 90, "issues": []},
                "content_analysis": {"score": 88, "issues": ["內容長度可以增加"]},
                "keyword_analysis": {"score": 92, "issues": []},
                "technical_analysis": {"score": 95, "issues": []}
            },
            "recommendations": [
                {
                    "type": "content",
                    "priority": "medium",
                    "description": "建議增加內容長度至 500 字以上",
                    "impact": "medium"
                }
            ],
            "overall_score": 90.0,
            "category_scores": {
                "technical": 95.0,
                "content": 88.0,
                "keywords": 92.0,
                "user_experience": 85.0
            },
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        operations.extend([
            {"index": {"_index": settings.ES_SEO_ANALYSIS_INDEX, "_id": "test_analysis_1"}},
            analysis_data
        ])
        
        # 執行批量操作
        stats = await es_client.bulk_index(operations)
        
        if stats["successful"] == 2 and stats["failed"] == 0:
            print("   ✅ 批量操作成功")
            print(f"      📝 成功: {stats['successful']} 個操作")
            print(f"      ❌ 失敗: {stats['failed']} 個操作")
        else:
            raise Exception(f"批量操作失敗: {stats['failed']} 個失敗")
    
    async def cleanup_test_data(self):
        """清理測試數據"""
        print("\n🧹 清理測試數據...")
        
        test_docs = [
            (settings.ES_SEO_CONTENT_INDEX, "test_content_1"),
            (settings.ES_SEO_ANALYSIS_INDEX, "test_analysis_1"),
            (settings.ES_SEO_KEYWORDS_INDEX, "test_keyword_1"),
            (settings.ES_SEO_KEYWORDS_INDEX, "test_keyword_2"),
            (settings.ES_SEO_COMPETITORS_INDEX, "test_competitor_1")
        ]
        
        cleaned_count = 0
        for index_name, doc_id in test_docs:
            success = await es_client.delete_document(index_name, doc_id)
            if success:
                cleaned_count += 1
        
        print(f"   ✅ 清理完成: {cleaned_count} 個測試文檔已刪除")


async def main():
    """主函數"""
    test = ElasticsearchIntegrationTest()
    
    try:
        await test.run_integration_tests()
        print("\n🎊 Elasticsearch 整合測試全部通過！")
        print("系統已準備好用於生產環境。")
        sys.exit(0)
        
    except KeyboardInterrupt:
        print("\n\n⏹️ 測試被用戶中斷")
        sys.exit(130)
    except Exception as e:
        print(f"\n❌ 整合測試失敗: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
