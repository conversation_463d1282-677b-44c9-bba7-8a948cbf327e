#!/bin/bash

# AI SEO 優化王 - Elasticsearch 管理界面啟動腳本

echo "🚀 AI SEO 優化王 - Elasticsearch 管理界面"
echo "=============================================="

# 設置顏色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 檢查依賴
echo -e "${BLUE}🔍 檢查系統依賴...${NC}"

# 檢查 Python
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ Python3 未安裝${NC}"
    exit 1
fi

# 檢查 Elasticsearch 是否運行
echo -e "${BLUE}🔍 檢查 Elasticsearch 狀態...${NC}"
if curl -s "http://localhost:9200" > /dev/null; then
    ES_STATUS=$(curl -s "http://localhost:9200/_cluster/health" | python3 -c "import sys, json; print(json.load(sys.stdin)['status'])")
    echo -e "${GREEN}✅ Elasticsearch 已啟動 - 狀態: ${ES_STATUS}${NC}"
else
    echo -e "${YELLOW}⚠️  Elasticsearch 未運行在 localhost:9200${NC}"
    echo -e "${YELLOW}💡 請確保 Elasticsearch 已啟動${NC}"
fi

# 檢查端口 8080
if lsof -i :8080 > /dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  端口 8080 已被占用${NC}"
    echo -e "${BLUE}🔄 嘗試終止占用的進程...${NC}"
    pkill -f "python.*8080" 2>/dev/null || true
    sleep 2
fi

# 切換到 web-ui 目錄
cd "$(dirname "$0")/../web-ui"

echo ""
echo -e "${GREEN}🎯 準備啟動 Elasticsearch 管理界面...${NC}"
echo ""

# 啟動服務器
python3 start-server.py 