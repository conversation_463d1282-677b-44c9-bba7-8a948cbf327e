#!/usr/bin/env python3
"""
簡化的後端啟動腳本
只啟動管道相關的 API 端點
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend-fastapi'))

from fastapi import FastAPI, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import uvicorn

# 簡化的依賴項
class CurrentUser:
    def __init__(self, id: str = "admin", email: str = "<EMAIL>", role: str = "admin"):
        self.id = id
        self.email = email
        self.role = role

async def get_current_user() -> CurrentUser:
    return CurrentUser()

# 模擬數據收集管道服務
class MockDataCollectionPipeline:
    def __init__(self):
        self.is_running = False
        self.status = {
            "current_stage": "待機",
            "progress": 0,
            "total_queries_processed": 0,
            "success_rate": 1.0,
            "avg_processing_time": 0
        }
        
    async def start(self):
        self.is_running = True
        self.status["current_stage"] = "運行中"
        return {"success": True, "message": "管道已啟動"}
        
    async def stop(self):
        self.is_running = False
        self.status["current_stage"] = "已停止"
        return {"success": True, "message": "管道已停止"}
        
    async def restart(self):
        await self.stop()
        await self.start()
        return {"success": True, "message": "管道已重啟"}
        
    async def get_status(self):
        return {
            "is_running": self.is_running,
            "last_update": "2025-06-26T13:45:00Z",
            **self.status
        }
        
    async def get_collectors(self):
        return [
            {
                "name": "Google搜索收集器",
                "status": "active" if self.is_running else "idle",
                "last_update": "2025-06-26T13:45:00Z",
                "total_collected": 150,
                "error_count": 2
            },
            {
                "name": "Bing搜索收集器", 
                "status": "active" if self.is_running else "idle",
                "last_update": "2025-06-26T13:45:00Z",
                "total_collected": 120,
                "error_count": 1
            }
        ]
        
    async def get_processors(self):
        return [
            {
                "name": "搜索結果處理器",
                "status": "active" if self.is_running else "idle",
                "last_update": "2025-06-26T13:45:00Z",
                "total_processed": 200,
                "queue_size": 5 if self.is_running else 0
            },
            {
                "name": "內容分析處理器",
                "status": "active" if self.is_running else "idle", 
                "last_update": "2025-06-26T13:45:00Z",
                "total_processed": 180,
                "queue_size": 3 if self.is_running else 0
            }
        ]
        
    async def get_metrics(self):
        return {
            "throughput": 25 if self.is_running else 0,
            "latency": 250,
            "error_rate": 0.02,
            "memory_usage": 45,
            "cpu_usage": 30 if self.is_running else 10
        }
        
    async def test_collection(self, query: str, samples: int = 5):
        return {
            "query": query,
            "samples_processed": samples,
            "success": True,
            "results": f"成功收集了 {samples} 個樣本"
        }

# 全局管道實例
pipeline = MockDataCollectionPipeline()

@asynccontextmanager
async def lifespan(app: FastAPI):
    print("🚀 管道服務器啟動中...")
    yield
    print("🛑 管道服務器關閉中...")

# 創建 FastAPI 應用
app = FastAPI(
    title="AI SEO 數據收集管道",
    description="AI SEO 數據收集管道管理系統",
    version="1.0.0",
    lifespan=lifespan
)

# CORS 中間件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 管道控制端點
@app.get("/api/v1/pipeline/health-check")
async def health_check():
    return {
        "success": True,
        "message": "管道系統健康",
        "data": {
            "status": "healthy",
            "version": "1.0.0",
            "timestamp": "2025-06-26T13:45:00Z"
        }
    }

@app.get("/api/v1/pipeline/status")
async def get_pipeline_status(current_user: CurrentUser = Depends(get_current_user)):
    status = await pipeline.get_status()
    return {
        "success": True,
        "message": "獲取管道狀態成功",
        "data": status
    }

@app.post("/api/v1/pipeline/start")
async def start_pipeline(current_user: CurrentUser = Depends(get_current_user)):
    result = await pipeline.start()
    return result

@app.post("/api/v1/pipeline/stop")
async def stop_pipeline(current_user: CurrentUser = Depends(get_current_user)):
    result = await pipeline.stop()
    return result

@app.post("/api/v1/pipeline/restart")
async def restart_pipeline(current_user: CurrentUser = Depends(get_current_user)):
    result = await pipeline.restart()
    return result

@app.get("/api/v1/pipeline/collectors")
async def get_collectors(current_user: CurrentUser = Depends(get_current_user)):
    collectors = await pipeline.get_collectors()
    return {
        "success": True,
        "message": "獲取收集器狀態成功",
        "data": collectors
    }

@app.get("/api/v1/pipeline/processors")
async def get_processors(current_user: CurrentUser = Depends(get_current_user)):
    processors = await pipeline.get_processors()
    return {
        "success": True,
        "message": "獲取處理器狀態成功",
        "data": processors
    }

@app.get("/api/v1/pipeline/metrics")
async def get_metrics(current_user: CurrentUser = Depends(get_current_user)):
    metrics = await pipeline.get_metrics()
    return {
        "success": True,
        "message": "獲取性能指標成功",
        "data": metrics
    }

@app.post("/api/v1/pipeline/test")
async def test_collection(
    request: dict,
    current_user: CurrentUser = Depends(get_current_user)
):
    query = request.get("query", "測試查詢")
    samples = request.get("samples", 5)
    
    result = await pipeline.test_collection(query, samples)
    return {
        "success": True,
        "message": "測試收集完成",
        "data": result
    }

# 儀表板端點
@app.get("/api/v1/pipeline/dashboard/overview")
async def get_dashboard_overview(current_user: CurrentUser = Depends(get_current_user)):
    metrics = await pipeline.get_metrics()
    collectors = await pipeline.get_collectors()
    processors = await pipeline.get_processors()
    
    return {
        "success": True,
        "message": "獲取儀表板概覽成功",
        "data": {
            "total_throughput": metrics["throughput"],
            "avg_latency": metrics["latency"],
            "total_errors": sum(c["error_count"] for c in collectors),
            "active_collectors": len([c for c in collectors if c["status"] == "active"]),
            "active_processors": len([p for p in processors if p["status"] == "active"]),
            "system_health_score": 95 if pipeline.is_running else 60
        }
    }

@app.get("/api/v1/pipeline/dashboard/timeseries")
async def get_timeseries_data(time_range: str = "1h"):
    import datetime
    import random
    
    # 生成模擬時間序列數據
    now = datetime.datetime.now()
    data = []
    
    for i in range(20):
        timestamp = now - datetime.timedelta(minutes=i*3)
        data.append({
            "timestamp": timestamp.isoformat(),
            "throughput": random.randint(20, 30) if pipeline.is_running else random.randint(0, 5),
            "latency": random.randint(200, 300),
            "error_rate": random.uniform(0.01, 0.05),
            "memory_usage": random.randint(40, 60),
            "cpu_usage": random.randint(20, 40) if pipeline.is_running else random.randint(5, 15)
        })
    
    return {
        "success": True,
        "message": "獲取時間序列數據成功",
        "data": data[::-1]  # 反轉列表，使時間順序正確
    }

@app.get("/api/v1/pipeline/dashboard/alerts")
async def get_alerts():
    return {
        "success": True,
        "message": "獲取告警規則成功",
        "data": [
            {
                "id": "alert_1",
                "name": "高錯誤率告警",
                "condition": "error_rate > threshold",
                "threshold": 0.1,
                "enabled": True,
                "severity": "high"
            },
            {
                "id": "alert_2", 
                "name": "內存使用率告警",
                "condition": "memory_usage > threshold",
                "threshold": 80,
                "enabled": True,
                "severity": "medium"
            }
        ]
    }

if __name__ == "__main__":
    print("🚀 啟動簡化管道服務器...")
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info") 