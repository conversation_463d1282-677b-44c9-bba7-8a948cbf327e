-- 查詢分析相關表結構
-- 創建時間：2024-01-01
-- 用途：支持查詢記錄、統計分析和關鍵詞分析功能

-- 1. 查詢記錄表（主要在Elasticsearch中，這裡作為備份和結構化查詢）
CREATE TABLE IF NOT EXISTS query_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    query TEXT NOT NULL,
    normalized_query TEXT,
    source VARCHAR(50) NOT NULL,
    intent VARCHAR(50),
    user_id UUID REFERENCES auth.users(id),
    session_id VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    device_type VARCHAR(50),
    language VARCHAR(10),
    location JSONB,
    response_time FLOAT,
    results_count INTEGER,
    clicked_results JSONB,
    keywords TEXT[],
    topics TEXT[],
    sentiment JSONB,
    metadata JSONB,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    processed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. 小時查詢統計表
CREATE TABLE IF NOT EXISTS query_hourly_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    hour TIMESTAMPTZ NOT NULL,
    total_queries INTEGER NOT NULL DEFAULT 0,
    unique_queries INTEGER NOT NULL DEFAULT 0,
    unique_users INTEGER NOT NULL DEFAULT 0,
    avg_response_time FLOAT NOT NULL DEFAULT 0,
    query_success_rate FLOAT NOT NULL DEFAULT 0,
    success_rate FLOAT NOT NULL DEFAULT 0,
    top_queries JSONB DEFAULT '{}',
    source_distribution JSONB DEFAULT '{}',
    intent_distribution JSONB DEFAULT '{}',
    device_distribution JSONB DEFAULT '{}',
    geographic_distribution JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(hour)
);

-- 3. 每日查詢統計表
CREATE TABLE IF NOT EXISTS query_daily_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    date DATE NOT NULL,
    total_queries INTEGER NOT NULL DEFAULT 0,
    unique_queries INTEGER NOT NULL DEFAULT 0,
    unique_users INTEGER NOT NULL DEFAULT 0,
    avg_response_time FLOAT NOT NULL DEFAULT 0,
    query_success_rate FLOAT NOT NULL DEFAULT 0,
    peak_hour INTEGER CHECK (peak_hour >= 0 AND peak_hour <= 23),
    hourly_distribution INTEGER[] DEFAULT ARRAY[]::INTEGER[],
    top_queries JSONB DEFAULT '{}',
    source_distribution JSONB DEFAULT '{}',
    intent_distribution JSONB DEFAULT '{}',
    device_distribution JSONB DEFAULT '{}',
    geographic_distribution JSONB DEFAULT '{}',
    growth_rate FLOAT DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(date)
);

-- 4. 每週查詢統計表
CREATE TABLE IF NOT EXISTS query_weekly_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    week_start DATE NOT NULL,
    week_end DATE NOT NULL,
    total_queries INTEGER NOT NULL DEFAULT 0,
    unique_queries INTEGER NOT NULL DEFAULT 0,
    unique_users INTEGER NOT NULL DEFAULT 0,
    avg_response_time FLOAT NOT NULL DEFAULT 0,
    query_success_rate FLOAT NOT NULL DEFAULT 0,
    daily_distribution INTEGER[] DEFAULT ARRAY[]::INTEGER[],
    growth_rate FLOAT DEFAULT 0,
    top_queries JSONB DEFAULT '{}',
    trending_queries JSONB DEFAULT '{}',
    source_distribution JSONB DEFAULT '{}',
    intent_distribution JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(week_start, week_end)
);

-- 5. 每月查詢統計表
CREATE TABLE IF NOT EXISTS query_monthly_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    year INTEGER NOT NULL,
    month INTEGER NOT NULL CHECK (month >= 1 AND month <= 12),
    total_queries INTEGER NOT NULL DEFAULT 0,
    unique_queries INTEGER NOT NULL DEFAULT 0,
    unique_users INTEGER NOT NULL DEFAULT 0,
    avg_response_time FLOAT NOT NULL DEFAULT 0,
    query_success_rate FLOAT NOT NULL DEFAULT 0,
    weekly_distribution INTEGER[] DEFAULT ARRAY[]::INTEGER[],
    growth_rate FLOAT DEFAULT 0,
    seasonal_trends JSONB DEFAULT '{}',
    top_queries JSONB DEFAULT '{}',
    source_distribution JSONB DEFAULT '{}',
    intent_distribution JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(year, month)
);

-- 6. 關鍵詞小時統計表
CREATE TABLE IF NOT EXISTS keyword_hourly_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    hour TIMESTAMPTZ NOT NULL,
    keyword VARCHAR(255) NOT NULL,
    frequency INTEGER NOT NULL DEFAULT 0,
    unique_sessions INTEGER NOT NULL DEFAULT 0,
    avg_position FLOAT,
    click_through_rate FLOAT,
    conversion_rate FLOAT,
    search_volume INTEGER,
    competition FLOAT,
    cpc FLOAT,
    avg_response_time FLOAT DEFAULT 0,
    source_distribution JSONB DEFAULT '{}',
    intent_distribution JSONB DEFAULT '{}',
    trend_direction VARCHAR(20),
    related_keywords TEXT[],
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(hour, keyword)
);

-- 7. 關鍵詞每日統計表
CREATE TABLE IF NOT EXISTS keyword_daily_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    date DATE NOT NULL,
    keyword VARCHAR(255) NOT NULL,
    frequency INTEGER NOT NULL DEFAULT 0,
    unique_sessions INTEGER NOT NULL DEFAULT 0,
    avg_position FLOAT,
    click_through_rate FLOAT,
    conversion_rate FLOAT,
    search_volume INTEGER,
    competition FLOAT,
    cpc FLOAT,
    avg_response_time FLOAT DEFAULT 0,
    source_distribution JSONB DEFAULT '{}',
    intent_distribution JSONB DEFAULT '{}',
    trend_direction VARCHAR(20),
    growth_rate FLOAT DEFAULT 0,
    related_keywords TEXT[],
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(date, keyword)
);

-- 8. 關鍵詞分析結果表
CREATE TABLE IF NOT EXISTS keyword_analysis_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    analysis_id VARCHAR(255) NOT NULL UNIQUE,
    timeframe VARCHAR(20) NOT NULL,
    start_date TIMESTAMPTZ NOT NULL,
    end_date TIMESTAMPTZ NOT NULL,
    total_keywords INTEGER NOT NULL DEFAULT 0,
    top_keywords JSONB DEFAULT '[]',
    rising_keywords JSONB DEFAULT '[]',
    declining_keywords JSONB DEFAULT '[]',
    keyword_clusters JSONB DEFAULT '[]',
    semantic_groups JSONB DEFAULT '[]',
    intent_distribution JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 9. 趨勢分析結果表
CREATE TABLE IF NOT EXISTS trend_analysis_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    analysis_id VARCHAR(255) NOT NULL UNIQUE,
    timeframe VARCHAR(20) NOT NULL,
    start_date TIMESTAMPTZ NOT NULL,
    end_date TIMESTAMPTZ NOT NULL,
    overall_trend VARCHAR(20) NOT NULL,
    growth_rate FLOAT DEFAULT 0,
    keyword_trends JSONB DEFAULT '[]',
    emerging_topics JSONB DEFAULT '[]',
    declining_topics JSONB DEFAULT '[]',
    seasonal_insights JSONB DEFAULT '{}',
    anomalies JSONB DEFAULT '[]',
    predictions JSONB DEFAULT '[]',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 10. 分析配置表
CREATE TABLE IF NOT EXISTS analytics_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    config_name VARCHAR(100) NOT NULL UNIQUE,
    retention_days INTEGER NOT NULL DEFAULT 365,
    sampling_rate FLOAT NOT NULL DEFAULT 1.0 CHECK (sampling_rate >= 0.1 AND sampling_rate <= 1.0),
    enable_real_time BOOLEAN NOT NULL DEFAULT TRUE,
    alert_thresholds JSONB DEFAULT '{}',
    auto_report_schedule JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 11. 分析報告表
CREATE TABLE IF NOT EXISTS analytics_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    report_id VARCHAR(255) NOT NULL UNIQUE,
    report_type VARCHAR(50) NOT NULL,
    timeframe VARCHAR(20) NOT NULL,
    start_date TIMESTAMPTZ NOT NULL,
    end_date TIMESTAMPTZ NOT NULL,
    format VARCHAR(20) NOT NULL DEFAULT 'json',
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    download_url TEXT,
    file_path TEXT,
    file_size BIGINT,
    include_keywords BOOLEAN DEFAULT TRUE,
    include_trends BOOLEAN DEFAULT TRUE,
    include_geographic BOOLEAN DEFAULT FALSE,
    email_to TEXT[],
    generated_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ
);

-- 12. 查詢會話表
CREATE TABLE IF NOT EXISTS query_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id VARCHAR(255) NOT NULL UNIQUE,
    user_id UUID REFERENCES auth.users(id),
    ip_address INET,
    user_agent TEXT,
    device_type VARCHAR(50),
    language VARCHAR(10),
    location JSONB,
    start_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    end_time TIMESTAMPTZ,
    duration_seconds INTEGER,
    total_queries INTEGER DEFAULT 0,
    unique_queries INTEGER DEFAULT 0,
    avg_response_time FLOAT DEFAULT 0,
    bounce_rate FLOAT DEFAULT 0,
    conversion_events JSONB DEFAULT '[]',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 創建索引
-- 查詢記錄索引
CREATE INDEX IF NOT EXISTS idx_query_records_timestamp ON query_records(timestamp);
CREATE INDEX IF NOT EXISTS idx_query_records_source ON query_records(source);
CREATE INDEX IF NOT EXISTS idx_query_records_user_id ON query_records(user_id);
CREATE INDEX IF NOT EXISTS idx_query_records_session_id ON query_records(session_id);
CREATE INDEX IF NOT EXISTS idx_query_records_normalized_query ON query_records(normalized_query);
CREATE INDEX IF NOT EXISTS idx_query_records_keywords ON query_records USING GIN(keywords);
CREATE INDEX IF NOT EXISTS idx_query_records_location ON query_records USING GIN(location);

-- 統計表索引
CREATE INDEX IF NOT EXISTS idx_query_hourly_stats_hour ON query_hourly_stats(hour);
CREATE INDEX IF NOT EXISTS idx_query_daily_stats_date ON query_daily_stats(date);
CREATE INDEX IF NOT EXISTS idx_query_weekly_stats_week_start ON query_weekly_stats(week_start);
CREATE INDEX IF NOT EXISTS idx_query_monthly_stats_year_month ON query_monthly_stats(year, month);

-- 關鍵詞統計索引
CREATE INDEX IF NOT EXISTS idx_keyword_hourly_stats_hour ON keyword_hourly_stats(hour);
CREATE INDEX IF NOT EXISTS idx_keyword_hourly_stats_keyword ON keyword_hourly_stats(keyword);
CREATE INDEX IF NOT EXISTS idx_keyword_hourly_stats_frequency ON keyword_hourly_stats(frequency DESC);
CREATE INDEX IF NOT EXISTS idx_keyword_daily_stats_date ON keyword_daily_stats(date);
CREATE INDEX IF NOT EXISTS idx_keyword_daily_stats_keyword ON keyword_daily_stats(keyword);

-- 分析結果索引
CREATE INDEX IF NOT EXISTS idx_keyword_analysis_results_timeframe ON keyword_analysis_results(timeframe);
CREATE INDEX IF NOT EXISTS idx_keyword_analysis_results_dates ON keyword_analysis_results(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_trend_analysis_results_timeframe ON trend_analysis_results(timeframe);
CREATE INDEX IF NOT EXISTS idx_trend_analysis_results_dates ON trend_analysis_results(start_date, end_date);

-- 會話索引
CREATE INDEX IF NOT EXISTS idx_query_sessions_session_id ON query_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_query_sessions_user_id ON query_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_query_sessions_start_time ON query_sessions(start_time);

-- 創建函數和觸發器
-- 更新 updated_at 欄位的函數
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 為相關表添加更新觸發器
CREATE TRIGGER update_query_records_updated_at BEFORE UPDATE ON query_records
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_query_hourly_stats_updated_at BEFORE UPDATE ON query_hourly_stats
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_query_daily_stats_updated_at BEFORE UPDATE ON query_daily_stats
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_query_weekly_stats_updated_at BEFORE UPDATE ON query_weekly_stats
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_query_monthly_stats_updated_at BEFORE UPDATE ON query_monthly_stats
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_keyword_hourly_stats_updated_at BEFORE UPDATE ON keyword_hourly_stats
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_keyword_daily_stats_updated_at BEFORE UPDATE ON keyword_daily_stats
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_keyword_analysis_results_updated_at BEFORE UPDATE ON keyword_analysis_results
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_trend_analysis_results_updated_at BEFORE UPDATE ON trend_analysis_results
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_analytics_configs_updated_at BEFORE UPDATE ON analytics_configs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_query_sessions_updated_at BEFORE UPDATE ON query_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 創建分析用的統計函數
CREATE OR REPLACE FUNCTION get_query_stats_by_timeframe(
    p_timeframe TEXT,
    p_start_date TIMESTAMPTZ,
    p_end_date TIMESTAMPTZ
) RETURNS TABLE (
    period TEXT,
    total_queries BIGINT,
    unique_queries BIGINT,
    unique_users BIGINT,
    avg_response_time NUMERIC
) AS $$
BEGIN
    CASE p_timeframe
        WHEN 'hour' THEN
            RETURN QUERY
            SELECT 
                to_char(date_trunc('hour', timestamp), 'YYYY-MM-DD HH24:00:00') as period,
                COUNT(*)::BIGINT as total_queries,
                COUNT(DISTINCT normalized_query)::BIGINT as unique_queries,
                COUNT(DISTINCT user_id)::BIGINT as unique_users,
                AVG(response_time)::NUMERIC as avg_response_time
            FROM query_records
            WHERE timestamp >= p_start_date AND timestamp <= p_end_date
            GROUP BY date_trunc('hour', timestamp)
            ORDER BY date_trunc('hour', timestamp);
        
        WHEN 'day' THEN
            RETURN QUERY
            SELECT 
                to_char(date_trunc('day', timestamp), 'YYYY-MM-DD') as period,
                COUNT(*)::BIGINT as total_queries,
                COUNT(DISTINCT normalized_query)::BIGINT as unique_queries,
                COUNT(DISTINCT user_id)::BIGINT as unique_users,
                AVG(response_time)::NUMERIC as avg_response_time
            FROM query_records
            WHERE timestamp >= p_start_date AND timestamp <= p_end_date
            GROUP BY date_trunc('day', timestamp)
            ORDER BY date_trunc('day', timestamp);
        
        WHEN 'week' THEN
            RETURN QUERY
            SELECT 
                to_char(date_trunc('week', timestamp), 'YYYY-MM-DD') as period,
                COUNT(*)::BIGINT as total_queries,
                COUNT(DISTINCT normalized_query)::BIGINT as unique_queries,
                COUNT(DISTINCT user_id)::BIGINT as unique_users,
                AVG(response_time)::NUMERIC as avg_response_time
            FROM query_records
            WHERE timestamp >= p_start_date AND timestamp <= p_end_date
            GROUP BY date_trunc('week', timestamp)
            ORDER BY date_trunc('week', timestamp);
        
        ELSE
            RETURN QUERY
            SELECT 
                to_char(date_trunc('month', timestamp), 'YYYY-MM') as period,
                COUNT(*)::BIGINT as total_queries,
                COUNT(DISTINCT normalized_query)::BIGINT as unique_queries,
                COUNT(DISTINCT user_id)::BIGINT as unique_users,
                AVG(response_time)::NUMERIC as avg_response_time
            FROM query_records
            WHERE timestamp >= p_start_date AND timestamp <= p_end_date
            GROUP BY date_trunc('month', timestamp)
            ORDER BY date_trunc('month', timestamp);
    END CASE;
END;
$$ LANGUAGE plpgsql;

-- 創建關鍵詞統計函數
CREATE OR REPLACE FUNCTION get_top_keywords(
    p_start_date TIMESTAMPTZ,
    p_end_date TIMESTAMPTZ,
    p_limit INTEGER DEFAULT 100
) RETURNS TABLE (
    keyword TEXT,
    frequency BIGINT,
    unique_sessions BIGINT,
    avg_response_time NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        unnest(keywords) as keyword,
        COUNT(*)::BIGINT as frequency,
        COUNT(DISTINCT session_id)::BIGINT as unique_sessions,
        AVG(response_time)::NUMERIC as avg_response_time
    FROM query_records
    WHERE timestamp >= p_start_date 
        AND timestamp <= p_end_date
        AND keywords IS NOT NULL
        AND array_length(keywords, 1) > 0
    GROUP BY unnest(keywords)
    HAVING COUNT(*) >= 5  -- 最小頻率閾值
    ORDER BY COUNT(*) DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- 插入默認配置
INSERT INTO analytics_configs (config_name, retention_days, sampling_rate, enable_real_time, alert_thresholds)
VALUES (
    'default',
    365,
    1.0,
    TRUE,
    '{
        "high_response_time_threshold": 2000,
        "low_success_rate_threshold": 0.95,
        "high_error_rate_threshold": 0.05,
        "query_volume_spike_threshold": 2.0
    }'::JSONB
)
ON CONFLICT (config_name) DO NOTHING;

-- 創建用於TimescaleDB的超表（如果使用TimescaleDB）
-- 注意：這需要TimescaleDB擴展
-- SELECT create_hypertable('query_records', 'timestamp', if_not_exists => TRUE);
-- SELECT create_hypertable('query_hourly_stats', 'hour', if_not_exists => TRUE);
-- SELECT create_hypertable('keyword_hourly_stats', 'hour', if_not_exists => TRUE);

-- 創建數據保留策略（如果使用TimescaleDB）
-- SELECT add_retention_policy('query_records', INTERVAL '1 year', if_not_exists => TRUE);
-- SELECT add_retention_policy('query_hourly_stats', INTERVAL '2 years', if_not_exists => TRUE);

COMMIT; 