# AI SEO 優化王 - 混合後端架構 Docker Compose

version: '3.8'

services:
  # Next.js 前端服務
  nextjs:
    build:
      context: .
      dockerfile: Dockerfile
      target: runner
    container_name: aiseo-frontend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_FASTAPI_URL=http://fastapi:8000
      - NEXT_PUBLIC_EXPRESS_URL=http://express:3001
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
      # 開發和調試設定
      - ENABLE_DEBUG_MODE=${ENABLE_DEBUG_MODE:-false}
      - ENABLE_PERFORMANCE_MONITORING=${ENABLE_PERFORMANCE_MONITORING:-true}
      - ENABLE_ERROR_TRACKING=${ENABLE_ERROR_TRACKING:-true}
    depends_on:
      - fastapi
      - express
      - redis
    networks:
      - ai-seo-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Python FastAPI 核心服務
  fastapi:
    build:
      context: ./backend-fastapi
      dockerfile: Dockerfile
      target: runner
    container_name: aiseo-backend-fastapi
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
      - DATABASE_URL=${DATABASE_URL:-******************************************************/ai_seo_king}
      - REDIS_URL=redis://redis:6379/0
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - JWT_SECRET=${JWT_SECRET}
      - SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - EXPRESS_SERVICE_URL=http://express:3001
      - NEXTJS_FRONTEND_URL=http://nextjs:3000
      # Elasticsearch 配置
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - ELASTICSEARCH_TIMEOUT=30
      - ELASTICSEARCH_MAX_RETRIES=3
    depends_on:
      - postgres
      - redis
      - elasticsearch
      - kibana
    networks:
      - ai-seo-network
    restart: unless-stopped
    volumes:
      - fastapi-logs:/app/logs
      - fastapi-uploads:/app/uploads
      - fastapi-reports:/app/reports
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Node.js Express 輔助服務
  express:
    build:
      context: ./backend-express
      dockerfile: Dockerfile
      target: runner
    container_name: aiseo-backend-express
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - FASTAPI_URL=http://fastapi:8000
      - DATABASE_URL=${DATABASE_URL:-******************************************************/ai_seo_king}
      - REDIS_URL=redis://redis:6379/1
      - JWT_SECRET=${JWT_SECRET}
      - UPLOAD_DIR=/app/uploads
      - UPLOAD_MAX_SIZE=50MB
    depends_on:
      - redis
      - postgres
    networks:
      - ai-seo-network
    restart: unless-stopped
    volumes:
      - express-uploads:/app/uploads
      - express-logs:/app/logs
      - express-temp:/app/temp
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL 數據庫
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=ai_seo_user
      - POSTGRES_PASSWORD=ai_seo_password
      - POSTGRES_DB=ai_seo_king
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - ai-seo-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ai_seo_user -d ai_seo_king"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis 快取服務
  redis:
    image: redis:7-alpine
    container_name: aiseo-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru --requirepass ${REDIS_PASSWORD:-aiseo123}
    volumes:
      - redis-data:/data
    networks:
      - ai-seo-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "${REDIS_PASSWORD:-aiseo123}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Elasticsearch 搜索引擎
  elasticsearch:
    image: elasticsearch:7.17.15
    container_name: aiseo-elasticsearch
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - cluster.name=ai-seo-king
      - node.name=seo-node-1
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - xpack.security.enabled=false
      - xpack.monitoring.collection.enabled=false
      # CORS 設置以支持 Web UI
      - http.cors.enabled=true
      - http.cors.allow-origin="*"
      - http.cors.allow-headers="X-Requested-With,X-Auth-Token,Content-Type,Content-Length,Authorization"
      - http.cors.allow-credentials=true
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
      - elasticsearch-logs:/usr/share/elasticsearch/logs
    networks:
      - ai-seo-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Kibana 數據可視化和分析
  kibana:
    image: kibana:7.17.15
    container_name: aiseo-kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - SERVER_NAME=ai-seo-kibana
      - SERVER_HOST=0.0.0.0
      - ELASTICSEARCH_USERNAME=
      - ELASTICSEARCH_PASSWORD=
      # 中文語言支持
      - I18N_LOCALE=zh-CN
      # 禁用遙測
      - TELEMETRY_ENABLED=false
      - TELEMETRY_OPTIN=false
    depends_on:
      - elasticsearch
    volumes:
      - kibana-data:/usr/share/kibana/data
      - kibana-logs:/usr/share/kibana/logs
    networks:
      - ai-seo-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

  # Elasticvue 輕量級管理界面
  elasticvue:
    image: cars10/elasticvue:latest
    container_name: aiseo-elasticvue
    ports:
      - "8080:8080"
    environment:
      - ELASTICVUE_CLUSTERS=[{"name":"ai-seo-king","uri":"http://elasticsearch:9200"}]
    depends_on:
      - elasticsearch
    networks:
      - ai-seo-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8080 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - nextjs
      - fastapi
      - express
    networks:
      - ai-seo-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus 監控
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - ai-seo-network
    restart: unless-stopped

  # Grafana 儀表板
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3002:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - ai-seo-network
    restart: unless-stopped

volumes:
  # 數據庫數據
  postgres-data:
    driver: local
  redis-data:
    driver: local

  # Elasticsearch 數據
  elasticsearch-data:
    driver: local
  elasticsearch-logs:
    driver: local

  # Kibana 數據
  kibana-data:
    driver: local
  kibana-logs:
    driver: local

  # FastAPI 服務數據
  fastapi-logs:
    driver: local
  fastapi-uploads:
    driver: local
  fastapi-reports:
    driver: local

  # Express 服務數據
  express-uploads:
    driver: local
  express-logs:
    driver: local
  express-temp:
    driver: local

  # 監控數據
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

networks:
  ai-seo-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
