{"name": "ai-seo-king", "version": "0.1.0", "description": "AI SEO 優化王 - 全端 SaaS 平台", "private": true, "scripts": {"dev": "next dev -p 3001", "dev:redirect": "concurrently \"npm run dev\" \"node scripts/port-redirect-server.js\"", "build": "next build", "start": "next start", "lint": "next lint", "admin:dev": "concurrently \"npm run dev\" \"npm run admin:backend\" \"npm run admin:ai\"", "admin:backend": "cd backend-fastapi && python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000", "admin:ai": "cd backend-fastapi/ai-service && python intent_classifier.py", "admin:start": "chmod +x scripts/start-admin-services.sh && ./scripts/start-admin-services.sh", "admin:stop": "chmod +x scripts/start-admin-services.sh && ./scripts/start-admin-services.sh stop", "admin:restart": "chmod +x scripts/start-admin-services.sh && ./scripts/start-admin-services.sh restart", "admin:logs": "docker-compose -f docker-compose.admin.yml logs -f", "admin:status": "./scripts/start-admin-services.sh status", "db:migrate": "psql $DATABASE_URL -f scripts/database-migration.sql", "test:admin": "jest --testMatch '**/admin/**/*.test.{js,ts,tsx}'", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "test:load": "artillery run performance/load-test.yml", "test:stress": "artillery run performance/stress-test.yml", "test:api": "artillery run performance/api-test.yml", "test:performance": "npm run test:load && npm run test:api", "audit": "npm audit fix", "outdated": "npm outdated", "validate-links": "ts-node --project scripts/tsconfig.json scripts/validate-links.ts", "validate-links:ci": "ts-node --project scripts/tsconfig.json scripts/validate-links-ci.ts", "validate-links:pre-commit": "ts-node --project scripts/tsconfig.json scripts/validate-links-pre-commit.ts", "post-deploy:check": "ts-node --project scripts/tsconfig.json scripts/post-deploy-check.ts", "prepare": "husky || true"}, "dependencies": {"@next/mdx": "14.0.4", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@supabase/auth-helpers-nextjs": "0.8.7", "@supabase/supabase-js": "2.39.0", "@types/bcryptjs": "^2.4.6", "@types/d3": "^7.4.3", "@types/file-saver": "^2.0.7", "@types/ioredis": "^4.28.10", "@types/jsdom": "^21.1.7", "@types/jsonwebtoken": "^9.0.10", "@types/react-window": "^1.8.8", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "concurrently": "^8.2.2", "contentful": "10.6.4", "critters": "^0.0.23", "d3": "^7.9.0", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "framer-motion": "^10.16.4", "gray-matter": "4.0.3", "html2canvas": "^1.4.1", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "kafkajs": "^2.2.4", "lucide-react": "^0.294.0", "next": "14.0.4", "next-intl": "3.4.1", "next-mdx-remote": "5.0.0", "node-cache": "^5.1.2", "nodemailer": "6.9.7", "openai": "^5.6.0", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-hook-form": "^7.58.1", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "recharts": "^2.15.4", "redis": "^5.5.6", "remark-gfm": "3.0.1", "socket.io": "^4.8.1", "sonner": "^2.0.5", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1", "web-vitals": "^5.0.3", "ws": "^8.18.2", "xlsx": "^0.18.5", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@playwright/test": "^1.40.1", "@shadcn/ui": "^0.0.4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/archiver": "^6.0.3", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/helmet": "^0.0.48", "@types/jest": "^29.5.14", "@types/mime-types": "^3.0.1", "@types/morgan": "^1.9.10", "@types/multer": "^1.4.13", "@types/node": "^20.10.1", "@types/nodemailer": "^6.4.17", "@types/react": "^18", "@types/react-dom": "^18", "@types/sharp": "^0.31.1", "@types/uuid": "^9.0.8", "@types/ws": "^8.18.1", "artillery": "^2.0.23", "autoprefixer": "^10.4.21", "chalk": "^4.1.2", "cypress": "^13.6.1", "eslint": "^8.40.0", "eslint-config-next": "14.0.4", "eslint-plugin-jest": "^27.6.0", "express": "^5.1.0", "glob": "^10.3.10", "http-proxy-middleware": "^3.0.5", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "^26.1.0", "lighthouse": "^12.6.1", "node-fetch": "^2.7.0", "ora": "^5.4.1", "postcss": "^8.5.6", "socket.io-client": "^4.8.1", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "tsx": "^4.20.3", "tw-animate-css": "^1.3.4", "typescript": "^5"}, "engines": {"node": ">=18.0.0"}}